#!/usr/bin/env python3
import xmlrpc.client

def quick_uom_fix():
    try:
        # Connection details
        url = 'http://localhost:8069'
        db = 'dataicraft'
        username = '<EMAIL>'
        password = 'admin123'
        
        # Connect to Odoo
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            print("❌ Authentication failed!")
            return
        
        print(f"✅ Connected to Odoo as user ID: {uid}")
        
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        print("\n🔧 === Quick UoM Fix === 🔧")
        
        # Get UoM IDs
        units_uom = models.execute_kw(
            db, uid, password,
            'uom.uom', 'search_read',
            [[['name', '=', 'Units']]],
            {'fields': ['id', 'name', 'category_id']}
        )
        
        hours_uom = models.execute_kw(
            db, uid, password,
            'uom.uom', 'search_read',
            [[['name', '=', 'Hours']]],
            {'fields': ['id', 'name', 'category_id']}
        )
        
        if not units_uom or not hours_uom:
            print("❌ Required UoMs not found")
            return
        
        units_id = units_uom[0]['id']
        hours_id = hours_uom[0]['id']
        
        print(f"📏 Units UoM ID: {units_id}")
        print(f"⏰ Hours UoM ID: {hours_id}")
        
        # Fix all service products to use Hours
        print("\n🔧 Fixing service products...")
        
        service_products = models.execute_kw(
            db, uid, password,
            'product.template', 'search_read',
            [[['type', '=', 'service']]],
            {'fields': ['id', 'name', 'uom_id', 'uom_po_id', 'service_type']}
        )
        
        for product in service_products:
            try:
                # Check if it's a timesheet service or if UoM is wrong
                if (product.get('service_type') == 'timesheet' or 
                    product['uom_id'][0] != hours_id):
                    
                    models.execute_kw(
                        db, uid, password,
                        'product.template', 'write',
                        [[product['id']], {
                            'uom_id': hours_id,
                            'uom_po_id': hours_id
                        }]
                    )
                    print(f"  ✅ Fixed service: {product['name']} → Hours")
                    
            except Exception as e:
                print(f"  ⚠️ Error fixing {product['name']}: {e}")
        
        # Fix all storable/consumable products to use Units
        print("\n🔧 Fixing physical products...")
        
        physical_products = models.execute_kw(
            db, uid, password,
            'product.template', 'search_read',
            [[['type', 'in', ['product', 'consu']]]],
            {'fields': ['id', 'name', 'uom_id', 'uom_po_id']}
        )
        
        for product in physical_products:
            try:
                if product['uom_id'][0] != units_id:
                    models.execute_kw(
                        db, uid, password,
                        'product.template', 'write',
                        [[product['id']], {
                            'uom_id': units_id,
                            'uom_po_id': units_id
                        }]
                    )
                    print(f"  ✅ Fixed product: {product['name']} → Units")
                    
            except Exception as e:
                print(f"  ⚠️ Error fixing {product['name']}: {e}")
        
        # Check for any pending sales/purchase order lines with UoM issues
        print("\n🔧 Checking order lines...")
        
        # Check sales order lines
        so_lines = models.execute_kw(
            db, uid, password,
            'sale.order.line', 'search_read',
            [[['state', 'in', ['draft', 'sent']]]],
            {'fields': ['id', 'product_id', 'product_uom', 'order_id']}
        )
        
        fixed_so_lines = 0
        for line in so_lines:
            try:
                if line['product_id']:
                    product = models.execute_kw(
                        db, uid, password,
                        'product.product', 'read',
                        [line['product_id'][0]],
                        {'fields': ['uom_id', 'type', 'service_type']}
                    )
                    
                    correct_uom = product['uom_id'][0]
                    if line['product_uom'][0] != correct_uom:
                        models.execute_kw(
                            db, uid, password,
                            'sale.order.line', 'write',
                            [[line['id']], {'product_uom': correct_uom}]
                        )
                        print(f"  ✅ Fixed SO line for product: {line['product_id'][1]}")
                        fixed_so_lines += 1
                        
            except Exception as e:
                print(f"  ⚠️ Error fixing SO line: {e}")
        
        # Check purchase order lines
        po_lines = models.execute_kw(
            db, uid, password,
            'purchase.order.line', 'search_read',
            [[['state', 'in', ['draft', 'sent']]]],
            {'fields': ['id', 'product_id', 'product_uom', 'order_id']}
        )
        
        fixed_po_lines = 0
        for line in po_lines:
            try:
                if line['product_id']:
                    product = models.execute_kw(
                        db, uid, password,
                        'product.product', 'read',
                        [line['product_id'][0]],
                        {'fields': ['uom_id', 'type', 'service_type']}
                    )
                    
                    correct_uom = product['uom_id'][0]
                    if line['product_uom'][0] != correct_uom:
                        models.execute_kw(
                            db, uid, password,
                            'purchase.order.line', 'write',
                            [[line['id']], {'product_uom': correct_uom}]
                        )
                        print(f"  ✅ Fixed PO line for product: {line['product_id'][1]}")
                        fixed_po_lines += 1
                        
            except Exception as e:
                print(f"  ⚠️ Error fixing PO line: {e}")
        
        print(f"\n🎉 Quick UoM Fix Complete!")
        print(f"✅ Fixed {len(service_products)} service products")
        print(f"✅ Fixed {len(physical_products)} physical products")
        print(f"✅ Fixed {fixed_so_lines} sales order lines")
        print(f"✅ Fixed {fixed_po_lines} purchase order lines")
        
        print(f"\n📋 UoM Rules Applied:")
        print(f"  ⏰ Service products → Hours (Working Time category)")
        print(f"  📦 Physical products → Units (Unit category)")
        print(f"  📋 Order lines → Match product UoM")
        
        print(f"\n🔄 Next Steps:")
        print("1. Refresh your browser")
        print("2. Try creating the order again")
        print("3. The UoM error should be resolved")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    quick_uom_fix()
