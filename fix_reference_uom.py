#!/usr/bin/env python3
import xmlrpc.client

def fix_reference_uom():
    try:
        # Connection details
        url = 'http://localhost:8069'
        db = 'dataicraft'
        username = '<EMAIL>'
        password = 'admin123'
        
        # Connect to Odoo
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            print("❌ Authentication failed!")
            return
        
        print(f"✅ Connected to Odoo as user ID: {uid}")
        
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        print("\n🔧 === Fixing Reference UoM Issue === 🔧")
        
        # Step 1: Find Working Time category
        working_time_cat = models.execute_kw(
            db, uid, password,
            'uom.category', 'search_read',
            [[['name', '=', 'Working Time']]],
            {'fields': ['id', 'name']}
        )
        
        working_time_cat_id = working_time_cat[0]['id']
        print(f"📊 Working Time category ID: {working_time_cat_id}")
        
        # Step 2: Check current UoMs in Working Time category
        time_uoms = models.execute_kw(
            db, uid, password,
            'uom.uom', 'search_read',
            [[['category_id', '=', working_time_cat_id]]],
            {'fields': ['id', 'name', 'uom_type', 'factor']}
        )
        
        print("Current Working Time UoMs:")
        hours_id = None
        days_id = None
        
        for uom in time_uoms:
            print(f"  📏 {uom['name']} (ID: {uom['id']}) - Type: {uom['uom_type']}, Factor: {uom['factor']}")
            if uom['name'] == 'Hours':
                hours_id = uom['id']
            elif uom['name'] == 'Days':
                days_id = uom['id']
        
        # Step 3: Make Hours the reference UoM
        if hours_id:
            print(f"\n🔧 Making Hours (ID: {hours_id}) the reference UoM...")
            
            # First, temporarily change Days to not be reference
            if days_id:
                models.execute_kw(
                    db, uid, password,
                    'uom.uom', 'write',
                    [[days_id], {
                        'uom_type': 'bigger',
                        'factor': 0.125  # 1 day = 8 hours
                    }]
                )
                print("✅ Changed Days to bigger unit (8 hours = 1 day)")
            
            # Now make Hours the reference
            models.execute_kw(
                db, uid, password,
                'uom.uom', 'write',
                [[hours_id], {
                    'uom_type': 'reference',
                    'factor': 1.0
                }]
            )
            print("✅ Made Hours the reference UoM")
        
        # Step 4: Verify the fix
        print("\n✅ Verifying the fix...")
        
        time_uoms_after = models.execute_kw(
            db, uid, password,
            'uom.uom', 'search_read',
            [[['category_id', '=', working_time_cat_id]]],
            {'fields': ['id', 'name', 'uom_type', 'factor']}
        )
        
        print("Working Time UoMs after fix:")
        for uom in time_uoms_after:
            print(f"  📏 {uom['name']} (ID: {uom['id']}) - Type: {uom['uom_type']}, Factor: {uom['factor']}")
        
        # Step 5: Update all service products to use Hours
        print("\n🔧 Updating service products...")
        
        if hours_id:
            service_products = models.execute_kw(
                db, uid, password,
                'product.template', 'search',
                [[['type', '=', 'service']]]
            )
            
            if service_products:
                models.execute_kw(
                    db, uid, password,
                    'product.template', 'write',
                    [service_products, {
                        'uom_id': hours_id,
                        'uom_po_id': hours_id
                    }]
                )
                print(f"✅ Updated {len(service_products)} service products to use Hours")
        
        # Step 6: Update company timesheet settings
        print("\n🔧 Updating company settings...")
        
        try:
            company_ids = models.execute_kw(
                db, uid, password,
                'res.company', 'search', [[]]
            )
            
            if company_ids and hours_id:
                models.execute_kw(
                    db, uid, password,
                    'res.company', 'write',
                    [company_ids, {
                        'timesheet_encode_uom_id': hours_id,
                    }]
                )
                print("✅ Updated company timesheet UoM to Hours")
        
        except Exception as e:
            print(f"⚠️ Company settings: {e}")
        
        print(f"\n🎉 Reference UoM Fix Complete!")
        print(f"✅ Hours is now the reference UoM for Working Time")
        print(f"✅ Days is properly configured as 8 hours")
        print(f"✅ Service products use Hours")
        print(f"✅ Company settings updated")
        
        print(f"\n🔄 Next Steps:")
        print("1. Refresh your browser (F5)")
        print("2. Try creating the order again")
        print("3. The UoM error should now be resolved")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    fix_reference_uom()
