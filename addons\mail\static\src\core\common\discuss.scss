.o-mail-Discuss-content {
    .o-mail-Thread {
        flex-grow: 1;
    }
}

.o-mail-Discuss-inspector {
    flex-basis: $o-mail-Discuss-inspector;
    flex-shrink: 0;
}

.o-mail-Discuss-selfAvatar {
    height: $o-mail-Avatar-sizeSmall;
    width: $o-mail-Avatar-sizeSmall;
}

.o-mail-Discuss-threadName {
    max-width: 75%;
}

.o-mail-Discuss-header {
    height: $o-mail-Discuss-headerHeight;

    button {
        &:not(.o-isActive):hover {
            background-color: $gray-200;
        }
        &.o-isActive {
            background-color: $gray-300;
        }
        &.o-isActive:hover {
            background-color: $gray-300;
        }
    }
}

.o-mail-Discuss-threadAvatar {
    img {
        height: 36px;
        width: 36px;
    }

    a {
        i {
            transform: translate(-50%, -50%);
            opacity: 0;
        }

        &:hover {
            background-color: rgba($black, 0.5);
            i {
                opacity: 1;
            }
        }
    }
}
