@include media-breakpoint-up(xxl, $o-extra-grid-breakpoints) {
    .o_attachment_preview {
        display: block;
        flex: auto;
        overflow: hidden;
        width: $o-mail-Chatter-minWidth;

        > .o-mail-Attachment {
            position: relative;
            width: 100%;
            height: 100%;
            .arrow {
                width: 5%;
                @include o-position-absolute(50%, 0);
                @include o-FileViewer-arrow;
                transition: width 0.3s;
                padding-top: 30px;
                height: 75px;
                &:hover {
                    width: 7%;
                }
                &.o_move_previous {
                    left: 2px;
                    right: 0px;
                }
            }
            > iframe {
                width: 100%;
                height: 100%;
            }
            > .o-mail-Attachment-imgContainer {
                position: absolute;
                overflow: auto;
                width: 100%;
                height: 100%;
                > img {
                    margin: auto;
                    box-shadow: 0px 0px 5px rgba(41, 41, 41, 0.43);
                }
            }
        }
    }
}
