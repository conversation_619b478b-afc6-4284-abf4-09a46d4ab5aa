#!/usr/bin/env python3
import xmlrpc.client

def test_backend_api():
    try:
        # Connection details
        url = 'http://localhost:8069'
        db = 'dataicraft'
        username = '<EMAIL>'
        password = 'admin123'
        
        print("🧪 === Backend API Test === 🧪")
        
        # Test 1: Authentication
        print("\n🔐 Testing Authentication...")
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if uid:
            print(f"✅ Authentication successful - User ID: {uid}")
        else:
            print("❌ Authentication failed")
            return
        
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        # Test 2: Check UoM structure
        print("\n📏 Testing UoM Structure...")
        
        uoms = models.execute_kw(
            db, uid, password,
            'uom.uom', 'search_read',
            [[['category_id.name', '=', 'Unit']]],
            {'fields': ['id', 'name', 'category_id']}
        )
        
        print(f"UoMs in Unit category: {len(uoms)}")
        for uom in uoms:
            print(f"  📏 {uom['name']} (ID: {uom['id']})")
        
        # Test 3: Check products
        print("\n📦 Testing Product Access...")
        
        products = models.execute_kw(
            db, uid, password,
            'product.template', 'search_read',
            [[]],
            {'fields': ['id', 'name', 'uom_id'], 'limit': 5}
        )
        
        print(f"Sample products: {len(products)}")
        for product in products:
            print(f"  📦 {product['name']} - UoM ID: {product['uom_id'][0]}")
        
        # Test 4: Try creating a sales order
        print("\n📋 Testing Sales Order Creation...")
        
        # Get a customer
        customers = models.execute_kw(
            db, uid, password,
            'res.partner', 'search_read',
            [[['is_company', '=', True]]],
            {'fields': ['id', 'name'], 'limit': 1}
        )
        
        if not customers:
            # Create a test customer
            customer_id = models.execute_kw(
                db, uid, password,
                'res.partner', 'create',
                [{'name': 'Test Customer', 'is_company': True}]
            )
            print(f"✅ Created test customer (ID: {customer_id})")
        else:
            customer_id = customers[0]['id']
            print(f"✅ Using existing customer: {customers[0]['name']} (ID: {customer_id})")
        
        # Get a product
        if products:
            product_id = products[0]['id']
            product_name = products[0]['name']
            product_uom_id = products[0]['uom_id'][0]
            
            print(f"✅ Using product: {product_name} (ID: {product_id}, UoM: {product_uom_id})")
            
            # Create sales order
            so_data = {
                'partner_id': customer_id,
                'order_line': [(0, 0, {
                    'product_id': product_id,
                    'product_uom_qty': 1.0,
                    'product_uom': product_uom_id,  # Use same UoM as product
                    'price_unit': 100.0
                })]
            }
            
            try:
                so_id = models.execute_kw(
                    db, uid, password,
                    'sale.order', 'create',
                    [so_data]
                )
                print(f"✅ Sales order created successfully (ID: {so_id})")
                
                # Read the created order
                so = models.execute_kw(
                    db, uid, password,
                    'sale.order', 'read',
                    [so_id],
                    {'fields': ['name', 'state', 'amount_total']}
                )
                
                print(f"✅ Order details: {so['name']} - State: {so['state']} - Total: {so['amount_total']}")
                
            except Exception as e:
                print(f"❌ Sales order creation failed: {e}")
                return False
        
        # Test 5: Test purchase order
        print("\n🛒 Testing Purchase Order Creation...")
        
        # Get a vendor
        vendors = models.execute_kw(
            db, uid, password,
            'res.partner', 'search_read',
            [[['supplier_rank', '>', 0]]],
            {'fields': ['id', 'name'], 'limit': 1}
        )
        
        if not vendors:
            # Create a test vendor
            vendor_id = models.execute_kw(
                db, uid, password,
                'res.partner', 'create',
                [{'name': 'Test Vendor', 'supplier_rank': 1}]
            )
            print(f"✅ Created test vendor (ID: {vendor_id})")
        else:
            vendor_id = vendors[0]['id']
            print(f"✅ Using existing vendor: {vendors[0]['name']} (ID: {vendor_id})")
        
        # Create purchase order
        if products:
            po_data = {
                'partner_id': vendor_id,
                'order_line': [(0, 0, {
                    'product_id': product_id,
                    'product_qty': 1.0,
                    'product_uom': product_uom_id,  # Use same UoM as product
                    'price_unit': 50.0
                })]
            }
            
            try:
                po_id = models.execute_kw(
                    db, uid, password,
                    'purchase.order', 'create',
                    [po_data]
                )
                print(f"✅ Purchase order created successfully (ID: {po_id})")
                
                # Read the created order
                po = models.execute_kw(
                    db, uid, password,
                    'purchase.order', 'read',
                    [po_id],
                    {'fields': ['name', 'state', 'amount_total']}
                )
                
                print(f"✅ Order details: {po['name']} - State: {po['state']} - Total: {po['amount_total']}")
                
            except Exception as e:
                print(f"❌ Purchase order creation failed: {e}")
                return False
        
        print(f"\n🎉 ALL BACKEND TESTS PASSED!")
        print(f"✅ Authentication works")
        print(f"✅ UoM structure is correct")
        print(f"✅ Products are accessible")
        print(f"✅ Sales orders can be created")
        print(f"✅ Purchase orders can be created")
        print(f"✅ NO UoM conflicts detected")
        
        return True
        
    except Exception as e:
        print(f"❌ Backend test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_backend_api()
    if success:
        print(f"\n🚀 Backend is working perfectly!")
        print(f"🌐 You can now use the frontend at: http://localhost:8069")
        print(f"🔑 Login: <EMAIL> / admin123")
    else:
        print(f"\n❌ Backend issues detected - check logs above")
