# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail
# 
# Translators:
# Chill <PERSON>o <<EMAIL>>, 2023
# <AUTHOR> <EMAIL>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON>EN <<EMAIL>>, 2024
# <PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# Wil Odoo, 2024
# <PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-11 01:02+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON>, 2025\n"
"Language-Team: Chinese (China) (https://app.transifex.com/odoo/teams/41243/zh_CN/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: zh_CN\n"
"Plural-Forms: nplurals=1; plural=0;\n"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_gateway_allowed.py:0
#, python-format
msgid ""
"\n"
"            <p class=\"o_view_nocontent_smiling_face\">\n"
"                Add addresses to the Allowed List\n"
"            </p><p>\n"
"                To protect you from spam and reply loops, Odoo automatically blocks emails\n"
"                coming to your gateway past a threshold of <b>%(threshold)i</b> emails every <b>%(minutes)i</b>\n"
"                minutes. If there are some addresses from which you need to receive very frequent\n"
"                updates, you can however add them below and Odoo will let them go through.\n"
"            </p>"
msgstr ""
"\n"
"            <p class=\"o_view_nocontent_smiling_face\">\n"
"                将地址添加到新人列表\n"
"            </p><p>\n"
"                为防止垃圾邮件和回复循环，\n"
"                ERP 会每隔<b>%(minutes)i</b>自动阻止超过<b>%(threshold)i</b>的电子邮件进入您的网关。 \n"
"                如您需要非常频繁从一些地址接收更新信息，\n"
"                您可以在下方添加它们，ERP 会对其放行.\n"
"            </p>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_document_unfollowed
msgid "\" no longer followed"
msgstr "\" 不再关注"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "\"%(activity_name)s: %(summary)s\" assigned to you"
msgstr "“%(activity_name)s：%(summary)s”分配给您"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
#, python-format
msgid "\"%(hostname)s\" needs to access your microphone"
msgstr "\"%(hostname)s\"需要访问您的麦克风"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "\"%(hostname)s\" requires microphone access"
msgstr "\"%(hostname)s\" 需要接入麦克风"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "%(activity)s, assigned to %(name)s, due on the %(deadline)s"
msgstr "%(activity)s，分配给%(name)s，到期日为%(deadline)s。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/out_of_focus_service.js:0
#, python-format
msgid "%(author name)s from %(channel name)s"
msgstr "%(author name)s 来自 %(channel name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.js:0
#, python-format
msgid "%(candidateType)s (%(protocol)s)"
msgstr "%(candidateType)s (%(protocol)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid ""
"%(email)s is not recognized as a valid email. This is required to create a "
"new customer."
msgstr "%(email)s不是有效的电子邮件。需要创建新的客户。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "%(name)s: %(message)s)"
msgstr "%(name)s: %(message)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"%(new_line)s%(new_line)sType %(bold_start)s@username%(bold_end)s to mention "
"someone, and grab their attention.%(new_line)sType "
"%(bold_start)s#channel%(bold_end)s to mention a channel.%(new_line)sType "
"%(bold_start)s/command%(bold_end)s to execute a command."
msgstr ""
"%(new_line)s%(new_line)s输入 %(bold_start)s@username%(bold_end)s "
"提及某人，以吸引他们注意。%(new_line)s输入 %(bold_start)s#channel%(bold_end)s "
"以提及该频道。%(new_line)s输入%(bold_start)s/command%(bold_end)s 以执行指令。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid ""
"%(open_button)s%(icon)s%(open_em)sDiscard "
"editing%(close_em)s%(close_button)s"
msgstr "%(open_button)s%(icon)s%(open_em)s放弃编辑%(close_em)s%(close_button)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid ""
"%(open_samp)sEscape%(close_samp)s %(open_em)sto "
"%(open_cancel)scancel%(close_cancel)s%(close_em)s, %(open_samp)sCTRL-"
"Enter%(close_samp)s %(open_em)sto "
"%(open_save)ssave%(close_save)s%(close_em)s"
msgstr ""
"按 %(open_samp)sEsc%(close_samp)s "
"%(open_em)s以%(open_cancel)s取消%(close_cancel)s%(close_em)s，%(open_samp)sCtrl+Enter%(close_samp)s"
" %(open_em)s以%(open_save)s储存%(close_save)s%(close_em)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid ""
"%(open_samp)sEscape%(close_samp)s %(open_em)sto "
"%(open_cancel)scancel%(close_cancel)s%(close_em)s, "
"%(open_samp)sEnter%(close_samp)s %(open_em)sto "
"%(open_save)ssave%(close_save)s%(close_em)s"
msgstr ""
"按 %(open_samp)sEsc%(close_samp)s "
"%(open_em)s以%(open_cancel)s取消%(close_cancel)s%(close_em)s，%(open_samp)sEnter%(close_samp)s"
" %(open_em)s以%(open_save)s储存%(close_save)s%(close_em)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_core_web_service.js:0
#, python-format
msgid "%(user)s connected. This is their first connection. Wish them luck."
msgstr "%(user)s 已连接。这是他们第一次登入。祝他们好运。"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow %(document)s document: %(title)s"
msgstr "%(user_name)s邀请您关注%(document)s 文件: %(title)s"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow a new document."
msgstr "%(user_name)s 邀请您关注新文件。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "%(user_name)s pinned a message to this channel."
msgstr "%(user_name)s 将一条信息固定在此频道上。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_mail_server.py:0
#, python-format
msgid "%s (Email Template)"
msgstr "%s(电子邮件模板)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s（副本）"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/out_of_focus_service_patch.js:0
#, python-format
msgid "%s Message"
msgstr "%s 消息"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/out_of_focus_service_patch.js:0
#, python-format
msgid "%s Messages"
msgstr "%s 消息"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
#, python-format
msgid "%s and %s are typing..."
msgstr "%s 和 %s 在输入..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.js:0
#, python-format
msgid "%s and %s have reacted with %s"
msgstr "%s 和%s 已经与%s发生了反应"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "%s created"
msgstr "%s 创建"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#, python-format
msgid "%s days overdue"
msgstr "逾期%s天"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "%s has a request"
msgstr "%s 有一个请求"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "%s has a suggestion"
msgstr "%s 有一个建议"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.js:0
#, python-format
msgid "%s has reacted with %s"
msgstr "%s 已与%s发生反应"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
#, python-format
msgid "%s is typing..."
msgstr "%s 正在输入..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.js:0
#, python-format
msgid "%s messages found"
msgstr "发现 %s 条信息"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "%s raised their hand"
msgstr "%s举手"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid "%s started a live conference"
msgstr "%s发起了实时会议"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "%s\" requires \"camera\" access"
msgstr "%s\" 需要使用 \"摄像头\""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "%s\" requires \"screen recording\" access"
msgstr "%s\" 需要 \"屏幕录制\" 权限"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
#, python-format
msgid "%s, %s and more are typing..."
msgstr "%s, %s 及更多人在沟通..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.js:0
#, python-format
msgid "%s, %s, %s and %s other persons have reacted with %s"
msgstr "%s,%s,%s,和其他%s个人对%s作出了回应"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.js:0
#, python-format
msgid "%s, %s, %s and 1 other person have reacted with %s"
msgstr "%s%s%s和其他 1 个人对%s作出了回应"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.js:0
#, python-format
msgid "%s, %s, %s have reacted with %s"
msgstr "%s, %s, %s 已与%s发生反应"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
msgid "&amp;nbsp;&amp;nbsp;"
msgstr "&amp;nbsp;&amp;nbsp;"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#, python-format
msgid "(Translated from: %(language)s)"
msgstr "(译自：%(language)s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#, python-format
msgid "(Translation Failure: %(error)s)"
msgstr "(翻译失败： %(error)s)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#, python-format
msgid "(from"
msgstr "(从"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "(originally assigned to"
msgstr "（最初分配给"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid ""
",\n"
"    <br/><br/>"
msgstr ""
",\n"
"    <br/><br/>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid ". Narrow your search to see more choices."
msgstr ". 缩小搜索范围以查看更多选择."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid ""
"<b invisible=\"not no_record\" class=\"text-warning\">No record for this "
"model</b>"
msgstr "<b invisible=\"not no_record\" class=\"text-warning\">此型号无记录</b>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "<i class=\"fa fa-globe\" aria-label=\"Document url\"/>"
msgstr "<i class=\"fa fa-globe\" aria-label=\"文件 url\"/>"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "<samp>%(send_keybind)s</samp><i> to send</i>"
msgstr "<samp>%(send_keybind)s</samp><i> 用于发送</i>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"d-block w-75 py-2\">Button Color</span>"
msgstr "<span class=\"d-block w-75 py-2\">按钮颜色</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"d-block w-75 py-2\">Header Color</span>"
msgstr "<span class=\"d-block w-75 py-2\">标题颜色</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "<span class=\"me-1 oe_inline\">@</span>"
msgstr "<span class=\"me-1 oe_inline\">@</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"me-1\">@</span>"
msgstr "<span class=\"me-1\">@</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "<span class=\"o_stat_text\">Open Document</span>"
msgstr "<span class=\"o_stat_text\">打开文档</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "<span class=\"o_stat_text\">Open Parent Document</span>"
msgstr "<span class=\"o_stat_text\">打开母文档</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "<span class=\"text-bg-danger\">Archived</span>"
msgstr "<span class=\"text-bg-danger\">已存档</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid ""
"<span invisible=\"mail_post_method != 'email'\">\n"
"                                The message will be sent as an email to the recipients of the\n"
"                                template and will not appear in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'note'\">\n"
"                                The message will be posted as an internal note visible to internal\n"
"                                users in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'comment'\">\n"
"                                The message will be posted as a message on the record,\n"
"                                notifying all followers. It will appear in the messaging history.\n"
"                            </span>"
msgstr ""
"<span invisible=\"mail_post_method != 'email'\">\n"
"                                信息将以电子邮件的形式发送给模板的收件人，不会出现在信息历史记录中。\n"
"                                模板的收件人，不会出现在信息历史记录中。\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'note'\">\n"
"                                信息将作为内部备注发布，内部用户可在信息历史记录中看到。\n"
"                                内部用户可见。\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'comment'\">\n"
"                                信息将作为记录上的信息发布、\n"
"                                通知所有关注者。它将出现在信息历史记录中。\n"
"                            </span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span name=\"document_followers_text\" invisible=\"not model or "
"composition_mode == 'mass_mail'\">Followers of the document and</span>"
msgstr ""
"<span name=\"document_followers_text\" invisible=\"not model or "
"composition_mode == 'mass_mail'\">该文件的关注者和</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>If this was done by you:</span><br/>"
msgstr "<span>如果由您执行此操作：</span><br/>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>If this was not done by you:</span>"
msgstr "<span>如果您并未执行此操作：</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "<span>Open Record</span>"
msgstr "<span>打开记录</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>We suggest you start by</span>"
msgstr "<span>我们建议您首先</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
msgid ""
"<strong>Internal communication</strong>: Replying will post an internal "
"note. Followers won't receive any email notification."
msgstr "<strong>内部沟通</strong>: 回复将发布内部注释。 关注者将不会收到任何电子邮件通知。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "<strong>Original note:</strong>"
msgstr "<strong>原始说明：</strong>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up the feature."
msgstr "<strong>保存</strong> 此页面并返回此处以设置该功能。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_document_unfollowed
msgid "<strong>You are no longer following the document:</strong>"
msgstr "<strong>您不再遵守文件规定：</strong>"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr "为该别名创建新记录时，将对其进行评估以提供默认值的Python字典。"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_bus_presence_partner_or_guest_exists
msgid "A bus presence must have a user or a guest."
msgstr "总线必须存在用户或者游客."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_member_partner_or_guest_exists
msgid "A channel member must be a partner or a guest."
msgstr "频道成员必须是合作伙伴或游客."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "A channel of type 'chat' cannot have more than two users."
msgstr "“聊天”类型的频道不能有两个以上的用户."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"A chat should not be created with more than 2 persons. Create a group "
"instead."
msgstr "不能同时和2个人以上一起聊天。请创建一个聊天组。"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_message_reaction_partner_or_guest_exists
msgid "A message reaction must be from a partner or from a guest."
msgstr "信息反应必须来自业务伙伴或游客。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "A next activity can only be planned on models that use activities."
msgstr "只能在使用活动的模型上计划下一个活动。"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__google_translate_api_key
msgid ""
"A valid Google API key is required to enable message translation. "
"https://cloud.google.com/translate/docs/setup"
msgstr ""
"要启用信息翻译，需要有效的 Google API 密钥。https://cloud.google.com/translate/docs/setup"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_settings_volumes_partner_or_guest_exists
msgid "A volume setting must have a partner or a guest."
msgstr "音量设置必须有业务合作伙伴或游客。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#, python-format
msgid "Accept"
msgstr "接受"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Access Denied"
msgstr "访问被拒绝"

#. module: mail
#: model:ir.model,name:mail.model_res_groups
msgid "Access Groups"
msgstr "访问组"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__access_token
msgid "Access Token"
msgstr "访问令牌"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_model.js:0
#, python-format
msgid "Access restricted to group \"%(groupFullName)s\""
msgstr "仅限于用户组“%(groupFullName)s”使用"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Account"
msgstr "账户"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__category
msgid "Action"
msgstr "动作"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction
msgid "Action Needed"
msgstr "待处理"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "动作窗口视图"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#, python-format
msgid "Actions"
msgstr "操作"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_schedule__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr "动作可能触发特定的行为，如打开日历视图或在上传单据时自动标记"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Actions to Perform on Incoming Mails"
msgstr "在传入邮件上执行的操作"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__default
msgid "Activated by default when subscribing."
msgstr "订阅时默认启用."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__active
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__active
#: model:ir.model.fields,field_description:mail.field_mail_activity__active
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__active
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__active
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__active
#: model:ir.model.fields,field_description:mail.field_mail_template__active
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Active"
msgstr "已启用"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_domain
msgid "Active domain"
msgstr "启用的域"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#: model:ir.actions.act_window,name:mail.mail_activity_action
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__template_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_users__activity_ids
#: model:ir.ui.menu,name:mail.menu_mail_activities
#: model:mail.message.subtype,name:mail.mt_activities
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
#, python-format
msgid "Activities"
msgstr "活动"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Activities To Generate"
msgstr "生成活动"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_activity_check_res_id_is_set
msgid "Activities have to be linked to records with a not null res_id."
msgstr "活动必须链接到具有非空res_id的记录。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model,name:mail.model_mail_activity
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_act_window_view__view_mode__activity
#: model:ir.model.fields.selection,name:mail.selection__ir_ui_view__type__activity
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_calendar
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#, python-format
msgid "Activity"
msgstr "活动"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活动异常标示"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr "活动Mixin"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_plan
msgid "Activity Plan"
msgstr "活动计划"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_plan_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_plan
msgid "Activity Plans"
msgstr "活动计划"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Activity Settings"
msgstr "活动设置"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_state
#: model:ir.model.fields,field_description:mail.field_res_users__activity_state
msgid "Activity State"
msgstr "活动状态"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_type
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_type_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_type_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity Type"
msgstr "活动类型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_icon
msgid "Activity Type Icon"
msgstr "活动类型图标"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_type_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activity Types"
msgstr "活动类型"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_plan_template
msgid "Activity plan template"
msgstr "活动计划模板"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr "活动日程计划向导"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Activity type"
msgstr "活动类型"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "Activity: %s"
msgstr "活动: %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Add Context Action"
msgstr "添加上下文操作"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Add Email Blacklist"
msgstr "添加 Email 黑名单"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__followers
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#, python-format
msgid "Add Followers"
msgstr "添加关注者"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Add Users"
msgstr "添加用户"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_button.xml:0
#: code:addons/mail/static/src/core/common/message_reaction_button.xml:0
#, python-format
msgid "Add a Reaction"
msgstr "添加反应"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add a Tenor GIF API key to enable GIFs support."
msgstr "添加 Tenor GIF API 密钥以启用 GIF 支持。"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_api_key
msgid ""
"Add a Tenor GIF API key to enable GIFs support. "
"https://developers.google.com/tenor/guides/quickstart#setup"
msgstr ""
"添加 Tenor GIF API 密钥以启用 GIF "
"支持。https://developers.google.com/tenor/guides/quickstart#setup"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.xml:0
#, python-format
msgid "Add a description"
msgstr "添加描述"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Add a description to your activity..."
msgstr "为您的活动添加说明..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Add a new %(document)s or send an email to %(email_link)s"
msgstr "新增 %(document)s 或发送邮件至%(email_link)s"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_plan_action
msgid "Add a new plan"
msgstr "添加新计划"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid "Add an email address to the blacklist"
msgstr "将电子邮件地址添加到黑名单"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient.js:0
#, python-format
msgid "Add as recipient and follower (reason: %s)"
msgstr "添加为收件人和关注者 (原因：%s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add contacts to notify..."
msgstr "添加要通知的联系人......"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss_app_model.js:0
#, python-format
msgid "Add or join a channel"
msgstr "添加或加入频道"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_add_signature
msgid "Add signature"
msgstr "添加签名"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add your twilio credentials for ICE servers"
msgstr "为ICE服务器添加您的twilio凭据"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"Adding followers on channels is not possible. Consider adding members "
"instead."
msgstr "无法在频道上添加关注者。考虑改为添加成员。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid ""
"Adding more members to this chat isn't possible; it's designed for just two "
"people."
msgstr "该聊天室无法添加更多成员，因为它只能容纳两个人。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__partner_ids
msgid "Additional Contacts"
msgstr "其他联系人"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Advanced"
msgstr "高级"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Advanced Options"
msgstr "高级选项"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__warning
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_exception_decoration__warning
msgid "Alert"
msgstr "警报"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Alias"
msgstr "别名"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Alias %(matching_name)s (%(current_id)s) is already linked with "
"%(alias_model_name)s (%(matching_id)s) and used by the %(parent_name)s "
"%(parent_model_name)s."
msgstr ""
"别名%(matching_name)s（%(current_id)s）已经与%(alias_model_name)s（%(matching_id)s）连接，并被%(parent_name)s%(parent_model_name)s使用。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Alias %(matching_name)s (%(current_id)s) is already linked with "
"%(alias_model_name)s (%(matching_id)s)."
msgstr ""
"别名 %(matching_name)s （%(current_id)s）已与 %(alias_model_name)s "
"（%(matching_id)s）相连。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_contact
msgid "Alias Contact Security"
msgstr "安全联系人别名"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_message__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_res_config_settings__alias_domain_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Alias Domain"
msgstr "别名域"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_domain
#: model:ir.model.fields,field_description:mail.field_res_company__alias_domain_name
msgid "Alias Domain Name"
msgstr "别名域名"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_alias_domain_action
#: model:ir.ui.menu,name:mail.mail_alias_domain_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_tree
msgid "Alias Domains"
msgstr "别名域"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_full_name
msgid "Alias Email"
msgstr "别名电子邮件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_name
msgid "Alias Name"
msgstr "别名"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_status
msgid "Alias Status"
msgstr "别名状态"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain
msgid "Alias domain name"
msgstr "别名域名"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_status
msgid "Alias status assessed on the last message received."
msgstr "根据收到的最后一条消息评估别名状态。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_model_id
msgid "Aliased Model"
msgstr "模型别名"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_alias_action
#: model:ir.ui.menu,name:mail.mail_alias_menu
msgid "Aliases"
msgstr "别名"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Aliases %(alias_names)s is already used as bounce or catchall address. "
"Please choose another alias."
msgstr "别名 %(alias_names)s 已被用作跳转地址或总括地址。请选择其他别名。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "All"
msgstr "全部"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "All Messages"
msgstr "所有消息"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__custom_notifications
msgid "All Messages if not specified"
msgstr "所有信息（如果未指定"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_resend_message.py:0
#, python-format
msgid "All partners must belong to the same message"
msgstr "所有合作伙伴必须属于同一信息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__allow_public_upload
msgid "Allow Public Upload"
msgstr "允许公开上传"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid ""
"An SSL exception occurred. Check SSL/TLS configuration on server port.\n"
" %s"
msgstr ""
"发生SSL异常。 检查服务器端口上的SSL / TLS配置。\n"
" %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid "An access token must be provided for each attachment."
msgstr "必须为每个附件提供一个访问令牌。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid "An email is required for find_or_create to work"
msgstr "要 find_or_create功能工作，需要一个电子邮件"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/failure_model.js:0
#, python-format
msgid "An error occurred when sending an email"
msgstr "发送电子邮件时发生错误"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "An error occurred while fetching messages."
msgstr "获取消息时发生错误。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_service.js:0
#, python-format
msgid "An unexpected error occurred during the creation of the chat."
msgstr "创建聊天时发生意外错误。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "And"
msgstr "和"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "And 1 other member."
msgstr "和另外 1 名其它成员."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_template__model_id
msgid "Applies to"
msgstr "应用于"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.xml:0
#, python-format
msgid "Apply"
msgstr "应用"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Archived"
msgstr "已存档"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid ""
"Archived because %(user_name)s (#%(user_id)s) deleted the portal account"
msgstr "已存档，因为 %(user_name)s （%(user_id)s 删除了门户帐户"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
msgid "Are you sure you want to delete this Mail Template?"
msgstr "您确定要删除此邮件模板吗？"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/common/message.js:0
#, python-format
msgid "Are you sure you want to delete this message?"
msgstr "您确定要删除此消息吗?"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
msgid ""
"Are you sure you want to reset these email templates to their original "
"configuration? Changes and translations will be lost."
msgstr "您确定要将这些电子邮件模板重置为原始配置吗？ 更改和翻译将丢失。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_blacklist.py:0
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid "Are you sure you want to unblacklist this Email Address?"
msgstr "您确定要将此电子邮件地址取消列入黑名单吗？"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__responsible_type__on_demand
msgid "Ask at launch"
msgstr "在发布时询问"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "Assign to ..."
msgstr "指派给…"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "Assign to me"
msgstr "分配给自己"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_on_demand_user_id
msgid "Assigned To"
msgstr "指派给"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__responsible_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_user_id
#, python-format
msgid "Assigned to"
msgstr "分派给"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid ""
"Assigned user %s has no access to the document and is not able to handle "
"this activity."
msgstr "指定用户 %s 对单据无权限，无法处理这个活动 。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__responsible_type
msgid "Assignment"
msgstr "指派"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "At this point lang should be correctly set"
msgstr "此时，语言设置应正确无误"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Attach a file"
msgstr "附文件"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#: code:addons/mail/static/src/core/common/composer.xml:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Attach files"
msgstr "附加文件"

#. module: mail
#: model:ir.model,name:mail.model_ir_attachment
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__attachment_ids
msgid "Attachment"
msgstr "附件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_partner__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_users__message_attachment_count
msgid "Attachment Count"
msgstr "附件计数"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Attachment counter loading..."
msgstr "附件计数器加载中..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__attachment_ids
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Attachments"
msgstr "附件"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Audio player:"
msgstr "音频播放器："

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__partners
msgid "Authenticated Partners"
msgstr "身份验证过的业务伙伴"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__author_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Author"
msgstr "作者"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,help:mail.field_mail_mail__author_id
#: model:ir.model.fields,help:mail.field_mail_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr "消息编写者。如果未设置，email_from可能包含与任何合作伙伴均不匹配的电子邮箱地址。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_message__author_avatar
msgid "Author's avatar"
msgstr "作者形象"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__group_public_id
msgid "Authorized Group"
msgstr "授权群组"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_template__auto_delete
msgid "Auto Delete"
msgstr "自动删除"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Auto Subscribe Groups"
msgstr "自动订阅组"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__group_ids
msgid "Auto Subscription"
msgstr "自动订阅"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Auto subscription"
msgstr "自动订阅"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__auto_comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__auto_comment
msgid "Automated Targeted Notification"
msgstr "自动定向通知"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__automated
msgid "Automated activity"
msgstr "自动活动"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#, python-format
msgid "Automated message"
msgstr "自动留言"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__triggered_next_type_id
msgid ""
"Automatically schedule this activity once the current one is marked as done."
msgstr "将当前活动标记为完成后，自动安排此活动。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Available for all Companies"
msgstr "所有公司可用"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.xml:0
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__avatar_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1920
#, python-format
msgid "Avatar"
msgstr "形象"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1024
msgid "Avatar 1024"
msgstr "形象 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_128
msgid "Avatar 128"
msgstr "形象 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_256
msgid "Avatar 256"
msgstr "形象 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_512
msgid "Avatar 512"
msgstr "形象 512"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#, python-format
msgid "Avatar of user"
msgstr "游客形象"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#, python-format
msgid "Away"
msgstr "离开"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Background blur intensity"
msgstr "背景模糊强度"

#. module: mail
#: model:ir.model,name:mail.model_base
msgid "Base"
msgstr "基数"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__base_template
msgid "Base Template"
msgstr "基础模板"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Base Templates"
msgstr "基础模板"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__sfu_server_key
msgid "Base64 encoded key"
msgstr "Base64 编码密钥"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_batch
msgid "Batch composition"
msgstr "批次组合"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Batch log cannot support attachments or tracking values on more than 1 "
"document"
msgstr "批处理日志无法支持 1 份以上文档的附件或追踪值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_users__is_blacklisted
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Blacklist"
msgstr "黑名单"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Blacklist Date"
msgstr "黑名单日期"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_bl
msgid "Blacklisted Address"
msgstr "黑名单地址"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_blacklist_action
msgid "Blacklisted Email Addresses"
msgstr "列入黑名单的电子邮件地址"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid ""
"Blocked by deletion of portal account %(portal_user_name)s by %(user_name)s "
"(#%(user_id)s)"
msgstr "通过删除门户帐户 %(portal_user_name)s 由 %(user_name)s %(user_id)s阻止"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Blur video background"
msgstr "模糊视频背景"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__body_html
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__body_html
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Body"
msgstr "正文"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body_has_template_value
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body_has_template_value
msgid "Body content is the same as the template"
msgstr "正文内容与模板相同"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "Bot"
msgstr "机器人"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_company__bounce_formatted
#: model:ir.model.fields,field_description:mail.field_res_partner__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_users__message_bounce
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_bounce
msgid "Bounce"
msgstr "退回"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__bounce_alias
msgid "Bounce Alias"
msgstr "弹跳别名"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__bounce_email
#: model:ir.model.fields,field_description:mail.field_res_company__bounce_email
msgid "Bounce Email"
msgstr "退件电子邮件"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
#, python-format
msgid ""
"Bounce alias %(bounce)s is already used for another domain with same name. "
"Use another bounce or simply use the other alias domain."
msgstr "跳转别名 %(bounce)s 已用于另一个同名域名。请使用另一个跳转或直接使用另一个别名域。"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_domain_bounce_email_uniques
msgid "Bounce emails should be unique"
msgstr "退件邮件应独一无二"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
#, python-format
msgid ""
"Bounce/Catchall '%(matching_alias_name)s' is already used by "
"%(document_name)s. Choose another alias or change it on the other document."
msgstr ""
"反弹/捕捉'%(matching_alias_name)s'已被 %(document_name)s 使用。请选择其他别名或在其他文件上更改别名。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
#, python-format
msgid ""
"Bounce/Catchall '%(matching_alias_name)s' is already used. Choose another "
"alias or change it on the linked model."
msgstr "Bounce/Catchall '%(matching_alias_name)s' 已被使用。请选择其他别名或在链接的模型上进行更改。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__bounce
#, python-format
msgid "Bounced"
msgstr "被退回"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/webclient/web/webclient.js:0
#, python-format
msgid ""
"Brave: enable 'Google Services for Push Messaging' to enable push "
"notifications"
msgstr "启用“Google Services for Push Messaging”以启用推送通知"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Browser default"
msgstr "默认浏览器"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__endpoint
msgid "Browser endpoint"
msgstr "浏览器端点"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__keys
msgid "Browser keys"
msgstr "浏览器按键"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_cc.py:0
#, python-format
msgid "CC Email"
msgstr "抄送邮件"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "CTRL-Enter"
msgstr "CTRL-Enter"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_call
msgid "Call"
msgstr "电话"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "Camera is off"
msgstr "关闭相机"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__can_cancel
msgid "Can Cancel"
msgstr "可以取消"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__can_edit_body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__can_edit_body
msgid "Can Edit Body"
msgstr "可以编辑主体"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__can_resend
msgid "Can Resend"
msgstr "可以重新发送"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__can_write
#: model:ir.model.fields,field_description:mail.field_mail_template__can_write
msgid "Can Write"
msgstr "可写"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "Can not update the message or recipient of a notification."
msgstr "无法更新通知的消息或收件人。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_confirm_dialog.xml:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Cancel"
msgstr "取消"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Cancel Email"
msgstr "取消Email"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__canceled
#, python-format
msgid "Canceled"
msgstr "已取消"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__cancel
msgid "Cancelled"
msgstr "已取消"

#. module: mail
#: model:ir.model,name:mail.model_mail_shortcode
msgid "Canned Response / Shortcode"
msgstr "自动回复"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_shortcode_action
msgid "Canned Responses"
msgstr "预设回复"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_search
msgid "Canned Responses Search"
msgstr "预设回复搜索"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid ""
"Canned responses allow you to insert prewritten responses in\n"
"                your messages by typing <i>:shortcut</i>. The shortcut is\n"
"                replaced directly in your message, so that you can still edit\n"
"                it before sending."
msgstr ""
"预设回复允许您通过输入 <i>:快捷方式</i>在\n"
"消息中插入预先写好的回复。快捷方式在消息中被\n"
"直接替换，这样您将仍能在消息发送之前进行编辑\n"
"。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "Cannot change the channel type of: %(channel_names)s"
msgstr "不能更改频道类型：%(channel_names)s"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_cc
msgid "Carbon copy message recipients"
msgstr "抄送"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_cc
msgid "Carbon copy recipients"
msgstr "抄送收件人"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_cc
msgid "Carbon copy recipients (placeholders may be used here)"
msgstr "抄送收件人（可以在这里使用占位符）"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_formatted
msgid "Catchall"
msgstr "预设"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__catchall_alias
msgid "Catchall Alias"
msgstr "全部别名"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__catchall_email
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_email
msgid "Catchall Email"
msgstr "预设邮件"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
#, python-format
msgid ""
"Catchall alias %(catchall)s is already used for another domain with same "
"name. Use another catchall or simply use the other alias domain."
msgstr "备用别名 %(catchall)s 已用于另一个同名域名。请使用另一个 catchall 或直接使用另一个别名域。"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_domain_catchall_email_uniques
msgid "Catchall emails should be unique"
msgstr "接收邮件应独一无二"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_cc
msgid "Cc"
msgstr "抄送"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__chaining_type
msgid "Chaining Type"
msgstr "链接类型"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,help:mail.field_mail_activity_type__decoration_type
msgid "Change the background color of the related activities of this type."
msgstr "修改此类相关活动的背景."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__channel_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__channel_id
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__channel
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_search
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
#, python-format
msgid "Channel"
msgstr "频道"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel_member
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__channel_member_id
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_member_view_form
msgid "Channel Member"
msgstr "频道成员"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_type
msgid "Channel Type"
msgstr "频道类型"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Channel full"
msgstr "频道已满"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid "Channel members cannot include public users."
msgstr "频道成员不能包括公众用户。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "Channel settings"
msgstr "频道设置"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss_app_model.js:0
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_guest__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_users__channel_ids
#: model:ir.ui.menu,name:mail.discuss_channel_menu_settings
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_member_view_tree
#, python-format
msgid "Channels"
msgstr "频道"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_member_action
#: model:ir.ui.menu,name:mail.discuss_channel_member_menu
msgid "Channels/Members"
msgstr "频道/成员"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__chat
#, python-format
msgid "Chat"
msgstr "聊天"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr "在 2 个人之间的聊天是私密且唯一的。受邀者之间群聊是私密的。频道可以自由加入（取决于其配置）。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "Chats"
msgstr "聊天"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__use_exclusion_list
msgid "Check Exclusion List"
msgstr "检查排除清单"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__child_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__child_ids
msgid "Child Messages"
msgstr "下级消息"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Choose a template..."
msgstr "选择模板"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Choose a user..."
msgstr "选择用户..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "Choose another value or change it on the other document."
msgstr "选择其他值或在其他文档上更改。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_schedule__plan_on_demand_user_id
msgid "Choose assignation for activities with on demand assignation."
msgstr "为按需分配的活动选择分配方式。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "Click here to retry"
msgstr "单击此处重试"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
#, python-format
msgid "Click to see the attachments"
msgstr "点击查看附件"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
#, python-format
msgid "Close"
msgstr "关闭"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#, python-format
msgid "Close Chat Window"
msgstr "关闭聊天视窗"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#, python-format
msgid "Close Search"
msgstr "关闭搜索"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "Close button"
msgstr "关闭按钮"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/action_panel.xml:0
#, python-format
msgid "Close panel"
msgstr "关闭面板"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "Close search"
msgstr "关闭搜索"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__closed
msgid "Closed"
msgstr "已关闭"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__new
msgid "Collect replies on a specific email address"
msgstr "收集对特定电子邮件地址的回复"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "Come here often? Install Odoo on your device!"
msgstr "经常来这里？在您的设备上安装 Odoo！"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated carbon copy recipients addresses"
msgstr "逗号分隔抄送收件人邮件地址"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated ids of recipient partners"
msgstr "逗号分隔的收件人合作伙伴的邮件地址"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__partner_to
msgid ""
"Comma-separated ids of recipient partners (placeholders may be used here)"
msgstr "逗号分隔的收件人合作伙伴的邮件地址（可以使用占位符）"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_to
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated recipient addresses"
msgstr "逗号分隔的收件人邮件地址"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_to
msgid "Comma-separated recipient addresses (placeholders may be used here)"
msgstr "逗号分隔的收件人邮件地址（可以在这里使用占位符）"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__comment
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Comment"
msgstr "备注"

#. module: mail
#: model:ir.model,name:mail.model_res_company
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__company_ids
msgid "Companies"
msgstr "公司"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__company_ids
msgid "Companies using this domain as default for sending mails"
msgstr "默认使用此域名发送邮件的公司"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__company_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__company_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__company_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_company_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_company_id
#: model:ir.model.fields,field_description:mail.field_mail_message__record_company_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
msgid "Company"
msgstr "公司"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/web/activity_mail_template.js:0
#: code:addons/mail/wizard/mail_compose_message.py:0
#: model:ir.actions.act_window,name:mail.action_email_compose_message_wizard
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Compose Email"
msgstr "撰写邮件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_mode
msgid "Composition mode"
msgstr "写作模式"

#. module: mail
#: model:ir.model,name:mail.model_res_config_settings
msgid "Config Settings"
msgstr "配置设置"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__configuration
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Configuration"
msgstr "配置"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your ICE server list for webRTC"
msgstr "为 webRTC 配置 ICE 服务器列表"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your activity types"
msgstr "配置您的活动类型"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your own email servers"
msgstr "配置您的邮件服务器参数"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_confirm_dialog.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
#, python-format
msgid "Confirm"
msgstr "确认"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#: code:addons/mail/static/src/core/common/message_confirm_dialog.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#, python-format
msgid "Confirmation"
msgstr "确认"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__state__done
msgid "Confirmed"
msgstr "已确认"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#, python-format
msgid "Congratulations, you're done with your activities."
msgstr "恭喜，您已完成您的活动。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "Congratulations, your inbox is empty"
msgstr "恭喜，您的收件箱已清空"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.js:0
#, python-format
msgid "Congratulations, your inbox is empty!"
msgstr "恭喜，您的收件箱是空的！"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_smtp
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_smtp
msgid "Connection failed (outgoing mail server problem)"
msgstr "连接失败(发信服务器问题)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid "Connection test failed: %s"
msgstr "连接测试失败：%s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Connection to SFU server closed by the server"
msgstr "服务器关闭了与 SFU 服务器的连接"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Connection type:"
msgstr "连接类型："

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Connection:"
msgstr "连接："

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__is_ssl
msgid ""
"Connections are encrypted with SSL/TLS through a dedicated port (default: "
"IMAPS=993, POP3S=995)"
msgstr "专用端口链接将通过SSL/TLS加密（默认为：IMAP=993，POP3S=995）"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_force_new
msgid "Considers answers as new thread"
msgstr "视答案为新线程"

#. module: mail
#: model:ir.model,name:mail.model_res_partner
msgid "Contact"
msgstr "联系人"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "Contact your administrator"
msgstr "联系您的管理员"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_activity
msgid "Contacts"
msgstr "联系人"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Container Model"
msgstr "容器模型"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__last_interest_dt
msgid ""
"Contains the date and time of the last interesting event that happened in "
"this channel for this partner. This includes: creating, joining, pinning, "
"and new message posted."
msgstr "包含此合作伙伴在此频道中发生的最后一个有趣事件的日期和时间。这包括：创建、加入、固定和发布新消息。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__content
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Content"
msgstr "内容"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__substitution
msgid ""
"Content that will automatically replace the shortcut of your choosing. This "
"content can still be adapted before sending your message."
msgstr "将自动替换您选择的快捷方式的内容。 此内容仍然可以在发送您的消息之前进行调整。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body
#: model:ir.model.fields,field_description:mail.field_mail_mail__body
#: model:ir.model.fields,field_description:mail.field_mail_message__body
msgid "Contents"
msgstr "内容"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__fold_state
msgid "Conversation Fold State"
msgstr "对话收拢状态"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_minimized
msgid "Conversation is minimized"
msgstr "对话已最小化"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,help:mail.field_res_partner__message_bounce
#: model:ir.model.fields,help:mail.field_res_users__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "此联系人退回邮件计数计数器"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__country_id
msgid "Country"
msgstr "国家/地区"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
#, python-format
msgid "Create"
msgstr "创建"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__next_activity
msgid "Create Activity"
msgstr "创建活动"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__create_date
msgid "Create Date"
msgstr "创建日期"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Create Group Chat"
msgstr "创建群聊"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_uid
msgid "Create Uid"
msgstr "创建Uid"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__object_id
msgid "Create a New Record"
msgstr "创建新记录"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Create a new Mail Template"
msgstr "创建新的邮件模板"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid "Create a new canned response"
msgstr "创建罐装响应"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create new %(document)s"
msgstr "新建 %(document)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create new %(document)s by sending an email to %(email_link)s"
msgstr "通过向%(document)s发送电子邮件来创建新的%(email_link)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.xml:0
#, python-format
msgid "Create: #"
msgstr "创建：#"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Created"
msgstr "已创建"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Created By"
msgstr "创建人"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__create_uid
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_uid
msgid "Created by"
msgstr "创建人"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__create_date
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__create_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_date
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__create_date
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__create_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_date
msgid "Created on"
msgstr "创建日期"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_service_patch.js:0
#, python-format
msgid "Creating a new record..."
msgstr "创建新纪录…"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Creation Date"
msgstr "创建时间"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Creator"
msgstr "创建者"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__credential
msgid "Credential"
msgstr "凭据"

#. module: mail
#: model:ir.model,name:mail.model_mail_notification_web_push
msgid "Cron data used for web push notification"
msgstr "用于网络推送通知的 Cron 数据"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__currency_id
msgid "Currency"
msgstr "币种"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__starred
#: model:ir.model.fields,help:mail.field_mail_message__starred
msgid "Current user has a starred notification linked to this message"
msgstr "当前用户有一个打星号的提醒与此消息连接"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "自定义退回消息"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Custom ICE server list"
msgstr "自定义 ICE 服务器列表"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__custom_template
msgid "Custom Template"
msgstr "自定义模板"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Custom Templates"
msgstr "自定义模板"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__custom_channel_name
msgid "Custom channel name"
msgstr "自定义频道名称"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_notification_notification_partner_required
msgid "Customer is required for inbox / email notification"
msgstr "客户需要收件箱/电子邮件通知"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Customize the look and feel of automated emails"
msgstr "定制自动化电子邮件的外观和感觉"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__custom_notifications
msgid "Customized Notifications"
msgstr "定制通知"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "DTLS:"
msgstr "DTLS："

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Data channel:"
msgstr "数据频道："

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__date
#: model:ir.model.fields,field_description:mail.field_mail_message__date
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Date"
msgstr "日期"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_schedule__scheduled_datetime
msgid "Datetime at which notification should be sent."
msgstr "应发送通知的日期时间。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__pinned_at
#: model:ir.model.fields,help:mail.field_mail_message__pinned_at
msgid "Datetime at which the message has been pinned"
msgstr "信息被固定的日期时间"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__days
msgid "Days"
msgstr "天"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Deadline"
msgstr "截止日期"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Deadline:"
msgstr "截止日期:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "Deadline: %s"
msgstr "截止日期：%s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Deafen"
msgstr "关闭扬声器"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "Dear"
msgstr "尊敬的"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "Dear Sender"
msgstr "亲爱的发件人"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "Dear Sender,"
msgstr "亲爱的发件人，"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__decoration_type
msgid "Decoration Type"
msgstr "排版类型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__default
msgid "Default"
msgstr "默认"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__default_display_mode
msgid "Default Display Mode"
msgstr "默认显示模式"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__default_from_email
#: model:ir.model.fields,field_description:mail.field_res_company__default_from_email
msgid "Default From"
msgstr "默认从"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__default_from
msgid "Default From Alias"
msgstr "默认发件人别名"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_note
msgid "Default Note"
msgstr "默认备注"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__summary
msgid "Default Summary"
msgstr "默认摘要"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_user_id
msgid "Default User"
msgstr "默认用户"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_defaults
msgid "Default Values"
msgstr "默认值"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__default_from
msgid ""
"Default from when it does not match outgoing server filters. Can be either a"
" local-part e.g. 'notifications' either a complete email address e.g. "
"'<EMAIL>' to override all outgoing emails."
msgstr ""
"当不匹配外发服务器筛选器时的默认值。它可以是一个本地部分，例如'notifications'，也可以是完整的电子邮件地址，如'<EMAIL>'，用于覆盖所有外发电子邮件。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__use_default_to
msgid "Default recipients"
msgstr "默认收件人"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__use_default_to
msgid ""
"Default recipients of the record:\n"
"- partner (using id on a partner or the partner_id field) OR\n"
"- email (using email_from or email field)"
msgstr ""
"记录的默认收件人： \n"
"- 合作伙伴（使用合作伙伴的Id或partner_id字段）或\n"
"- EMail(使用发件人或email字段)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__responsible_type__other
msgid "Default user"
msgstr "默认用户"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__priority
msgid "Defines the order of processing, lower values mean higher priority"
msgstr "定义处理的顺序，值越小意味着优先级越高"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_label
msgid "Delay Label"
msgstr "延迟标签"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_from
msgid "Delay Type"
msgstr "延迟类型"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Delay after releasing push-to-talk"
msgstr "释放一键通话后延迟"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_unit
msgid "Delay units"
msgstr "延迟单位"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.xml:0
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
#, python-format
msgid "Delete"
msgstr "删除"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete
msgid "Delete Emails"
msgstr "删除邮件"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#, python-format
msgid "Delete all previews"
msgstr "删除全部预览"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__sent
#, python-format
msgid "Delivered"
msgstr "已发货"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__exception
msgid "Delivery Failed"
msgstr "投递失败"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#, python-format
msgid "Delivery failure"
msgstr "传递失败"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Deprecated usage of 'default_res_id', should use 'default_res_ids'."
msgstr "'default_res_id\" 已过时，应使用 'default_res_ids'。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__description
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_description
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__description
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__description
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Description"
msgstr "描述"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__description
msgid ""
"Description that will be added in the message posted for this subtype. If "
"void, the name will be added instead."
msgstr "将为该子模型的消息添加注解。如为空，则添加名称。"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__default_display_mode
msgid ""
"Determines how the channel will be displayed by default when opening it from"
" its invitation link. No value means display text (no voice/video)."
msgstr "确定从邀请链接打开频道时默认显示频道的方式。无值表示显示文本（无语音/视频）"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss_app_model.js:0
#, python-format
msgid "Direct messages"
msgstr "直接消息"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
#, python-format
msgid "Discard"
msgstr "丢弃"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_tree
#, python-format
msgid "Disconnect"
msgstr "中断连接"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Disconnected from the RTC call by the server"
msgstr "服务器断开与 RTC 调用的连接"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/thread_actions.js:0
#: model:ir.actions.client,name:mail.action_discuss
#: model:ir.ui.menu,name:mail.mail_menu_technical
#: model:ir.ui.menu,name:mail.menu_root_discuss
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
#, python-format
msgid "Discuss"
msgstr "讨论"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Discuss sidebar"
msgstr "讨论侧边栏"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_discuss_channel_member_unmute_ir_actions_server
msgid "Discuss: channel member unmute"
msgstr "讨论：频道成员取消静音"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel
msgid "Discussion Channel"
msgstr "讨论频道"

#. module: mail
#: model:mail.message.subtype,name:mail.mt_comment
msgid "Discussions"
msgstr "讨论"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "Dismiss"
msgstr "拒绝受理"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__display_name
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__display_name
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_followers__display_name
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__display_name
#: model:ir.model.fields,field_description:mail.field_mail_guest__display_name
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__display_name
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__display_name
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__display_name
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__display_name
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Display an option on related documents to open a composition wizard with "
"this template"
msgstr "显示一个关联单据的选项 ，用该模版打开撰写向导"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/many2one_avatar_user_field/many2one_avatar_user_field.js:0
#, python-format
msgid "Display avatar name"
msgstr "显示头像名称"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#, python-format
msgid "Do you really want to delete \"%s\"?"
msgstr "您真的要删除“%s”吗？"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#, python-format
msgid "Do you really want to delete this preview?"
msgstr "您确定要删除这个预览吗？"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Document"
msgstr "单据"

#. module: mail
#: model:ir.model,name:mail.model_mail_followers
msgid "Document Followers"
msgstr "单据关注者"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_ids
msgid "Document IDs"
msgstr "文档 ID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Document Model"
msgstr "单据模型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_name
msgid "Document Name"
msgstr "单据名称"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Document: \""
msgstr "文档：“"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "Domain"
msgstr "域"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__done
#, python-format
msgid "Done"
msgstr "已完成"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Done & Launch Next"
msgstr "完成和启动下一页"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Schedule Next"
msgstr "完成&安排下一次"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_done
msgid "Done Date"
msgstr "完成日期"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#, python-format
msgid "Done and Schedule Next"
msgstr "已完成，安排下一次"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
#, python-format
msgid "Download"
msgstr "下载"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Download Files"
msgstr "下载文件"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Download logs"
msgstr "下载日志"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Download:"
msgstr "下载："

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/dropzone.xml:0
#, python-format
msgid "Drag Files Here"
msgstr "将档案拖动到此处"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_deadline
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__date_deadline
msgid "Due Date"
msgstr "到期日期"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range
msgid "Due Date In"
msgstr "截止日期至"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Due in"
msgstr "到期日期"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#, python-format
msgid "Due in %s days"
msgstr "%s天后到期"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Due on"
msgstr "截止"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range_type
msgid "Due type"
msgstr "到期类型"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_dup
msgid "Duplicated Email"
msgstr "复制电子邮件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__voice_active_duration
msgid "Duration of voice activity in ms"
msgstr "语音活动的持续时间（毫秒）"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__report_template_ids
msgid "Dynamic Reports"
msgstr "动态报告"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__generic
msgid "Dynamic User (based on record)"
msgstr "动态用户（基于记录）"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Edge blur intensity"
msgstr "边缘模糊强度"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Edit"
msgstr "编辑"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "编辑伙伴"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.js:0
#, python-format
msgid "Edit Subscription of %(name)s"
msgstr "编辑%(name)s订阅"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#, python-format
msgid "Edit subscription"
msgstr "编辑订阅"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.js:0
#, python-format
msgid "Edit: %s"
msgstr "修改: %s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__email
#: model:ir.model.fields,field_description:mail.field_mail_followers__email
#: model:ir.model.fields,field_description:mail.field_res_partner__email
#: model:ir.model.fields,field_description:mail.field_res_users__email
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__email
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__email
#: model:mail.activity.type,name:mail.mail_activity_data_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email"
msgstr "电子邮件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_add_signature
#: model:ir.model.fields,field_description:mail.field_mail_message__email_add_signature
msgid "Email Add Signature"
msgstr "电子邮件添加签字"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__email
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__email
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__email
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Email Address"
msgstr "Email地址"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_email
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_email
msgid "Email Alias"
msgstr "电子邮件别名"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias
msgid "Email Aliases"
msgstr "EMail别名"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin
msgid "Email Aliases Mixin"
msgstr "EMail别名 Mixin"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin_optional
msgid "Email Aliases Mixin (light)"
msgstr "电子邮件别名混合素（轻型）"

#. module: mail
#: model:ir.ui.menu,name:mail.mail_blacklist_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Email Blacklist"
msgstr "EMail黑名单"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_secondary_color
#: model:ir.model.fields,field_description:mail.field_res_config_settings__email_secondary_color
msgid "Email Button Color"
msgstr "电子邮件按钮颜色"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_cc
msgid "Email CC management"
msgstr "电子邮件副本管理"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Email Configuration"
msgstr "EMail配置"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_domain
#: model:ir.model.fields,field_description:mail.field_res_company__alias_domain_id
msgid "Email Domain"
msgstr "电子邮件域名"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_primary_color
#: model:ir.model.fields,field_description:mail.field_res_config_settings__email_primary_color
msgid "Email Header Color"
msgstr "电子邮件标题颜色"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__mass_mail
msgid "Email Mass Mailing"
msgstr "邮件群发"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_template__email_layout_xmlid
msgid "Email Notification Layout"
msgstr "电子邮件通知布局"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Email Preview"
msgstr "EMail预览"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email Search"
msgstr "EMail搜索"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__template_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__template_id
msgid "Email Template"
msgstr "EMail模板"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_preview
msgid "Email Template Preview"
msgstr "EMail模板预览"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_template_tree_all
#: model:ir.model,name:mail.model_mail_template
#: model:ir.ui.menu,name:mail.menu_email_templates
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Email Templates"
msgstr "EMail模板"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread
msgid "Email Thread"
msgstr "邮件会话"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_blacklist_unique_email
msgid "Email address already exists!"
msgstr "EMail地址已存在！"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,help:mail.field_mail_mail__email_from
#: model:ir.model.fields,help:mail.field_mail_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr "发送人的电子邮箱地址。找不到匹配的合作伙伴时，设置该字段，并替换聊天窗口中的author_id字段。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass"
msgstr "批量发送电子邮件时将回复重定向到的电子邮件地址"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__reply_to
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass; only used when the reply is not logged in the original discussion "
"thread."
msgstr "当群发邮件时，回复将被重定向的电子邮件地址;仅当回复没有被记录在原始讨论线程中时使用。"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid ""
"Email addresses that are blacklisted won't receive Email mailings anymore."
msgstr "被列入黑名单的电子邮件地址将不再接收电子邮件。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Email aliases %(alias_name)s cannot be used on several records at the same "
"time. Please update records one by one."
msgstr "电子邮件别名 %(alias_name)s 不能同时用于多个记录。请逐一更新记录。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__email_cc
msgid "Email cc"
msgstr "邮件抄送"

#. module: mail
#: model:ir.model,name:mail.model_mail_compose_message
msgid "Email composition wizard"
msgstr "EMail撰写向导"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_domain
#: model:ir.model.fields,help:mail.field_mail_alias_domain__name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_domain
#: model:ir.model.fields,help:mail.field_res_company__alias_domain_name
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "电子邮件域名，例如 '<EMAIL>' 中的 'example.com'"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Email message"
msgstr "EMail消息"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_message
msgid "Email resend wizard"
msgstr "EMail重发向导"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__mail_template_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__mail_template_ids
msgid "Email templates"
msgstr "电子邮件模板"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_mail
#: model:ir.ui.menu,name:mail.menu_mail_mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_tree
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Emails"
msgstr "EMail"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
#, python-format
msgid "Emoji"
msgstr "表情符号"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#: code:addons/mail/static/src/views/web/fields/emojis_field_common/emojis_field_common.xml:0
#, python-format
msgid "Emojis"
msgstr "表情符号"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_internal
#: model:ir.model.fields,field_description:mail.field_mail_message__is_internal
msgid "Employee Only"
msgstr "仅限员工"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model_fields__tracking
msgid "Enable Ordered Tracking"
msgstr "启用跟踪"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Enable Push-to-talk"
msgstr "启用一键通话功能"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "Enable desktop notifications to chat"
msgstr "启用桌面聊天通知"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Enter"
msgstr "输入"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Enter Full Screen"
msgstr "进入全屏模式"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__error
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__danger
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_exception_decoration__danger
#, python-format
msgid "Error"
msgstr "错误"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__error_msg
msgid "Error Message"
msgstr "错误消息"

#. module: mail
#. odoo-python
#: code:addons/mail/models/update.py:0
#, python-format
msgid "Error during communication with the publisher warranty server."
msgstr "当与保障服务器通信时发生错误。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__message
msgid "Error message"
msgstr "错误消息"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due to concurrent access update of "
"notification records. Please see with an administrator."
msgstr "错误无一例外。可能是由于通知记录的并发访问更新。请与管理员联系。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due to sending an email without computed "
"recipients."
msgstr "错误无一例外。可能是由于发送没有计算收件人的电子邮件。"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_followers_mail_followers_res_partner_res_model_id_uniq
msgid "Error, a partner cannot follow twice the same object."
msgstr "错误，用户不能重复创建联系人。"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__everyone
msgid "Everyone"
msgstr "所有人"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__exception
#: model:mail.activity.type,name:mail.mail_activity_data_warning
msgid "Exception"
msgstr "异常"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Exit Full Screen"
msgstr "退出全屏模式"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#, python-format
msgid "Expand"
msgstr "展开"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__expiration_time
msgid "Expiration Token Date"
msgstr "到期令牌日期"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Extended Filters..."
msgstr "扩展筛选..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__fail_counter
msgid "Fail Mail"
msgstr "失败的邮件"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Failed"
msgstr "失败的"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/webclient/web/webclient.js:0
#, python-format
msgid "Failed to enable push notifications"
msgstr "未能启用推送通知"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "Failed to load gifs..."
msgstr "加载 gif 失败 ..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Failed to load the SFU server, falling back to peer-to-peer"
msgstr "加载 SFU 服务器失败，退回到点对点模式"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Failed to render QWeb template: %(template_src)s\n"
"\n"
"%(template_traceback)s)"
msgstr ""
"渲染 QWeb 模板失败：%(template_src)s\n"
"\n"
"%(template_traceback)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render inline_template template: %(template_txt)s"
msgstr "渲染 inline_template 模板失败：%(template_txt)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render template: %(view_ref)s"
msgstr "渲染模板失败：%(view_ref)s"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_reason
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__failure_reason
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Failure Reason"
msgstr "失败原因"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_reason
msgid "Failure reason"
msgstr "失败原因"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__failure_reason
msgid ""
"Failure reason. This is usually the exception thrown by the email server, "
"stored to ease the debugging of mailing issues."
msgstr "失败原因。通过EMail服务器抛出的异常，存储以缓解邮件问题的调试。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_type
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_type
msgid "Failure type"
msgstr "失败类型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__starred_partner_ids
msgid "Favorited By"
msgstr "收藏夹"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "Favorites"
msgstr "收藏夹"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Fetch Now"
msgstr "现在收取"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_gif_limit
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Fetch up to the specified number of GIF."
msgstr "获取指定数量的 GIF。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_id
msgid "Field"
msgstr "字段"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Activity\" cannot be changed to \"False\"."
msgstr "字段“邮件活动”不能更改为“False”。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Blacklist\" cannot be changed to \"False\"."
msgstr "“邮件黑名单”字段不能更改为“False”。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Thread\" cannot be changed to \"False\"."
msgstr "字段\"Type\" 不能在模型上更改."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_groups
msgid "Field Groups"
msgstr "字段组"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Field details"
msgstr "字段详情"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__relation_field
msgid ""
"Field used to link the related model to the subtype model when using "
"automatic subscription on a related document. The field is used to compute "
"getattr(related_document.relation_field)."
msgstr ""
"字段用于在使用相关单据的自动订阅时将相关模型链接到子类型模型。该字段用于计算GETAFTR（RelabyDo.DeffixFieldFieldfield）。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_tracking_duration_mixin.py:0
#, python-format
msgid ""
"Field “%(field)s” on model “%(model)s” must be of type Many2one and have "
"tracking=True for the computation of duration."
msgstr ""
"字段 “%(field)s”（位于模型 “%(model)s”内）必须是Many2one 类型，而且计算持续时间设为 tracking=True。"

#. module: mail
#: model:ir.model,name:mail.model_ir_model_fields
msgid "Fields"
msgstr "字段"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__template_fs
#: model:ir.model.fields,help:mail.field_template_reset_mixin__template_fs
msgid ""
"File from where the template originates. Used to reset broken template."
msgstr "模板来源的文件。 用于重置损坏的模板。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_upload_service.js:0
#, python-format
msgid "File too large"
msgstr "文件过大"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#, python-format
msgid "File upload is disabled for external users"
msgstr "外部用户无法上传文件"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#, python-format
msgid "File upload is enabled for external users"
msgstr "外部用户可上传文件"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Files"
msgstr "文件"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#, python-format
msgid "Fold"
msgstr "收拢"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__folded
msgid "Folded"
msgstr "收起"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Follow"
msgstr "关注"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_followers
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_follower_ids
#: model:ir.ui.menu,name:mail.menu_email_followers
#: model_terms:ir.ui.view,arch_db:mail.view_followers_tree
msgid "Followers"
msgstr "关注者"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_partner_ids
msgid "Followers (Partners)"
msgstr "关注者（合作伙伴）"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_subscription_form
msgid "Followers Form"
msgstr "关注者表单"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__followers
msgid "Followers only"
msgstr "仅关注者"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Followers to add"
msgstr "添加关注者"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Followers to remove"
msgstr "要删除的追随者"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.js:0
#, python-format
msgid "Following"
msgstr "正在关注中"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__icon
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,help:mail.field_mail_activity_type__icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font Awesome图标，例如：fa-tasks"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"For %(channels)s, channel_type should be 'channel' to have the group-based "
"authorization or group auto-subscription."
msgstr "对于 %(channels)s，channel_type应为“频道”，以获得基于组的授权或组自动订阅。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "For 1 hour"
msgstr "1 小时"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "For 15 minutes"
msgstr "15 分钟"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "For 24 hours"
msgstr "24 小时"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "For 3 hours"
msgstr "3小时"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "For 8 hours"
msgstr "8 小时"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_form
msgid "Force Send"
msgstr "强制发送"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Force a Language:"
msgstr "强制使用一种语言："

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_formatted
msgid "Formatted Email"
msgstr "格式化的邮件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_from
#: model:ir.model.fields,field_description:mail.field_mail_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_from
msgid "From"
msgstr "来自"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "From peer:"
msgstr "来自同行："

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#: code:addons/mail/static/src/core/common/composer.xml:0
#, python-format
msgid "Full composer"
msgstr "完全创建者"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__default_display_mode__video_full_screen
msgid "Full screen video"
msgstr "全屏视频"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#, python-format
msgid "Future"
msgstr "未来"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Future Activities"
msgstr "未来活动"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "GIF"
msgstr "GIF"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "GIF Category"
msgstr "GIF 类别"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "GIF Favorites"
msgstr "GIF 收藏夹"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_gif_favorite_action
#: model:ir.ui.menu,name:mail.discuss_gif_favorite_menu
#: model_terms:ir.ui.view,arch_db:mail.discuss_gif_favorite_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_gif_favorite_view_tree
msgid "GIF favorite"
msgstr "GIF 收藏夹"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__tenor_gif_id
msgid "GIF id from Tenor"
msgstr "来自 Tenor 的 GIF ID"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/composer_patch.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
#, python-format
msgid "GIFs"
msgstr "GIF"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Gateway"
msgstr "网关"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Go to conversation"
msgstr "打开对话"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Google Translate Integration"
msgstr "谷歌翻译集成"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__group
msgid "Group"
msgstr "群组"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Group By"
msgstr "分组方式"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Group Name"
msgstr "群组名称"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_group_public_id_check
msgid ""
"Group authorization and group auto-subscription are only supported on "
"channels."
msgstr "仅频道支持群组授权和群组自动订阅。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Group by..."
msgstr "分组…"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#, python-format
msgid "Grouped Chat"
msgstr "群聊"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_tree
msgid "Groups"
msgstr "群组"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/controllers/discuss/public_page.py:0
#: code:addons/mail/static/src/discuss/core/public/welcome_page.js:0
#: model:ir.model,name:mail.model_mail_guest
#: model:ir.model.fields,field_description:mail.field_bus_presence__guest_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__guest_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_guest_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_guest_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__guest_id
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_form
#, python-format
msgid "Guest"
msgstr "游客"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/mail_guest.py:0
#, python-format
msgid "Guest's name cannot be empty."
msgstr "游客姓名不能为空."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/mail_guest.py:0
#, python-format
msgid "Guest's name is too long."
msgstr "游客姓名太长."

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_guest_action
#: model:ir.ui.menu,name:mail.mail_guest_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_tree
msgid "Guests"
msgstr "顾客"

#. module: mail
#: model:ir.model,name:mail.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP 路由"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__email
msgid "Handle by Emails"
msgstr "用邮件处理"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__inbox
msgid "Handle in Odoo"
msgstr "在Odoo内处理"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__has_error
msgid "Has Error"
msgstr "有错误"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_activity
msgid "Has Mail Activity"
msgstr "有邮件活动"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_blacklist
msgid "Has Mail Blacklist"
msgstr "有邮件黑名单"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_thread
msgid "Has Mail Thread"
msgstr "有邮件会话"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has Mentions"
msgstr "提及"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__has_message
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__has_message
#: model:ir.model.fields,field_description:mail.field_res_partner__has_message
#: model:ir.model.fields,field_description:mail.field_res_users__has_message
msgid "Has Message"
msgstr "有消息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_deaf
msgid "Has disabled incoming sound"
msgstr "禁用来电声音"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__has_error
#: model:ir.model.fields,field_description:mail.field_mail_message__has_error
msgid "Has error"
msgstr "有错误"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__has_user_on_demand
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_has_user_on_demand
msgid "Has on demand responsible"
msgstr "按需负责"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__headers
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Headers"
msgstr "标题"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Hello"
msgstr "您好"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Hello,"
msgstr "你好,"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__hidden
msgid "Hidden"
msgstr "隐藏"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__hidden_template
msgid "Hidden Template"
msgstr "已隐藏的模板"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Hide Attachments"
msgstr "隐藏附件"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
#, python-format
msgid "Hide Call Settings"
msgstr "隐藏呼叫设置"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Hide Member List"
msgstr "隐藏成员列表"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/thread_actions.js:0
#, python-format
msgid "Hide Pinned Messages"
msgstr "隐藏置顶消息"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.xml:0
#, python-format
msgid "Hide sidebar"
msgstr "隐藏侧边栏"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__hidden
msgid "Hide the subtype in the follower options"
msgstr "隐藏关注者选项的子类型"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_internal
#: model:ir.model.fields,help:mail.field_mail_message__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr "对公共/门户用户隐藏，独立于子类型配置."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__high
msgid "High"
msgstr "高"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/messaging_service.js:0
#: code:addons/mail/static/src/core/common/thread.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#, python-format
msgid "History"
msgstr "历史"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__server
msgid "Hostname or IP of the mail server"
msgstr "邮件服务器的主机名或IP"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__voice_active_duration
msgid ""
"How long the audio broadcast will remain active after passing the volume "
"threshold"
msgstr "超过音量阈值后音频广播将保持活动多长时间"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "ICE Servers"
msgstr "ICE 服务器"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "ICE gathering:"
msgstr "ICE 采集："

#. module: mail
#: model:ir.model,name:mail.model_mail_ice_server
#: model_terms:ir.ui.view,arch_db:mail.view_ice_server_form
msgid "ICE server"
msgstr "ICE 服务器"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_ice_servers
#: model:ir.ui.menu,name:mail.ice_servers_menu
msgid "ICE servers"
msgstr "ICE 服务器"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "ICE:"
msgstr "ICE："

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__id
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__id
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__id
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__id
#: model:ir.model.fields,field_description:mail.field_mail_activity__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__id
#: model:ir.model.fields,field_description:mail.field_mail_alias__id
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__id
#: model:ir.model.fields,field_description:mail.field_mail_followers__id
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__id
#: model:ir.model.fields,field_description:mail.field_mail_guest__id
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__id
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_mail__id
#: model:ir.model.fields,field_description:mail.field_mail_message__id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__id
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__id
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__id
#: model:ir.model.fields,field_description:mail.field_mail_notification__id
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__id
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__id
#: model:ir.model.fields,field_description:mail.field_mail_template__id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__id
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__id
msgid "ID"
msgstr "ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr "上级记录ID支持别名(例如:项目支持任务创建别名)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__im_status
msgid "IM Status"
msgstr "IM的状态"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "IMAP"
msgstr "IMAP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__imap
msgid "IMAP Server"
msgstr "IMAP 服务器"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_icon
msgid "Icon"
msgstr "图标"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "指示异常活动的图标。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__res_id
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_id
msgid "Id of the followed resource"
msgstr "被关注资源的ID"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
msgid "Identity"
msgstr "身份"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "Idle"
msgstr "空闲"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "If SSL required."
msgstr "是否需要SSL。"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_needaction
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_needaction
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction
#: model:ir.model.fields,help:mail.field_res_users__message_needaction
msgid "If checked, new messages require your attention."
msgstr "如果勾选此项，则需要查看新消息。"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_has_error
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_has_error
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error
#: model:ir.model.fields,help:mail.field_res_users__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "如果勾选此项， 某些消息将出现发送错误。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "If not set, shared with all users."
msgstr "如果未设置，则与所有用户共享。"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model_fields__tracking
msgid ""
"If set every modification done to this field is tracked. Value is used to "
"order tracking values."
msgstr "如果设置，对该字段的每次修改都会被跟踪。该值用于对跟踪值进行排序。"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__mute_until_dt
msgid ""
"If set, the member will not receive notifications from the channel until "
"this date."
msgstr "如果设置了该选项，会员在此日期之前将不会收到来自频道的通知。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. Unless a timezone is specified, "
"it is considered as being in UTC timezone."
msgstr "如果设置，队列管理器将在该日期之后发送电子邮件。 如果未设置，电子邮件将尽快发送。 除非指定时区，否则它被视为在 UTC 时区。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. You can use dynamic expression."
msgstr "如果设置，队列管理器将在日期之后发送电子邮件。如果未设置，电子邮件将尽快发送。您可以使用动态表达式。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr "如果设置，此内容将自动发送给未经授权的用户，而不是默认消息。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"If set, will restrict the template to this specific user."
"                                                   If not set, shared with "
"all users."
msgstr ""
"如果设置，将限制该用户使用模板。                                                   "
"如果未设置，则与所有用户共享。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_users__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr "存在于黑名单的EMail表示收件者不会再收到任何列表的群发邮件"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,help:mail.field_mail_message__reply_to_force_new
msgid ""
"If true, answers do not go in the original document discussion thread. "
"Instead, it will check for the reply_to in tracking message-id and "
"redirected accordingly. This has an impact on the generated message-id."
msgstr ""
"如果为真，那么答案不会出现在原始文档讨论线程中。相反，它将检查跟踪message-id中的reply_to，并相应地重定向。这将对生成的message-"
"id产生影响。"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__alias_domain_id
msgid ""
"If you have setup a catch-all email domain redirected to the Odoo server, "
"enter the domain name here."
msgstr "如果您设置了电子邮件域名重定向到这个 ERP 服务器，请在这里输入域名称."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "If you want to use twilio as TURN/STUN server provider"
msgstr "如果您想使用twilio作为TURN/STUN服务器提供商"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Ignore all"
msgstr "忽略所有"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__image_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1920
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_image
msgid "Image"
msgstr "图像"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1024
msgid "Image 1024"
msgstr "图像 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_128
msgid "Image 128"
msgstr "图像128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_256
msgid "Image 256"
msgstr "图像 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_512
msgid "Image 512"
msgstr "图像 512"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__image_mimetype
msgid "Image MIME type"
msgstr "图像哑剧类型"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Image is a link"
msgstr "图像为链接"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__scheduled_date
msgid ""
"In comment mode: if set, postpone notifications sending. In mass mail mode: "
"if sent, send emails after that date. This date is considered as being in "
"UTC timezone."
msgstr "在评论模式下：如果设置，则推迟发送通知。在邮件群发模式下：如果已发送，则在该日期后发送邮件。该日期为 UTC 时区。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "Inactive Alias"
msgstr "未启用的别名"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__fetchmail_server_id
msgid "Inbound Mail Server"
msgstr "收件服务器"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/messaging_service.js:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__inbox
#, python-format
msgid "Inbox"
msgstr "收件箱"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#, python-format
msgid "Incoming Call..."
msgstr "来电..."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Incoming Email"
msgstr "接收电子邮件"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Incoming Email Servers"
msgstr "收件服务器"

#. module: mail
#: model:ir.model,name:mail.model_fetchmail_server
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Incoming Mail Server"
msgstr "收件服务器"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_server_tree
#: model:ir.ui.menu,name:mail.menu_action_fetchmail_server_tree
msgid "Incoming Mail Servers"
msgstr "收件服务器"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__automated
msgid ""
"Indicates this activity has been created automatically and not by any user."
msgstr "表示这个活动是自动生成的，不是任何用户创建。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Info"
msgstr "信息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__initial_res_model
msgid "Initial model"
msgstr "初始模型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__contact_address_inline
#: model:ir.model.fields,field_description:mail.field_res_users__contact_address_inline
msgid "Inlined Complete Address"
msgstr "内嵌完整地址"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Input device"
msgstr "输入设备"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "Install"
msgstr "安装"

#. module: mail
#: model:ir.ui.menu,name:mail.discuss_channel_integrations_menu
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Integrations"
msgstr "集成"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__internal
msgid "Internal Only"
msgstr "仅内部的"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.notification_preview
msgid "Internal communication:"
msgstr "国际交流："

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__invalid
msgid "Invalid"
msgstr "无效"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Invalid domain %(domain)r (type %(domain_type)s)"
msgstr "无效域 %(domain)r （类型%(domain_type)s）"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_invalid
msgid "Invalid email address"
msgstr "无效的EMail地址"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_blacklist.py:0
#, python-format
msgid "Invalid email address %r"
msgstr "无效的EMail地址 %r"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Invalid expression, it must be a literal python dictionary definition e.g. "
"\"{'field': 'value'}\""
msgstr "错误的表达式，必须是字面python字典定义，例如： \"{'field': 'value'}\""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "Invalid field “%(field_name)s” when creating a channel with members."
msgstr "建立包含成员的频道时，栏位%(field_name)s无效。"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_from_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_from_invalid
msgid "Invalid from address"
msgstr "来自地址无效"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid "Invalid primary email field on model %s"
msgstr "初级邮件模型中无效的部分 %s"

#. module: mail
#. odoo-python
#: code:addons/mail/tools/parser.py:0
#, python-format
msgid "Invalid res_ids %(res_ids_str)s (type %(res_ids_type)s)"
msgstr "无效  %(res_ids_str)s （类型 %(res_ids_type)s）"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid ""
"Invalid server name!\n"
" %s"
msgstr ""
"服务器名称无效！\n"
" %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Invalid template or view source %(svalue)s (type %(stype)s), should be a "
"record or an XMLID"
msgstr "无效模板或视图源%(svalue)s（类型%(stype)s），应为记录或 XMLID"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Invalid template or view source Xml ID %(source_ref)s does not exist anymore"
msgstr "无效模板或视图源 Xml ID%(source_ref)s不再存在"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Invalid template or view source record %(svalue)s, is %(model)s instead"
msgstr "模版或视图来源记录%(svalue)s无效，应为%(model)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Invalid template or view source reference %(svalue)s, is %(model)s instead"
msgstr "模版或视图来源参考%(svalue)s无效，应为%(model)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"Invalid value when creating a channel with members, only 4 or 6 are allowed."
msgstr "创建包含成员的频道时的值无效，仅允许 4 或 6 个。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"Invalid value when creating a channel with memberships, only 0 is allowed."
msgstr "创建具有会员资格的频道时的值无效，仅允许为 0。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__invitation_url
msgid "Invitation URL"
msgstr "邀请URL"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Invitation to follow %(document_model)s: %(document_name)s"
msgstr "邀请关注 %(document_model)s: %(document_name)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Invite"
msgstr "邀请"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.js:0
#, python-format
msgid "Invite Follower"
msgstr "邀请关注者"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "Invite a User"
msgstr "邀请用户"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Invite people"
msgstr "邀请人员"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Invite to Channel"
msgstr "邀请加入频道"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Invite to Group Chat"
msgstr "邀请加入群聊"

#. module: mail
#: model:ir.model,name:mail.model_mail_wizard_invite
msgid "Invite wizard"
msgstr "邀请向导"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__is_active
msgid "Is Active"
msgstr "处于启用状态"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_current_user_or_guest_author
#: model:ir.model.fields,field_description:mail.field_mail_message__is_current_user_or_guest_author
msgid "Is Current User Or Guest Author"
msgstr "是当前用户还是游客作者"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_editable
msgid "Is Editable"
msgstr "能编辑"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_mail_template_editor
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__is_mail_template_editor
msgid "Is Editor"
msgstr "是编辑器"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_partner__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_users__message_is_follower
msgid "Is Follower"
msgstr "是关注者"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_member
msgid "Is Member"
msgstr "是会员"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__is_read
msgid "Is Read"
msgstr "已读"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_self
msgid "Is Self"
msgstr "是自我"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__is_template_editor
msgid "Is Template Editor"
msgstr "是模板编辑器"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_chat
msgid "Is a chat"
msgstr "是聊天"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_is_log
msgid "Is a log"
msgstr "是系统日志"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_channel_open
msgid "Is discuss sidebar category channel open?"
msgstr "是否打开讨论侧边栏类别聊天？"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_chat_open
msgid "Is discuss sidebar category chat open?"
msgstr "是否打开讨论侧边栏类别聊天？"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_muted
msgid "Is microphone muted"
msgstr "麦克风静音"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_pinned
msgid "Is pinned on the interface"
msgstr "是否置顶"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_camera_on
msgid "Is sending user video"
msgstr "正在发送用户视频"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_screen_sharing_on
msgid "Is sharing the screen"
msgstr "正在共享屏幕"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid ""
"It appears you're trying to create a channel member, but it seems like you "
"forgot to specify the related channel. To move forward, please make sure to "
"provide the necessary channel information."
msgstr "您正在尝试创建一个频道成员，但似乎忘记指定相关频道。如要继续，请确保提供必要的频道信息。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_partner_device__keys
msgid ""
"It's refer to browser keys used by the notification: \n"
"- p256dh: It's the subscription public key generated by the browser. The browser will \n"
"          keep the private key secret and use it for decrypting the payload\n"
"- auth: The auth value should be treated as a secret and not shared outside of Odoo"
msgstr ""
"它指的是通知使用的浏览器密钥：\n"
"- p256dh：这是浏览器生成的订阅公共密钥。浏览器将\n"
"          私钥保密，并用于解密有效载荷\n"
"- auth： 验证值应视为机密，不得在 ERP 以外共享"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_duration_mixin__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr "JSON 将 many2one字段中的ID对应至所花费时间(以秒为单位)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Join"
msgstr "加入"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Join Call"
msgstr "加入语音会议"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "Join Channel"
msgstr "加入频道"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_action_view
msgid "Join a group"
msgstr "加入群组"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.xml:0
#, python-format
msgid "Jump"
msgstr "跳"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "Jump to Present"
msgstr "跳转到现在"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__attach
msgid "Keep Attachments"
msgstr "保存附件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__keep_done
msgid "Keep Done"
msgstr "保持完成"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete_keep_log
msgid "Keep Message Copy"
msgstr "保存消息副本"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__original
msgid "Keep Original"
msgstr "保留原件"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete_keep_log
msgid ""
"Keep a copy of the email content if emails are removed (mass mailing only)"
msgstr "如果删除邮件（仅限群发邮件），保留邮件内容的副本。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__keep_done
msgid "Keep activities marked as done in the activity view"
msgstr "在活动视图中保留标记为已完成的活动"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Key"
msgstr "键"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#: code:addons/mail/models/mail_alias.py:0
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
#, python-format
msgid "Kind Regards"
msgstr "亲切问候"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "LIVE"
msgstr "在线"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__lang
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_guest__lang
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_template__lang
msgid "Language"
msgstr "语言"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__date
msgid "Last Fetch Date"
msgstr "最后收取日期"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__fetched_message_id
msgid "Last Fetched"
msgstr "最后一次提取"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__last_interest_dt
msgid "Last Interest"
msgstr "持续的兴趣"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__seen_message_id
msgid "Last Seen"
msgstr "最近一次查阅"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__write_date
msgid "Last Updated On"
msgstr "最后更新时间"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__write_uid
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_uid
msgid "Last Updated by"
msgstr "最后更新人"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__write_date
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__write_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__write_date
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__write_date
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__write_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_date
msgid "Last Updated on"
msgstr "上次更新日期"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__last_used
msgid "Last Used"
msgstr "上次使用"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__last_seen_dt
msgid "Last seen date"
msgstr "上次查看日期"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__last_used
msgid "Last time this shortcode was used"
msgstr "上次使用此简码"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#, python-format
msgid "Late"
msgstr "迟到"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Late Activities"
msgstr "最近活动"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "Launch Plans"
msgstr "发射计划"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_message__email_layout_xmlid
msgid "Layout"
msgstr "布局"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Leave"
msgstr "离开"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.js:0
#, python-format
msgid "Leave Conversation"
msgstr "离开对话。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "Leave this channel"
msgstr "退出此频道"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_link_preview_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__link_preview_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__link_preview_ids
#: model:ir.ui.menu,name:mail.mail_link_preview_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_link_preview_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_link_preview_view_tree
msgid "Link Previews"
msgstr "链接预览"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Link copied!"
msgstr "链接已复制！"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
#, python-format
msgid "List users in the current channel"
msgstr "在当前频道列出用户"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "Load More"
msgstr "加载更多"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: code:addons/mail/static/src/core/web/recipient_list.xml:0
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "Load more"
msgstr "加载更多"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Load template"
msgstr "加载模板"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
#, python-format
msgid "Loading"
msgstr "正在加载   稍安勿躁"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__local
msgid "Local Server"
msgstr "本地服务器"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr "基于本地部件的来件检测"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__catchall_alias
msgid ""
"Local-part of email used for Reply-To to catch answers e.g. 'catchall' in "
"'<EMAIL>'"
msgstr "电子邮件的本地部分用于回复至以捕获答案，例如在'<EMAIL>'中的'catchall'。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__bounce_alias
msgid ""
"Local-part of email used for Return-Path used when emails bounce e.g. "
"'bounce' in '<EMAIL>'"
msgstr ""
"电子邮件的本地部分用于在电子邮件反弹时用作Return-Path的使用，例如在'<EMAIL>'中的'bounce'。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Log"
msgstr "记录"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Log RTC events"
msgstr "记录 RTC 事件"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Log a note..."
msgstr "记录一个备注..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Log an Activity"
msgstr "记录一个活动"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/composer_patch.js:0
#, python-format
msgid "Log an internal note…"
msgstr "记录内部说明..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Log note"
msgstr "记录备注"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Log step:"
msgstr "记录步骤："

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.js:0
#, python-format
msgid "Logged in as %s"
msgstr "已登入为%s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Login Information"
msgstr "登录信息"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__low
msgid "Low"
msgstr "低"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Lower Hand"
msgstr "低手"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_mimetype
msgid "MIME type"
msgstr "MIME 类型"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_mail_id
#, python-format
msgid "Mail"
msgstr "邮件"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Activity"
msgstr "邮件活动"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_activity_type_id
msgid "Mail Activity Type"
msgstr "邮件活动类型"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Blacklist"
msgstr "邮件黑名单"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_blacklist
msgid "Mail Blacklist mixin"
msgstr "mixin邮件黑名单"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Mail Channel Form"
msgstr "邮件频道表单"

#. module: mail
#: model:ir.model,name:mail.model_mail_composer_mixin
msgid "Mail Composer Mixin"
msgstr "邮件编辑器混合"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "Mail Failures"
msgstr "邮件失败"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_gateway_allowed_action
#: model:ir.model,name:mail.model_mail_gateway_allowed
#: model:ir.ui.menu,name:mail.mail_gateway_allowed_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_gateway_allowed_view_tree
msgid "Mail Gateway Allowed"
msgstr "允许的邮件网关"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_config_settings.py:0
#, python-format
msgid "Mail Layout"
msgstr "邮件布局"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_main_attachment
msgid "Mail Main Attachment management"
msgstr "主邮件附件管理"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id_int
msgid "Mail Message Id Int"
msgstr "邮件消息 ID 整型"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel_rtc_session
msgid "Mail RTC session"
msgstr "邮件 RTC 会话"

#. module: mail
#: model:ir.model,name:mail.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr "邮件渲染混合"

#. module: mail
#: model:ir.model,name:mail.model_ir_mail_server
msgid "Mail Server"
msgstr "邮件服务器"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__template_id
msgid "Mail Template"
msgstr "邮件模板"

#. module: mail
#: model:res.groups,name:mail.group_mail_template_editor
msgid "Mail Template Editor"
msgstr "邮件模板编辑器"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_reset
msgid "Mail Template Reset"
msgstr "邮件模板重置"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_template_reset.py:0
#, python-format
msgid "Mail Templates have been reset"
msgstr "邮件模板已被重置"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Thread"
msgstr "邮件线程"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_value
msgid "Mail Tracking Value"
msgstr "邮件跟踪值"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid ""
"Mail composer in comment mode should run on at least one record. No records "
"found (model %(model_name)s)."
msgstr "注释模式下，邮件撰写器应至少在一条记录上运行。未找到记录（模型%(model_name)s）。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_notification
msgid "Mail has been created to notify people of an existing mail.message"
msgstr "邮件被创建，以通知当前收件人"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "Mail template model of %(action_name)s does not match action model."
msgstr "邮件模板模型 %(action_name)s 与动作模型不匹配。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_mail_server__mail_template_ids
msgid "Mail template using this mail server"
msgstr "使用此邮件服务器的邮件模板"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_scheduler_action_ir_actions_server
msgid "Mail: Email Queue Manager"
msgstr "邮件：EMail队列管理器"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_gateway_action_ir_actions_server
msgid "Mail: Fetchmail Service"
msgstr "邮件：Fetchmail 服务"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_web_push_notification_ir_actions_server
msgid "Mail: send web push notification"
msgstr "邮件：发送网络推送通知"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Mailbox unavailable - %s"
msgstr "邮箱不可用 - %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "Mailboxes"
msgstr "邮箱"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Mailing or posting with a source should not be called with an empty "
"%(source_type)s"
msgstr "若邮件或帖文有来源，便不应以空的%(source_type)s呼叫"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_ids
msgid "Mails"
msgstr "邮件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_main_attachment_id
msgid "Main Attachment"
msgstr "主要附件"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tools/debug_manager.js:0
#, python-format
msgid "Manage Messages"
msgstr "管理消息"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_force_new
msgid ""
"Manage answers as new incoming emails instead of replies going to the same "
"thread."
msgstr "将答案作为新的传入电邮而非发送至同一线程的回复进行管理。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/notification_item.xml:0
#, python-format
msgid "Mark As Read"
msgstr "标记为已读"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#, python-format
msgid "Mark Done"
msgstr "标记完成"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
#, python-format
msgid "Mark all read"
msgstr "标记全部已读"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree_without_record_access
msgid "Mark as Done"
msgstr "标记为完成"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Mark as Read"
msgstr "标记为已读"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Mark as Todo"
msgstr "标记为待办"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Mark as Unread"
msgstr "标记为未读"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
#, python-format
msgid "Mark as done"
msgstr "完成"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.js:0
#, python-format
msgid "Media devices unobtainable. SSL might not be set up properly."
msgstr "无法获得媒体设备。SSL 可能未正确设置。"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__medium
msgid "Medium"
msgstr "媒介"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_meeting
msgid "Meeting"
msgstr "会议"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__member_count
msgid "Member Count"
msgstr "成员计数"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.js:0
#, python-format
msgid "Member List"
msgstr "成员列表"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_member_ids
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Members"
msgstr "成员"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__group_ids
msgid ""
"Members of those groups will automatically added as followers. Note that "
"they will be able to manage their subscription manually if necessary."
msgstr "这些群组的成员将自动添加为关注者。注意, 如果有必要，他们可以手工管理他们的订阅。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/command_category.js:0
#, python-format
msgid "Mentions"
msgstr "提及"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__mentions
#, python-format
msgid "Mentions Only"
msgstr "仅提及"

#. module: mail
#: model:ir.model,name:mail.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr "合并业务伙伴向导"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/base_partner_merge_automatic_wizard.py:0
#, python-format
msgid "Merged with the following partners:"
msgstr "合并以下业务伙伴:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model,name:mail.model_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__message_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__message_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__message
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__comment
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#, python-format
msgid "Message"
msgstr "消息"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Message #%(thread name)s…"
msgstr "发送消息给 #%(thread name)s…"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Message %(thread name)s…"
msgstr "发送消息给 #%(thread name)s…"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error
msgid "Message Delivery error"
msgstr "消息发送错误"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__mail_message_id
msgid "Message ID"
msgstr "消息ID"

#. module: mail
#: model:ir.model,name:mail.model_mail_notification
msgid "Message Notifications"
msgstr "消息通知"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_reaction
msgid "Message Reaction"
msgstr "消息反应"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_message_reaction_action
#: model:ir.ui.menu,name:mail.mail_message_reaction_menu
msgid "Message Reactions"
msgstr "消息反应"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_name
#: model:ir.model.fields,field_description:mail.field_mail_message__record_name
msgid "Message Record Name"
msgstr "消息记录名称"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_translation
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Message Translation"
msgstr "信息翻译"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__google_translate_api_key
msgid "Message Translation API Key"
msgstr "消息翻译 API 密钥"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__name
msgid "Message Type"
msgstr "消息类型"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__description
#: model:ir.model.fields,help:mail.field_mail_message__description
msgid "Message description: either the subject, or the beginning of the body"
msgstr "消息描述：主题或正文的开始部分"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Message posted on \"%s\""
msgstr "在“%s”上发布的消息"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_to
msgid "Message recipients (emails)"
msgstr "消息收件人(EMail)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__references
msgid "Message references, such as identifiers of previous messages"
msgstr "消息引用"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Message should be a valid EmailMessage instance"
msgstr "消息应该具有一个电子邮件消息实例"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__name
msgid ""
"Message subtype gives a more precise type on the message, especially for "
"system notifications. For example, it can be a notification related to a new"
" record (New), or to a stage change in a process (Stage change). Message "
"subtypes allow to precisely tune the notifications the user want to receive "
"on its wall."
msgstr ""
"消息子类型在消息上提供了更精确的类型，尤其是对于系统通知。例如，它可以是与新记录（新）相关的通知，或者是过程（阶段更改）中的阶段改变。消息子类型允许精确调整用户希望在其壁上接收到的通知。"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_subtype
msgid "Message subtypes"
msgstr "消息子类型"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__subtype_ids
msgid ""
"Message subtypes followed, meaning subtypes that will be pushed onto the "
"user's Wall."
msgstr "消息子类型跟随，意味着将被推到用户墙上的子类型。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr "消息类型：电邮用于电邮消息，通知用于系统消息，评论用于其他消息，如用户回复"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_id
#: model:ir.model.fields,help:mail.field_mail_message__message_id
msgid "Message unique identifier"
msgstr "消息唯一编号"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message__message_id
msgid "Message-Id"
msgstr "消息ID"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#: model:ir.actions.act_window,name:mail.act_server_history
#: model:ir.actions.act_window,name:mail.action_view_mail_message
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_ids
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_ids
#: model:ir.ui.menu,name:mail.menu_mail_message
#: model_terms:ir.ui.view,arch_db:mail.view_message_tree
#, python-format
msgid "Messages"
msgstr "消息"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Messages Search"
msgstr "消息搜索"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "Messages marked as read will appear in the history."
msgstr "标记为已读的消息将出现在历史记录中。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__internal
msgid ""
"Messages with internal subtypes will be visible only by employees, aka "
"members of base_user group"
msgstr "内部消息只有员工可见，即基础用户组成员"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Messages with tracking values cannot be modified"
msgstr "无法修改具有追踪值的消息"

#. module: mail
#: model:ir.model,name:mail.model_discuss_voice_metadata
msgid "Metadata for voice attachments"
msgstr "语音附件的元数据"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_missing
msgid "Missing email"
msgstr "缺少电子邮件"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_missing
msgid "Missing email address"
msgstr "缺少电子邮件地址"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_from_missing
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_from_missing
msgid "Missing from address"
msgstr "地址丢失"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_duration_mixin
msgid ""
"Mixin to compute the time a record has spent in each value a many2one field "
"can take"
msgstr "用于计算记录在 many2one 字段的每个值上所花费时间的混合函数"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__res_model
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Model"
msgstr "模型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_change
msgid "Model has change"
msgstr "模型已更改"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_model
msgid "Model of the followed resource"
msgstr "关注资源的模型"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__res_model
msgid ""
"Model the subtype applies to. If False, this subtype applies to all models."
msgstr "子类型适用的模型：如果错误，子类型适用于所有模型."

#. module: mail
#: model:ir.model,name:mail.model_ir_model
msgid "Models"
msgstr "模型"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid ""
"Modifying the model can have an impact on existing activities using this "
"activity type, be careful."
msgstr "注意，这个活动类型修改模型会影响现有活动."

#. module: mail
#: model:ir.model,name:mail.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "模块卸载"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__months
msgid "Months"
msgstr "个月"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "More"
msgstr "更多"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Mute"
msgstr "静音"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
#, python-format
msgid "Mute Channel"
msgstr "静音频道"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__mute_until_dt
msgid "Mute notifications until"
msgstr "静音通知，直到"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活动截止时间"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "My Templates"
msgstr "我的模板"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__name
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__name
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__name
#: model:ir.model.fields,field_description:mail.field_mail_followers__name
#: model:ir.model.fields,field_description:mail.field_mail_guest__name
#: model:ir.model.fields,field_description:mail.field_mail_template__name
#: model:ir.model.fields,field_description:mail.field_res_partner__name
#: model:ir.model.fields,field_description:mail.field_res_users__name
msgid "Name"
msgstr "名称"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__needaction
#: model:ir.model.fields,field_description:mail.field_mail_message__needaction
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Need Action"
msgstr "需要行动"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
#, python-format
msgid "New Channel"
msgstr "新频道"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
#, python-format
msgid "New Message"
msgstr "新消息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_char
msgid "New Value Char"
msgstr "新字符值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_datetime
msgid "New Value Datetime"
msgstr "新日期时间值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_float
msgid "New Value Float"
msgstr "新浮点值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_integer
msgid "New Value Integer"
msgstr "新整数值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_text
msgid "New Value Text"
msgstr "新文本值"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window_model.js:0
#: code:addons/mail/static/src/core/common/out_of_focus_service.js:0
#, python-format
msgid "New message"
msgstr "新建消息"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "New messages"
msgstr "新建消息"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "New messages appear here."
msgstr "新消息出现在这里."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "New values"
msgstr "新值"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Next Activities"
msgstr "下一步活动"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Next Activity"
msgstr "下一个活动"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活动截止日期"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_users__activity_summary
msgid "Next Activity Summary"
msgstr "下一活动摘要"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_id
msgid "Next Activity Type"
msgstr "下一活动类型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__has_recommended_activities
msgid "Next activities available"
msgstr "下一活动可用"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
#, python-format
msgid "No"
msgstr "否"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "No Error"
msgstr "没有错误"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#, python-format
msgid "No Followers"
msgstr "无关注者"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "No IM status available"
msgstr "没有可用的 IM 状态"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__no_record
msgid "No Record"
msgstr "没有记录"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_without_access_action
msgid "No activities."
msgstr "没有活动。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "No channel found"
msgstr "未找到相应的频道"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.xml:0
#, python-format
msgid "No conversation selected."
msgstr "未选择对话。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "No conversation yet..."
msgstr "暂无对话..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "No history messages"
msgstr "没有历史讯息"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_resend_message.py:0
#, python-format
msgid "No message_id found in context"
msgstr "上下文中没有找到message_id"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.js:0
#, python-format
msgid "No messages found"
msgstr "找不到消息"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.js:0
#, python-format
msgid "No recipient"
msgstr "无收件人"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "No recipient found."
msgstr "没有找到收件人。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid ""
"No response received. Check server information.\n"
" %s"
msgstr ""
"没有收到回应。 检查服务器信息。\n"
" %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
#, python-format
msgid ""
"No responsible specified for %(activity_type_name)s: %(activity_summary)s."
msgstr "未指定 %(activity_type_name)s 的负责人：%(activity_summary)s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
#, python-format
msgid "No results found"
msgstr "未找到结果"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "No starred messages"
msgstr "没有已加星标的消息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to_force_new
msgid "No threading for answers"
msgstr "无响应"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "No user found"
msgstr "未找到相应的用户"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid "No user found that is not already a member of this channel."
msgstr "未找到尚未成为此频道成员的用户。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "No users found"
msgstr "未找到相应的用户"

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/mail.py:0
#, python-format
msgid "Non existing record or wrong token."
msgstr "记录不存在或令牌错误。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__default
#, python-format
msgid "None"
msgstr "无"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__email_normalized
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_partner__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_users__email_normalized
msgid "Normalized Email"
msgstr "规范化邮件"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__state__draft
msgid "Not Confirmed"
msgstr "未确认"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__not_tested
msgid "Not Tested"
msgstr "未测试"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_note
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_note
#: model:ir.model.fields,field_description:mail.field_mail_activity__note
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__note
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__note
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__note
#: model:mail.message.subtype,name:mail.mt_note
#, python-format
msgid "Note"
msgstr "注释"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__no_notif
#, python-format
msgid "Nothing"
msgstr "没有"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__notification_id
#: model:ir.model.fields,field_description:mail.field_res_users__notification_type
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Notification"
msgstr "通知"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_notification
msgid "Notification Email"
msgstr "通知电子邮件"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/notification_item.xml:0
#, python-format
msgid "Notification Item Image"
msgstr "通知项目图像"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__notification_parameters
msgid "Notification Parameter"
msgstr "通知参数"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Notification Settings"
msgstr "通知设置"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "通知类型"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Notification should receive attachments as a list of list or tuples "
"(received %(aids)s)"
msgstr "通知应收到串列（list）或元组（tuple）清单形式的附件（已收到%(aids)s）"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Notification should receive attachments records as a list of IDs (received "
"%(aids)s)"
msgstr "通知应收到 ID 清单形式的附件记录（已收到%(aids)s）"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Notification should receive partners given as a list of IDs (received "
"%(pids)s)"
msgstr "通知应收到以 ID 清单形式提供的合作伙伴名单（已收到%(pids)s）"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_delete_notification_ir_actions_server
msgid "Notification: Delete Notifications older than 6 Month"
msgstr "通知：删除超过6个月的通知"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_send_scheduled_message_ir_actions_server
msgid "Notification: Send scheduled message notifications"
msgstr "通知：发送计划消息通知"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_notification_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__notification_ids
#: model:ir.ui.menu,name:mail.mail_notification_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_tree
msgid "Notifications"
msgstr "通知"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
#, python-format
msgid "Notifications allowed"
msgstr "允许通知"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
#, python-format
msgid "Notifications blocked"
msgstr "消息阻塞"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction_counter
msgid "Number of Actions"
msgstr "操作数量"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_count
msgid ""
"Number of days/week/month before executing the action. It allows to plan the"
" action deadline."
msgstr "在执行该动作前几天/周/月，允许操的截止日期。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error_counter
msgid "Number of errors"
msgstr "错误数量"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_users__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "需要采取行动的消息数量"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_users__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "发送错误的消息的数量"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Odoo"
msgstr "Odoo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
#, python-format
msgid "Odoo will not send notifications on this device."
msgstr "Odoo 不会在此设备上发送通知。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
#, python-format
msgid "Odoo will send notifications on this device!"
msgstr "Odoo 将在此设备上发送通知！"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__off
msgid "Off"
msgstr "关闭"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "Offline"
msgstr "离线"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "Offline -"
msgstr "离线 -"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_char
msgid "Old Value Char"
msgstr "旧字符值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_datetime
msgid "Old Value DateTime"
msgstr "旧日期时间值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_float
msgid "Old Value Float"
msgstr "旧浮点值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_integer
msgid "Old Value Integer"
msgstr "旧整数值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_text
msgid "Old Value Text"
msgstr "旧文本值"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Old values"
msgstr "旧值"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "Online"
msgstr "线上"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "Online -"
msgstr "在线 -"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Only administrators are allowed to export mail message"
msgstr "只允许管理员导出邮件消息"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Only custom models can be modified."
msgstr "仅有自定义模型能被修改."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_notification_type
msgid "Only internal user can receive notifications in Odoo"
msgstr "只有内部用户才能在Odoo中接收通知"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Only messages type comment can have their content updated"
msgstr "只有消息类型的备注才能更新其内容"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"Only messages type comment can have their content updated on model "
"'discuss.channel'"
msgstr "只有消息类型的备注才能在模型“discuss.channel”上更新其内容"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Only users belonging to the \"%(group_name)s\" group can modify dynamic "
"templates."
msgstr "仅限“%(group_name)s”群组用户修改动态模板。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__open
#, python-format
msgid "Open"
msgstr "打开"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.js:0
#, python-format
msgid "Open Actions Menu"
msgstr "打开操作菜单"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Open Document"
msgstr "打开单据"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
#, python-format
msgid "Open Form View"
msgstr "打开表单视图"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#, python-format
msgid "Open Link"
msgstr "打开链接"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Open Owner"
msgstr "打开所有者"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
#, python-format
msgid "Open card"
msgstr "开卡"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/thread_actions.js:0
#, python-format
msgid "Open in Discuss"
msgstr "在讨论打开"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Operation not supported"
msgstr "不支持的操作"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_optout
msgid "Opted Out"
msgstr "选择退出"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr "无论是否回复，所有的接收的邮件都将附上一条线索（记录）选配的ID。如果设置了，这个将完全阻止新记录的创建。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_notification__mail_mail_id
msgid "Optional mail_mail ID. Used mainly to optimize searches."
msgstr "mail_mail ID可选。主要用于优化搜索."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__mail_server_id
msgid ""
"Optional preferred server for outgoing mails. If not set, the highest "
"priority one will be used."
msgstr "可选的推荐发信邮件服务器。如果没有设置，优先级最高的一个会被选中。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__lang
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""
"发送邮件时可选择的翻译代码（ISO 代码）。如果未设置，将使用英文版本。一般使用占位符确定适当语言，例如{{ "
"object.partner_id.lang }}。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_mode
msgid ""
"Original Discussion: Answers go in the original document discussion thread. \n"
" Another Email Address: Answers go to the email address mentioned in the tracking message-id instead of original document discussion thread. \n"
" This has an impact on the generated message-id."
msgstr ""
"原始讨论：答案在原始单据讨论线程中。\n"
" 另一个电子邮箱地址：答案发送至追踪消息ID中提到的电子邮箱地址，而非原始单据讨论线程。\n"
"将影响生成的消息ID。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
#, python-format
msgid "Original message was deleted"
msgstr "原邮件已删除"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#: model:ir.actions.act_window,name:mail.mail_activity_without_access_action
#, python-format
msgid "Other activities"
msgstr "其他活动"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing"
msgstr "发信"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email_outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing Email"
msgstr "外发电子邮件"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Outgoing Email Servers"
msgstr "发信服务器"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__mail_server_id
msgid "Outgoing Mail Server"
msgstr "发信邮件服务器"

#. module: mail
#: model:ir.model,name:mail.model_mail_mail
msgid "Outgoing Mails"
msgstr "发出邮件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_server_id
msgid "Outgoing mail server"
msgstr "发送邮件服务器"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_state__overdue
#, python-format
msgid "Overdue"
msgstr "逾期"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Override author's email"
msgstr "覆盖作者EMail"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "POP"
msgstr "POP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__pop
msgid "POP Server"
msgstr "POP 服务器"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_tree
msgid "POP/IMAP Servers"
msgstr "POP/IMAP 服务器"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Packets received:"
msgstr "已收到数据包："

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Packets sent:"
msgstr "已发送数据包："

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__parent_id
msgid "Parent"
msgstr "上级"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_message__parent_id
msgid "Parent Message"
msgstr "上级消息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_model_id
msgid "Parent Model"
msgstr "上级模型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "上级记录ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"父级模型拥有别名。拥有别名参考的模型不一定是alias_model_id给出的模型 (例如：project(parent_model) 和任务 "
"(模型))"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__parent_id
msgid ""
"Parent subtype, used for automatic subscription. This field is not correctly"
" named. For example on a project, the parent_id of project subtypes refers "
"to task-related subtypes."
msgstr "父类,用于自动订阅.这个字段没有正确的命名。例如在一个项目上，项目子类型的上一级ID指任务相关的子类型。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__partner_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__partner_id
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__partner_ids
#: model:ir.model.fields,field_description:mail.field_ir_cron__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__partner_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "Partner"
msgstr "合作伙伴"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid "Partner Profile"
msgstr "业务伙伴资料"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_readonly
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_readonly
msgid "Partner Readonly"
msgstr "业务合作伙伴只读"

#. module: mail
#: model:ir.model,name:mail.model_mail_partner_device
msgid "Partner Web Push Device"
msgstr "合作伙伴网络推送设备"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_partner
msgid "Partner with additional information for mail resend"
msgstr "为邮件重新发送提供附加信息的合作伙伴"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_partner_ids
msgid "Partners"
msgstr "合作伙伴"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notified_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notified_partner_ids
msgid "Partners with Need Action"
msgstr "需要行动的业务伙伴"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__password
msgid "Password"
msgstr "密码"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Paste your API key"
msgstr "粘贴您的 API 密钥"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
#, python-format
msgid "Pause"
msgstr "暂停"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__payload
msgid "Payload"
msgstr "负载"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Permanently delete this template"
msgstr "永久删除此模板"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__phone
#: model:ir.model.fields,field_description:mail.field_res_users__phone
msgid "Phone"
msgstr "电话"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__phonecall
msgid "Phonecall"
msgstr "电话"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_patch.js:0
#, python-format
msgid "Pin"
msgstr "置顶"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid "Pin It"
msgstr "置顶它"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__pinned_at
#: model:ir.model.fields,field_description:mail.field_mail_message__pinned_at
msgid "Pinned"
msgstr "已置顶"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
#: code:addons/mail/static/src/discuss/message_pin/common/thread_actions.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__pinned_message_ids
#, python-format
msgid "Pinned Messages"
msgstr "置顶消息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__plan_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
msgid "Plan"
msgstr "计划"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_available_ids
msgid "Plan Available"
msgstr "可用计划"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_date_deadline
msgid "Plan Date"
msgstr "计划日期"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Plan Name"
msgstr "计划名称"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__assignation_summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_assignation_summary
msgid "Plan Summary"
msgstr "计划概要"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Plan summary"
msgstr "计划概要"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_state__planned
#, python-format
msgid "Planned"
msgstr "已安排"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Planned Activities"
msgstr "已计划活动"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Planned in"
msgstr "计划于"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_tree
msgid "Planning"
msgstr "计划"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
#, python-format
msgid "Play"
msgstr "播放"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient.js:0
#, python-format
msgid "Please complete customer's information"
msgstr "请填写客户信息"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Please contact us instead using"
msgstr "不要使用，请联系我们"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Please wait while the file is uploading."
msgstr "文件上传中，请稍等。"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__notification_type
msgid ""
"Policy on how to handle Chatter notifications:\n"
"- Handle by Emails: notifications are sent to your email address\n"
"- Handle in Odoo: notifications appear in your Odoo Inbox"
msgstr ""
"处理聊天通知的规则政策:\n"
"- 通过EMail处理: 通知会发送至您的邮件地址\n"
"- 系统内部处理: 通知会出现在 ERP 收件箱"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"使用邮件网关在文档中发布消息的政策：\n"
"- 所有人：任何人都可以发布\n"
"- 合作伙伴：仅限认证通过的合作伙伴\n"
"- 关注者：仅限关联文档或关注频道的成员\n"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__port
msgid "Port"
msgstr "端口"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Portal Access Granted"
msgstr "门户访问已授予"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Portal Access Revoked"
msgstr "门户访问已撤销"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__comment
msgid "Post on a document"
msgstr "提交在文件上"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message should be done on a business document. Use message_notify "
"to send a notification to an user."
msgstr "应在业务文档上发布消息。使用 message_notify 向用户发送通知。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message should receive attachments as a list of list or tuples "
"(received %(aids)s)"
msgstr "发布一则消息应收到串列（list）或元组（tuple）清单形式的附件（已收到%(aids)s）"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message should receive attachments records as a list of IDs "
"(received %(aids)s)"
msgstr "发布一则消息应收到 ID 清单形式的附件记录（已收到%(aids)s）"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message should receive partners as a list of IDs (received "
"%(pids)s)"
msgstr "发布一则消息应收到 ID 清单形式的合作伙伴名单（已收到%(pids)s）"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Powered by"
msgstr "技术提供"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__previous_type_ids
msgid "Preceding Activities"
msgstr "前置活动"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__reply_to
msgid "Preferred response address"
msgstr "首选回复地址"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
#, python-format
msgid "Press Enter to start"
msgstr "按输入键开始"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Press a key to register it as the push-to-talk shortcut."
msgstr "按键可将其注册为 \"一键通话\" 快捷方式。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__preview
#: model:ir.model.fields,field_description:mail.field_mail_message__preview
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#, python-format
msgid "Preview"
msgstr "预览"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Preview of"
msgstr "预览"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__previous_activity_type_id
msgid "Previous Activity Type"
msgstr "前一活动类型"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Privacy"
msgstr "隐私"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__object_id
msgid ""
"Process each incoming mail as part of a conversation corresponding to this "
"document type. This will create new documents for new conversations, or "
"attach follow-up emails to the existing conversations (documents)."
msgstr "对于这种类型的文档，把收到的邮件作为会话的一部分。也就是说，为新会话创建新的文档，或把接下来的邮件附加到已有的会话中。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__process
#, python-format
msgid "Processing"
msgstr "处理中"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#, python-format
msgid "Public Channel"
msgstr "公开频道"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.js:0
#, python-format
msgid "Public Channels"
msgstr "公众频道"

#. module: mail
#: model:ir.model,name:mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr "出版商保修合同"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_module_update_notification_ir_actions_server
msgid "Publisher: Update Notification"
msgstr "发布者: 更新通知"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__push_to_talk_key
msgid "Push-To-Talk shortcut"
msgstr "一键通话快捷方式"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Push-to-talk key"
msgstr "一键通话按键"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "Quick search..."
msgstr "快速搜索..."

#. module: mail
#: model:ir.model,name:mail.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_tree
msgid "RTC Session"
msgstr "RTC会话"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "RTC Session ID:"
msgstr "RTC 会话 ID："

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__rtc_session_ids
msgid "RTC Sessions"
msgstr "RTC 会话"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_rtc_session_action
#: model:ir.ui.menu,name:mail.discuss_channel_rtc_session_menu
msgid "RTC sessions"
msgstr "RTC 会话"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Raise Hand"
msgstr "举手"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings_volumes__volume
msgid ""
"Ranges between 0.0 and 1.0, scale depends on the browser implementation"
msgstr "范围在 0.0 和 1.0 之间，比例取决于浏览器实现"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__guest_id
msgid "Reacting Guest"
msgstr "反应的游客"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__partner_id
msgid "Reacting Partner"
msgstr "反应的伙伴"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reaction_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__reaction_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_tree
msgid "Reactions"
msgstr "反应"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__read_date
msgid "Read Date"
msgstr "读取日期"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#, python-format
msgid "Ready"
msgstr "就绪"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__ready
msgid "Ready to Send"
msgstr "准备好发送"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__reason
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Reason"
msgstr "原因"

#. module: mail
#: model:res.groups,name:mail.group_mail_notification_type_inbox
msgid "Receive notifications in Odoo"
msgstr "在 Odoo 系统中接收通知"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__received
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Received"
msgstr "已收"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/command_category.js:0
#, python-format
msgid "Recent"
msgstr "最近"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__res_partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Recipient"
msgstr "收件人"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__name
msgid "Recipient Name"
msgstr "收件人名称"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__partner_ids
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Recipients"
msgstr "收款人"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Recommended Activities"
msgstr "推荐的活动"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__recommended_activity_type_id
msgid "Recommended Activity Type"
msgstr "推荐的活动类型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__resource_ref
msgid "Record"
msgstr "记录"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_name
msgid "Record Name"
msgstr "记录名称"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_force_thread_id
msgid "Record Thread ID"
msgstr "记录线索ID"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Records:"
msgstr "记录:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__references
msgid "References"
msgstr "参考"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#, python-format
msgid "Refuse"
msgstr "拒绝"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Regards,"
msgstr "祝愿,"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Register new key"
msgstr "注册新密钥"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Reject"
msgstr "拒绝"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__parent_id
#: model:ir.model.fields,field_description:mail.field_res_users__parent_id
msgid "Related Company"
msgstr "关联公司"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__res_id
#: model:ir.model.fields,field_description:mail.field_mail_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_id
msgid "Related Document ID"
msgstr "相关单据ID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_ids
msgid "Related Document IDs"
msgstr "相关文档 ID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model
#: model:ir.model.fields,field_description:mail.field_mail_mail__model
#: model:ir.model.fields,field_description:mail.field_mail_message__model
#: model:ir.model.fields,field_description:mail.field_mail_template__model
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_model
msgid "Related Document Model"
msgstr "相关单据模型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_model
msgid "Related Document Model Name"
msgstr "相关的单据模型名称"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__mail_template_id
msgid "Related Mail Template"
msgstr "相关的邮件模板"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Related Message"
msgstr "相关的消息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__partner_id
msgid "Related Partner"
msgstr "相关的业务伙伴"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__relation_field
msgid "Relation field"
msgstr "关联字段"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/link_preview.xml:0
#: code:addons/mail/static/src/core/common/message_reaction_menu.xml:0
#, python-format
msgid "Remove"
msgstr "移除"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove Context Action"
msgstr "移除上下文操作"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__remove_followers
msgid "Remove Followers"
msgstr "删除跟踪者"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist_remove
msgid "Remove email from blacklist wizard"
msgstr "从黑名单向导中删除电子邮件"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove the contextual action to use this template on related documents"
msgstr "移除上下文动作，使用相关单据的模板"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#, python-format
msgid "Remove this follower"
msgstr "移除此关注者"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_info
msgid "Removed field information"
msgstr "删除字段信息"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#, python-format
msgid "Rename"
msgstr "重命名"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__render_model
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_template__render_model
msgid "Rendering Model"
msgstr "渲染模型"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_composer_mixin.py:0
#, python-format
msgid ""
"Rendering of %(field_name)s is not possible as no counterpart on template."
msgstr "由于模板上无对应，因此无法呈现%(field_name)s。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_composer_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Rendering of %(field_name)s is not possible as not defined on template."
msgstr "由于模板中未定义，因此无法呈现%(field_name)s。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
#, python-format
msgid "Repeat"
msgstr "重复"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_mode
msgid "Replies"
msgstr "回复"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Reply"
msgstr "回复"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template__reply_to
msgid "Reply To"
msgstr "回复"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to
#: model:ir.model.fields,help:mail.field_mail_message__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr "回复电邮地址。设置reply_to，以绕过自动创建线程。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__reply_to
msgid "Reply-To"
msgstr "回复 至"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Reply-to Address"
msgstr "回复至地址"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#, python-format
msgid "Replying to"
msgstr "回复"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Report"
msgstr "报告"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__request_partner_id
msgid "Requesting Partner"
msgstr "请求伙伴"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "Resend"
msgstr "重发"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_partner_action
msgid "Resend Email"
msgstr "重发邮件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend_wizard_id
msgid "Resend wizard"
msgstr "重发向导"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Reset Confirmation"
msgstr "重置确认"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_reset_action
msgid "Reset Mail Template"
msgstr "重置邮件模板"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
msgid "Reset Template"
msgstr "重置模板"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "Resetting Your Password"
msgstr "重置密码"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_domain_user_id
msgid "Responsible"
msgstr "负责人"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_user_id
msgid "Responsible User"
msgstr "责任用户"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__restrict_template_rendering
msgid "Restrict Template Rendering"
msgstr "限制模板渲染"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Restrict mail templates edition and QWEB placeholders usage."
msgstr "限制邮件模板版本和 QWEB 占位符的使用。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__restricted_attachment_count
msgid "Restricted attachments"
msgstr "受限制的附件"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__source_lang
msgid "Result of the language detection based on its content."
msgstr "基于语言内容的语言检测结果。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Retry"
msgstr "重试"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Revert"
msgstr "还原"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Review All Templates"
msgstr "审查所有模板"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_content
msgid "Rich-text Contents"
msgstr "富文本内容"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__body_html
msgid "Rich-text/HTML message"
msgstr "富文本/HTML消息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__rtc_inviting_session_id
msgid "Ringing session"
msgstr "振铃会话"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__rtc_session_ids
msgid "Rtc Session"
msgstr "Rtc 会话"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__sfu_server_url
msgid "SFU Server URL"
msgstr "SFU 服务器 URL"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__sfu_server_key
msgid "SFU Server key"
msgstr "SFU 服务器密钥"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "SFU server"
msgstr "SFU 服务器"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "SMTP Server"
msgstr "SMTP 服务器"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "SSL"
msgstr "SSL"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__is_ssl
msgid "SSL/TLS"
msgstr "SSL/TLS"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__user_id
#: model:ir.model.fields,field_description:mail.field_res_users__user_id
msgid "Salesperson"
msgstr "销售员"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
msgid "Save"
msgstr "保存"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save Template"
msgstr "保存模板"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as a new template"
msgstr "保存为新模版"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Save editing"
msgstr "保存编辑"

#. module: mail
#: model:ir.model,name:mail.model_discuss_gif_favorite
msgid "Save favorite GIF from Tenor API"
msgstr "从 Tenor API 保存收藏的 GIF"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_count
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Schedule"
msgstr "安排"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Schedule & Mark as Done"
msgstr "计划和标记为已完成"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/web/activity_service.js:0
#: code:addons/mail/static/src/core/web/activity_service.js:0
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "Schedule Activity"
msgstr "安排活动"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/web/activity_service.js:0
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "Schedule Activity On Selected Records"
msgstr "选定记录的计划活动"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#, python-format
msgid "Schedule activities to help you get things done."
msgstr "安排活动以帮助您完成工作。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
#, python-format
msgid "Schedule activity"
msgstr "安排活动"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "Schedule an Activity"
msgstr "安排活动"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#, python-format
msgid "Schedule an activity"
msgstr "安排活动"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#, python-format
msgid "Schedule an activity on selected records"
msgstr "为选定记录安排活动"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__scheduled_date
msgid "Scheduled Date"
msgstr "安排的日期"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_form
msgid "Scheduled Message"
msgstr "安排的消息"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_message_schedule_action
#: model:ir.model,name:mail.model_mail_message_schedule
#: model:ir.ui.menu,name:mail.mail_message_schedule_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_search
msgid "Scheduled Messages"
msgstr "安排的消息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__scheduled_datetime
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Scheduled Send Date"
msgstr "安排的发送日期"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__script
msgid "Script"
msgstr "脚本"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "Search"
msgstr "搜索"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Search Alias"
msgstr "搜索别名"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_search
msgid "Search Groups"
msgstr "搜索群组"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Search Incoming Mail Servers"
msgstr "搜索收件服务器"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Search Messages"
msgstr "搜索消息"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_search
msgid "Search RTC session"
msgstr "搜索 RTC 会话"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "Search button"
msgstr "搜索按钮"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "Search for a GIF"
msgstr "搜索 GIF"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "Search for a channel..."
msgstr "搜索频道……"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "Search for a user..."
msgstr "搜索用户……"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "Search in progress"
msgstr "搜索中"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.js:0
#, python-format
msgid "Search messages"
msgstr "搜索消息"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/core/web/mention_list.xml:0
#: code:addons/mail/static/src/core/web/mention_list.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "Search..."
msgstr "搜索..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_controller.js:0
#, python-format
msgid "Search: %s"
msgstr "搜索: %s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Security Update: Email Changed"
msgstr "安全更新：电子邮件已更改"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Security Update: Login Changed"
msgstr "安全更新：登录信息已更改"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Security Update: Password Changed"
msgstr "安全更新：密码已更改"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "See Error Details"
msgstr "查看错误详情"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "See all pinned messages."
msgstr "查看所有置顶消息。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Select a language"
msgstr "选择语言"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "Select a user..."
msgstr "选择用户…"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Select the content filter used for filtering GIFs"
msgstr "选择用于筛选 GIF 的内容筛选器"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid "Selected users:"
msgstr "选择用户:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/common/composer.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Send"
msgstr "发送"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Send & close"
msgstr "发送和关闭"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__mail_post
msgid "Send Email"
msgstr "发送电邮"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__mail_post_method
#: model:ir.model.fields,field_description:mail.field_ir_cron__mail_post_method
msgid "Send Email As"
msgstr "发送电子邮件"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Send Mail (%s)"
msgstr "发送邮件（ %s）"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__notify
msgid "Send Notification"
msgstr "发送通知"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
#, python-format
msgid "Send Now"
msgstr "立即发送"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/composer_patch.js:0
#, python-format
msgid "Send a message to followers…"
msgstr "向跟踪者发送信息..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Send and receive emails through your Gmail account."
msgstr "用您的Gmail账户发送和接收电子邮件。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Send and receive emails through your Outlook account."
msgstr "用您的Outlook账户发送和接收电子邮件。"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_partner_mass_mail
msgid "Send email"
msgstr "发送邮件"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__force_send
msgid "Send mailing or notifications directly"
msgstr "直接发送邮件或通知"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "Send message"
msgstr "发送消息"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_from
msgid "Sender address"
msgstr "发件人地址"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_from
msgid ""
"Sender address (placeholders may be used here). If not set, the default "
"value will be the author's email alias if configured, or email address."
msgstr "发送人地址（所在位置会被在这里使用），如果不设置，作者的邮箱别名或邮箱地址就会被作为默认值。"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_message_action
msgid "Sending Failures"
msgstr "发送失败"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__sent
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__pending
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#, python-format
msgid "Sent"
msgstr "已发送"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__sequence
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__sequence
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__sequence
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__sequence
msgid "Sequence"
msgstr "序列"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Server & Login"
msgstr "服务器和登录"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_server
msgid "Server Action"
msgstr "服务器动作"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Server Information"
msgstr "服务器信息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server
msgid "Server Name"
msgstr "服务器名称"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__priority
msgid "Server Priority"
msgstr "服务器优先级"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server_type
msgid "Server Type"
msgstr "服务器类型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server_type_info
msgid "Server Type Info"
msgstr "服务器类型信息"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_upload_service.js:0
#, python-format
msgid "Server error"
msgstr "服务器错误"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid ""
"Server replied with following exception:\n"
" %s"
msgstr ""
"服务器已回复，但以下情况除外：\n"
" %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Server type IMAP."
msgstr "服务器类型 IMAP。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Server type POP."
msgstr "服务器类型 POP。"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__active
msgid "Set active to false to hide the channel without removing it."
msgstr "将 启用 设置为 否 以隐藏频道而不删除它。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Settings"
msgstr "设置"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sfu_channel_uuid
msgid "Sfu Channel Uuid"
msgstr "Sfu 频道 Uuid"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sfu_server_url
msgid "Sfu Server Url"
msgstr "Sfu 服务器网址"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Share Screen"
msgstr "共享屏幕"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__description
#: model:ir.model.fields,field_description:mail.field_mail_message__description
msgid "Short description"
msgstr "简述"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_tree
msgid "Shortcodes"
msgstr "短码"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__source
msgid "Shortcut"
msgstr "快捷"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__source
msgid ""
"Shortcut that will automatically be substituted with longer content in your "
"messages. Type ':' followed by the name of your shortcut (e.g. :hello) to "
"use in your messages."
msgstr "快捷方式将自动替换为邮件中的较长内容。键入“：”，后跟要在消息中使用的快捷方式的名称（例如 ：hello）。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__target_lang
msgid ""
"Shortened language code used as the target for the translation request."
msgstr "作为翻译请求目标语言代码的缩写。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Show Attachments"
msgstr "显示附件"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
#, python-format
msgid "Show Call Settings"
msgstr "显示呼叫设置"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.js:0
#, python-format
msgid "Show Followers"
msgstr "显示关注者"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Show Member List"
msgstr "显示成员列表"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
#, python-format
msgid "Show a helper message"
msgstr "显示帮助消息"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_button.js:0
#, python-format
msgid "Show activities"
msgstr "显示活动"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Show all recipients"
msgstr "显示全部收件人"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Show all records which has next action date is before today"
msgstr "显示下一操作日期早于今天的所有记录"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient_list.xml:0
#, python-format
msgid "Show less"
msgstr "折叠"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient_list.xml:0
#, python-format
msgid "Show more"
msgstr "展开"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.xml:0
#, python-format
msgid "Show sidebar"
msgstr "显示侧边栏"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Show video participants only"
msgstr "只显示视频参与者"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid "Showing"
msgstr "显示"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__ref_ir_act_window
msgid "Sidebar action"
msgstr "边栏动作"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__ref_ir_act_window
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr "用于在相关单据上调用此模版的边栏按钮"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_site_name
msgid "Site name"
msgstr "网站名称"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "So uhh... maybe go favorite some GIFs?"
msgstr "所以，呃......或许可以去收藏一些 GIF？"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
msgid "Source"
msgstr "来源"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__source_lang
msgid "Source Language"
msgstr "语言"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__specific
msgid "Specific User"
msgstr "特定用户"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_plan__res_model
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__res_model
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model
msgid ""
"Specify a model if the activity should be specific to a model and not "
"available when managing activities for other models."
msgstr "如果活动应特定于某个模型并且在管理其他模型的活动时不可用，则指定一个模型。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/messaging_service.js:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred
#: model:ir.model.fields,field_description:mail.field_mail_message__starred
#, python-format
msgid "Starred"
msgstr "星标消息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__starred_message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__starred_message_ids
msgid "Starred Message"
msgstr "星标消息"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
#, python-format
msgid "Start a Call"
msgstr "开启语音会议"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Start a Conversation"
msgstr "开始对话"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss_app_model.js:0
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
#, python-format
msgid "Start a conversation"
msgstr "开启新会议"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/web/discuss_sidebar_start_meeting.xml:0
#, python-format
msgid "Start a meeting"
msgstr "开始会议"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__state
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
msgid "State"
msgstr "状态"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__state
#: model:ir.model.fields,field_description:mail.field_mail_mail__state
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_status
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Status"
msgstr "状态"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,help:mail.field_res_partner__activity_state
#: model:ir.model.fields,help:mail.field_res_users__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"基于活动的状态\n"
"逾期：超出到期日期\n"
"今天：活动日期是今天\n"
"计划：未来活动。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_duration_mixin__duration_tracking
msgid "Status time"
msgstr "状态时间"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/fields/statusbar_duration/statusbar_duration_field.js:0
#, python-format
msgid "Status with time"
msgstr "随时间变化的状态"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__steps_count
msgid "Steps Count"
msgstr "步数计数"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Stop Adding Users"
msgstr "停止添加用户"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.xml:0
#, python-format
msgid "Stop Recording"
msgstr "停止录制"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Stop Sharing Screen"
msgstr "停止共享屏幕"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Stop camera"
msgstr "关闭相机"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#, python-format
msgid "Stop replying"
msgstr "停止回复"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__update
msgid "Store email and replies in the chatter of each record"
msgstr "将电子邮件和回复存储在每条记录的聊天中"

#. module: mail
#: model:ir.model,name:mail.model_mail_link_preview
msgid "Store link preview data"
msgstr "存储链接预览数据"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__push_to_talk_key
msgid ""
"String formatted to represent a key with modifiers following this pattern: "
"shift.ctrl.alt.key, e.g: truthy.1.true.b"
msgstr "格式化字符串以表示具有以下模式的修饰符的键：shift.ctrl.alt.key，例如：truthy.1.true.b"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__body
msgid "String received from the translation request."
msgstr "从翻译请求中收到的字符串。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__subject
#: model:ir.model.fields,field_description:mail.field_mail_mail__subject
#: model:ir.model.fields,field_description:mail.field_mail_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_template__subject
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__subject
msgid "Subject"
msgstr "主题"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__subject
msgid "Subject (placeholders may be used here)"
msgstr "主题（可以在这里使用占位符）"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#, python-format
msgid "Subject:"
msgstr "主题:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__mail_post_autofollow
#: model:ir.model.fields,field_description:mail.field_ir_cron__mail_post_autofollow
msgid "Subscribe Recipients"
msgstr "订阅收件人"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__substitution
msgid "Substitution"
msgstr "替换"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__subtype_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_message__subtype_id
#: model_terms:ir.ui.view,arch_db:mail.view_message_subtype_tree
msgid "Subtype"
msgstr "子类型"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_message_subtype
#: model:ir.ui.menu,name:mail.menu_message_subtype
msgid "Subtypes"
msgstr "子类型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest"
msgstr "建议"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__suggest
msgid "Suggest Next Activity"
msgstr "建议下一个活动"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest these activities once the current one is marked as done."
msgstr "一旦当前活动被标记为完成，建议这些活动。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__summary
msgid "Summary"
msgstr "摘要"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Summary:"
msgstr "摘要:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__module_google_gmail
msgid "Support Gmail Authentication"
msgstr "支持Gmail身份验证"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__module_microsoft_outlook
msgid "Support Outlook Authentication"
msgstr "支持Outlook身份验证"

#. module: mail
#: model:ir.model,name:mail.model_ir_config_parameter
msgid "System Parameter"
msgstr "系统参数"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__notification
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__notification
#, python-format
msgid "System notification"
msgstr "系统通知"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__target_lang
msgid "Target Language"
msgstr "目标语言"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__model_id
msgid "Targeted model"
msgstr "目标模型"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__vat
#: model:ir.model.fields,field_description:mail.field_res_users__vat
msgid "Tax ID"
msgstr "税项ID"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Technical Settings"
msgstr "技术设置"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__initial_res_model
msgid ""
"Technical field to keep track of the model at the start of editing to "
"support UX related behaviour"
msgstr "技术字段，在编辑开始时跟踪模型，以支持用户体验相关行为"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__template_ids
msgid "Template"
msgstr "模板"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__template_category
msgid "Template Category"
msgstr "模板类别"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__template_fs
#: model:ir.model.fields,field_description:mail.field_template_reset_mixin__template_fs
msgid "Template Filename"
msgstr "模板文件名"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_preview_action
msgid "Template Preview"
msgstr "模板预览"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__lang
msgid "Template Preview Language"
msgstr "模板预览语言"

#. module: mail
#: model:ir.model,name:mail.model_template_reset_mixin
msgid "Template Reset Mixin"
msgstr "模板重置混合"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Template creation from composer requires a valid model."
msgstr "从合成器创建模板需要有效的模型。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__description
msgid "Template description"
msgstr "模板描述"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Template rendering should be called only using on a list of IDs; received "
"%(res_ids)r instead."
msgstr "模板渲染应该仅在 ID 清单上呼叫；但实际接收到了 %(res_ids)r。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Template rendering supports only inline_template, qweb, or qweb_view (view "
"or raw); received %(engine)s instead."
msgstr "模板渲染仅支持 inline_template、qweb或qweb_view（视图或原始视图）；但实际接收到了%(engine)s。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Templates"
msgstr "模板"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_api_key
msgid "Tenor API key"
msgstr "Tenor API 密钥"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF API key"
msgstr "Tenor GIF API 密钥"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF content filter"
msgstr "Tenor GIF 内容过滤器"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF limits"
msgstr "Tenor GIF 限额"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_gif_limit
msgid "Tenor Gif Limit"
msgstr "Tenor Gif 限额"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_content_filter
msgid "Tenor content filter"
msgstr "Tenor 内容过滤器"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Test & Confirm"
msgstr "测试和确认"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Test Record:"
msgstr "测试记录："

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_html
msgid "Text Contents"
msgstr "文本内容"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The"
msgstr "此"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "The 'Due Date In' value can't be negative."
msgstr "'期望到期日'不能是以前的日期。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.js:0
#, python-format
msgid "The Fullscreen mode was denied by the browser"
msgstr "浏览器不支持全屏模式"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__vat
#: model:ir.model.fields,help:mail.field_res_users__vat
msgid ""
"The Tax Identification Number. Values here will be validated based on the "
"country format. You can use '/' to indicate that the partner is not subject "
"to tax."
msgstr "税务识别号。此处的值将根据国家格式进行验证。可以使用“/”表示合作伙伴无需纳税。"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "The activity cannot be launched:"
msgstr "活动无法启动："

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
#, python-format
msgid ""
"The activity type \"%(activity_type_name)s\" is not compatible with the plan"
" \"%(plan_name)s\" because it is limited to the model "
"\"%(activity_type_model)s\"."
msgstr ""
"活动类型\"%(activity_type_name)s\"与计划\"%(plan_name)s\"不兼容，因为它仅限于模型\"%(activity_type_model)s\"。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid ""
"The attachment %s does not exist or you do not have the rights to access it."
msgstr "附件%s不存在或者您没有权限访问它。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid "The attachment %s does not exist."
msgstr "附件%s不存在。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.js:0
#, python-format
msgid "The avatar has been updated!"
msgstr "头像已更新！"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_uuid_unique
msgid "The channel UUID must be unique"
msgstr "频道 UUID 必须是唯一的"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_channel_type_not_null
msgid "The channel type cannot be empty"
msgstr "频道类型不能为空"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__can_write
msgid "The current user can edit the template."
msgstr "当前用户可以编辑模板。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
#, python-format
msgid "The duration of voice messages is limited to 1 minute."
msgstr "语音信息的时长限制为 1 分钟。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The email sent to"
msgstr "EMail发到"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_partner_device_endpoint_unique
msgid "The endpoint must be unique !"
msgstr "端点必须是唯一的！"

#. module: mail
#. odoo-python
#: code:addons/mail/models/template_reset_mixin.py:0
#, python-format
msgid ""
"The following email templates could not be reset because their related source files could not be found:\n"
"- %s"
msgstr ""
"因为找不到与其相关的源文件，下面的电子邮件模板不能被重置：\n"
"- %s"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__user_id
#: model:ir.model.fields,help:mail.field_res_users__user_id
msgid "The internal user in charge of this contact."
msgstr "负责此联系人的内部用户。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "The last message received on this alias has caused an error."
msgstr "该邮件别名最后收到的信息导致错误。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "The message below could not be accepted by the address"
msgstr "下面的消息不能被地址接受"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"                 Only %(contact_description)s are allowed to contact it.<br /><br />\n"
"                 Please make sure you are using the correct address or contact us at %(default_email)s instead."
msgstr ""
"地址%(alias_display_name)s无法接受以下信息。\n"
"                 只允许%(contact_description)s与之联系。<br /><br />\n"
"                 请确保您使用的地址正确，或通过%(default_email)s与我们联系。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"Please try again later or contact %(company_name)s instead."
msgstr ""
"%(alias_display_name)s无法接收以下信息，\n"
"请稍后再试或联系%(company_name)s。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"相应于这个别名对应的模型(ERP 单据种类)。任何一封不属于对某个已存在的记录的到来电子邮件，将导致此模型中新记录的创建(例如，一个新的项目任务)."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr "邮件别名的名称，例如，如果您要收取 <<EMAIL>> 的EMail,别名就要设为：\"jobs\""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "The plan \"%(plan_name)s\" cannot be launched:"
msgstr "无法启动计划\"%(plan_name)s\"："

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "The plan \"%(plan_name)s\" has been started"
msgstr "计划\"%(plan_name)s\"已启动"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__scheduled_date
msgid "The queue manager will send the email after the date"
msgstr "队列管理器将在日期之后发送电子邮件"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "The records must belong to the same company."
msgstr "记录必须属于同一家公司。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid ""
"The requested operation cannot be completed due to security restrictions. Please contact your system administrator.\n"
"\n"
"(Document type: %s, Operation: %s)"
msgstr ""
"由于安全限制，请求的操作无法完成。请联系您的系统管理员。\n"
"\n"
"(单据类型: %s, 操作: %s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid "The server \"%s\" cannot be used because it is archived."
msgstr "无法使用服务器“%s”，因为它已存档。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.js:0
#, python-format
msgid "The subscription preferences were successfully applied."
msgstr "已成功应用订阅偏好."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__user_id
msgid "The template belongs to this user"
msgstr "模板属于该用户"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__preview
#: model:ir.model.fields,help:mail.field_mail_message__preview
msgid "The text-only beginning of the body used as email preview."
msgstr "用作电子邮件预览的正文的纯文本开头。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "There are no messages in this conversation."
msgstr "此对话中没有消息。"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_rtc_session_channel_member_unique
msgid "There can only be one rtc session per channel member"
msgstr "每个频道成员只能有一个 rtc 会话"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "This"
msgstr "此"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "This action can only be done on a mail thread models"
msgstr "此操作只能在邮件线程模型上进行"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "This action cannot be done on transient models."
msgstr "无法对瞬态模型执行此操作。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
#, python-format
msgid "This action will send an email."
msgstr "此操作将发出EMail。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#, python-format
msgid "This channel doesn't have any attachments."
msgstr "该频道没有任何附件。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
#, python-format
msgid "This channel doesn't have any pinned messages."
msgstr "该频道无任何已置顶消息。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#, python-format
msgid "This conversation doesn't have any attachments."
msgstr "这次对话没有任何附件。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
#, python-format
msgid "This conversation doesn't have any pinned messages."
msgstr "此对话无任何已置顶消息。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_form_inherit_mail
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr "此电子邮件已列入群发邮件黑名单。单击以取消黑名单。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__email
msgid "This field is case insensitive."
msgstr "此字段不区分大小写。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__description
msgid "This field is used for internal description of the template's usage."
msgstr "此字段用于模板使用情况的内部描述。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,help:mail.field_res_partner__email_normalized
#: model:ir.model.fields,help:mail.field_res_users__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr "这个字段用来在邮件地址上搜索，因为初始的邮件字段会包含严格来说至少两个邮件地址。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_config_settings.py:0
#, python-format
msgid "This layout seems to no longer exist."
msgstr "这种布局似乎已不复存在。"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete
#: model:ir.model.fields,help:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,help:mail.field_mail_template__auto_delete
msgid ""
"This option permanently removes any track of email after it's been sent, "
"including from the Technical menu in the Settings, in order to preserve "
"storage space of your Odoo database."
msgstr "该选项在电邮发送后永久删除任何电子邮件跟踪，包括从设置中的技术菜单删除订单，以保留 ERP 数据库的储存空间."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/activity_exception/activity_exception.xml:0
#, python-format
msgid "This record has an exception activity."
msgstr "此记录有异常活动。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Those values are not supported as options when rendering: %(param_names)s"
msgstr "渲染时不支持这些值作为选项：%(param_names)s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Those values are not supported when posting or notifying: %(param_names)s"
msgstr "发布或通知时不支持这些值：%(param_names)s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Thread"
msgstr "线程"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.xml:0
#: code:addons/mail/static/src/core/common/discuss.xml:0
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "Thread Image"
msgstr "线程图像"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model_is_thread
msgid "Thread-Enabled"
msgstr "已启用对话串"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__timezone
msgid "Timezone"
msgstr "时区"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_summary
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_summary
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_title
msgid "Title"
msgstr "称谓"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_to
msgid "To"
msgstr "至"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__email_to
msgid "To (Emails)"
msgstr "至（EMail）"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__recipient_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__partner_to
msgid "To (Partners)"
msgstr "至（合作伙伴）"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/chat_window_patch.xml:0
#, python-format
msgid "To :"
msgstr "到:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "To peer:"
msgstr "致同伴："

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_todo
msgid "To-Do"
msgstr "待办"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "To:"
msgstr "至:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_state__today
#, python-format
msgid "Today"
msgstr "今天"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Today Activities"
msgstr "今日活动"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Today:"
msgstr "今天:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#, python-format
msgid "Tomorrow"
msgstr "明天"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Tomorrow:"
msgstr "明天:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Topics discussed in this group..."
msgstr "在该群组中讨论的话题 ..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__track_recipients
msgid "Track Recipients"
msgstr "跟踪收件人"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_message__tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr "在单独的模型中存储跟踪值。该字段允许重建跟踪，以便在模型中生成统计数据。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Tracking"
msgstr "追溯"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_tree
msgid "Tracking Value"
msgstr "追踪值"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_tracking_value
#: model:ir.ui.menu,name:mail.menu_mail_tracking_value
msgid "Tracking Values"
msgstr "追踪值"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__tracking_value_ids
msgid "Tracking values"
msgstr "追踪值"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Translate"
msgstr "翻译"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__body
msgid "Translation Body"
msgstr "翻译正文"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#, python-format
msgid "Translation Failure"
msgstr "翻译失败"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__triggered_next_type_id
msgid "Trigger"
msgstr "触发器"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__trigger
msgid "Trigger Next Activity"
msgstr "触发下一个活动"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend
msgid "Try Again"
msgstr "重试"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Turn camera on"
msgstr "打开相机"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_token
msgid "Twilio Account Auth Token"
msgstr "Twilio 帐户身份验证令牌"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_sid
msgid "Twilio Account SID"
msgstr "Twilio 帐户 SID"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__state
#: model:ir.model.fields,field_description:mail.field_ir_cron__state
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__server_type
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_type
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:mail.field_mail_message__message_type
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Type"
msgstr "类型"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_from
msgid "Type of delay"
msgstr "延迟类型"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__state
#: model:ir.model.fields,help:mail.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Update a Record': update the values of a record\n"
"- 'Create Activity': create an activity (Discuss)\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Send SMS': send SMS, log them on documents (SMS)- 'Add/Remove Followers': add or remove followers to a record (Discuss)\n"
"- 'Create Record': create a new record with new values\n"
"- 'Execute Code': a block of Python code that will be executed\n"
"- 'Send Webhook Notification': send a POST request to an external system, also known as a Webhook\n"
"- 'Execute Existing Actions': define an action that triggers several other server actions\n"
msgstr ""
"服务器操作类型。可使用以下值：\n"
"- 更新记录\"：更新记录的值\n"
"- 创建活动\"：创建活动（讨论）\n"
"- 发送电子邮件\"：发布信息、备注或发送电子邮件（讨论）\n"
"- 发送短信\"：发送短信，将其记录在文件中（短信）- \"添加/删除关注者\"：添加或删除记录中的关注者（讨论）\n"
"- 创建记录\"：使用新值创建新记录（讨论\n"
"- 执行代码\"：将执行的 Python 代码块\n"
"- 发送 Webhook 通知\"：向外部系统发送 POST 请求，也称为 Webhook\n"
"- 执行现有操作\"：定义一个可触发多个其他服务器操作的操作\n"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "记录中异常活动的类型。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid "Type the name of a person"
msgstr "请输入需要邀请的人员名字"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__uri
msgid "URI"
msgstr "URI"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__source_url
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "URL"
msgstr "网址"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid "Unable to connect to SMTP Server"
msgstr "无法连接到 SMTP 服务器"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr "无法发布邮件，请配置发件人的EMail地址。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Unable to send message, please configure the sender's email address."
msgstr "无法发送消息，请配置发件人的电子邮件地址。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "Unassign"
msgstr "取消分配"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "Unassign from me"
msgstr "从我这里取消分配"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Unblacklist"
msgstr "黑名单"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_blacklist_remove.py:0
#, python-format
msgid "Unblock Reason: %(reason)s"
msgstr "解锁原因：%(reason)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Undeafen"
msgstr "打开扬声器"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/core/web/chatter.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#, python-format
msgid "Unfollow"
msgstr "取消关注"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_unit
msgid "Unit of delay"
msgstr "延迟单位"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_tracking_value.py:0
#, python-format
msgid "Unknown"
msgstr "未知"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__unknown
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__unknown
#, python-format
msgid "Unknown error"
msgstr "未知错误"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Unmute"
msgstr "打开麦克风"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
#, python-format
msgid "Unmute Channel"
msgstr "取消频道静音"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.xml:0
#: code:addons/mail/static/src/discuss/message_pin/common/message_patch.js:0
#, python-format
msgid "Unpin"
msgstr "取消置顶"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "Unpin Conversation"
msgstr "取消固定通话"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid "Unpin Message"
msgstr "取消置顶消息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__message_unread_counter
msgid "Unread Messages Counter"
msgstr "未读消息数"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Unread messages"
msgstr "未读消息"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__unrestricted_attachment_ids
msgid "Unrestricted Attachments"
msgstr "不受限制的附件"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
#, python-format
msgid "Unstar all"
msgstr "取消所有星标"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Unsupported report type %s found."
msgstr "发现不支持的报告类型 %s。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.js:0
#, python-format
msgid "Until "
msgstr "直到"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "Until I turn it back on"
msgstr "直到我重新打开"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Update Mail Layout"
msgstr "更新邮件布局"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.xml:0
#, python-format
msgid "Upload Avatar"
msgstr "上传头像"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__upload_file
#: model:mail.activity.type,name:mail.mail_activity_data_upload_document
msgid "Upload Document"
msgstr "上传单据"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
#, python-format
msgid "Upload File"
msgstr "上传文件"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
#, python-format
msgid "Upload file"
msgstr "上传文件"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Upload:"
msgstr "上传："

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#, python-format
msgid "Uploaded"
msgstr "已上传"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#, python-format
msgid "Uploading"
msgstr "上传中"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Dynamic User' to specify the field name of the user to choose on the "
"record."
msgstr "使用 \"特定用户 \"可在下一个活动中始终指定相同的用户。使用 \"动态用户 \"指定要在记录中选择的用户字段名。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__external_email_server_default
msgid "Use Custom Email Servers"
msgstr "使用自定义电子邮件服务器"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "Use Twilio ICE servers"
msgstr "使用 Twilio ICE 服务器"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use a Gmail Server"
msgstr "使用 Gmail 服务器"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid "Use a local script to fetch your emails and create new records."
msgstr "使用一个本地脚本来获取您的电子邮件并创建新的记录。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use an Outlook Server"
msgstr "使用 Outlook 服务器"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use different domains for your mail aliases"
msgstr "为邮件别名使用不同的域名"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__is_batch_mode
msgid "Use in batch"
msgstr "批量使用"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Use message_notify to send a notification to an user."
msgstr "使用 message_notify 通知用户。"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_id
msgid "Use template"
msgstr "使用模版"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__use_push_to_talk
msgid "Use the push to talk feature"
msgstr "使用一键通话功能"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "Used In"
msgstr "用于"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__res_domain_user_id
msgid "Used as context used to evaluate composer domain"
msgstr "用作评估作者域名的上下文"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_type
#: model:ir.model.fields,help:mail.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""
"用于对信息生成器进行分类\n"
"电子邮件\"：由接收到的电子邮件生成，如 mailgateway\n"
"评论\"：由用户输入生成，例如通过讨论或作曲家\n"
"email_outgoing\"：由邮件生成\n"
"通知\"：由系统生成，例如跟踪信息\n"
"自动评论\"：由自动通知机制生成，例如回执\n"
"用户通知\"：为特定收件人生成"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_value__currency_id
msgid "Used to display the currency when tracking monetary values"
msgstr "用于在追踪货币价值时显示货币"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__sequence
msgid "Used to order subtypes."
msgstr "用于排序子类型。"

#. module: mail
#: model:ir.model,name:mail.model_res_users
#: model:ir.model.fields,field_description:mail.field_mail_template__user_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "User"
msgstr "用户"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_field_name
msgid "User Field"
msgstr "用户字段"

#. module: mail
#: model:ir.model,name:mail.model_bus_presence
msgid "User Presence"
msgstr "用户上线"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__user_setting_id
msgid "User Setting"
msgstr "用户设置"

#. module: mail
#: model:ir.actions.act_window,name:mail.res_users_settings_action
#: model:ir.model,name:mail.model_res_users_settings
#: model:ir.ui.menu,name:mail.res_users_settings_menu
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_tree
msgid "User Settings"
msgstr "用户设置"

#. module: mail
#: model:ir.model,name:mail.model_res_users_settings_volumes
msgid "User Settings Volumes"
msgstr "用户设置数量"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__user_notification
msgid "User Specific Notification"
msgstr "用户特定通知"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_type
msgid "User Type"
msgstr "用户类型"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "User is a bot"
msgstr "用户是机器人"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "User is idle"
msgstr "用户空闲"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "User is offline"
msgstr "用户离线"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "User is online"
msgstr "用户上线"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_gif_favorite_user_gif_favorite
msgid "User should not have duplicated favorite GIF"
msgstr "用户不应重复收藏 GIF"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "User:"
msgstr "用户:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__user
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__username
msgid "Username"
msgstr "用户名"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "Users in this channel: %(members)s %(dots)s and you."
msgstr "此频道中的用户: %(members)s %(dots)s和您."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__restrict_template_rendering
msgid ""
"Users will still be able to render templates.\n"
"However only Mail Template Editors will be able to create new dynamic templates or modify existing ones."
msgstr ""
"用户仍然可以渲染模板.\n"
"但是只有邮件模板编辑器才能创建新的动态模板或修改现有模板."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"Using your own email server is required to send/receive emails in Community "
"and Enterprise versions. Online users already benefit from a ready-to-use "
"email server (@mycompany.odoo.com)."
msgstr ""
"在社区和企业版本中，发送/接收电子邮件需要使用自己的电子邮件服务器。上线用户已受益于随时可用的电子邮件服务器 "
"(@mycompany.baidu.com)."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__valid
msgid "Valid"
msgstr "有效的"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Value %(allowed_domains)s for `mail.catchall.domain.allowed` cannot be validated.\n"
"It should be a comma separated list of domains e.g. example.com,example.org."
msgstr ""
"mail.catchall.domain.allowed`的值 %(allowed_domains)s 无法验证。\n"
"它应该是一个以逗号分隔的域名列表，例如 example.com,example.org。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Video Settings"
msgstr "视频设置"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Video player:"
msgstr "视频播放器："

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#: model:ir.model,name:mail.model_ir_ui_view
#, python-format
msgid "View"
msgstr "视图"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "View %s"
msgstr "视图 %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "View Reactions"
msgstr "查看反应"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:mail.field_ir_ui_view__type
msgid "View Type"
msgstr "视图类型"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "View or join channels"
msgstr "查看或加入频道"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_attachment__voice_ids
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Voice"
msgstr "语音"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.xml:0
#, python-format
msgid "Voice Message"
msgstr "語音訊息"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.js:0
#, python-format
msgid "Voice Settings"
msgstr "语音设置"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Voice detection threshold"
msgstr "语音检测阈值"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
#, python-format
msgid "Voice recording stopped"
msgstr "录音停止"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__volume
msgid "Volume"
msgstr "体积"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Volume per partner"
msgstr "每个业务合作伙伴的数量"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__volume_settings_ids
msgid "Volumes of other partners"
msgstr "其他业务合作伙伴的数量"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
#, python-format
msgid ""
"Want to spice up your conversations with GIFs? Activate the feature in the "
"settings!"
msgstr "想用 GIF 为对话增添乐趣？在设置中激活该功能！"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_button.js:0
#: code:addons/mail/static/src/views/web/fields/list_activity/list_activity.js:0
#, python-format
msgid "Warning"
msgstr "警告"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"We could not create alias %(alias_name)s because domain "
"%(alias_domain_name)s belongs to company %(alias_company_names)s while the "
"owner document belongs to company %(company_name)s."
msgstr ""
"我们无法创建别名 %(alias_name)s，因为域 %(alias_domain_name)s 属于公司 "
"%(alias_company_names)s，而所有者文件属于公司 %(company_name)s。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"We could not create alias %(alias_name)s because domain "
"%(alias_domain_name)s belongs to company %(alias_company_names)s while the "
"target document belongs to company %(company_name)s."
msgstr ""
"我们无法创建别名 %(alias_name)s，因为域 %(alias_domain_name)s 属于公司 "
"%(alias_company_names)s，而目标文档属于公司 %(company_name)s。"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "周"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Welcome to MyCompany!"
msgstr "欢迎来到我的公司！"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid ""
"Well, nothing lasts forever, but are you sure you want to unpin this "
"message?"
msgstr "好吧，没有什么是永恒的，但您确定要取消置顶这条消息吗？"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "What's your name?"
msgstr "您叫什么名字？"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
#, python-format
msgid ""
"When selecting \"Default user\" assignment, you must specify a responsible."
msgstr "选择“默认用户”分配时，您必须指定负责人。"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__original
msgid ""
"Whether a full original copy of each email should be kept for reference and "
"attached to each processed message. This will usually double the size of "
"your message database."
msgstr "一直保留每封EMail的完整原件以供参考并附于每个处理的邮件中。这通常会使消息数据库的大小增加一倍。"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__attach
msgid ""
"Whether attachments should be downloaded. If not enabled, incoming emails "
"will be stripped of any attachments before being processed"
msgstr "如果不勾选此项，无论是否下载附件，收到的邮件会在被处理前去掉附件"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__track_recipients
msgid "Whether to display all the recipients or only the important ones."
msgstr "是显示所有的收件人还是只显示重要的收件人。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#, python-format
msgid "Write Feedback"
msgstr "反馈"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Write your message here..."
msgstr "在这里写下您的消息..."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Wrong operation name (%s)"
msgstr "错误的操作名称 (%s)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "YYYY-MM-DD HH:MM:SS"
msgstr "YYYY-MM-DD HH:MM:SS"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid "Yeah, pin it!"
msgstr "时的，把它置顶！"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
#, python-format
msgid "Yes"
msgstr "是"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid "Yes, remove it please"
msgstr "是的，请移除它"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#, python-format
msgid "Yesterday"
msgstr "昨天"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Yesterday:"
msgstr "昨天:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.js:0
#, python-format
msgid ""
"You are about to leave this group conversation and will no longer have "
"access to it unless you are invited again. Are you sure you want to "
"continue?"
msgstr "您即将离开此群组对话，除非再次受到邀请，否则将无法再访问它。 您确定您要继续吗？"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "You are alone in a private conversation."
msgstr "您正独自一人在私人对话内。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "You are alone in this channel."
msgstr "您是此频道的唯一用户."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "You are in a private conversation with %(member_names)s."
msgstr "你正在私人对话内，其他成员有：%(member_names)s。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "You are in channel %(bold_start)s#%(channel_name)s%(bold_end)s."
msgstr "你正在频道 %(bold_start)s#%(channel_name)s%(bold_end)s。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_service.js:0
#, python-format
msgid "You are no longer following \"%(thread_name)s\"."
msgstr "您不再关注 \"%(thread_name)s\"。"

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/attachment.py:0
#, python-format
msgid "You are not allowed to upload an attachment here."
msgstr "您无权在此处上传附件。"

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/attachment.py:0
#, python-format
msgid "You are not allowed to upload attachments on this channel."
msgstr "本频道不允许上传附件。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.js:0
#, python-format
msgid ""
"You are the administrator of this channel. Are you sure you want to leave?"
msgstr "您是该频道的管理员。您确定要离开吗？"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid ""
"You can mark any message as 'starred', and it shows up in this mailbox."
msgstr "您可以给任何消息加星号，然后它会出现在邮箱里。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid "You can not write on %(field_name)s."
msgstr "您不能修改 %(field_name)s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_service.js:0
#, python-format
msgid "You can only chat with existing users."
msgstr "您只能与现有用户聊天。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_service.js:0
#, python-format
msgid "You can only chat with partners that have a dedicated user."
msgstr "您只能与拥有专属用户的业务合作伙伴聊天。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "You can safely ignore this message"
msgstr "您可以放心忽略此消息"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_type.py:0
#, python-format
msgid ""
"You cannot delete %(activity_names)s as it is required in various apps."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"You cannot delete those groups, as the Whole Company group is required by "
"other modules."
msgstr "您不能删除这些群组，因为整个公司群组被其它模块引用。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_type.py:0
#, python-format
msgid ""
"You cannot modify %(activities_names)s target model as they are are required"
" in various apps."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"You cannot use anything else than unaccented latin characters in the alias "
"address %(alias_name)s."
msgstr "在别名地址%(alias_name)s中，除了不带尾音的拉丁字符外，不能使用任何其他字符。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
#, python-format
msgid ""
"You cannot use anything else than unaccented latin characters in the domain "
"name %(domain_name)s."
msgstr "域名%(domain_name)s中不能使用非拉丁字符。"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "You do not have access to"
msgstr "您无权访问"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid ""
"You do not have the access right to unblacklist emails. Please contact your "
"administrator."
msgstr "<br>您没有将电子邮件列入黑名单的访问权限。请联系您的管理员。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "You have been assigned to %s"
msgstr "您已被指派到 %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "You have been assigned to the"
msgstr "您被指派到"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
#, python-format
msgid "You have been invited to #%s"
msgstr "您已受邀参加  #%s"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__attachment_ids
msgid ""
"You may attach files to this template, to be added to all emails created "
"from this template"
msgstr "您可以对这个模版附加文件，可以附加到所有基于此模版生成的邮件中"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "You may not define a template on an abstract model: %s"
msgstr "无法在抽象模型上定义模板：%s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid ""
"You sure want this message pinned to %(conversation)s forever and ever?"
msgstr "您确定要把这条信息永久置顶在%(conversation)s上？"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
#, python-format
msgid "You unpinned your conversation with %s"
msgstr "您取消了与%s的对话"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
#, python-format
msgid "You unsubscribed from %s."
msgstr "您退出了\"%s\" 会议对话。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "You're viewing older messages"
msgstr "您正在查看较早的消息"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "You've been invited to a chat!"
msgstr "您已被邀请参加聊天!"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "You've been invited to a meeting!"
msgstr "您已被邀请参加一个会议！"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "You:"
msgstr "您："

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Your"
msgstr "您的"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid ""
"Your account email has been changed from %(old_email)s to %(new_email)s."
msgstr "您的帐户电子邮件已从%(old_email)s更改为%(new_email)s。"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Your account login has been updated"
msgstr "您的账户登录信息已更新"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Your account password has been updated"
msgstr "您的账户密码已更新"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "Your browser does not support videoconference"
msgstr "您的浏览器不支持视频会议"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Your browser does not support voice activation"
msgstr "您的浏览器不支持语音通话"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Your browser does not support webRTC."
msgstr "您的浏览器不支持 webRTC."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "Your name"
msgstr "您的名字"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/recipient_list.js:0
#, python-format
msgid "[%(name)s] (no email address)"
msgstr "[%(name)s] (没有邮件地址)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "addresses linked to registered partners"
msgstr "链接到注册合作伙伴的地址"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__current_date
msgid "after completion date"
msgstr "完成日期后"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__previous_activity
msgid "after previous activity deadline"
msgstr "上次活动结束后"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "alias %(name)s: %(error)s"
msgstr "别名 %(name)s: %(error)s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "attachment(s) of this email."
msgstr "本电子邮件的附件。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "available bitrate:"
msgstr "可用比特率："

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "back"
msgstr "返回"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid ""
"because you have\n"
"                contacted it too many times in the last few minutes.\n"
"                <br/>\n"
"                Please try again later."
msgstr ""
"因为你有\n"
"                在过去的几分钟里联系了太多次。                <br/>请稍后重试。"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "by"
msgstr "单位"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "camera"
msgstr "照相机"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid ""
"cannot be processed. This address\n"
"    is used to collect replies and should not be used to directly contact"
msgstr ""
"无法处理。此地址\n"
"        用于收集答复，不用于直接联系"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "channels"
msgstr "频道"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "clock rate:"
msgstr "时钟频率："

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "close"
msgstr "关闭"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "codec:"
msgstr "编解码器："

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "created this channel."
msgstr "创建了这个频道。"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__days
msgid "days"
msgstr "天数"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "days overdue:"
msgstr "逾期天数："

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "days:"
msgstr "天数："

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "deaf"
msgstr "聋"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__user_device
msgid "devices"
msgstr "设备"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "document"
msgstr "单据"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.js:0
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
#, python-format
msgid "done"
msgstr "完成"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "down DTLS:"
msgstr "down DTLS:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "down ICE:"
msgstr "down ICE:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. \"Discuss proposal\""
msgstr "例如.讨论提案"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. \"Go over the offer and discuss details\""
msgstr "例如，“查看报价并讨论细节”"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. \"Welcome email\""
msgstr "例如“欢迎电子邮件”"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
msgid "e.g. \"Welcome to MyCompany\" or \"Nice to meet you, {{ object.name }}\""
msgstr "例如“欢迎来到我的公司”或“很高兴认识你，{{ object.name }}”"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"bounce\""
msgstr "例如 \"bounce\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"catchall\""
msgstr "例如 \"catchall\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"mycompany.com\""
msgstr "例如 \"mycompany.com\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"notifications\""
msgstr "e.g. \"通知\""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. 65ea4f9e948b693N5156F350256bd152"
msgstr "例如 65ea4f9e948b693N5156F350256bd152"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. ACd5543a0b450ar4c7t95f1b6e8a39t543"
msgstr "例如 ACd5543a0b450ar4c7t95f1b6e8a39t543"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. Contact"
msgstr "例如：联系"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. Discuss proposal"
msgstr "例如.讨论提案"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "e.g. Onboarding"
msgstr "例如，入职"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. Schedule a meeting"
msgstr "例如安排会议"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. domain.com"
msgstr "例如：domain.com"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "e.g. support"
msgstr "例如支持"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "e.g. true.true..f"
msgstr "例如 true.true..f"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. user_id"
msgstr "例如：user_id"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "e.g: \"<EMAIL>\""
msgstr "例如：“<EMAIL>”"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "for"
msgstr "由于"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been created from:"
msgstr "已经创建自:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been modified from:"
msgstr "已经被修改自:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "has just assigned you the following activity:"
msgstr "刚刚为您分配了以下活动："

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_content_filter
msgid "https://developers.google.com/tenor/guides/content-filtering"
msgstr "https://developers.google.com/tenor/guides/content-filtering"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
#, python-format
msgid "incorrectly configured alias"
msgstr "不正确配置的别名"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
#, python-format
msgid "incorrectly configured alias (unknown reference record)"
msgstr "不正确配置的别名（未知参考记录）"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "invited %s to the channel"
msgstr "邀请%s加入频道"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "joined the channel"
msgstr "加入频道"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "left the channel"
msgstr "离开频道"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "list"
msgstr "列表"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "list-item"
msgstr "刊登物品"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "live"
msgstr "在线"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "mail_blacklist_removal"
msgstr "mail_blacklist_removal"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "media player Error"
msgstr "媒体播放器故障"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "microphone"
msgstr "麦克风"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "model %s does not accept document creation"
msgstr "模型 %s 不接受单据创建"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__months
msgid "months"
msgstr "月"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "ms"
msgstr "秒"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "muted"
msgstr "静音"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "new"
msgstr "新建"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.js:0
#, python-format
msgid "no connection"
msgstr "无连接"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/relative_time.js:0
#, python-format
msgid "now"
msgstr "现在"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "on"
msgstr "在"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#, python-format
msgid "on:"
msgstr "于:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#, python-format
msgid "or"
msgstr "或"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "other members."
msgstr "其他成员."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "raising hand"
msgstr "举手"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"reply to missing document (%(model)s,%(thread)s), fall back on document "
"creation"
msgstr "回复丢失的文档 (%(model)s,%(thread)s), 退回到文档创建"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"reply to model %s that does not accept document update, fall back on "
"document creation"
msgstr "回复模型 %s 不接受单据更新单据创建"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
#, python-format
msgid "restricted to followers"
msgstr "仅限于关注者"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
#, python-format
msgid "restricted to known authors"
msgstr "仅限于已知的作者"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid "results out of"
msgstr "结果出自于"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "screen"
msgstr "屏幕"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "some specific addresses"
msgstr "一些具体地址"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__stun
msgid "stun:"
msgstr "stun:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "target model unspecified"
msgstr "目标模型未指定"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "team."
msgstr "团队."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "template"
msgstr "模板"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "toggle push-to-talk"
msgstr "切换按键通话"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__turn
msgid "turn:"
msgstr "turn:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown error"
msgstr "未知错误"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown target model %s"
msgstr "未知目标模型 %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "up DTLS:"
msgstr "up DTLS:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "up ICE:"
msgstr "up ICE:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "users"
msgstr "用户"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "view"
msgstr "查看"

#. module: mail
#: model:ir.model,name:mail.model_ir_websocket
msgid "websocket message handling"
msgstr "websocket消息处理"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__weeks
msgid "weeks"
msgstr "周"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "your alias"
msgstr "你的别名"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "{{ object.partner_id.lang }}"
msgstr "{{ object.partner_id.lang }}"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid "“%(member_name)s” in “%(channel_name)s”"
msgstr "\"%(channel_name)s\"中的\"%(member_name)s\""
