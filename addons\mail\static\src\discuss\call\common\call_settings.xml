<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">

    <t t-name="discuss.CallSettings">
        <ActionPanel title="title">
            <div class="d-flex flex-column px-3 overflow-auto">
                <div class="mb-3 d-flex align-items-center flex-wrap">
                    <label class="d-flex align-items-center flex-wrap mw-100 cursor-pointer" title="Input device" aria-label="Input device">
                        <span class="me-2 text-truncate">Input device</span>
                        <div>
                            <select name="inputDevice" class="form-select" t-on-change="onChangeSelectAudioInput">
                                <option value="">Browser default</option>
                                <t t-foreach="state.userDevices" t-as="device" t-key="device_index" device="device">
                                    <option t-if="device.kind === 'audioinput'" t-att-selected="userSettings.audioInputDeviceId === device.deviceId" t-att-value="device.deviceId"><t t-esc="device.label || 'audio device ' + device_index"/></option>
                                </t>
                            </select>
                        </div>
                    </label>
                </div>
                <div class="mb-3 d-flex align-items-center flex-wrap">
                    <label class="d-flex align-items-center flex-wrap mw-100 cursor-pointer" title="Enable Push-to-talk" aria-label="Enable Push-to-talk">
                        <input type="checkbox" aria-label="toggle push-to-talk" title="toggle push-to-talk" t-on-change="onChangePushToTalk" t-att-checked="userSettings.usePushToTalk ? 'checked' : ''" class="form-check-input"/>
                        <span class="ms-2 text-truncate">Enable Push-to-talk</span>
                    </label>
                </div>
                <t t-if="userSettings.usePushToTalk">
                    <div class="mb-3 d-flex align-items-center flex-wrap">
                        <label class="d-flex align-items-center flex-wrap mw-100 cursor-pointer" title="Push-to-talk key" aria-label="Push-to-talk key">
                            <span class="me-2 text-truncate">Push-to-talk key</span>
                            <span class="d-flex">
                                <span t-if="userSettings.pushToTalkKey" class="ms-1 px-3 border border-2 rounded fs-3" t-attf-class="{{ userSettings.isRegisteringKey ? 'border-danger' : 'border-primary' }}" t-esc="pushToTalkKeyText"/>
                                <button class="btn btn-link px-2 py-0 text-black" t-on-click="onClickRegisterKeyButton">
                                    <i t-if="userSettings.isRegisteringKey" title="Cancel" aria-label="Cancel" class="fa fa-2x fa-times-circle"/>
                                    <i t-else="" title="Register new key" aria-label="Register new key" class="fa fa-2x fa-keyboard-o"/>
                                </button>
                            </span>
                        </label>
                    </div>
                    <div t-if="userSettings.isRegisteringKey">Press a key to register it as the push-to-talk shortcut.</div>
                    <div class="mb-3 d-flex align-items-center flex-wrap">
                        <label class="d-flex align-items-center flex-wrap mw-100 cursor-pointer" title="Delay after releasing push-to-talk" aria-label="Delay after releasing push-to-talk">
                            <span class="me-2 text-truncate">Delay after releasing push-to-talk</span>
                            <div class="d-flex w-100 align-items-center">
                                <input class="flex-grow-2 form-range" type="range" min="0" max="2000" step="1" t-att-value="userSettings.voiceActiveDuration" t-on-input="onChangeDelay"/>
                                <span class="p-1 w-50 text-end"><t t-out="userSettings.voiceActiveDuration"/>ms</span>
                            </div>
                        </label>
                    </div>
                </t>
                <div t-else="" class="mb-3 d-flex align-items-center flex-wrap">
                    <label class="d-flex align-items-center flex-wrap mw-100 cursor-pointer" title="Voice detection threshold" aria-label="Voice detection threshold">
                        <span class="me-2 text-truncate">Voice detection threshold</span>
                        <div class="d-flex w-100 align-items-center">
                            <input class="flex-grow-2 form-range" type="range" min="0.001" max="1" step="0.001" t-att-value="userSettings.voiceActivationThreshold" t-on-input="onChangeThreshold"/>
                            <span class="p-1 w-50 text-end"><t t-out="Math.floor(userSettings.voiceActivationThreshold * 100)"/>%</span>
                        </div>
                    </label>
                </div>
            </div>
            <div class="d-flex flex-column px-3 overflow-auto">
                <div class="py-2 fw-bolder text-700 text-truncate text-uppercase">Video Settings</div>
                <div class="mb-3 d-flex align-items-center flex-wrap">
                    <label class="d-flex align-items-center flex-wrap mw-100 cursor-pointer" title="Show video participants only" aria-label="Show video participants only">
                        <input type="checkbox" aria-label="toggle push-to-talk" title="Show video participants only" t-on-change="onChangeVideoFilterCheckbox" t-att-checked="props.thread.showOnlyVideo ? 'checked' : ''" class="form-check-input"/>
                        <span class="ms-2 text-truncate">Show video participants only</span>
                    </label>
                </div>
                <div t-if="userSettings.hasCanvasFilterSupport" class="mb-3 d-flex align-items-center flex-wrap">
                    <label class="d-flex align-items-center flex-wrap mw-100 cursor-pointer" title="Blur video background" aria-label="Blur video background">
                        <input type="checkbox" aria-label="Blur video background" title="Blur video background" t-on-change="onChangeBlur" t-att-checked="userSettings.useBlur ? 'checked' : ''" class="form-check-input"/>
                        <span class="ms-2 text-truncate">Blur video background</span>
                    </label>
                </div>
                <t t-if="userSettings.useBlur">
                    <div class="mb-3 d-flex align-items-center flex-wrap">
                        <label class="d-flex align-items-center flex-wrap mw-100 cursor-pointer" title="Background blur intensity" aria-label="Background blur intensity">
                            <span class="me-2 text-truncate">Background blur intensity</span>
                            <div class="d-flex w-100 align-items-center">
                                <input class="flex-grow-2 form-range" type="range" min="0" max="20" step="1" t-att-value="userSettings.backgroundBlurAmount" t-on-input="onChangeBackgroundBlurAmount"/>
                                <span class="p-1 w-50 text-end"><t t-out="Math.floor(userSettings.backgroundBlurAmount * 5)"/>%</span>
                            </div>
                        </label>
                    </div>
                    <div class="mb-3 d-flex align-items-center flex-wrap">
                        <label class="d-flex align-items-center flex-wrap mw-100 cursor-pointer" title="Edge blur intensity" aria-label="Edge blur intensity">
                            <span class="me-2 text-truncate">Edge blur intensity</span>
                            <div class="d-flex w-100 align-items-center">
                                <input class="flex-grow-2 form-range" type="range" min="0" max="20" step="1" t-att-value="userSettings.edgeBlurAmount" t-on-input="onChangeEdgeBlurAmount"/>
                                <span class="p-1 w-50 text-end"><t t-out="Math.floor(userSettings.edgeBlurAmount * 5)"/>%</span>
                            </div>
                        </label>
                    </div>
                </t>
            </div>
            <div t-if="env.debug" class="d-flex flex-column px-3 overflow-auto">
                <div class="py-2 fw-bolder text-700 text-truncate text-uppercase">Technical Settings</div>
                <div class="mb-3 d-flex align-items-center flex-wrap">
                    <label class="d-flex align-items-center flex-wrap mw-100 cursor-pointer" title="Log RTC events" aria-label="Log RTC events">
                        <input type="checkbox" aria-label="Log RTC events" title="Log RTC events" t-on-change="onChangeLogRtcCheckbox" t-att-checked="userSettings.logRtc ? 'checked' : ''" class="form-check-input"/>
                        <span class="ms-2 text-truncate">Log RTC events</span>
                    </label>
                </div>
                <div t-if="userSettings.logRtc" class="mb-3 d-flex align-items-center flex-wrap">
                    <button class="btn btn-primary" t-att-disabled="rtc.state.logs.size === 0" t-on-click="onClickDownloadLogs">Download logs</button>
                </div>
            </div>
        </ActionPanel>
    </t>

</templates>
