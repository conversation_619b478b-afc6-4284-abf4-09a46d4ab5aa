#!/usr/bin/env python3
import xmlrpc.client

def fix_hours_uom():
    try:
        # Connection details
        url = 'http://localhost:8069'
        db = 'dataicraft'
        username = '<EMAIL>'
        password = 'admin123'
        
        # Connect to Odoo
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            print("❌ Authentication failed!")
            return
        
        print(f"✅ Connected to Odoo as user ID: {uid}")
        
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        print("\n🔧 === Fixing Hours UoM Issue === 🔧")
        
        # Step 1: Find Working Time category
        working_time_cat = models.execute_kw(
            db, uid, password,
            'uom.category', 'search_read',
            [[['name', '=', 'Working Time']]],
            {'fields': ['id', 'name']}
        )
        
        if not working_time_cat:
            print("❌ Working Time category not found!")
            return
        
        working_time_cat_id = working_time_cat[0]['id']
        print(f"📊 Working Time category ID: {working_time_cat_id}")
        
        # Step 2: Find or create Hours UoM in Working Time category
        hours_uom = models.execute_kw(
            db, uid, password,
            'uom.uom', 'search_read',
            [[['name', '=', 'Hours']]],
            {'fields': ['id', 'name', 'category_id', 'uom_type']}
        )
        
        if hours_uom:
            hours_uom_id = hours_uom[0]['id']
            current_category = hours_uom[0]['category_id'][1] if hours_uom[0]['category_id'] else 'None'
            print(f"📏 Found Hours UoM: ID {hours_uom_id}, Category: {current_category}")
            
            # Check if it's in the wrong category
            if hours_uom[0]['category_id'][0] != working_time_cat_id:
                print(f"🔧 Moving Hours UoM to Working Time category...")
                
                # Update the Hours UoM to be in Working Time category
                models.execute_kw(
                    db, uid, password,
                    'uom.uom', 'write',
                    [[hours_uom_id], {
                        'category_id': working_time_cat_id,
                        'uom_type': 'reference',  # Make it the reference UoM
                        'factor': 1.0,
                        'rounding': 0.01
                    }]
                )
                print("✅ Hours UoM moved to Working Time category")
            else:
                print("✅ Hours UoM already in correct category")
        else:
            # Create Hours UoM
            print("🔧 Creating Hours UoM in Working Time category...")
            hours_uom_id = models.execute_kw(
                db, uid, password,
                'uom.uom', 'create',
                [{
                    'name': 'Hours',
                    'category_id': working_time_cat_id,
                    'uom_type': 'reference',
                    'factor': 1.0,
                    'rounding': 0.01
                }]
            )
            print(f"✅ Created Hours UoM: ID {hours_uom_id}")
        
        # Step 3: Update Days UoM to be smaller than Hours (if it exists)
        days_uom = models.execute_kw(
            db, uid, password,
            'uom.uom', 'search_read',
            [[['name', '=', 'Days'], ['category_id', '=', working_time_cat_id]]],
            {'fields': ['id', 'name', 'uom_type']}
        )
        
        if days_uom:
            days_uom_id = days_uom[0]['id']
            print(f"📏 Found Days UoM: ID {days_uom_id}")
            
            # Make Days a bigger unit (8 hours = 1 day)
            models.execute_kw(
                db, uid, password,
                'uom.uom', 'write',
                [[days_uom_id], {
                    'uom_type': 'bigger',
                    'factor': 0.125  # 1 day = 8 hours, so factor = 1/8
                }]
            )
            print("✅ Updated Days UoM to be 8 hours")
        
        # Step 4: Fix all service products to use Hours
        print("\n🔧 Updating service products to use Hours...")
        
        service_products = models.execute_kw(
            db, uid, password,
            'product.template', 'search',
            [[['type', '=', 'service']]]
        )
        
        if service_products:
            models.execute_kw(
                db, uid, password,
                'product.template', 'write',
                [service_products, {
                    'uom_id': hours_uom_id,
                    'uom_po_id': hours_uom_id
                }]
            )
            print(f"✅ Updated {len(service_products)} service products to use Hours")
        
        # Step 5: Fix all physical products to use Units
        print("\n🔧 Updating physical products to use Units...")
        
        # Find Units UoM
        units_uom = models.execute_kw(
            db, uid, password,
            'uom.uom', 'search_read',
            [[['name', '=', 'Units']]],
            {'fields': ['id']}
        )
        
        if units_uom:
            units_uom_id = units_uom[0]['id']
            
            physical_products = models.execute_kw(
                db, uid, password,
                'product.template', 'search',
                [[['type', 'in', ['product', 'consu']]]]
            )
            
            if physical_products:
                models.execute_kw(
                    db, uid, password,
                    'product.template', 'write',
                    [physical_products, {
                        'uom_id': units_uom_id,
                        'uom_po_id': units_uom_id
                    }]
                )
                print(f"✅ Updated {len(physical_products)} physical products to use Units")
        
        # Step 6: Clear any problematic order lines
        print("\n🔧 Clearing problematic draft order lines...")
        
        try:
            # Delete draft sales order lines that might have UoM conflicts
            draft_so_lines = models.execute_kw(
                db, uid, password,
                'sale.order.line', 'search',
                [[['order_id.state', 'in', ['draft', 'sent']]]]
            )
            
            if draft_so_lines:
                models.execute_kw(
                    db, uid, password,
                    'sale.order.line', 'unlink',
                    [draft_so_lines]
                )
                print(f"✅ Cleared {len(draft_so_lines)} draft sales order lines")
            
            # Delete draft purchase order lines that might have UoM conflicts
            draft_po_lines = models.execute_kw(
                db, uid, password,
                'purchase.order.line', 'search',
                [[['order_id.state', 'in', ['draft', 'sent']]]]
            )
            
            if draft_po_lines:
                models.execute_kw(
                    db, uid, password,
                    'purchase.order.line', 'unlink',
                    [draft_po_lines]
                )
                print(f"✅ Cleared {len(draft_po_lines)} draft purchase order lines")
                
        except Exception as e:
            print(f"⚠️ Error clearing order lines: {e}")
        
        print(f"\n🎉 UoM Fix Complete!")
        print(f"✅ Hours UoM is now in Working Time category")
        print(f"✅ Service products use Hours")
        print(f"✅ Physical products use Units")
        print(f"✅ Cleared problematic order lines")
        
        print(f"\n🔄 Next Steps:")
        print("1. Refresh your browser (F5)")
        print("2. Try creating a new order")
        print("3. The UoM error should be resolved")
        print("4. If you still get errors, check the specific product mentioned")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    fix_hours_uom()
