#!/usr/bin/env python3
import psycopg2

def fix_construction_module_conflict():
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='dataicraft',
            user='odoo',
            password='odoo123'
        )
        cur = conn.cursor()
        
        print("🔧 === Fixing Construction Module Conflict === 🔧")
        
        # Step 1: Find all timesheet-related products
        print("\n🔍 Step 1: Finding timesheet products...")
        
        cur.execute("""
            SELECT pt.id, pt.name, pt.service_type, pt.uom_id, u.name as uom_name
            FROM product_template pt
            LEFT JOIN uom_uom u ON pt.uom_id = u.id
            WHERE pt.service_type = 'timesheet' OR pt.name::text LIKE '%timesheet%'
            OR pt.name::text LIKE '%hour%';
        """)
        
        timesheet_products = cur.fetchall()
        
        if timesheet_products:
            print(f"Found {len(timesheet_products)} timesheet products:")
            for product in timesheet_products:
                print(f"  📦 ID: {product[0]}, Service Type: {product[2]}, UoM: {product[4]}")
        else:
            print("✅ No timesheet products found")
        
        # Step 2: Check project configurations that might auto-create timesheet products
        print("\n🔍 Step 2: Checking project configurations...")
        
        cur.execute("""
            SELECT id, name, allow_timesheets, timesheet_product_id
            FROM project_project
            WHERE allow_timesheets = true;
        """)
        
        timesheet_projects = cur.fetchall()
        
        if timesheet_projects:
            print(f"Found {len(timesheet_projects)} projects with timesheets enabled:")
            for project in timesheet_projects:
                print(f"  🏗️ Project ID: {project[0]}, Timesheet Product ID: {project[3]}")
        else:
            print("✅ No projects with timesheets enabled")
        
        # Step 3: Check sale order templates that might cause issues
        print("\n🔍 Step 3: Checking sale order templates...")
        
        cur.execute("SELECT COUNT(*) FROM sale_order_template;")
        template_count = cur.fetchone()[0]
        print(f"Sale order templates: {template_count}")
        
        if template_count > 0:
            cur.execute("DELETE FROM sale_order_template_option;")
            deleted_options = cur.rowcount
            print(f"✅ Deleted {deleted_options} sale order template options")
            
            cur.execute("DELETE FROM sale_order_template_line;")
            deleted_lines = cur.rowcount
            print(f"✅ Deleted {deleted_lines} sale order template lines")
            
            cur.execute("DELETE FROM sale_order_template;")
            deleted_templates = cur.rowcount
            print(f"✅ Deleted {deleted_templates} sale order templates")
        
        # Step 4: Disable timesheet functionality on projects
        print("\n🔧 Step 4: Disabling timesheet functionality...")
        
        cur.execute("""
            UPDATE project_project 
            SET allow_timesheets = false, 
                timesheet_product_id = NULL
            WHERE allow_timesheets = true;
        """)
        updated_projects = cur.rowcount
        print(f"✅ Disabled timesheets on {updated_projects} projects")
        
        # Step 5: Convert timesheet products to regular services with Units UoM
        print("\n🔧 Step 5: Converting timesheet products...")
        
        for product in timesheet_products:
            product_id = product[0]
            cur.execute("""
                UPDATE product_template 
                SET service_type = 'manual',
                    uom_id = 1,
                    uom_po_id = 1
                WHERE id = %s;
            """, (product_id,))
            print(f"✅ Converted product ID {product_id} to regular service with Units UoM")
        
        # Step 6: Clear any cached timesheet data
        print("\n🧹 Step 6: Clearing timesheet cache...")
        
        cur.execute("DELETE FROM account_analytic_line WHERE project_id IS NOT NULL;")
        deleted_analytic = cur.rowcount
        print(f"✅ Deleted {deleted_analytic} project analytic lines")
        
        # Step 7: Reset company timesheet settings
        print("\n⚙️ Step 7: Resetting company settings...")
        
        cur.execute("UPDATE res_company SET timesheet_encode_uom_id = 1;")
        updated_companies = cur.rowcount
        print(f"✅ Set {updated_companies} companies to use Units for timesheets")
        
        # Commit changes
        conn.commit()
        
        print(f"\n🎉 Construction Module Conflict Fixed!")
        print(f"✅ Disabled timesheet functionality on projects")
        print(f"✅ Converted timesheet products to regular services")
        print(f"✅ All products now use Units UoM")
        print(f"✅ Cleared timesheet cache data")
        print(f"✅ Reset company settings")
        
        print(f"\n📋 What This Fixed:")
        print(f"  🚫 No more automatic timesheet product creation")
        print(f"  🚫 No more Hours UoM conflicts")
        print(f"  ✅ Projects still work for task management")
        print(f"  ✅ Sales orders will work normally")
        
        print(f"\n🔄 Next Steps:")
        print("1. Restart Odoo server")
        print("2. Try creating a sales order")
        print("3. The UoM error should be gone")
        print("4. You can still use projects for task management")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    fix_construction_module_conflict()
