#!/usr/bin/env python3
import psycopg2

def check_users():
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='dataicraft',
            user='odoo',
            password='odoo123'
        )
        cur = conn.cursor()
        
        print("=== CHECKING USERS IN DATAICRAFT DATABASE ===")
        
        # Check all users
        cur.execute("SELECT id, login, active, create_date FROM res_users ORDER BY id;")
        users = cur.fetchall()
        
        print(f"Total users: {len(users)}")
        for user in users:
            print(f"ID: {user[0]} | Login: {user[1]} | Active: {user[2]} | Created: {user[3]}")
        
        print("\n=== CHECKING DATABASE LIST ===")
        # Check what databases exist
        cur.execute("SELECT current_database();")
        current_db = cur.fetchone()[0]
        print(f"Current database: {current_db}")
        
        conn.close()
        
        # Check all databases
        conn2 = psycopg2.connect(
            host='localhost',
            database='postgres',
            user='odoo',
            password='odoo123'
        )
        cur2 = conn2.cursor()
        cur2.execute("SELECT datname FROM pg_database WHERE datistemplate = false;")
        databases = cur2.fetchall()
        
        print("Available databases:")
        for db in databases:
            print(f"- {db[0]}")
        
        conn2.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    check_users()
