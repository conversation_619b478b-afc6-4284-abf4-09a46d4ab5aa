#!/usr/bin/env python3
import psycopg2

def delete_all_products():
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='dataicraft',
            user='odoo',
            password='odoo123'
        )
        cur = conn.cursor()
        
        print("🗑️ === DELETING ALL PRODUCTS === 🗑️")
        
        # Step 1: Delete all order-related data
        print("\n🗑️ Step 1: Deleting order data...")
        
        cur.execute("DELETE FROM sale_order_line;")
        print(f"✅ Deleted sales order lines")
        
        cur.execute("DELETE FROM purchase_order_line;")
        print(f"✅ Deleted purchase order lines")
        
        cur.execute("DELETE FROM sale_order;")
        print(f"✅ Deleted sales orders")
        
        cur.execute("DELETE FROM purchase_order;")
        print(f"✅ Deleted purchase orders")
        
        # Step 2: Delete accounting entries related to products
        print("\n🗑️ Step 2: Deleting accounting entries...")
        
        cur.execute("DELETE FROM account_move_line WHERE product_id IS NOT NULL;")
        deleted_aml = cur.rowcount
        print(f"✅ Deleted {deleted_aml} account move lines with products")
        
        cur.execute("DELETE FROM account_move WHERE id NOT IN (SELECT DISTINCT move_id FROM account_move_line WHERE move_id IS NOT NULL);")
        deleted_am = cur.rowcount
        print(f"✅ Deleted {deleted_am} empty account moves")
        
        # Step 3: Delete stock/inventory related data
        print("\n🗑️ Step 3: Deleting inventory data...")
        
        cur.execute("DELETE FROM stock_move WHERE product_id IS NOT NULL;")
        deleted_sm = cur.rowcount
        print(f"✅ Deleted {deleted_sm} stock moves")
        
        cur.execute("DELETE FROM stock_quant WHERE product_id IS NOT NULL;")
        deleted_sq = cur.rowcount
        print(f"✅ Deleted {deleted_sq} stock quants")
        
        # Step 4: Delete project/timesheet related data
        print("\n🗑️ Step 4: Deleting project/timesheet data...")
        
        cur.execute("DELETE FROM account_analytic_line WHERE product_id IS NOT NULL;")
        deleted_aal = cur.rowcount
        print(f"✅ Deleted {deleted_aal} analytic lines with products")
        
        # Step 5: Delete product variants first
        print("\n🗑️ Step 5: Deleting product variants...")
        
        cur.execute("SELECT COUNT(*) FROM product_product;")
        variant_count = cur.fetchone()[0]
        
        cur.execute("DELETE FROM product_product;")
        deleted_variants = cur.rowcount
        print(f"✅ Deleted {deleted_variants} product variants (was {variant_count})")
        
        # Step 6: Delete product templates
        print("\n🗑️ Step 6: Deleting product templates...")
        
        cur.execute("SELECT COUNT(*) FROM product_template;")
        template_count = cur.fetchone()[0]
        
        cur.execute("DELETE FROM product_template;")
        deleted_templates = cur.rowcount
        print(f"✅ Deleted {deleted_templates} product templates (was {template_count})")
        
        # Step 7: Commit and verify
        conn.commit()
        
        print("\n✅ Verification:")
        cur.execute("SELECT COUNT(*) FROM product_template;")
        remaining_templates = cur.fetchone()[0]
        
        cur.execute("SELECT COUNT(*) FROM product_product;")
        remaining_variants = cur.fetchone()[0]
        
        print(f"Remaining product templates: {remaining_templates}")
        print(f"Remaining product variants: {remaining_variants}")
        
        print(f"\n🎉 ALL PRODUCTS DELETED!")
        print(f"✅ Deleted all order data")
        print(f"✅ Deleted all accounting entries")
        print(f"✅ Deleted all inventory data")
        print(f"✅ Deleted all project/timesheet data")
        print(f"✅ Deleted all product variants")
        print(f"✅ Deleted all product templates")
        
        print(f"\n🔄 Next Steps:")
        print("1. Restart Odoo server")
        print("2. Try creating a new product")
        print("3. Try creating an order with the new product")
        print("4. See if the UoM error still occurs")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    delete_all_products()
