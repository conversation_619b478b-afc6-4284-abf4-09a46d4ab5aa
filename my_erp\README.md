# My ERP - Complete Accounting System

A comprehensive ERP accounting system built with Django + React, replicating Odoo's accounting module structure and business logic.

## 🚀 Features

### 📊 **Core Accounting Features (Based on Odoo)**
- **Chart of Accounts** - Complete account hierarchy with Odoo's account types
- **Journal Entries** - Double-entry bookkeeping system
- **Customer/Vendor Management** - Partner management like Odoo's res.partner
- **Invoicing** - Customer invoices with automatic journal entries
- **Bill Management** - Vendor bills processing
- **Payment Processing** - Customer and vendor payments
- **Bank Reconciliation** - Reconcile bank statements
- **Financial Reports** - Trial Balance, P&L, Balance Sheet

### 🏗️ **Technical Architecture**
- **Backend**: Django REST Framework
- **Frontend**: React with Ant Design
- **Database**: PostgreSQL
- **Models**: Replicated from Odoo's accounting module
- **API**: RESTful APIs matching Odoo's structure

## 📁 Project Structure

```
my_erp/
├── backend/                 # Django Backend
│   ├── erp_backend/        # Main Django project
│   ├── accounting/         # Accounting module (based on Odoo)
│   │   ├── models.py       # Odoo-style models
│   │   ├── views.py        # API views
│   │   ├── serializers.py  # DRF serializers
│   │   ├── admin.py        # Django admin
│   │   └── urls.py         # URL routing
│   ├── requirements.txt    # Python dependencies
│   └── manage.py          # Django management
├── frontend/               # React Frontend
│   ├── src/
│   │   ├── components/     # Reusable components
│   │   ├── pages/         # Page components
│   │   ├── services/      # API services
│   │   └── App.js         # Main app
│   ├── package.json       # Node dependencies
│   └── public/            # Static files
└── README.md              # This file
```

## 🛠️ Installation & Setup

### Prerequisites
- Python 3.8+
- Node.js 16+
- PostgreSQL 12+
- Redis (optional, for Celery)

### Backend Setup

1. **Create Virtual Environment**
```bash
cd my_erp/backend
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

2. **Install Dependencies**
```bash
pip install -r requirements.txt
```

3. **Database Setup**
```bash
# Create PostgreSQL database
createdb erp_accounting

# Run migrations
python manage.py makemigrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser
```

4. **Load Initial Data (Optional)**
```bash
python manage.py loaddata initial_chart_of_accounts.json
```

5. **Start Backend Server**
```bash
python manage.py runserver
```

### Frontend Setup

1. **Install Dependencies**
```bash
cd my_erp/frontend
npm install
```

2. **Start Frontend Server**
```bash
npm start
```

## 🔧 Configuration

### Environment Variables
Copy `.env.example` to `.env` and configure:

```env
SECRET_KEY=your-secret-key
DEBUG=True
DB_NAME=erp_accounting
DB_USER=postgres
DB_PASSWORD=your-password
DB_HOST=localhost
DB_PORT=5432
```

### Database Models (Based on Odoo)

#### Core Models
- **ResCompany** - Company information (res.company)
- **ResPartner** - Customers/Vendors (res.partner)
- **AccountAccount** - Chart of accounts (account.account)
- **AccountJournal** - Journals (account.journal)
- **AccountMove** - Journal entries/Invoices (account.move)
- **AccountMoveLine** - Journal entry lines (account.move.line)
- **AccountPayment** - Payments (account.payment)

## 📊 Usage

### 1. Dashboard
- View financial overview
- Key statistics and charts
- Recent transactions

### 2. Chart of Accounts
- Manage account hierarchy
- Account types based on Odoo
- Balance calculations

### 3. Journal Entries
- Create double-entry transactions
- Post/unpost entries
- Journal-based organization

### 4. Partners
- Customer management
- Vendor management
- Credit limits and payment terms

### 5. Invoicing
- Create customer invoices
- Automatic journal entries
- Payment tracking

### 6. Reports
- Trial Balance
- Profit & Loss Statement
- Balance Sheet
- Aged Receivables/Payables

## 🔌 API Endpoints

### Companies
- `GET /api/accounting/api/companies/` - List companies
- `POST /api/accounting/api/companies/` - Create company

### Partners
- `GET /api/accounting/api/partners/` - List partners
- `GET /api/accounting/api/partners/customers/` - List customers
- `GET /api/accounting/api/partners/vendors/` - List vendors

### Accounts
- `GET /api/accounting/api/accounts/` - List accounts
- `GET /api/accounting/api/accounts/receivable/` - Receivable accounts
- `GET /api/accounting/api/accounts/payable/` - Payable accounts

### Journal Entries
- `GET /api/accounting/api/moves/` - List moves
- `POST /api/accounting/api/moves/{id}/action_post/` - Post move
- `GET /api/accounting/api/moves/invoices/` - List invoices

### Reports
- `GET /api/accounting/api/reports/trial-balance/` - Trial balance
- `GET /api/accounting/api/reports/profit-loss/` - P&L statement
- `GET /api/accounting/api/reports/balance-sheet/` - Balance sheet

## 🎨 Frontend Features

### Components
- **Layout** - Main application layout with sidebar
- **Dashboard** - Financial overview with charts
- **Tables** - Data tables with filtering and sorting
- **Forms** - Create/edit forms with validation
- **Charts** - Financial data visualization

### Technologies
- **React 18** - Modern React with hooks
- **Ant Design** - Professional UI components
- **React Query** - Data fetching and caching
- **Axios** - HTTP client
- **Recharts** - Chart library

## 🔒 Security Features

- CORS configuration
- Authentication middleware
- Input validation
- SQL injection protection
- XSS protection

## 🚀 Deployment

### Backend Deployment
```bash
# Collect static files
python manage.py collectstatic

# Use production settings
export DJANGO_SETTINGS_MODULE=erp_backend.settings.production

# Run with Gunicorn
gunicorn erp_backend.wsgi:application
```

### Frontend Deployment
```bash
# Build for production
npm run build

# Serve with nginx or any static server
```

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📝 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- **Odoo** - For the excellent accounting module structure and business logic
- **Django** - For the robust backend framework
- **React** - For the modern frontend framework
- **Ant Design** - For the beautiful UI components

## 📞 Support

For support and questions:
- Create an issue on GitHub
- Email: <EMAIL>
- Documentation: [Wiki](https://github.com/yourrepo/wiki)

---

**Built with ❤️ using Django + React, inspired by Odoo's accounting excellence**
