<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="mail.ActivityListPopover">
        <div class="o-mail-ActivityListPopover d-flex flex-column" t-ref="root">
            <div class="overflow-y-auto d-flex flex-column flex-grow-1">
                <span t-if="activities.length === 0" class="p-3 text-center fst-italic text-500 border-bottom fs-6">Schedule activities to help you get things done.</span>
                <t t-if="overdueActivities.length > 0">
                    <div class="d-flex bg-100 py-2 border-bottom">
                        <span class="text-danger fw-bold mx-3">Overdue</span>
                        <span class="flex-grow-1"/>
                        <span class="badge rounded-pill text-bg-danger mx-3 align-self-center" t-esc="overdueActivities.length"/>
                    </div>
                    <ActivityListPopoverItem t-foreach="overdueActivities" t-as="activity" t-key="activity.id" activity="activity" onActivityChanged="props.onActivityChanged" onClickEditActivityButton="props.close" onClickDoneAndScheduleNext="props.close"/>
                </t>
                <t t-if="todayActivities.length > 0">
                    <div class="d-flex bg-100 py-2 border-bottom">
                        <span class="o-mail-ActivityListPopover-todayTitle text-warning fw-bold mx-3">Today</span>
                        <span class="flex-grow-1"/>
                        <span class="badge rounded-pill text-bg-warning mx-3 align-self-center" t-esc="todayActivities.length"/>
                    </div>
                    <ActivityListPopoverItem t-foreach="todayActivities" t-as="activity" t-key="activity.id" activity="activity" onActivityChanged="props.onActivityChanged" onClickEditActivityButton="props.close" onClickDoneAndScheduleNext="props.close"/>
                </t>
                <t t-if="plannedActivities.length > 0">
                    <div class="d-flex bg-100 py-2 border-bottom">
                        <span class="text-success fw-bold mx-3">Planned</span>
                        <span class="flex-grow-1"/>
                        <span class="badge rounded-pill text-bg-success mx-3 align-self-center" t-esc="plannedActivities.length"/>
                    </div>
                    <ActivityListPopoverItem t-foreach="plannedActivities" t-as="activity" t-key="activity.id" activity="activity" onActivityChanged="props.onActivityChanged" onClickEditActivityButton="props.close" onClickDoneAndScheduleNext="props.close"/>
                </t>
                <t t-if="doneActivities.length > 0">
                    <div class="o-mail-ActivityListPopover-done d-flex bg-100 py-2 border-bottom">
                        <span class="fw-bold mx-3">Done</span>
                        <span class="flex-grow-1"/>
                        <span class="badge rounded-pill text-bg-secondary mx-3 align-self-center" t-esc="doneActivities.length"/>
                    </div>
                    <ActivityListPopoverItem t-foreach="doneActivities" t-as="activity" t-key="activity.id" activity="activity"/>
                </t>
            </div>
            <button class="btn btn-secondary p-3 text-center" t-on-click="onClickAddActivityButton">
                <t t-if="props.resIds"><i class="fa fa-plus fa-fw"/><strong>Schedule an activity on selected records</strong></t>
                <t t-else=""><i class="fa fa-plus fa-fw"/><strong>Schedule an activity</strong></t>
            </button>
        </div>
    </t>
</templates>
