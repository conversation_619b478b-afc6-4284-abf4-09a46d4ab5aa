"""
Fixed Accounting Models for ERP System - Django Compatible
Based on Odoo's Accounting Module Structure
"""
from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import date, datetime
import uuid


class ResCompany(models.Model):
    """Company/Organization Information - Based on Odoo res.company"""
    name = models.CharField(max_length=200)
    legal_name = models.CharField(max_length=200, blank=True)
    
    # Basic Information
    street = models.CharField(max_length=128, blank=True)
    street2 = models.CharField(max_length=128, blank=True)
    city = models.CharField(max_length=128, blank=True)
    state_id = models.CharField(max_length=128, blank=True)
    zip = models.CharField(max_length=24, blank=True)
    country_id = models.CharField(max_length=2, default='PK')
    
    # Contact Information
    phone = models.CharField(max_length=32, blank=True)
    email = models.CharField(max_length=240, blank=True)
    website = models.CharField(max_length=64, blank=True)
    
    # Tax & Legal
    vat = models.CharField(max_length=32, blank=True, help_text="Tax ID")
    company_registry = models.CharField(max_length=64, blank=True)
    
    # Accounting Configuration
    currency_id = models.CharField(max_length=3, default='PKR')
    fiscalyear_last_day = models.IntegerField(default=31)
    fiscalyear_last_month = models.IntegerField(default=12)
    period_lock_date = models.DateField(null=True, blank=True)
    fiscalyear_lock_date = models.DateField(null=True, blank=True)
    
    # Chart of Accounts
    chart_template_id = models.CharField(max_length=64, blank=True)
    bank_account_code_prefix = models.CharField(max_length=32, default='1014')
    cash_account_code_prefix = models.CharField(max_length=32, default='1011')
    
    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name_plural = "Companies"
        db_table = 'res_company'

    def __str__(self):
        return self.name


class ResPartner(models.Model):
    """Partner Model - Based on Odoo res.partner (Customers/Vendors/Contacts)"""
    
    # Basic Information
    name = models.CharField(max_length=128, db_index=True)
    display_name = models.CharField(max_length=128, blank=True)
    ref = models.CharField(max_length=64, blank=True, help_text="Internal Reference")
    
    # Partner Type
    is_company = models.BooleanField(default=False)
    parent_id = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, db_index=True)
    
    # Contact Information
    street = models.CharField(max_length=128, blank=True)
    street2 = models.CharField(max_length=128, blank=True)
    city = models.CharField(max_length=128, blank=True)
    state_id = models.CharField(max_length=128, blank=True)
    zip = models.CharField(max_length=24, blank=True)
    country_id = models.CharField(max_length=2, default='PK')
    
    phone = models.CharField(max_length=32, blank=True)
    mobile = models.CharField(max_length=32, blank=True)
    email = models.CharField(max_length=240, blank=True)
    website = models.CharField(max_length=64, blank=True)
    
    # Business Information
    function = models.CharField(max_length=128, blank=True, help_text="Job Position")
    title = models.CharField(max_length=16, blank=True)
    
    # Accounting Fields
    customer_rank = models.IntegerField(default=0, help_text="Customer ranking for prioritization")
    supplier_rank = models.IntegerField(default=0, help_text="Vendor ranking for prioritization")
    
    # Credit Management
    credit_limit = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    
    # Tax Information
    vat = models.CharField(max_length=32, blank=True, help_text="Tax ID")
    
    # Status
    active = models.BooleanField(default=True)
    
    # Company
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)
    
    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'res_partner'
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['customer_rank']),
            models.Index(fields=['supplier_rank']),
        ]

    def __str__(self):
        return self.name

    @property
    def is_customer(self):
        return self.customer_rank > 0

    @property
    def is_vendor(self):
        return self.supplier_rank > 0


class AccountAccount(models.Model):
    """Chart of Accounts - Based on Odoo account.account"""
    
    # Account Types from Odoo
    ACCOUNT_TYPES = [
        ('asset_receivable', 'Receivable'),
        ('asset_cash', 'Bank and Cash'),
        ('asset_current', 'Current Assets'),
        ('asset_non_current', 'Non-current Assets'),
        ('asset_prepayments', 'Prepayments'),
        ('asset_fixed', 'Fixed Assets'),
        ('liability_payable', 'Payable'),
        ('liability_credit_card', 'Credit Card'),
        ('liability_current', 'Current Liabilities'),
        ('liability_non_current', 'Non-current Liabilities'),
        ('equity', 'Equity'),
        ('equity_unaffected', 'Current Year Earnings'),
        ('income', 'Income'),
        ('income_other', 'Other Income'),
        ('expense', 'Expenses'),
        ('expense_depreciation', 'Depreciation'),
        ('expense_direct_cost', 'Cost of Revenue'),
        ('off_balance', 'Off-Balance Sheet'),
    ]
    
    # Basic Fields
    name = models.CharField(max_length=128, db_index=True, help_text="Account Name")
    code = models.CharField(max_length=64, db_index=True, help_text="Account Code")
    account_type = models.CharField(max_length=64, choices=ACCOUNT_TYPES, db_index=True)
    
    # Configuration
    reconcile = models.BooleanField(default=False, help_text="Allow reconciliation of journal items")
    deprecated = models.BooleanField(default=False, help_text="Deprecated accounts are hidden by default")
    
    # Currency
    currency_id = models.CharField(max_length=3, blank=True, help_text="Forces all moves for this account to have this currency")
    
    # Notes
    note = models.TextField(blank=True, help_text="Internal notes")
    
    # Company
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)
    
    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'account_account'
        unique_together = [('code', 'company_id')]
        indexes = [
            models.Index(fields=['code', 'company_id']),
            models.Index(fields=['account_type']),
        ]
        ordering = ['code']

    def __str__(self):
        return f"{self.code} {self.name}"

    def clean(self):
        """Validation similar to Odoo's constraints"""
        if self.account_type in ('asset_receivable', 'liability_payable') and not self.reconcile:
            raise ValidationError('Receivable/Payable accounts must be reconcilable')

    @property
    def balance(self):
        """Calculate current balance - similar to Odoo's balance computation"""
        from django.db.models import Sum
        lines = self.move_line_ids.filter(move_id__state='posted')
        debit_sum = lines.aggregate(total=Sum('debit'))['total'] or Decimal('0')
        credit_sum = lines.aggregate(total=Sum('credit'))['total'] or Decimal('0')
        
        # Normal balance calculation based on account type
        if self.account_type.startswith('asset') or self.account_type.startswith('expense'):
            return debit_sum - credit_sum
        else:
            return credit_sum - debit_sum


class AccountJournal(models.Model):
    """Journal - Based on Odoo account.journal"""
    
    JOURNAL_TYPES = [
        ('sale', 'Sales'),
        ('purchase', 'Purchase'),
        ('cash', 'Cash'),
        ('bank', 'Bank'),
        ('general', 'Miscellaneous'),
    ]
    
    # Basic Information
    name = models.CharField(max_length=64)
    code = models.CharField(max_length=5)
    type = models.CharField(max_length=8, choices=JOURNAL_TYPES)
    
    # Configuration
    active = models.BooleanField(default=True)
    sequence = models.IntegerField(default=10, help_text="Used to order journals in the dashboard")
    
    # Accounts
    default_account_id = models.ForeignKey(AccountAccount, on_delete=models.RESTRICT, help_text="Default account for journal entries")
    
    # Company
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)
    
    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'account_journal'
        unique_together = [('code', 'company_id')]
        ordering = ['sequence', 'type', 'code']

    def __str__(self):
        return f"{self.code} - {self.name}"


class AccountTax(models.Model):
    """Tax Configuration - Based on Odoo account.tax"""

    TAX_TYPES = [
        ('sale', 'Sales'),
        ('purchase', 'Purchase'),
        ('none', 'None'),
    ]

    AMOUNT_TYPES = [
        ('group', 'Group of Taxes'),
        ('fixed', 'Fixed'),
        ('percent', 'Percentage of Price'),
        ('division', 'Percentage of Price Tax Included'),
    ]

    name = models.CharField(max_length=64)
    type_tax_use = models.CharField(max_length=8, choices=TAX_TYPES)
    amount_type = models.CharField(max_length=8, choices=AMOUNT_TYPES, default='percent')
    amount = models.DecimalField(max_digits=16, decimal_places=4, default=0)
    active = models.BooleanField(default=True)
    sequence = models.IntegerField(default=1)
    description = models.CharField(max_length=16, blank=True)

    # Accounts for tax posting
    invoice_repartition_line_ids = models.ManyToManyField('AccountTaxRepartitionLine', blank=True)
    refund_repartition_line_ids = models.ManyToManyField('AccountTaxRepartitionLine', blank=True, related_name='refund_tax_ids')

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'account_tax'

    def __str__(self):
        return f"{self.name} ({self.amount}%)"


class AccountTaxRepartitionLine(models.Model):
    """Tax Repartition Lines - Based on Odoo account.tax.repartition.line"""

    REPARTITION_TYPES = [
        ('base', 'Base'),
        ('tax', 'Tax'),
    ]

    factor_percent = models.DecimalField(max_digits=16, decimal_places=4, default=100)
    repartition_type = models.CharField(max_length=8, choices=REPARTITION_TYPES, default='tax')
    account_id = models.ForeignKey(AccountAccount, null=True, blank=True, on_delete=models.SET_NULL)
    invoice_tax_id = models.ForeignKey(AccountTax, null=True, blank=True, on_delete=models.CASCADE, related_name='invoice_repartition_lines')
    refund_tax_id = models.ForeignKey(AccountTax, null=True, blank=True, on_delete=models.CASCADE, related_name='refund_repartition_lines')
    sequence = models.IntegerField(default=1)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'account_tax_repartition_line'


class AccountMove(models.Model):
    """Journal Entry/Invoice - Based on Odoo account.move"""

    MOVE_TYPES = [
        ('entry', 'Journal Entry'),
        ('out_invoice', 'Customer Invoice'),
        ('out_refund', 'Customer Credit Note'),
        ('in_invoice', 'Vendor Bill'),
        ('in_refund', 'Vendor Credit Note'),
        ('out_receipt', 'Sales Receipt'),
        ('in_receipt', 'Purchase Receipt'),
    ]

    STATES = [
        ('draft', 'Draft'),
        ('posted', 'Posted'),
        ('cancel', 'Cancelled'),
    ]

    PAYMENT_STATES = [
        ('not_paid', 'Not Paid'),
        ('in_payment', 'In Payment'),
        ('paid', 'Paid'),
        ('partial', 'Partially Paid'),
        ('reversed', 'Reversed'),
        ('invoicing_legacy', 'Invoicing App Legacy'),
    ]

    # Basic Fields
    name = models.CharField(max_length=64, default='/')
    ref = models.CharField(max_length=64, blank=True)
    date = models.DateField(db_index=True)

    # Type and State
    move_type = models.CharField(max_length=16, choices=MOVE_TYPES, default='entry', db_index=True)
    state = models.CharField(max_length=8, choices=STATES, default='draft', db_index=True)

    # Journal and Company
    journal_id = models.ForeignKey(AccountJournal, on_delete=models.CASCADE, db_index=True)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE, db_index=True)

    # Partner (Customer/Vendor)
    partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.RESTRICT, db_index=True)
    commercial_partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.RESTRICT, related_name='commercial_moves')

    # Currency
    currency_id = models.CharField(max_length=3, default='PKR')

    # Invoice specific fields
    invoice_date = models.DateField(null=True, blank=True, db_index=True)
    invoice_date_due = models.DateField(null=True, blank=True, db_index=True)

    # Amounts
    amount_untaxed = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_tax = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_total = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_residual = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Payment
    payment_state = models.CharField(max_length=16, choices=PAYMENT_STATES, default='not_paid', db_index=True)
    payment_reference = models.CharField(max_length=64, blank=True)

    # Narration
    narration = models.TextField(blank=True)

    # Sequence
    sequence_number = models.IntegerField(default=0)
    sequence_prefix = models.CharField(max_length=64, blank=True)

    # Auto-posting
    auto_post = models.BooleanField(default=False)

    # Reversal
    reversed_entry_id = models.ForeignKey('self', null=True, blank=True, on_delete=models.SET_NULL)

    # User tracking
    create_uid = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_moves')
    write_uid = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='modified_moves')

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'account_move'
        indexes = [
            models.Index(fields=['date', 'journal_id']),
            models.Index(fields=['state', 'move_type']),
            models.Index(fields=['partner_id', 'state']),
        ]
        ordering = ['-date', '-name', '-invoice_date', '-id']

    def __str__(self):
        return self.name or 'Draft Move'

    @property
    def is_invoice(self):
        return self.move_type in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund')

    def action_post(self):
        """Post the journal entry - similar to Odoo's action_post"""
        if self.state != 'draft':
            return False

        # Validate the move
        if not self.line_ids.exists():
            raise ValidationError("Cannot post a move without journal items")

        # Check if balanced
        if not self._is_balanced():
            raise ValidationError("Journal entry is not balanced")

        # Generate sequence number if needed
        if self.name == '/':
            self.name = self._get_sequence()

        self.state = 'posted'
        self.save()
        return True

    def _is_balanced(self):
        """Check if the move is balanced"""
        total_debit = sum(line.debit for line in self.line_ids.all())
        total_credit = sum(line.credit for line in self.line_ids.all())
        return abs(total_debit - total_credit) < 0.01

    def _get_sequence(self):
        """Generate sequence number"""
        return f"{self.journal_id.code}/{datetime.now().year}/{self.id:05d}"


class AccountMoveLine(models.Model):
    """Journal Entry Lines - Based on Odoo account.move.line"""

    # Basic Fields
    name = models.CharField(max_length=64, help_text="Label")
    move_id = models.ForeignKey(AccountMove, on_delete=models.CASCADE, related_name='line_ids', db_index=True)

    # Account
    account_id = models.ForeignKey(AccountAccount, on_delete=models.RESTRICT, db_index=True, related_name='move_line_ids')

    # Partner
    partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.RESTRICT, db_index=True)

    # Amounts
    debit = models.DecimalField(max_digits=16, decimal_places=2, default=0, db_index=True)
    credit = models.DecimalField(max_digits=16, decimal_places=2, default=0, db_index=True)
    balance = models.DecimalField(max_digits=16, decimal_places=2, default=0, db_index=True)

    # Currency
    currency_id = models.CharField(max_length=3, blank=True)
    amount_currency = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Product (for invoice lines)
    quantity = models.DecimalField(max_digits=16, decimal_places=4, default=0)
    price_unit = models.DecimalField(max_digits=16, decimal_places=4, default=0)
    price_subtotal = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    price_total = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    discount = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Tax
    tax_ids = models.ManyToManyField(AccountTax, blank=True, related_name='move_line_tax_ids')
    tax_line_id = models.ForeignKey(AccountTax, null=True, blank=True, on_delete=models.RESTRICT, help_text="Tax line", related_name='tax_line_move_ids')
    tax_base_amount = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Reconciliation
    reconciled = models.BooleanField(default=False, db_index=True)
    full_reconcile_id = models.ForeignKey('AccountFullReconcile', null=True, blank=True, on_delete=models.SET_NULL)
    matching_number = models.CharField(max_length=32, blank=True, db_index=True)

    # Dates
    date = models.DateField(db_index=True)
    date_maturity = models.DateField(null=True, blank=True, db_index=True)

    # Reference
    ref = models.CharField(max_length=64, blank=True)

    # Sequence
    sequence = models.IntegerField(default=10)

    # Company
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE, db_index=True)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'account_move_line'
        indexes = [
            models.Index(fields=['account_id', 'date']),
            models.Index(fields=['partner_id', 'account_id']),
            models.Index(fields=['reconciled', 'account_id']),
            models.Index(fields=['date', 'move_id']),
        ]

    def __str__(self):
        return f"{self.move_id.name} - {self.name}"

    def clean(self):
        """Validation similar to Odoo's constraints"""
        if self.debit < 0:
            raise ValidationError("Debit amount cannot be negative")
        if self.credit < 0:
            raise ValidationError("Credit amount cannot be negative")
        if self.debit > 0 and self.credit > 0:
            raise ValidationError("A line cannot have both debit and credit amounts")

    def save(self, *args, **kwargs):
        # Calculate balance
        self.balance = self.debit - self.credit
        # Set date from move if not provided
        if not self.date:
            self.date = self.move_id.date
        super().save(*args, **kwargs)


class AccountFullReconcile(models.Model):
    """Full Reconciliation - Based on Odoo account.full.reconcile"""
    name = models.CharField(max_length=64)
    reconciled_line_ids = models.ManyToManyField(AccountMoveLine, related_name='full_reconcile_ids')
    exchange_move_id = models.ForeignKey(AccountMove, null=True, blank=True, on_delete=models.SET_NULL)

    class Meta:
        db_table = 'account_full_reconcile'

    def __str__(self):
        return self.name


class AccountPartialReconcile(models.Model):
    """Partial Reconciliation - Based on Odoo account.partial.reconcile"""
    debit_move_id = models.ForeignKey(AccountMoveLine, on_delete=models.CASCADE, related_name='debit_partial_reconcile_ids')
    credit_move_id = models.ForeignKey(AccountMoveLine, on_delete=models.CASCADE, related_name='credit_partial_reconcile_ids')
    amount = models.DecimalField(max_digits=16, decimal_places=2)
    amount_currency = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    currency_id = models.CharField(max_length=3, blank=True)
    full_reconcile_id = models.ForeignKey(AccountFullReconcile, null=True, blank=True, on_delete=models.CASCADE)

    class Meta:
        db_table = 'account_partial_reconcile'


class AccountPayment(models.Model):
    """Payment - Based on Odoo account.payment"""

    PAYMENT_TYPES = [
        ('outbound', 'Send Money'),
        ('inbound', 'Receive Money'),
    ]

    PARTNER_TYPES = [
        ('customer', 'Customer'),
        ('supplier', 'Vendor'),
    ]

    PAYMENT_METHODS = [
        ('manual', 'Manual'),
        ('electronic', 'Electronic'),
        ('check_printing', 'Check'),
    ]

    STATES = [
        ('draft', 'Draft'),
        ('posted', 'Validated'),
        ('sent', 'Sent'),
        ('reconciled', 'Reconciled'),
        ('cancelled', 'Cancelled'),
    ]

    # Basic Fields
    name = models.CharField(max_length=64, default='/')
    move_id = models.OneToOneField(AccountMove, on_delete=models.CASCADE, help_text="Journal Entry")

    # Payment Details
    payment_type = models.CharField(max_length=8, choices=PAYMENT_TYPES)
    partner_type = models.CharField(max_length=8, choices=PARTNER_TYPES)
    partner_id = models.ForeignKey(ResPartner, on_delete=models.RESTRICT)

    # Amount
    amount = models.DecimalField(max_digits=16, decimal_places=2)
    currency_id = models.CharField(max_length=3, default='PKR')

    # Journal
    journal_id = models.ForeignKey(AccountJournal, on_delete=models.RESTRICT)

    # Destination Account
    destination_account_id = models.ForeignKey(AccountAccount, on_delete=models.RESTRICT)

    # Outstanding Account
    outstanding_account_id = models.ForeignKey(AccountAccount, on_delete=models.RESTRICT, related_name='outstanding_payments')

    # State
    state = models.CharField(max_length=16, choices=STATES, default='draft')

    # Reconciliation
    reconciled_invoice_ids = models.ManyToManyField(AccountMove, blank=True, help_text="Invoices reconciled with this payment", related_name='reconciled_payments')
    reconciled_invoices_count = models.IntegerField(default=0)

    # Reference
    ref = models.CharField(max_length=64, blank=True)

    # Date
    date = models.DateField(default=date.today)

    # Company
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'account_payment'
        indexes = [
            models.Index(fields=['partner_id', 'state']),
            models.Index(fields=['date', 'journal_id']),
        ]

    def __str__(self):
        return self.name or 'Draft Payment'

    def action_post(self):
        """Post the payment"""
        if self.state != 'draft':
            return False

        # Post the related journal entry
        self.move_id.action_post()
        self.state = 'posted'
        self.save()
        return True


class AccountPaymentTerm(models.Model):
    """Payment Terms - Based on Odoo account.payment.term"""
    name = models.CharField(max_length=64)
    active = models.BooleanField(default=True)
    note = models.TextField(blank=True)
    sequence = models.IntegerField(default=10)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'account_payment_term'

    def __str__(self):
        return self.name


class AccountPaymentTermLine(models.Model):
    """Payment Term Lines - Based on Odoo account.payment.term.line"""

    VALUE_TYPES = [
        ('balance', 'Balance'),
        ('percent', 'Percent'),
        ('fixed', 'Fixed Amount'),
    ]

    payment_id = models.ForeignKey(AccountPaymentTerm, on_delete=models.CASCADE, related_name='line_ids')
    value = models.CharField(max_length=8, choices=VALUE_TYPES, default='balance')
    value_amount = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    days = models.IntegerField(default=0)
    sequence = models.IntegerField(default=10)

    class Meta:
        db_table = 'account_payment_term_line'


class AccountFiscalPosition(models.Model):
    """Fiscal Position - Based on Odoo account.fiscal.position"""
    name = models.CharField(max_length=64)
    active = models.BooleanField(default=True)
    auto_apply = models.BooleanField(default=False)
    vat_required = models.BooleanField(default=False)
    note = models.TextField(blank=True)
    sequence = models.IntegerField(default=10)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'account_fiscal_position'

    def __str__(self):
        return self.name


class AccountFiscalPositionTax(models.Model):
    """Fiscal Position Tax Mapping - Based on Odoo account.fiscal.position.tax"""
    position_id = models.ForeignKey(AccountFiscalPosition, on_delete=models.CASCADE, related_name='tax_ids')
    tax_src_id = models.ForeignKey(AccountTax, on_delete=models.CASCADE, related_name='fiscal_position_taxes_src')
    tax_dest_id = models.ForeignKey(AccountTax, null=True, blank=True, on_delete=models.CASCADE, related_name='fiscal_position_taxes_dest')

    class Meta:
        db_table = 'account_fiscal_position_tax'


class AccountFiscalPositionAccount(models.Model):
    """Fiscal Position Account Mapping - Based on Odoo account.fiscal.position.account"""
    position_id = models.ForeignKey(AccountFiscalPosition, on_delete=models.CASCADE, related_name='account_ids')
    account_src_id = models.ForeignKey(AccountAccount, on_delete=models.CASCADE, related_name='fiscal_position_accounts_src')
    account_dest_id = models.ForeignKey(AccountAccount, on_delete=models.CASCADE, related_name='fiscal_position_accounts_dest')

    class Meta:
        db_table = 'account_fiscal_position_account'


class AccountAnalyticAccount(models.Model):
    """Analytic Account - Based on Odoo account.analytic.account"""
    name = models.CharField(max_length=128)
    code = models.CharField(max_length=32, blank=True)
    active = models.BooleanField(default=True)
    partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.SET_NULL)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'account_analytic_account'

    def __str__(self):
        return self.name


class AccountAnalyticLine(models.Model):
    """Analytic Lines - Based on Odoo account.analytic.line"""
    name = models.CharField(max_length=256)
    date = models.DateField(db_index=True)
    amount = models.DecimalField(max_digits=16, decimal_places=2)
    unit_amount = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    account_id = models.ForeignKey(AccountAnalyticAccount, on_delete=models.CASCADE, related_name='line_ids')
    partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.SET_NULL)
    move_id = models.ForeignKey(AccountMove, null=True, blank=True, on_delete=models.CASCADE)
    general_account_id = models.ForeignKey(AccountAccount, on_delete=models.RESTRICT)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'account_analytic_line'
