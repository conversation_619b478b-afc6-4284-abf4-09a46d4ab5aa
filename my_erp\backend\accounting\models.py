"""
Comprehensive Accounting Models for ERP System
Based on Odoo's Accounting Module Structure
"""
from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import date, datetime
import uuid


class ResCompany(models.Model):
    """Company/Organization Information - Based on Odoo res.company"""
    name = models.CharField(max_length=200, required=True)
    legal_name = models.CharField(max_length=200, blank=True)
    partner_id = models.OneToOneField('ResPartner', on_delete=models.CASCADE, null=True, blank=True)

    # Basic Information
    street = models.CharField(max_length=128, blank=True)
    street2 = models.CharField(max_length=128, blank=True)
    city = models.CharField(max_length=128, blank=True)
    state_id = models.CharField(max_length=128, blank=True)
    zip = models.CharField(max_length=24, blank=True)
    country_id = models.CharField(max_length=2, default='PK')

    # Contact Information
    phone = models.CharField(max_length=32, blank=True)
    email = models.CharField(max_length=240, blank=True)
    website = models.CharField(max_length=64, blank=True)

    # Tax & Legal
    vat = models.CharField(max_length=32, blank=True, help_text="Tax ID")
    company_registry = models.CharField(max_length=64, blank=True)

    # Accounting Configuration
    currency_id = models.CharField(max_length=3, default='PKR')
    fiscalyear_last_day = models.IntegerField(default=31)
    fiscalyear_last_month = models.IntegerField(default=12)
    period_lock_date = models.DateField(null=True, blank=True)
    fiscalyear_lock_date = models.DateField(null=True, blank=True)

    # Chart of Accounts
    chart_template_id = models.CharField(max_length=64, blank=True)
    bank_account_code_prefix = models.CharField(max_length=32, default='1014')
    cash_account_code_prefix = models.CharField(max_length=32, default='1011')

    # Default Accounts
    default_cash_difference_income_account_id = models.ForeignKey('AccountAccount', null=True, blank=True, on_delete=models.SET_NULL, related_name='cash_income_companies')
    default_cash_difference_expense_account_id = models.ForeignKey('AccountAccount', null=True, blank=True, on_delete=models.SET_NULL, related_name='cash_expense_companies')

    # Sequence Configuration
    internal_transit_location_id = models.CharField(max_length=64, blank=True)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Companies"
        db_table = 'res_company'

    def __str__(self):
        return self.name


class ResPartner(models.Model):
    """Partner Model - Based on Odoo res.partner (Customers/Vendors/Contacts)"""

    # Basic Information
    name = models.CharField(max_length=128, required=True, index=True)
    display_name = models.CharField(max_length=128, blank=True)
    ref = models.CharField(max_length=64, blank=True, help_text="Internal Reference")

    # Partner Type
    is_company = models.BooleanField(default=False)
    parent_id = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, index=True)
    child_ids = models.ManyToManyField('self', blank=True, symmetrical=False)

    # Contact Information
    street = models.CharField(max_length=128, blank=True)
    street2 = models.CharField(max_length=128, blank=True)
    city = models.CharField(max_length=128, blank=True)
    state_id = models.CharField(max_length=128, blank=True)
    zip = models.CharField(max_length=24, blank=True)
    country_id = models.CharField(max_length=2, default='PK')

    phone = models.CharField(max_length=32, blank=True)
    mobile = models.CharField(max_length=32, blank=True)
    email = models.CharField(max_length=240, blank=True)
    website = models.CharField(max_length=64, blank=True)

    # Business Information
    function = models.CharField(max_length=128, blank=True, help_text="Job Position")
    title = models.CharField(max_length=16, blank=True)

    # Accounting Fields
    customer_rank = models.IntegerField(default=0, help_text="Customer ranking for prioritization")
    supplier_rank = models.IntegerField(default=0, help_text="Vendor ranking for prioritization")

    # Financial Information
    property_account_receivable_id = models.ForeignKey('AccountAccount', null=True, blank=True, on_delete=models.SET_NULL, related_name='receivable_partners')
    property_account_payable_id = models.ForeignKey('AccountAccount', null=True, blank=True, on_delete=models.SET_NULL, related_name='payable_partners')
    property_payment_term_id = models.ForeignKey('AccountPaymentTerm', null=True, blank=True, on_delete=models.SET_NULL, related_name='customer_partners')
    property_supplier_payment_term_id = models.ForeignKey('AccountPaymentTerm', null=True, blank=True, on_delete=models.SET_NULL, related_name='supplier_partners')

    # Credit Management
    credit_limit = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Tax Information
    vat = models.CharField(max_length=32, blank=True, help_text="Tax ID")

    # Status
    active = models.BooleanField(default=True)

    # Company
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE, default=1)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'res_partner'
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['customer_rank']),
            models.Index(fields=['supplier_rank']),
        ]

    def __str__(self):
        return self.name

    @property
    def is_customer(self):
        return self.customer_rank > 0

    @property
    def is_vendor(self):
        return self.supplier_rank > 0

    def _compute_debit_credit(self):
        """Compute total debit and credit for this partner"""
        # This would be implemented with actual accounting calculations
        pass


class AccountAccount(models.Model):
    """Chart of Accounts - Based on Odoo account.account"""

    # Account Types from Odoo
    ACCOUNT_TYPES = [
        ('asset_receivable', 'Receivable'),
        ('asset_cash', 'Bank and Cash'),
        ('asset_current', 'Current Assets'),
        ('asset_non_current', 'Non-current Assets'),
        ('asset_prepayments', 'Prepayments'),
        ('asset_fixed', 'Fixed Assets'),
        ('liability_payable', 'Payable'),
        ('liability_credit_card', 'Credit Card'),
        ('liability_current', 'Current Liabilities'),
        ('liability_non_current', 'Non-current Liabilities'),
        ('equity', 'Equity'),
        ('equity_unaffected', 'Current Year Earnings'),
        ('income', 'Income'),
        ('income_other', 'Other Income'),
        ('expense', 'Expenses'),
        ('expense_depreciation', 'Depreciation'),
        ('expense_direct_cost', 'Cost of Revenue'),
        ('off_balance', 'Off-Balance Sheet'),
    ]

    # Basic Fields
    name = models.CharField(max_length=128, required=True, index=True, help_text="Account Name")
    code = models.CharField(max_length=64, required=True, index=True, help_text="Account Code")
    account_type = models.CharField(max_length=64, choices=ACCOUNT_TYPES, required=True, index=True)

    # Hierarchy
    group_id = models.ForeignKey('AccountGroup', null=True, blank=True, on_delete=models.CASCADE)
    root_id = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, help_text="Root account of the hierarchy")

    # Configuration
    reconcile = models.BooleanField(default=False, help_text="Allow reconciliation of journal items")
    deprecated = models.BooleanField(default=False, help_text="Deprecated accounts are hidden by default")

    # Currency
    currency_id = models.CharField(max_length=3, blank=True, help_text="Forces all moves for this account to have this currency")

    # Default Taxes
    tax_ids = models.ManyToManyField('AccountTax', blank=True, help_text="Default taxes for this account")

    # Notes
    note = models.TextField(blank=True, help_text="Internal notes")

    # Company
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE, required=True)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'account_account'
        unique_together = [('code', 'company_id')]
        indexes = [
            models.Index(fields=['code', 'company_id']),
            models.Index(fields=['account_type']),
        ]
        ordering = ['code']

    def __str__(self):
        return f"{self.code} {self.name}"

    def clean(self):
        """Validation similar to Odoo's constraints"""
        if self.account_type in ('asset_receivable', 'liability_payable') and not self.reconcile:
            raise ValidationError('Receivable/Payable accounts must be reconcilable')

    @property
    def balance(self):
        """Calculate current balance - similar to Odoo's balance computation"""
        from django.db.models import Sum
        lines = self.move_line_ids.filter(move_id__state='posted')
        debit_sum = lines.aggregate(total=Sum('debit'))['total'] or Decimal('0')
        credit_sum = lines.aggregate(total=Sum('credit'))['total'] or Decimal('0')

        # Normal balance calculation based on account type
        if self.account_type.startswith('asset') or self.account_type.startswith('expense'):
            return debit_sum - credit_sum
        else:
            return credit_sum - debit_sum


class AccountGroup(models.Model):
    """Account Groups - Based on Odoo account.group"""
    name = models.CharField(max_length=128, required=True)
    code_prefix_start = models.CharField(max_length=64, required=True)
    code_prefix_end = models.CharField(max_length=64, required=True)
    parent_id = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE, required=True)

    class Meta:
        db_table = 'account_group'
        unique_together = [('code_prefix_start', 'company_id')]

    def __str__(self):
        return f"{self.code_prefix_start} {self.name}"


class AccountJournal(models.Model):
    """Journal - Based on Odoo account.journal"""

    JOURNAL_TYPES = [
        ('sale', 'Sales'),
        ('purchase', 'Purchase'),
        ('cash', 'Cash'),
        ('bank', 'Bank'),
        ('general', 'Miscellaneous'),
    ]

    # Basic Information
    name = models.CharField(max_length=64, required=True)
    code = models.CharField(max_length=5, required=True)
    type = models.CharField(max_length=8, choices=JOURNAL_TYPES, required=True)

    # Configuration
    active = models.BooleanField(default=True)
    sequence = models.IntegerField(default=10, help_text="Used to order journals in the dashboard")

    # Accounts
    default_account_id = models.ForeignKey(AccountAccount, on_delete=models.RESTRICT, required=True, help_text="Default account for journal entries")
    suspense_account_id = models.ForeignKey(AccountAccount, on_delete=models.RESTRICT, null=True, blank=True, related_name='suspense_journals')

    # Sequences
    sequence_override_regex = models.CharField(max_length=64, blank=True)

    # Restrictions
    account_control_ids = models.ManyToManyField(AccountAccount, blank=True, help_text="Allowed accounts for this journal")

    # Bank/Cash specific
    bank_account_id = models.ForeignKey('ResPartnerBank', null=True, blank=True, on_delete=models.SET_NULL)
    bank_statements_source = models.CharField(max_length=64, default='undefined')

    # Company
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE, required=True)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'account_journal'
        unique_together = [('code', 'company_id')]
        ordering = ['sequence', 'type', 'code']

    def __str__(self):
        return f"{self.code} - {self.name}"


class ResPartnerBank(models.Model):
    """Partner Bank Accounts - Based on Odoo res.partner.bank"""
    acc_number = models.CharField(max_length=64, required=True, help_text="Account Number")
    partner_id = models.ForeignKey(ResPartner, on_delete=models.CASCADE, required=True)
    bank_id = models.ForeignKey('ResBank', null=True, blank=True, on_delete=models.CASCADE)
    sequence = models.IntegerField(default=10)
    currency_id = models.CharField(max_length=3, blank=True)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE, required=True)

    class Meta:
        db_table = 'res_partner_bank'

    def __str__(self):
        return f"{self.partner_id.name} - {self.acc_number}"


class ResBank(models.Model):
    """Banks - Based on Odoo res.bank"""
    name = models.CharField(max_length=128, required=True)
    street = models.CharField(max_length=128, blank=True)
    city = models.CharField(max_length=128, blank=True)
    country = models.CharField(max_length=2, blank=True)
    bic = models.CharField(max_length=11, blank=True, help_text="Bank Identifier Code")

    class Meta:
        db_table = 'res_bank'

    def __str__(self):
        return self.name


class AccountPaymentTerm(models.Model):
    """Payment Terms - Based on Odoo account.payment.term"""
    name = models.CharField(max_length=64, required=True)
    active = models.BooleanField(default=True)
    note = models.TextField(blank=True)
    sequence = models.IntegerField(default=10)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE, required=True)

    class Meta:
        db_table = 'account_payment_term'

    def __str__(self):
        return self.name


class AccountTax(models.Model):
    """Taxes - Based on Odoo account.tax"""

    TAX_TYPES = [
        ('sale', 'Sales'),
        ('purchase', 'Purchase'),
        ('none', 'None'),
    ]

    name = models.CharField(max_length=64, required=True)
    type_tax_use = models.CharField(max_length=8, choices=TAX_TYPES, required=True)
    amount_type = models.CharField(max_length=8, default='percent')
    amount = models.DecimalField(max_digits=16, decimal_places=4, default=0)
    active = models.BooleanField(default=True)
    sequence = models.IntegerField(default=1)
    description = models.CharField(max_length=16, blank=True)

    # Accounts
    invoice_repartition_line_ids = models.ManyToManyField('AccountTaxRepartitionLine', blank=True)
    refund_repartition_line_ids = models.ManyToManyField('AccountTaxRepartitionLine', blank=True, related_name='refund_tax_ids')

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE, required=True)

    class Meta:
        db_table = 'account_tax'

    def __str__(self):
        return self.name


class AccountTaxRepartitionLine(models.Model):
    """Tax Repartition Lines - Based on Odoo account.tax.repartition.line"""
    factor_percent = models.DecimalField(max_digits=16, decimal_places=4, default=100)
    repartition_type = models.CharField(max_length=8, default='tax')
    account_id = models.ForeignKey(AccountAccount, null=True, blank=True, on_delete=models.SET_NULL)
    invoice_tax_id = models.ForeignKey(AccountTax, null=True, blank=True, on_delete=models.CASCADE)
    refund_tax_id = models.ForeignKey(AccountTax, null=True, blank=True, on_delete=models.CASCADE)
    sequence = models.IntegerField(default=1)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE, required=True)

    class Meta:
        db_table = 'account_tax_repartition_line'


class AccountMove(models.Model):
    """Journal Entry/Invoice - Based on Odoo account.move"""

    MOVE_TYPES = [
        ('entry', 'Journal Entry'),
        ('out_invoice', 'Customer Invoice'),
        ('out_refund', 'Customer Credit Note'),
        ('in_invoice', 'Vendor Bill'),
        ('in_refund', 'Vendor Credit Note'),
        ('out_receipt', 'Sales Receipt'),
        ('in_receipt', 'Purchase Receipt'),
    ]

    STATES = [
        ('draft', 'Draft'),
        ('posted', 'Posted'),
        ('cancel', 'Cancelled'),
    ]

    PAYMENT_STATES = [
        ('not_paid', 'Not Paid'),
        ('in_payment', 'In Payment'),
        ('paid', 'Paid'),
        ('partial', 'Partially Paid'),
        ('reversed', 'Reversed'),
        ('invoicing_legacy', 'Invoicing App Legacy'),
    ]

    # Basic Fields
    name = models.CharField(max_length=64, default='/', help_text="Entry Number")
    ref = models.CharField(max_length=64, blank=True, help_text="Reference/Description")
    date = models.DateField(required=True, index=True)

    # Type and State
    move_type = models.CharField(max_length=16, choices=MOVE_TYPES, default='entry', required=True, index=True)
    state = models.CharField(max_length=8, choices=STATES, default='draft', required=True, index=True)

    # Journal and Company
    journal_id = models.ForeignKey(AccountJournal, on_delete=models.CASCADE, required=True, index=True)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE, required=True, index=True)

    # Partner (Customer/Vendor)
    partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.RESTRICT, index=True)
    commercial_partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.RESTRICT, related_name='commercial_moves')

    # Currency
    currency_id = models.CharField(max_length=3, required=True, default='PKR')

    # Invoice specific fields
    invoice_date = models.DateField(null=True, blank=True, index=True)
    invoice_date_due = models.DateField(null=True, blank=True, index=True)
    invoice_payment_term_id = models.ForeignKey(AccountPaymentTerm, null=True, blank=True, on_delete=models.RESTRICT)

    # Payment
    payment_state = models.CharField(max_length=16, choices=PAYMENT_STATES, default='not_paid', index=True)
    payment_reference = models.CharField(max_length=64, blank=True)

    # Amounts
    amount_untaxed = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_tax = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_total = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_residual = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_untaxed_signed = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_tax_signed = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_total_signed = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_residual_signed = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Narration
    narration = models.TextField(blank=True, help_text="Internal Note")

    # Sequence
    sequence_number = models.IntegerField(default=0)
    sequence_prefix = models.CharField(max_length=64, blank=True)

    # Auto-posting
    auto_post = models.BooleanField(default=False)

    # Reversal
    reversed_entry_id = models.ForeignKey('self', null=True, blank=True, on_delete=models.SET_NULL)

    # User tracking
    create_uid = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_moves')
    write_uid = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='modified_moves')

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'account_move'
        indexes = [
            models.Index(fields=['date', 'journal_id']),
            models.Index(fields=['state', 'move_type']),
            models.Index(fields=['partner_id', 'state']),
        ]
        ordering = ['-date', '-name', '-invoice_date', '-id']

    def __str__(self):
        return self.name or 'Draft Move'

    @property
    def is_invoice(self):
        return self.move_type in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund')

    def action_post(self):
        """Post the journal entry - similar to Odoo's action_post"""
        if self.state != 'draft':
            return False

        # Validate the move
        if not self.line_ids.exists():
            raise ValidationError("Cannot post a move without journal items")

        # Check if balanced
        if not self._is_balanced():
            raise ValidationError("Journal entry is not balanced")

        # Generate sequence number if needed
        if self.name == '/':
            self.name = self._get_sequence()

        self.state = 'posted'
        self.save()
        return True

    def _is_balanced(self):
        """Check if the move is balanced"""
        total_debit = sum(line.debit for line in self.line_ids.all())
        total_credit = sum(line.credit for line in self.line_ids.all())
        return abs(total_debit - total_credit) < 0.01

    def _get_sequence(self):
        """Generate sequence number"""
        # Simplified sequence generation
        return f"{self.journal_id.code}/{datetime.now().year}/{self.id:05d}"


class AccountMoveLine(models.Model):
    """Journal Entry Lines - Based on Odoo account.move.line"""

    # Basic Fields
    name = models.CharField(max_length=64, required=True, help_text="Label")
    move_id = models.ForeignKey(AccountMove, on_delete=models.CASCADE, required=True, related_name='line_ids', index=True)

    # Account
    account_id = models.ForeignKey(AccountAccount, on_delete=models.RESTRICT, required=True, index=True, related_name='move_line_ids')

    # Partner
    partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.RESTRICT, index=True)

    # Amounts
    debit = models.DecimalField(max_digits=16, decimal_places=2, default=0, index=True)
    credit = models.DecimalField(max_digits=16, decimal_places=2, default=0, index=True)
    balance = models.DecimalField(max_digits=16, decimal_places=2, default=0, index=True)

    # Currency
    currency_id = models.CharField(max_length=3, blank=True)
    amount_currency = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Product (for invoice lines)
    product_id = models.ForeignKey('ProductProduct', null=True, blank=True, on_delete=models.SET_NULL)
    product_uom_id = models.ForeignKey('UomUom', null=True, blank=True, on_delete=models.SET_NULL)
    quantity = models.DecimalField(max_digits=16, decimal_places=4, default=0)
    price_unit = models.DecimalField(max_digits=16, decimal_places=4, default=0)
    price_subtotal = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    price_total = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    discount = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Tax
    tax_ids = models.ManyToManyField(AccountTax, blank=True)
    tax_line_id = models.ForeignKey(AccountTax, null=True, blank=True, on_delete=models.RESTRICT, help_text="Tax line")
    tax_base_amount = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Reconciliation
    reconciled = models.BooleanField(default=False, index=True)
    full_reconcile_id = models.ForeignKey('AccountFullReconcile', null=True, blank=True, on_delete=models.SET_NULL)
    matching_number = models.CharField(max_length=32, blank=True, index=True)

    # Dates
    date = models.DateField(required=True, index=True)
    date_maturity = models.DateField(null=True, blank=True, index=True)

    # Reference
    ref = models.CharField(max_length=64, blank=True)

    # Sequence
    sequence = models.IntegerField(default=10)

    # Company
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE, required=True, index=True)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'account_move_line'
        indexes = [
            models.Index(fields=['account_id', 'date']),
            models.Index(fields=['partner_id', 'account_id']),
            models.Index(fields=['reconciled', 'account_id']),
            models.Index(fields=['date', 'move_id']),
        ]

    def __str__(self):
        return f"{self.move_id.name} - {self.name}"

    def clean(self):
        """Validation similar to Odoo's constraints"""
        if self.debit < 0:
            raise ValidationError("Debit amount cannot be negative")
        if self.credit < 0:
            raise ValidationError("Credit amount cannot be negative")
        if self.debit > 0 and self.credit > 0:
            raise ValidationError("A line cannot have both debit and credit amounts")

    def save(self, *args, **kwargs):
        # Calculate balance
        self.balance = self.debit - self.credit
        super().save(*args, **kwargs)


class AccountFullReconcile(models.Model):
    """Full Reconciliation - Based on Odoo account.full.reconcile"""
    name = models.CharField(max_length=64, required=True)
    reconciled_line_ids = models.ManyToManyField(AccountMoveLine, related_name='full_reconcile_ids')
    exchange_move_id = models.ForeignKey(AccountMove, null=True, blank=True, on_delete=models.SET_NULL)

    class Meta:
        db_table = 'account_full_reconcile'

    def __str__(self):
        return self.name


class AccountPartialReconcile(models.Model):
    """Partial Reconciliation - Based on Odoo account.partial.reconcile"""
    debit_move_id = models.ForeignKey(AccountMoveLine, on_delete=models.CASCADE, related_name='debit_partial_reconcile_ids')
    credit_move_id = models.ForeignKey(AccountMoveLine, on_delete=models.CASCADE, related_name='credit_partial_reconcile_ids')
    amount = models.DecimalField(max_digits=16, decimal_places=2, required=True)
    amount_currency = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    currency_id = models.CharField(max_length=3, blank=True)
    full_reconcile_id = models.ForeignKey(AccountFullReconcile, null=True, blank=True, on_delete=models.CASCADE)

    class Meta:
        db_table = 'account_partial_reconcile'


# Placeholder models for references
class ProductProduct(models.Model):
    """Product - Placeholder"""
    name = models.CharField(max_length=128)

    class Meta:
        db_table = 'product_product'

    def __str__(self):
        return self.name


class UomUom(models.Model):
    """Unit of Measure - Placeholder"""
    name = models.CharField(max_length=64)

    class Meta:
        db_table = 'uom_uom'

    def __str__(self):
        return self.name


class AccountPayment(models.Model):
    """Payment - Based on Odoo account.payment"""

    PAYMENT_TYPES = [
        ('outbound', 'Send Money'),
        ('inbound', 'Receive Money'),
    ]

    PARTNER_TYPES = [
        ('customer', 'Customer'),
        ('supplier', 'Vendor'),
    ]

    PAYMENT_METHODS = [
        ('manual', 'Manual'),
        ('electronic', 'Electronic'),
        ('check_printing', 'Check'),
    ]

    STATES = [
        ('draft', 'Draft'),
        ('posted', 'Validated'),
        ('sent', 'Sent'),
        ('reconciled', 'Reconciled'),
        ('cancelled', 'Cancelled'),
    ]

    # Basic Fields
    name = models.CharField(max_length=64, default='/', help_text="Payment Reference")
    move_id = models.OneToOneField(AccountMove, on_delete=models.CASCADE, required=True, help_text="Journal Entry")

    # Payment Details
    payment_type = models.CharField(max_length=8, choices=PAYMENT_TYPES, required=True)
    partner_type = models.CharField(max_length=8, choices=PARTNER_TYPES, required=True)
    partner_id = models.ForeignKey(ResPartner, on_delete=models.RESTRICT, required=True)

    # Amount
    amount = models.DecimalField(max_digits=16, decimal_places=2, required=True)
    currency_id = models.CharField(max_length=3, required=True, default='PKR')

    # Payment Method
    payment_method_line_id = models.ForeignKey('AccountPaymentMethodLine', on_delete=models.RESTRICT, required=True)
    payment_method_code = models.CharField(max_length=32, blank=True)

    # Journal
    journal_id = models.ForeignKey(AccountJournal, on_delete=models.RESTRICT, required=True)

    # Destination Account
    destination_account_id = models.ForeignKey(AccountAccount, on_delete=models.RESTRICT, required=True)

    # Outstanding Account
    outstanding_account_id = models.ForeignKey(AccountAccount, on_delete=models.RESTRICT, required=True)

    # State
    state = models.CharField(max_length=16, choices=STATES, default='draft')

    # Reconciliation
    reconciled_invoice_ids = models.ManyToManyField(AccountMove, blank=True, help_text="Invoices reconciled with this payment")
    reconciled_invoices_count = models.IntegerField(default=0)

    # Reference
    ref = models.CharField(max_length=64, blank=True, help_text="Memo")

    # Date
    date = models.DateField(required=True, default=date.today)

    # Company
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE, required=True)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'account_payment'
        indexes = [
            models.Index(fields=['partner_id', 'state']),
            models.Index(fields=['date', 'journal_id']),
        ]

    def __str__(self):
        return self.name or 'Draft Payment'

    def action_post(self):
        """Post the payment"""
        if self.state != 'draft':
            return False

        # Post the related journal entry
        self.move_id.action_post()
        self.state = 'posted'
        self.save()
        return True


class AccountPaymentMethod(models.Model):
    """Payment Methods - Based on Odoo account.payment.method"""

    PAYMENT_TYPES = [
        ('inbound', 'Inbound'),
        ('outbound', 'Outbound'),
    ]

    name = models.CharField(max_length=64, required=True)
    code = models.CharField(max_length=32, required=True, unique=True)
    payment_type = models.CharField(max_length=8, choices=PAYMENT_TYPES, required=True)

    class Meta:
        db_table = 'account_payment_method'

    def __str__(self):
        return self.name


class AccountPaymentMethodLine(models.Model):
    """Payment Method Lines - Based on Odoo account.payment.method.line"""
    name = models.CharField(max_length=64, required=True)
    payment_method_id = models.ForeignKey(AccountPaymentMethod, on_delete=models.CASCADE, required=True)
    journal_id = models.ForeignKey(AccountJournal, on_delete=models.CASCADE, required=True)
    sequence = models.IntegerField(default=10)

    class Meta:
        db_table = 'account_payment_method_line'
        unique_together = [('payment_method_id', 'journal_id')]

    def __str__(self):
        return f"{self.journal_id.name} - {self.name}"


class TaxRate(models.Model):
    """Tax Configuration"""
    name = models.CharField(max_length=100)
    rate = models.DecimalField(max_digits=5, decimal_places=2, 
                              validators=[MinValueValidator(0), MaxValueValidator(100)])
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.name} ({self.rate}%)"


class Journal(models.Model):
    """Journal/Book Configuration"""
    JOURNAL_TYPES = [
        ('GENERAL', 'General Journal'),
        ('SALES', 'Sales Journal'),
        ('PURCHASE', 'Purchase Journal'),
        ('CASH', 'Cash Journal'),
        ('BANK', 'Bank Journal'),
    ]
    
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=10, unique=True)
    type = models.CharField(max_length=20, choices=JOURNAL_TYPES)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.code} - {self.name}"


class JournalEntry(models.Model):
    """Journal Entry Header"""
    ENTRY_STATES = [
        ('DRAFT', 'Draft'),
        ('POSTED', 'Posted'),
        ('CANCELLED', 'Cancelled'),
    ]
    
    reference = models.CharField(max_length=50, unique=True)
    journal = models.ForeignKey(Journal, on_delete=models.CASCADE)
    date = models.DateField()
    description = models.TextField()
    state = models.CharField(max_length=20, choices=ENTRY_STATES, default='DRAFT')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Journal Entries"
        ordering = ['-date', '-created_at']

    def __str__(self):
        return f"{self.reference} - {self.date}"

    @property
    def total_debit(self):
        return sum(line.debit_amount for line in self.lines.all())

    @property
    def total_credit(self):
        return sum(line.credit_amount for line in self.lines.all())

    @property
    def is_balanced(self):
        return self.total_debit == self.total_credit

    def post(self):
        """Post the journal entry"""
        if self.is_balanced and self.state == 'DRAFT':
            self.state = 'POSTED'
            self.save()
            return True
        return False


class JournalEntryLine(models.Model):
    """Journal Entry Line Items"""
    entry = models.ForeignKey(JournalEntry, related_name='lines', on_delete=models.CASCADE)
    account = models.ForeignKey(Account, related_name='journal_entries', on_delete=models.CASCADE)
    description = models.CharField(max_length=200)
    debit_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    credit_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    partner = models.CharField(max_length=200, blank=True, help_text="Customer/Vendor name")

    def __str__(self):
        return f"{self.entry.reference} - {self.account.code}"

    def clean(self):
        from django.core.exceptions import ValidationError
        if self.debit_amount > 0 and self.credit_amount > 0:
            raise ValidationError("A line cannot have both debit and credit amounts")
        if self.debit_amount == 0 and self.credit_amount == 0:
            raise ValidationError("A line must have either debit or credit amount")


class Invoice(models.Model):
    """Customer Invoices"""
    INVOICE_STATES = [
        ('DRAFT', 'Draft'),
        ('SENT', 'Sent'),
        ('PARTIAL', 'Partially Paid'),
        ('PAID', 'Paid'),
        ('CANCELLED', 'Cancelled'),
    ]

    number = models.CharField(max_length=50, unique=True)
    customer = models.ForeignKey(Customer, related_name='invoices', on_delete=models.CASCADE)
    date = models.DateField()
    due_date = models.DateField()
    description = models.TextField(blank=True)
    subtotal = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    paid_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    status = models.CharField(max_length=20, choices=INVOICE_STATES, default='DRAFT')
    journal_entry = models.ForeignKey(JournalEntry, null=True, blank=True, on_delete=models.SET_NULL)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-date', '-created_at']

    def __str__(self):
        return f"INV-{self.number}"

    @property
    def outstanding_amount(self):
        return self.total_amount - self.paid_amount

    @property
    def is_overdue(self):
        from django.utils import timezone
        return self.due_date < timezone.now().date() and self.outstanding_amount > 0


class InvoiceLine(models.Model):
    """Invoice Line Items"""
    invoice = models.ForeignKey(Invoice, related_name='lines', on_delete=models.CASCADE)
    description = models.CharField(max_length=200)
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=1)
    unit_price = models.DecimalField(max_digits=15, decimal_places=2)
    tax_rate = models.ForeignKey(TaxRate, null=True, blank=True, on_delete=models.SET_NULL)
    line_total = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    def save(self, *args, **kwargs):
        # Calculate line total
        subtotal = self.quantity * self.unit_price
        tax_amount = subtotal * (self.tax_rate.rate / 100) if self.tax_rate else 0
        self.line_total = subtotal + tax_amount
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.invoice.number} - {self.description}"


class Bill(models.Model):
    """Vendor Bills"""
    BILL_STATES = [
        ('DRAFT', 'Draft'),
        ('RECEIVED', 'Received'),
        ('PARTIAL', 'Partially Paid'),
        ('PAID', 'Paid'),
        ('CANCELLED', 'Cancelled'),
    ]

    number = models.CharField(max_length=50, unique=True)
    vendor = models.ForeignKey(Vendor, related_name='bills', on_delete=models.CASCADE)
    vendor_reference = models.CharField(max_length=50, blank=True)
    date = models.DateField()
    due_date = models.DateField()
    description = models.TextField(blank=True)
    subtotal = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    paid_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    status = models.CharField(max_length=20, choices=BILL_STATES, default='DRAFT')
    journal_entry = models.ForeignKey(JournalEntry, null=True, blank=True, on_delete=models.SET_NULL)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-date', '-created_at']

    def __str__(self):
        return f"BILL-{self.number}"

    @property
    def outstanding_amount(self):
        return self.total_amount - self.paid_amount


class Payment(models.Model):
    """Payments (Customer/Vendor)"""
    PAYMENT_TYPES = [
        ('CUSTOMER', 'Customer Payment'),
        ('VENDOR', 'Vendor Payment'),
    ]

    PAYMENT_METHODS = [
        ('CASH', 'Cash'),
        ('BANK', 'Bank Transfer'),
        ('CHECK', 'Check'),
        ('CARD', 'Credit/Debit Card'),
    ]

    reference = models.CharField(max_length=50, unique=True)
    type = models.CharField(max_length=20, choices=PAYMENT_TYPES)
    customer = models.ForeignKey(Customer, null=True, blank=True, on_delete=models.CASCADE)
    vendor = models.ForeignKey(Vendor, null=True, blank=True, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    date = models.DateField()
    method = models.CharField(max_length=20, choices=PAYMENT_METHODS)
    description = models.TextField(blank=True)
    journal_entry = models.ForeignKey(JournalEntry, null=True, blank=True, on_delete=models.SET_NULL)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"PAY-{self.reference}"
