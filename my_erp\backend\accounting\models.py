"""
Fixed Accounting Models for ERP System - Django Compatible
Based on Odoo's Accounting Module Structure
"""
from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import date, datetime
import uuid


class ResCompany(models.Model):
    """Company/Organization Information - Based on Odoo res.company"""
    name = models.CharField(max_length=200)
    legal_name = models.CharField(max_length=200, blank=True)
    
    # Basic Information
    street = models.CharField(max_length=128, blank=True)
    street2 = models.CharField(max_length=128, blank=True)
    city = models.CharField(max_length=128, blank=True)
    state_id = models.CharField(max_length=128, blank=True)
    zip = models.CharField(max_length=24, blank=True)
    country_id = models.CharField(max_length=2, default='PK')
    
    # Contact Information
    phone = models.CharField(max_length=32, blank=True)
    email = models.CharField(max_length=240, blank=True)
    website = models.CharField(max_length=64, blank=True)
    
    # Tax & Legal
    vat = models.CharField(max_length=32, blank=True, help_text="Tax ID")
    company_registry = models.CharField(max_length=64, blank=True)
    
    # Accounting Configuration
    currency_id = models.CharField(max_length=3, default='PKR')
    fiscalyear_last_day = models.IntegerField(default=31)
    fiscalyear_last_month = models.IntegerField(default=12)
    period_lock_date = models.DateField(null=True, blank=True)
    fiscalyear_lock_date = models.DateField(null=True, blank=True)
    
    # Chart of Accounts
    chart_template_id = models.CharField(max_length=64, blank=True)
    bank_account_code_prefix = models.CharField(max_length=32, default='1014')
    cash_account_code_prefix = models.CharField(max_length=32, default='1011')
    
    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name_plural = "Companies"
        db_table = 'res_company'

    def __str__(self):
        return self.name


class ResPartner(models.Model):
    """Partner Model - Based on Odoo res.partner (Customers/Vendors/Contacts)"""
    
    # Basic Information
    name = models.CharField(max_length=128, db_index=True)
    display_name = models.CharField(max_length=128, blank=True)
    ref = models.CharField(max_length=64, blank=True, help_text="Internal Reference")
    
    # Partner Type
    is_company = models.BooleanField(default=False)
    parent_id = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, db_index=True)
    
    # Contact Information
    street = models.CharField(max_length=128, blank=True)
    street2 = models.CharField(max_length=128, blank=True)
    city = models.CharField(max_length=128, blank=True)
    state_id = models.CharField(max_length=128, blank=True)
    zip = models.CharField(max_length=24, blank=True)
    country_id = models.CharField(max_length=2, default='PK')
    
    phone = models.CharField(max_length=32, blank=True)
    mobile = models.CharField(max_length=32, blank=True)
    email = models.CharField(max_length=240, blank=True)
    website = models.CharField(max_length=64, blank=True)
    
    # Business Information
    function = models.CharField(max_length=128, blank=True, help_text="Job Position")
    title = models.CharField(max_length=16, blank=True)
    
    # Accounting Fields
    customer_rank = models.IntegerField(default=0, help_text="Customer ranking for prioritization")
    supplier_rank = models.IntegerField(default=0, help_text="Vendor ranking for prioritization")
    
    # Credit Management
    credit_limit = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    
    # Tax Information
    vat = models.CharField(max_length=32, blank=True, help_text="Tax ID")
    
    # Status
    active = models.BooleanField(default=True)
    
    # Company
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)
    
    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'res_partner'
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['customer_rank']),
            models.Index(fields=['supplier_rank']),
        ]

    def __str__(self):
        return self.name

    @property
    def is_customer(self):
        return self.customer_rank > 0

    @property
    def is_vendor(self):
        return self.supplier_rank > 0


class AccountAccount(models.Model):
    """Chart of Accounts - Based on Odoo account.account"""
    
    # Account Types from Odoo
    ACCOUNT_TYPES = [
        ('asset_receivable', 'Receivable'),
        ('asset_cash', 'Bank and Cash'),
        ('asset_current', 'Current Assets'),
        ('asset_non_current', 'Non-current Assets'),
        ('asset_prepayments', 'Prepayments'),
        ('asset_fixed', 'Fixed Assets'),
        ('liability_payable', 'Payable'),
        ('liability_credit_card', 'Credit Card'),
        ('liability_current', 'Current Liabilities'),
        ('liability_non_current', 'Non-current Liabilities'),
        ('equity', 'Equity'),
        ('equity_unaffected', 'Current Year Earnings'),
        ('income', 'Income'),
        ('income_other', 'Other Income'),
        ('expense', 'Expenses'),
        ('expense_depreciation', 'Depreciation'),
        ('expense_direct_cost', 'Cost of Revenue'),
        ('off_balance', 'Off-Balance Sheet'),
    ]
    
    # Basic Fields
    name = models.CharField(max_length=128, db_index=True, help_text="Account Name")
    code = models.CharField(max_length=64, db_index=True, help_text="Account Code")
    account_type = models.CharField(max_length=64, choices=ACCOUNT_TYPES, db_index=True)
    
    # Configuration
    reconcile = models.BooleanField(default=False, help_text="Allow reconciliation of journal items")
    deprecated = models.BooleanField(default=False, help_text="Deprecated accounts are hidden by default")
    
    # Currency
    currency_id = models.CharField(max_length=3, blank=True, help_text="Forces all moves for this account to have this currency")
    
    # Notes
    note = models.TextField(blank=True, help_text="Internal notes")
    
    # Company
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)
    
    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'account_account'
        unique_together = [('code', 'company_id')]
        indexes = [
            models.Index(fields=['code', 'company_id']),
            models.Index(fields=['account_type']),
        ]
        ordering = ['code']

    def __str__(self):
        return f"{self.code} {self.name}"

    def clean(self):
        """Validation similar to Odoo's constraints"""
        if self.account_type in ('asset_receivable', 'liability_payable') and not self.reconcile:
            raise ValidationError('Receivable/Payable accounts must be reconcilable')

    @property
    def balance(self):
        """Calculate current balance - similar to Odoo's balance computation"""
        from django.db.models import Sum
        lines = self.move_line_ids.filter(move_id__state='posted')
        debit_sum = lines.aggregate(total=Sum('debit'))['total'] or Decimal('0')
        credit_sum = lines.aggregate(total=Sum('credit'))['total'] or Decimal('0')
        
        # Normal balance calculation based on account type
        if self.account_type.startswith('asset') or self.account_type.startswith('expense'):
            return debit_sum - credit_sum
        else:
            return credit_sum - debit_sum


class AccountJournal(models.Model):
    """Journal - Based on Odoo account.journal"""
    
    JOURNAL_TYPES = [
        ('sale', 'Sales'),
        ('purchase', 'Purchase'),
        ('cash', 'Cash'),
        ('bank', 'Bank'),
        ('general', 'Miscellaneous'),
    ]
    
    # Basic Information
    name = models.CharField(max_length=64)
    code = models.CharField(max_length=5)
    type = models.CharField(max_length=8, choices=JOURNAL_TYPES)
    
    # Configuration
    active = models.BooleanField(default=True)
    sequence = models.IntegerField(default=10, help_text="Used to order journals in the dashboard")
    
    # Accounts
    default_account_id = models.ForeignKey(AccountAccount, on_delete=models.RESTRICT, help_text="Default account for journal entries")
    
    # Company
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)
    
    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'account_journal'
        unique_together = [('code', 'company_id')]
        ordering = ['sequence', 'type', 'code']

    def __str__(self):
        return f"{self.code} - {self.name}"


class AccountTax(models.Model):
    """Tax Configuration - Based on Odoo account.tax"""

    TAX_TYPES = [
        ('sale', 'Sales'),
        ('purchase', 'Purchase'),
        ('none', 'None'),
    ]

    AMOUNT_TYPES = [
        ('group', 'Group of Taxes'),
        ('fixed', 'Fixed'),
        ('percent', 'Percentage of Price'),
        ('division', 'Percentage of Price Tax Included'),
    ]

    name = models.CharField(max_length=64)
    type_tax_use = models.CharField(max_length=8, choices=TAX_TYPES)
    amount_type = models.CharField(max_length=8, choices=AMOUNT_TYPES, default='percent')
    amount = models.DecimalField(max_digits=16, decimal_places=4, default=0)
    active = models.BooleanField(default=True)
    sequence = models.IntegerField(default=1)
    description = models.CharField(max_length=16, blank=True)

    # Accounts for tax posting
    invoice_repartition_line_ids = models.ManyToManyField('AccountTaxRepartitionLine', blank=True)
    refund_repartition_line_ids = models.ManyToManyField('AccountTaxRepartitionLine', blank=True, related_name='refund_tax_ids')

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'account_tax'

    def __str__(self):
        return f"{self.name} ({self.amount}%)"


class AccountTaxRepartitionLine(models.Model):
    """Tax Repartition Lines - Based on Odoo account.tax.repartition.line"""

    REPARTITION_TYPES = [
        ('base', 'Base'),
        ('tax', 'Tax'),
    ]

    factor_percent = models.DecimalField(max_digits=16, decimal_places=4, default=100)
    repartition_type = models.CharField(max_length=8, choices=REPARTITION_TYPES, default='tax')
    account_id = models.ForeignKey(AccountAccount, null=True, blank=True, on_delete=models.SET_NULL)
    invoice_tax_id = models.ForeignKey(AccountTax, null=True, blank=True, on_delete=models.CASCADE, related_name='invoice_repartition_lines')
    refund_tax_id = models.ForeignKey(AccountTax, null=True, blank=True, on_delete=models.CASCADE, related_name='refund_repartition_lines')
    sequence = models.IntegerField(default=1)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'account_tax_repartition_line'


class AccountMove(models.Model):
    """Journal Entry/Invoice - Based on Odoo account.move"""

    MOVE_TYPES = [
        ('entry', 'Journal Entry'),
        ('out_invoice', 'Customer Invoice'),
        ('out_refund', 'Customer Credit Note'),
        ('in_invoice', 'Vendor Bill'),
        ('in_refund', 'Vendor Credit Note'),
        ('out_receipt', 'Sales Receipt'),
        ('in_receipt', 'Purchase Receipt'),
    ]

    STATES = [
        ('draft', 'Draft'),
        ('posted', 'Posted'),
        ('cancel', 'Cancelled'),
    ]

    PAYMENT_STATES = [
        ('not_paid', 'Not Paid'),
        ('in_payment', 'In Payment'),
        ('paid', 'Paid'),
        ('partial', 'Partially Paid'),
        ('reversed', 'Reversed'),
        ('invoicing_legacy', 'Invoicing App Legacy'),
    ]

    # Basic Fields
    name = models.CharField(max_length=64, default='/')
    ref = models.CharField(max_length=64, blank=True)
    date = models.DateField(db_index=True)

    # Type and State
    move_type = models.CharField(max_length=16, choices=MOVE_TYPES, default='entry', db_index=True)
    state = models.CharField(max_length=8, choices=STATES, default='draft', db_index=True)

    # Journal and Company
    journal_id = models.ForeignKey(AccountJournal, on_delete=models.CASCADE, db_index=True)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE, db_index=True)

    # Partner (Customer/Vendor)
    partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.RESTRICT, db_index=True)
    commercial_partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.RESTRICT, related_name='commercial_moves')

    # Currency
    currency_id = models.CharField(max_length=3, default='PKR')

    # Invoice specific fields
    invoice_date = models.DateField(null=True, blank=True, db_index=True)
    invoice_date_due = models.DateField(null=True, blank=True, db_index=True)

    # Amounts
    amount_untaxed = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_tax = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_total = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_residual = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Payment
    payment_state = models.CharField(max_length=16, choices=PAYMENT_STATES, default='not_paid', db_index=True)
    payment_reference = models.CharField(max_length=64, blank=True)

    # Narration
    narration = models.TextField(blank=True)

    # Sequence
    sequence_number = models.IntegerField(default=0)
    sequence_prefix = models.CharField(max_length=64, blank=True)

    # Auto-posting
    auto_post = models.BooleanField(default=False)

    # Reversal
    reversed_entry_id = models.ForeignKey('self', null=True, blank=True, on_delete=models.SET_NULL)

    # User tracking
    create_uid = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_moves')
    write_uid = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='modified_moves')

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'account_move'
        indexes = [
            models.Index(fields=['date', 'journal_id']),
            models.Index(fields=['state', 'move_type']),
            models.Index(fields=['partner_id', 'state']),
        ]
        ordering = ['-date', '-name', '-invoice_date', '-id']

    def __str__(self):
        return self.name or 'Draft Move'

    @property
    def is_invoice(self):
        return self.move_type in ('out_invoice', 'out_refund', 'in_invoice', 'in_refund')

    def action_post(self):
        """Post the journal entry - similar to Odoo's action_post"""
        if self.state != 'draft':
            return False

        # Validate the move
        if not self.line_ids.exists():
            raise ValidationError("Cannot post a move without journal items")

        # Check if balanced
        if not self._is_balanced():
            raise ValidationError("Journal entry is not balanced")

        # Generate sequence number if needed
        if self.name == '/':
            self.name = self._get_sequence()

        self.state = 'posted'
        self.save()
        return True

    def _is_balanced(self):
        """Check if the move is balanced"""
        total_debit = sum(line.debit for line in self.line_ids.all())
        total_credit = sum(line.credit for line in self.line_ids.all())
        return abs(total_debit - total_credit) < 0.01

    def _get_sequence(self):
        """Generate sequence number"""
        return f"{self.journal_id.code}/{datetime.now().year}/{self.id:05d}"


class AccountMoveLine(models.Model):
    """Journal Entry Lines - Based on Odoo account.move.line"""

    # Basic Fields
    name = models.CharField(max_length=64, help_text="Label")
    move_id = models.ForeignKey(AccountMove, on_delete=models.CASCADE, related_name='line_ids', db_index=True)

    # Account
    account_id = models.ForeignKey(AccountAccount, on_delete=models.RESTRICT, db_index=True, related_name='move_line_ids')

    # Partner
    partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.RESTRICT, db_index=True)

    # Amounts
    debit = models.DecimalField(max_digits=16, decimal_places=2, default=0, db_index=True)
    credit = models.DecimalField(max_digits=16, decimal_places=2, default=0, db_index=True)
    balance = models.DecimalField(max_digits=16, decimal_places=2, default=0, db_index=True)

    # Currency
    currency_id = models.CharField(max_length=3, blank=True)
    amount_currency = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Product (for invoice lines)
    quantity = models.DecimalField(max_digits=16, decimal_places=4, default=0)
    price_unit = models.DecimalField(max_digits=16, decimal_places=4, default=0)
    price_subtotal = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    price_total = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    discount = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Tax
    tax_ids = models.ManyToManyField(AccountTax, blank=True, related_name='move_line_tax_ids')
    tax_line_id = models.ForeignKey(AccountTax, null=True, blank=True, on_delete=models.RESTRICT, help_text="Tax line", related_name='tax_line_move_ids')
    tax_base_amount = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Reconciliation
    reconciled = models.BooleanField(default=False, db_index=True)
    full_reconcile_id = models.ForeignKey('AccountFullReconcile', null=True, blank=True, on_delete=models.SET_NULL)
    matching_number = models.CharField(max_length=32, blank=True, db_index=True)

    # Dates
    date = models.DateField(db_index=True)
    date_maturity = models.DateField(null=True, blank=True, db_index=True)

    # Reference
    ref = models.CharField(max_length=64, blank=True)

    # Sequence
    sequence = models.IntegerField(default=10)

    # Company
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE, db_index=True)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'account_move_line'
        indexes = [
            models.Index(fields=['account_id', 'date']),
            models.Index(fields=['partner_id', 'account_id']),
            models.Index(fields=['reconciled', 'account_id']),
            models.Index(fields=['date', 'move_id']),
        ]

    def __str__(self):
        return f"{self.move_id.name} - {self.name}"

    def clean(self):
        """Validation similar to Odoo's constraints"""
        if self.debit < 0:
            raise ValidationError("Debit amount cannot be negative")
        if self.credit < 0:
            raise ValidationError("Credit amount cannot be negative")
        if self.debit > 0 and self.credit > 0:
            raise ValidationError("A line cannot have both debit and credit amounts")

    def save(self, *args, **kwargs):
        # Calculate balance
        self.balance = self.debit - self.credit
        # Set date from move if not provided
        if not self.date:
            self.date = self.move_id.date
        super().save(*args, **kwargs)


class AccountFullReconcile(models.Model):
    """Full Reconciliation - Based on Odoo account.full.reconcile"""
    name = models.CharField(max_length=64)
    reconciled_line_ids = models.ManyToManyField(AccountMoveLine, related_name='full_reconcile_ids')
    exchange_move_id = models.ForeignKey(AccountMove, null=True, blank=True, on_delete=models.SET_NULL)

    class Meta:
        db_table = 'account_full_reconcile'

    def __str__(self):
        return self.name


class AccountPartialReconcile(models.Model):
    """Partial Reconciliation - Based on Odoo account.partial.reconcile"""
    debit_move_id = models.ForeignKey(AccountMoveLine, on_delete=models.CASCADE, related_name='debit_partial_reconcile_ids')
    credit_move_id = models.ForeignKey(AccountMoveLine, on_delete=models.CASCADE, related_name='credit_partial_reconcile_ids')
    amount = models.DecimalField(max_digits=16, decimal_places=2)
    amount_currency = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    currency_id = models.CharField(max_length=3, blank=True)
    full_reconcile_id = models.ForeignKey(AccountFullReconcile, null=True, blank=True, on_delete=models.CASCADE)

    class Meta:
        db_table = 'account_partial_reconcile'


class AccountPayment(models.Model):
    """Payment - Based on Odoo account.payment"""

    PAYMENT_TYPES = [
        ('outbound', 'Send Money'),
        ('inbound', 'Receive Money'),
    ]

    PARTNER_TYPES = [
        ('customer', 'Customer'),
        ('supplier', 'Vendor'),
    ]

    PAYMENT_METHODS = [
        ('manual', 'Manual'),
        ('electronic', 'Electronic'),
        ('check_printing', 'Check'),
    ]

    STATES = [
        ('draft', 'Draft'),
        ('posted', 'Validated'),
        ('sent', 'Sent'),
        ('reconciled', 'Reconciled'),
        ('cancelled', 'Cancelled'),
    ]

    # Basic Fields
    name = models.CharField(max_length=64, default='/')
    move_id = models.OneToOneField(AccountMove, on_delete=models.CASCADE, help_text="Journal Entry")

    # Payment Details
    payment_type = models.CharField(max_length=8, choices=PAYMENT_TYPES)
    partner_type = models.CharField(max_length=8, choices=PARTNER_TYPES)
    partner_id = models.ForeignKey(ResPartner, on_delete=models.RESTRICT)

    # Amount
    amount = models.DecimalField(max_digits=16, decimal_places=2)
    currency_id = models.CharField(max_length=3, default='PKR')

    # Journal
    journal_id = models.ForeignKey(AccountJournal, on_delete=models.RESTRICT)

    # Destination Account
    destination_account_id = models.ForeignKey(AccountAccount, on_delete=models.RESTRICT)

    # Outstanding Account
    outstanding_account_id = models.ForeignKey(AccountAccount, on_delete=models.RESTRICT, related_name='outstanding_payments')

    # State
    state = models.CharField(max_length=16, choices=STATES, default='draft')

    # Reconciliation
    reconciled_invoice_ids = models.ManyToManyField(AccountMove, blank=True, help_text="Invoices reconciled with this payment", related_name='reconciled_payments')
    reconciled_invoices_count = models.IntegerField(default=0)

    # Reference
    ref = models.CharField(max_length=64, blank=True)

    # Date
    date = models.DateField(default=date.today)

    # Company
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'account_payment'
        indexes = [
            models.Index(fields=['partner_id', 'state']),
            models.Index(fields=['date', 'journal_id']),
        ]

    def __str__(self):
        return self.name or 'Draft Payment'

    def action_post(self):
        """Post the payment"""
        if self.state != 'draft':
            return False

        # Post the related journal entry
        self.move_id.action_post()
        self.state = 'posted'
        self.save()
        return True


class AccountPaymentTerm(models.Model):
    """Payment Terms - Based on Odoo account.payment.term"""
    name = models.CharField(max_length=64)
    active = models.BooleanField(default=True)
    note = models.TextField(blank=True)
    sequence = models.IntegerField(default=10)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'account_payment_term'

    def __str__(self):
        return self.name


class AccountPaymentTermLine(models.Model):
    """Payment Term Lines - Based on Odoo account.payment.term.line"""

    VALUE_TYPES = [
        ('balance', 'Balance'),
        ('percent', 'Percent'),
        ('fixed', 'Fixed Amount'),
    ]

    payment_id = models.ForeignKey(AccountPaymentTerm, on_delete=models.CASCADE, related_name='line_ids')
    value = models.CharField(max_length=8, choices=VALUE_TYPES, default='balance')
    value_amount = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    days = models.IntegerField(default=0)
    sequence = models.IntegerField(default=10)

    class Meta:
        db_table = 'account_payment_term_line'


class AccountFiscalPosition(models.Model):
    """Fiscal Position - Based on Odoo account.fiscal.position"""
    name = models.CharField(max_length=64)
    active = models.BooleanField(default=True)
    auto_apply = models.BooleanField(default=False)
    vat_required = models.BooleanField(default=False)
    note = models.TextField(blank=True)
    sequence = models.IntegerField(default=10)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'account_fiscal_position'

    def __str__(self):
        return self.name


class AccountFiscalPositionTax(models.Model):
    """Fiscal Position Tax Mapping - Based on Odoo account.fiscal.position.tax"""
    position_id = models.ForeignKey(AccountFiscalPosition, on_delete=models.CASCADE, related_name='tax_ids')
    tax_src_id = models.ForeignKey(AccountTax, on_delete=models.CASCADE, related_name='fiscal_position_taxes_src')
    tax_dest_id = models.ForeignKey(AccountTax, null=True, blank=True, on_delete=models.CASCADE, related_name='fiscal_position_taxes_dest')

    class Meta:
        db_table = 'account_fiscal_position_tax'


class AccountFiscalPositionAccount(models.Model):
    """Fiscal Position Account Mapping - Based on Odoo account.fiscal.position.account"""
    position_id = models.ForeignKey(AccountFiscalPosition, on_delete=models.CASCADE, related_name='account_ids')
    account_src_id = models.ForeignKey(AccountAccount, on_delete=models.CASCADE, related_name='fiscal_position_accounts_src')
    account_dest_id = models.ForeignKey(AccountAccount, on_delete=models.CASCADE, related_name='fiscal_position_accounts_dest')

    class Meta:
        db_table = 'account_fiscal_position_account'


class AccountAnalyticAccount(models.Model):
    """Analytic Account - Based on Odoo account.analytic.account"""
    name = models.CharField(max_length=128)
    code = models.CharField(max_length=32, blank=True)
    active = models.BooleanField(default=True)
    partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.SET_NULL)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'account_analytic_account'

    def __str__(self):
        return self.name


class AccountAnalyticLine(models.Model):
    """Analytic Lines - Based on Odoo account.analytic.line"""
    name = models.CharField(max_length=256)
    date = models.DateField(db_index=True)
    amount = models.DecimalField(max_digits=16, decimal_places=2)
    unit_amount = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    account_id = models.ForeignKey(AccountAnalyticAccount, on_delete=models.CASCADE, related_name='line_ids')
    partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.SET_NULL)
    move_id = models.ForeignKey(AccountMove, null=True, blank=True, on_delete=models.CASCADE)
    general_account_id = models.ForeignKey(AccountAccount, on_delete=models.RESTRICT)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'account_analytic_line'


# ============================================================================
# BUSINESS PROCESSES & WORKFLOWS (Odoo-Style)
# ============================================================================

class IrSequence(models.Model):
    """Sequence Generator - Based on Odoo ir.sequence"""

    IMPLEMENTATION_TYPES = [
        ('standard', 'Standard'),
        ('no_gap', 'No Gap'),
    ]

    name = models.CharField(max_length=64)
    code = models.CharField(max_length=64, db_index=True)
    implementation = models.CharField(max_length=8, choices=IMPLEMENTATION_TYPES, default='standard')
    active = models.BooleanField(default=True)
    prefix = models.CharField(max_length=64, blank=True)
    suffix = models.CharField(max_length=64, blank=True)
    number_next = models.IntegerField(default=1)
    number_increment = models.IntegerField(default=1)
    padding = models.IntegerField(default=0)
    use_date_range = models.BooleanField(default=False)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'ir_sequence'
        unique_together = [('code', 'company_id')]

    def __str__(self):
        return f"{self.name} ({self.code})"

    def next_by_code(self, code, company_id=None):
        """Get next sequence number by code"""
        sequence = self.__class__.objects.get(code=code, company_id=company_id)
        number = sequence.number_next
        sequence.number_next += sequence.number_increment
        sequence.save()

        # Format the number
        formatted_number = str(number).zfill(sequence.padding)
        return f"{sequence.prefix}{formatted_number}{sequence.suffix}"


class AccountReconcileModel(models.Model):
    """Auto Reconciliation Rules - Based on Odoo account.reconcile.model"""

    RULE_TYPES = [
        ('writeoff_button', 'Button to generate counterpart entry'),
        ('writeoff_suggestion', 'Rule to suggest counterpart entry'),
        ('invoice_matching', 'Rule to match invoices/bills'),
    ]

    name = models.CharField(max_length=64)
    sequence = models.IntegerField(default=10)
    rule_type = models.CharField(max_length=25, choices=RULE_TYPES, default='writeoff_button')
    auto_reconcile = models.BooleanField(default=False)
    to_check = models.BooleanField(default=False)

    # Matching conditions
    match_journal_ids = models.ManyToManyField(AccountJournal, blank=True, related_name='reconcile_models_match')
    match_nature = models.CharField(max_length=16, choices=[('amount_received', 'Amount Received'), ('amount_paid', 'Amount Paid')], blank=True)
    match_amount = models.CharField(max_length=8, choices=[('lower', 'Is Lower Than'), ('greater', 'Is Greater Than'), ('between', 'Is Between')], blank=True)
    match_amount_min = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    match_amount_max = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    match_label = models.CharField(max_length=16, choices=[('contains', 'Contains'), ('not_contains', 'Not Contains'), ('match_regex', 'Match Regex')], blank=True)
    match_label_param = models.CharField(max_length=100, blank=True)
    match_note = models.CharField(max_length=16, choices=[('contains', 'Contains'), ('not_contains', 'Not Contains'), ('match_regex', 'Match Regex')], blank=True)
    match_note_param = models.CharField(max_length=100, blank=True)
    match_transaction_type = models.CharField(max_length=100, blank=True)
    match_same_currency = models.BooleanField(default=True)

    # Counterpart creation
    account_id = models.ForeignKey(AccountAccount, null=True, blank=True, on_delete=models.CASCADE)
    journal_id = models.ForeignKey(AccountJournal, null=True, blank=True, on_delete=models.CASCADE, related_name='reconcile_models')
    label = models.CharField(max_length=64, blank=True)
    amount_type = models.CharField(max_length=16, choices=[('fixed', 'Fixed'), ('percentage', 'Percentage of balance')], default='percentage')
    amount = models.DecimalField(max_digits=16, decimal_places=2, default=100)
    tax_ids = models.ManyToManyField(AccountTax, blank=True, related_name='reconcile_models')

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'account_reconcile_model'

    def __str__(self):
        return self.name


class AccountBankStatement(models.Model):
    """Bank Statement - Based on Odoo account.bank.statement"""

    STATES = [
        ('open', 'New'),
        ('posted', 'Validated'),
    ]

    name = models.CharField(max_length=64)
    reference = models.CharField(max_length=32, blank=True)
    date = models.DateField(db_index=True)
    date_done = models.DateTimeField(null=True, blank=True)
    balance_start = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    balance_end_real = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    balance_end = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    state = models.CharField(max_length=8, choices=STATES, default='open')
    currency_id = models.CharField(max_length=3, default='PKR')
    journal_id = models.ForeignKey(AccountJournal, on_delete=models.CASCADE)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'account_bank_statement'
        ordering = ['-date']

    def __str__(self):
        return f"{self.name} - {self.date}"

    @property
    def difference(self):
        return self.balance_end_real - self.balance_end


class AccountBankStatementLine(models.Model):
    """Bank Statement Lines - Based on Odoo account.bank.statement.line"""

    statement_id = models.ForeignKey(AccountBankStatement, on_delete=models.CASCADE, related_name='line_ids')
    sequence = models.IntegerField(default=1)
    date = models.DateField(db_index=True)
    name = models.CharField(max_length=64)
    ref = models.CharField(max_length=32, blank=True)
    amount = models.DecimalField(max_digits=16, decimal_places=2)
    currency_id = models.CharField(max_length=3, default='PKR')
    partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.SET_NULL)
    account_number = models.CharField(max_length=32, blank=True)
    partner_name = models.CharField(max_length=128, blank=True)
    transaction_type = models.CharField(max_length=32, blank=True)

    # Reconciliation
    is_reconciled = models.BooleanField(default=False)
    move_id = models.ForeignKey(AccountMove, null=True, blank=True, on_delete=models.SET_NULL)

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'account_bank_statement_line'

    def __str__(self):
        return f"{self.statement_id.name} - {self.name}"


class AccountAssetCategory(models.Model):
    """Asset Category - Based on Odoo account.asset.category"""

    ASSET_TYPES = [
        ('sale', 'Deferred Revenue'),
        ('purchase', 'Deferred Expense'),
        ('expense', 'Expense'),
    ]

    METHOD_TYPES = [
        ('linear', 'Linear'),
        ('degressive', 'Degressive'),
        ('degressive_then_linear', 'Degressive then Linear'),
    ]

    name = models.CharField(max_length=64)
    account_asset_id = models.ForeignKey(AccountAccount, on_delete=models.CASCADE, related_name='asset_categories')
    account_depreciation_id = models.ForeignKey(AccountAccount, on_delete=models.CASCADE, related_name='depreciation_categories')
    account_depreciation_expense_id = models.ForeignKey(AccountAccount, on_delete=models.CASCADE, related_name='asset_expense_categories')
    journal_id = models.ForeignKey(AccountJournal, on_delete=models.CASCADE)

    type = models.CharField(max_length=8, choices=ASSET_TYPES, default='purchase')
    method = models.CharField(max_length=25, choices=METHOD_TYPES, default='linear')
    method_number = models.IntegerField(default=5, help_text="Number of depreciations")
    method_period = models.IntegerField(default=1, help_text="Period length")
    method_progress_factor = models.DecimalField(max_digits=4, decimal_places=2, default=0.3)

    prorata = models.BooleanField(default=True)
    active = models.BooleanField(default=True)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'account_asset_category'

    def __str__(self):
        return self.name


class AccountAsset(models.Model):
    """Asset Management - Based on Odoo account.asset.asset"""

    STATES = [
        ('draft', 'Draft'),
        ('open', 'Running'),
        ('close', 'Close'),
    ]

    name = models.CharField(max_length=64)
    code = models.CharField(max_length=32, blank=True)
    value = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    value_residual = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    method_number = models.IntegerField(default=5)
    method_period = models.IntegerField(default=1)
    method_end = models.DateField(null=True, blank=True)

    purchase_date = models.DateField(null=True, blank=True)
    purchase_value = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    category_id = models.ForeignKey(AccountAssetCategory, on_delete=models.CASCADE)
    partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.SET_NULL)
    invoice_id = models.ForeignKey(AccountMove, null=True, blank=True, on_delete=models.SET_NULL)

    state = models.CharField(max_length=8, choices=STATES, default='draft')
    active = models.BooleanField(default=True)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'account_asset_asset'

    def __str__(self):
        return self.name


class AccountAssetDepreciationLine(models.Model):
    """Asset Depreciation Lines - Based on Odoo account.asset.depreciation.line"""
    name = models.CharField(max_length=64)
    sequence = models.IntegerField()
    asset_id = models.ForeignKey(AccountAsset, on_delete=models.CASCADE, related_name='depreciation_line_ids')
    amount = models.DecimalField(max_digits=16, decimal_places=2)
    remaining_value = models.DecimalField(max_digits=16, decimal_places=2)
    depreciated_value = models.DecimalField(max_digits=16, decimal_places=2)
    depreciation_date = models.DateField(db_index=True)
    move_id = models.ForeignKey(AccountMove, null=True, blank=True, on_delete=models.SET_NULL)
    move_posted = models.BooleanField(default=False)

    class Meta:
        db_table = 'account_asset_depreciation_line'

    def __str__(self):
        return f"{self.asset_id.name} - {self.depreciation_date}"


# ============================================================================
# BUDGET MANAGEMENT (Odoo-Style)
# ============================================================================

class AccountBudgetPost(models.Model):
    """Budget Position - Based on Odoo account.budget.post"""
    name = models.CharField(max_length=64)
    code = models.CharField(max_length=64, blank=True)
    account_ids = models.ManyToManyField(AccountAccount, related_name='budget_posts')
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'account_budget_post'

    def __str__(self):
        return self.name


class CrossoveredBudget(models.Model):
    """Budget - Based on Odoo crossovered.budget"""

    STATES = [
        ('draft', 'Draft'),
        ('cancel', 'Cancelled'),
        ('confirm', 'Confirmed'),
        ('validate', 'Validated'),
        ('done', 'Done'),
    ]

    name = models.CharField(max_length=64)
    code = models.CharField(max_length=16, blank=True)
    user_id = models.ForeignKey(User, on_delete=models.CASCADE, related_name='budgets')
    date_from = models.DateField()
    date_to = models.DateField()
    state = models.CharField(max_length=8, choices=STATES, default='draft')
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'crossovered_budget'

    def __str__(self):
        return self.name


class CrossoveredBudgetLines(models.Model):
    """Budget Lines - Based on Odoo crossovered.budget.lines"""
    crossovered_budget_id = models.ForeignKey(CrossoveredBudget, on_delete=models.CASCADE, related_name='crossovered_budget_line')
    analytic_account_id = models.ForeignKey(AccountAnalyticAccount, null=True, blank=True, on_delete=models.CASCADE)
    general_budget_id = models.ForeignKey(AccountBudgetPost, on_delete=models.CASCADE)
    date_from = models.DateField()
    date_to = models.DateField()
    planned_amount = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    practical_amount = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    theoretical_amount = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    percentage = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'crossovered_budget_lines'

    def __str__(self):
        return f"{self.crossovered_budget_id.name} - {self.general_budget_id.name}"


# ============================================================================
# REPORTING VIEWS & ANALYTICS (Odoo-Style)
# ============================================================================

class AccountInvoiceReport(models.Model):
    """Invoice Analysis - Based on Odoo account.invoice.report"""

    # This is typically a database view in Odoo, but we'll make it a model for simplicity
    date = models.DateField(db_index=True)
    invoice_date = models.DateField(null=True, blank=True, db_index=True)
    invoice_date_due = models.DateField(null=True, blank=True, db_index=True)
    partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.SET_NULL, db_index=True)
    commercial_partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.SET_NULL, related_name='commercial_invoice_reports')
    country_id = models.CharField(max_length=2, blank=True, db_index=True)
    invoice_user_id = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL)
    move_type = models.CharField(max_length=16, db_index=True)
    state = models.CharField(max_length=8, db_index=True)
    journal_id = models.ForeignKey(AccountJournal, null=True, blank=True, on_delete=models.SET_NULL, db_index=True)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE, db_index=True)
    currency_id = models.CharField(max_length=3, db_index=True)
    account_id = models.ForeignKey(AccountAccount, null=True, blank=True, on_delete=models.SET_NULL, db_index=True)

    # Amounts
    price_subtotal = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    price_average = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    residual = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Quantities
    quantity = models.DecimalField(max_digits=16, decimal_places=4, default=0)

    class Meta:
        db_table = 'account_invoice_report'
        managed = False  # This would typically be a database view

    def __str__(self):
        return f"Invoice Report - {self.date}"


class AccountMoveLineReport(models.Model):
    """Journal Items Analysis - Based on Odoo account.move.line.report"""

    # Key dimensions
    date = models.DateField(db_index=True)
    move_id = models.ForeignKey(AccountMove, on_delete=models.CASCADE, db_index=True)
    journal_id = models.ForeignKey(AccountJournal, on_delete=models.CASCADE, db_index=True)
    account_id = models.ForeignKey(AccountAccount, on_delete=models.CASCADE, db_index=True)
    partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.SET_NULL, db_index=True)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE, db_index=True)

    # Account details
    account_type = models.CharField(max_length=64, db_index=True)
    account_code = models.CharField(max_length=64, db_index=True)
    account_name = models.CharField(max_length=128)

    # Amounts
    debit = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    credit = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    balance = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Status
    reconciled = models.BooleanField(default=False, db_index=True)

    class Meta:
        db_table = 'account_move_line_report'
        managed = False  # This would typically be a database view

    def __str__(self):
        return f"Move Line Report - {self.date}"


class AccountAgedTrialBalance(models.Model):
    """Aged Trial Balance - Based on Odoo account.aged.trial.balance"""

    report_date = models.DateField(db_index=True)
    account_id = models.ForeignKey(AccountAccount, on_delete=models.CASCADE, db_index=True)
    partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.SET_NULL, db_index=True)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE, db_index=True)

    # Aging buckets
    not_due = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    period_1 = models.DecimalField(max_digits=16, decimal_places=2, default=0)  # 1-30 days
    period_2 = models.DecimalField(max_digits=16, decimal_places=2, default=0)  # 31-60 days
    period_3 = models.DecimalField(max_digits=16, decimal_places=2, default=0)  # 61-90 days
    period_4 = models.DecimalField(max_digits=16, decimal_places=2, default=0)  # 91-120 days
    period_5 = models.DecimalField(max_digits=16, decimal_places=2, default=0)  # 120+ days
    total = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    class Meta:
        db_table = 'account_aged_trial_balance'
        managed = False  # This would typically be a database view

    def __str__(self):
        return f"Aged Trial Balance - {self.report_date}"


class AccountFinancialReport(models.Model):
    """Financial Report Structure - Based on Odoo account.financial.report"""

    REPORT_TYPES = [
        ('sum', 'View'),
        ('accounts', 'Accounts'),
        ('account_type', 'Account Type'),
        ('account_report', 'Report Value'),
    ]

    SIGN_TYPES = [
        (1, 'Positive'),
        (-1, 'Negative'),
    ]

    name = models.CharField(max_length=128)
    parent_id = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, related_name='children_reports')
    children_ids = models.ManyToManyField('self', blank=True, symmetrical=False, related_name='parent_reports')
    sequence = models.IntegerField(default=10)
    level = models.IntegerField(default=0)
    type = models.CharField(max_length=16, choices=REPORT_TYPES, default='sum')
    account_ids = models.ManyToManyField(AccountAccount, blank=True)
    account_report_id = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, related_name='report_references')
    account_type_ids = models.CharField(max_length=256, blank=True)  # Store account types as comma-separated
    sign = models.IntegerField(choices=SIGN_TYPES, default=1)
    display_detail = models.CharField(max_length=32, choices=[('no_detail', 'No detail'), ('detail_flat', 'Display children flat'), ('detail_with_hierarchy', 'Display children with hierarchy')], default='detail_flat')
    style_overwrite = models.BooleanField(default=False)

    class Meta:
        db_table = 'account_financial_report'

    def __str__(self):
        return self.name


# ============================================================================
# WORKFLOW & AUTOMATION (Odoo-Style)
# ============================================================================

class IrCron(models.Model):
    """Scheduled Actions - Based on Odoo ir.cron"""

    INTERVAL_TYPES = [
        ('minutes', 'Minutes'),
        ('hours', 'Hours'),
        ('days', 'Days'),
        ('weeks', 'Weeks'),
        ('months', 'Months'),
    ]

    name = models.CharField(max_length=64)
    user_id = models.ForeignKey(User, on_delete=models.CASCADE, default=1)
    active = models.BooleanField(default=True)
    interval_number = models.IntegerField(default=1)
    interval_type = models.CharField(max_length=8, choices=INTERVAL_TYPES, default='months')
    numbercall = models.IntegerField(default=-1, help_text="Number of calls, -1 for unlimited")
    doall = models.BooleanField(default=True)
    nextcall = models.DateTimeField()
    model_id = models.CharField(max_length=64)
    function = models.CharField(max_length=64)
    args = models.TextField(blank=True)
    priority = models.IntegerField(default=5)

    class Meta:
        db_table = 'ir_cron'

    def __str__(self):
        return self.name


class AccountAutomaticEntryWizard(models.Model):
    """Automatic Entry Wizard - Based on Odoo account.automatic.entry.wizard"""

    ACTION_TYPES = [
        ('change_period', 'Change Period'),
        ('change_account', 'Change Account'),
        ('reverse', 'Reverse'),
    ]

    action = models.CharField(max_length=16, choices=ACTION_TYPES)
    date = models.DateField()
    journal_id = models.ForeignKey(AccountJournal, on_delete=models.CASCADE)
    account_id = models.ForeignKey(AccountAccount, null=True, blank=True, on_delete=models.CASCADE)
    move_ids = models.ManyToManyField(AccountMove, help_text="Moves to process")
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'account_automatic_entry_wizard'

    def __str__(self):
        return f"Auto Entry - {self.action}"


# ============================================================================
# INTEGRATION TABLES (Sales, Purchase, Inventory)
# ============================================================================

class ProductCategory(models.Model):
    """Product Category - Based on Odoo product.category"""
    name = models.CharField(max_length=128)
    complete_name = models.CharField(max_length=256, blank=True)
    parent_id = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, related_name='child_categories')
    child_id = models.ManyToManyField('self', blank=True, symmetrical=False, related_name='parent_categories')
    sequence = models.IntegerField(default=10)

    # Accounting integration
    property_account_income_categ_id = models.ForeignKey(AccountAccount, null=True, blank=True, on_delete=models.SET_NULL, related_name='income_categories')
    property_account_expense_categ_id = models.ForeignKey(AccountAccount, null=True, blank=True, on_delete=models.SET_NULL, related_name='expense_categories')
    property_stock_account_input_categ_id = models.ForeignKey(AccountAccount, null=True, blank=True, on_delete=models.SET_NULL, related_name='stock_input_categories')
    property_stock_account_output_categ_id = models.ForeignKey(AccountAccount, null=True, blank=True, on_delete=models.SET_NULL, related_name='stock_output_categories')
    property_stock_valuation_account_id = models.ForeignKey(AccountAccount, null=True, blank=True, on_delete=models.SET_NULL, related_name='stock_valuation_categories')

    class Meta:
        db_table = 'product_category'
        verbose_name_plural = "Product Categories"

    def __str__(self):
        return self.name


class ProductTemplate(models.Model):
    """Product Template - Based on Odoo product.template"""

    PRODUCT_TYPES = [
        ('consu', 'Consumable'),
        ('service', 'Service'),
        ('product', 'Storable Product'),
    ]

    name = models.CharField(max_length=128)
    sequence = models.IntegerField(default=1)
    description = models.TextField(blank=True)
    description_purchase = models.TextField(blank=True)
    description_sale = models.TextField(blank=True)
    type = models.CharField(max_length=8, choices=PRODUCT_TYPES, default='consu')
    categ_id = models.ForeignKey(ProductCategory, on_delete=models.CASCADE, default=1)
    list_price = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Sale Price")
    standard_price = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Cost Price")

    # Sales & Purchase
    sale_ok = models.BooleanField(default=True)
    purchase_ok = models.BooleanField(default=True)

    # Accounting
    property_account_income_id = models.ForeignKey(AccountAccount, null=True, blank=True, on_delete=models.SET_NULL, related_name='income_products')
    property_account_expense_id = models.ForeignKey(AccountAccount, null=True, blank=True, on_delete=models.SET_NULL, related_name='expense_products')
    taxes_id = models.ManyToManyField(AccountTax, blank=True, help_text="Customer Taxes")
    supplier_taxes_id = models.ManyToManyField(AccountTax, blank=True, related_name='supplier_products', help_text="Vendor Taxes")

    # Inventory
    tracking = models.CharField(max_length=8, choices=[('none', 'No Tracking'), ('lot', 'By Lots'), ('serial', 'By Unique Serial Number')], default='none')

    active = models.BooleanField(default=True)
    company_id = models.ForeignKey(ResCompany, null=True, blank=True, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'product_template'

    def __str__(self):
        return self.name


class ProductProduct(models.Model):
    """Product Variant - Based on Odoo product.product"""
    product_tmpl_id = models.ForeignKey(ProductTemplate, on_delete=models.CASCADE, related_name='product_variant_ids')
    default_code = models.CharField(max_length=64, blank=True, help_text="Internal Reference")
    barcode = models.CharField(max_length=64, blank=True, unique=True)
    active = models.BooleanField(default=True)

    # Computed fields from template
    @property
    def name(self):
        return self.product_tmpl_id.name

    @property
    def list_price(self):
        return self.product_tmpl_id.list_price

    @property
    def standard_price(self):
        return self.product_tmpl_id.standard_price

    class Meta:
        db_table = 'product_product'

    def __str__(self):
        return self.name


class SaleOrder(models.Model):
    """Sales Order - Based on Odoo sale.order"""

    STATES = [
        ('draft', 'Quotation'),
        ('sent', 'Quotation Sent'),
        ('sale', 'Sales Order'),
        ('done', 'Locked'),
        ('cancel', 'Cancelled'),
    ]

    name = models.CharField(max_length=64, default='New')
    origin = models.CharField(max_length=64, blank=True)
    client_order_ref = models.CharField(max_length=64, blank=True)
    reference = models.CharField(max_length=64, blank=True)
    state = models.CharField(max_length=8, choices=STATES, default='draft')
    date_order = models.DateTimeField(default=datetime.now)
    validity_date = models.DateField(null=True, blank=True)
    confirmation_date = models.DateTimeField(null=True, blank=True)

    # Partner information
    partner_id = models.ForeignKey(ResPartner, on_delete=models.RESTRICT, help_text="Customer")
    partner_invoice_id = models.ForeignKey(ResPartner, on_delete=models.RESTRICT, related_name='invoice_orders')
    partner_shipping_id = models.ForeignKey(ResPartner, on_delete=models.RESTRICT, related_name='shipping_orders')

    # Amounts
    amount_untaxed = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_tax = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_total = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Payment and delivery
    payment_term_id = models.ForeignKey(AccountPaymentTerm, null=True, blank=True, on_delete=models.SET_NULL)
    fiscal_position_id = models.ForeignKey(AccountFiscalPosition, null=True, blank=True, on_delete=models.SET_NULL)

    # Other
    note = models.TextField(blank=True)
    user_id = models.ForeignKey(User, on_delete=models.CASCADE, default=1)
    team_id = models.IntegerField(null=True, blank=True)  # Sales team
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)
    currency_id = models.CharField(max_length=3, default='PKR')

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'sale_order'

    def __str__(self):
        return self.name

    def action_confirm(self):
        """Confirm the sales order"""
        if self.state == 'draft':
            self.state = 'sale'
            self.confirmation_date = datetime.now()
            self.save()
            return True
        return False


class SaleOrderLine(models.Model):
    """Sales Order Line - Based on Odoo sale.order.line"""
    order_id = models.ForeignKey(SaleOrder, on_delete=models.CASCADE, related_name='order_line')
    name = models.TextField(help_text="Description")
    sequence = models.IntegerField(default=10)

    # Product
    product_id = models.ForeignKey(ProductProduct, null=True, blank=True, on_delete=models.RESTRICT)
    product_uom_qty = models.DecimalField(max_digits=16, decimal_places=4, default=1)
    product_uom = models.IntegerField(null=True, blank=True)  # UoM reference
    qty_delivered = models.DecimalField(max_digits=16, decimal_places=4, default=0)
    qty_invoiced = models.DecimalField(max_digits=16, decimal_places=4, default=0)
    qty_to_invoice = models.DecimalField(max_digits=16, decimal_places=4, default=0)

    # Pricing
    price_unit = models.DecimalField(max_digits=16, decimal_places=4, default=0)
    price_subtotal = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    price_total = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    price_tax = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    discount = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Taxes
    tax_id = models.ManyToManyField(AccountTax, blank=True)

    # Analytics
    analytic_account_id = models.ForeignKey(AccountAnalyticAccount, null=True, blank=True, on_delete=models.SET_NULL)

    # Invoice
    invoice_lines = models.ManyToManyField(AccountMoveLine, blank=True)
    invoice_status = models.CharField(max_length=16, choices=[('upselling', 'Upselling Opportunity'), ('invoiced', 'Fully Invoiced'), ('to invoice', 'To Invoice'), ('no', 'Nothing to Invoice')], default='no')

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'sale_order_line'

    def __str__(self):
        return f"{self.order_id.name} - {self.name[:50]}"


class PurchaseOrder(models.Model):
    """Purchase Order - Based on Odoo purchase.order"""

    STATES = [
        ('draft', 'RFQ'),
        ('sent', 'RFQ Sent'),
        ('to approve', 'To Approve'),
        ('purchase', 'Purchase Order'),
        ('done', 'Locked'),
        ('cancel', 'Cancelled'),
    ]

    name = models.CharField(max_length=64, default='New')
    origin = models.CharField(max_length=64, blank=True)
    partner_ref = models.CharField(max_length=64, blank=True)
    state = models.CharField(max_length=12, choices=STATES, default='draft')
    date_order = models.DateTimeField(default=datetime.now)
    date_approve = models.DateTimeField(null=True, blank=True)
    date_planned = models.DateTimeField(null=True, blank=True)

    # Partner information
    partner_id = models.ForeignKey(ResPartner, on_delete=models.RESTRICT, help_text="Vendor")

    # Amounts
    amount_untaxed = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_tax = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_total = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Payment
    payment_term_id = models.ForeignKey(AccountPaymentTerm, null=True, blank=True, on_delete=models.SET_NULL)
    fiscal_position_id = models.ForeignKey(AccountFiscalPosition, null=True, blank=True, on_delete=models.SET_NULL)

    # Other
    notes = models.TextField(blank=True)
    user_id = models.ForeignKey(User, on_delete=models.CASCADE, default=1)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)
    currency_id = models.CharField(max_length=3, default='PKR')

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'purchase_order'

    def __str__(self):
        return self.name

    def button_confirm(self):
        """Confirm the purchase order"""
        if self.state in ['draft', 'sent']:
            self.state = 'purchase'
            self.date_approve = datetime.now()
            self.save()
            return True
        return False


class PurchaseOrderLine(models.Model):
    """Purchase Order Line - Based on Odoo purchase.order.line"""
    order_id = models.ForeignKey(PurchaseOrder, on_delete=models.CASCADE, related_name='order_line')
    name = models.TextField(help_text="Description")
    sequence = models.IntegerField(default=10)

    # Product
    product_id = models.ForeignKey(ProductProduct, null=True, blank=True, on_delete=models.RESTRICT)
    product_qty = models.DecimalField(max_digits=16, decimal_places=4, default=1)
    product_uom = models.IntegerField(null=True, blank=True)
    qty_received = models.DecimalField(max_digits=16, decimal_places=4, default=0)
    qty_invoiced = models.DecimalField(max_digits=16, decimal_places=4, default=0)

    # Pricing
    price_unit = models.DecimalField(max_digits=16, decimal_places=4, default=0)
    price_subtotal = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    price_total = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    price_tax = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Taxes
    taxes_id = models.ManyToManyField(AccountTax, blank=True)

    # Analytics
    account_analytic_id = models.ForeignKey(AccountAnalyticAccount, null=True, blank=True, on_delete=models.SET_NULL)

    # Invoice
    invoice_lines = models.ManyToManyField(AccountMoveLine, blank=True)

    # Dates
    date_planned = models.DateTimeField()

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'purchase_order_line'

    def __str__(self):
        return f"{self.order_id.name} - {self.name[:50]}"


# ============================================================================
# INVENTORY INTEGRATION (Stock Management)
# ============================================================================

class StockLocation(models.Model):
    """Stock Location - Based on Odoo stock.location"""

    LOCATION_TYPES = [
        ('supplier', 'Vendor Location'),
        ('view', 'View'),
        ('internal', 'Internal Location'),
        ('customer', 'Customer Location'),
        ('inventory', 'Inventory Loss'),
        ('procurement', 'Procurement'),
        ('production', 'Production'),
        ('transit', 'Transit Location'),
    ]

    name = models.CharField(max_length=128)
    complete_name = models.CharField(max_length=256, blank=True)
    active = models.BooleanField(default=True)
    usage = models.CharField(max_length=12, choices=LOCATION_TYPES, default='internal')
    location_id = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE, help_text="Parent Location", related_name='child_locations')
    child_ids = models.ManyToManyField('self', blank=True, symmetrical=False, related_name='parent_locations')

    # Accounting integration
    valuation_in_account_id = models.ForeignKey(AccountAccount, null=True, blank=True, on_delete=models.SET_NULL, related_name='stock_valuation_in_locations')
    valuation_out_account_id = models.ForeignKey(AccountAccount, null=True, blank=True, on_delete=models.SET_NULL, related_name='stock_valuation_out_locations')

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'stock_location'

    def __str__(self):
        return self.complete_name or self.name


class StockWarehouse(models.Model):
    """Warehouse - Based on Odoo stock.warehouse"""
    name = models.CharField(max_length=128)
    code = models.CharField(max_length=5)
    partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.SET_NULL)
    view_location_id = models.ForeignKey(StockLocation, on_delete=models.CASCADE, related_name='warehouse_view')
    lot_stock_id = models.ForeignKey(StockLocation, on_delete=models.CASCADE, related_name='warehouse_lot_stock')
    wh_input_stock_loc_id = models.ForeignKey(StockLocation, on_delete=models.CASCADE, related_name='warehouse_input')
    wh_qc_stock_loc_id = models.ForeignKey(StockLocation, on_delete=models.CASCADE, related_name='warehouse_qc')
    wh_output_stock_loc_id = models.ForeignKey(StockLocation, on_delete=models.CASCADE, related_name='warehouse_output')
    wh_pack_stock_loc_id = models.ForeignKey(StockLocation, on_delete=models.CASCADE, related_name='warehouse_pack')

    sequence = models.IntegerField(default=10)
    active = models.BooleanField(default=True)
    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    class Meta:
        db_table = 'stock_warehouse'

    def __str__(self):
        return self.name


class StockMove(models.Model):
    """Stock Move - Based on Odoo stock.move"""

    STATES = [
        ('draft', 'New'),
        ('cancel', 'Cancelled'),
        ('waiting', 'Waiting Another Move'),
        ('confirmed', 'Waiting Availability'),
        ('partially_available', 'Partially Available'),
        ('assigned', 'Available'),
        ('done', 'Done'),
    ]

    name = models.CharField(max_length=256)
    sequence = models.IntegerField(default=10)
    priority = models.CharField(max_length=1, default='1')
    date = models.DateTimeField(default=datetime.now)
    date_expected = models.DateTimeField(default=datetime.now)
    date_deadline = models.DateTimeField(null=True, blank=True)

    # Product and quantities
    product_id = models.ForeignKey(ProductProduct, on_delete=models.CASCADE)
    product_qty = models.DecimalField(max_digits=16, decimal_places=4, default=1)
    product_uom_qty = models.DecimalField(max_digits=16, decimal_places=4, default=1)
    product_uom = models.IntegerField(default=1)

    # Locations
    location_id = models.ForeignKey(StockLocation, on_delete=models.CASCADE, related_name='stock_move_location_id')
    location_dest_id = models.ForeignKey(StockLocation, on_delete=models.CASCADE, related_name='stock_move_location_dest_id')

    # Partner
    partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.SET_NULL)

    # State and origin
    state = models.CharField(max_length=20, choices=STATES, default='draft')
    origin = models.CharField(max_length=64, blank=True)

    # Procurement and planning
    procure_method = models.CharField(max_length=16, choices=[('make_to_stock', 'Take From Stock'), ('make_to_order', 'Create Procurement')], default='make_to_stock')

    # Accounting integration
    account_move_ids = models.ManyToManyField(AccountMove, blank=True, help_text="Generated accounting entries")

    # References
    sale_line_id = models.ForeignKey(SaleOrderLine, null=True, blank=True, on_delete=models.SET_NULL)
    purchase_line_id = models.ForeignKey(PurchaseOrderLine, null=True, blank=True, on_delete=models.SET_NULL)

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'stock_move'

    def __str__(self):
        return self.name

    def action_done(self):
        """Mark the move as done"""
        if self.state == 'assigned':
            self.state = 'done'
            self.save()
            # Here you would create accounting entries
            return True
        return False


# ============================================================================
# MAIL & COMMUNICATION SYSTEM (Odoo-Style)
# ============================================================================

class MailMessage(models.Model):
    """Mail Message - Based on Odoo mail.message"""

    MESSAGE_TYPES = [
        ('email', 'Email'),
        ('comment', 'Comment'),
        ('notification', 'System notification'),
        ('user_notification', 'User notification'),
    ]

    subject = models.CharField(max_length=256, blank=True)
    date = models.DateTimeField(default=datetime.now, db_index=True)
    body = models.TextField(blank=True)
    attachment_ids = models.CharField(max_length=256, blank=True)  # Simplified
    parent_id = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE)

    # Type and subtype
    message_type = models.CharField(max_length=32, choices=MESSAGE_TYPES, default='email')
    subtype_id = models.ForeignKey('MailMessageSubtype', null=True, blank=True, on_delete=models.SET_NULL)

    # Author and recipients
    author_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.SET_NULL)
    email_from = models.CharField(max_length=128, blank=True)
    reply_to = models.CharField(max_length=128, blank=True)

    # Tracking
    model = models.CharField(max_length=128, blank=True, db_index=True)
    res_id = models.IntegerField(null=True, blank=True, db_index=True)
    record_name = models.CharField(max_length=128, blank=True)

    # Notification
    notification_ids = models.ManyToManyField('MailNotification', blank=True)

    # Tracking values
    tracking_value_ids = models.ManyToManyField('MailTrackingValue', blank=True)

    class Meta:
        db_table = 'mail_message'
        indexes = [
            models.Index(fields=['model', 'res_id']),
            models.Index(fields=['date']),
        ]

    def __str__(self):
        return self.subject or 'No Subject'


class MailMessageSubtype(models.Model):
    """Mail Message Subtype - Based on Odoo mail.message.subtype"""
    name = models.CharField(max_length=64)
    description = models.TextField(blank=True)
    internal = models.BooleanField(default=True)
    parent_id = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE)
    relation_field = models.CharField(max_length=64, blank=True)
    res_model = models.CharField(max_length=64, blank=True)
    default = models.BooleanField(default=True)
    sequence = models.IntegerField(default=1)
    hidden = models.BooleanField(default=False)

    class Meta:
        db_table = 'mail_message_subtype'

    def __str__(self):
        return self.name


class MailNotification(models.Model):
    """Mail Notification - Based on Odoo mail.notification"""

    NOTIFICATION_TYPES = [
        ('inbox', 'Inbox'),
        ('email', 'Email'),
    ]

    NOTIFICATION_STATUS = [
        ('ready', 'Ready to Send'),
        ('sent', 'Sent'),
        ('bounce', 'Bounced'),
        ('exception', 'Exception'),
        ('canceled', 'Canceled'),
    ]

    mail_message_id = models.ForeignKey(MailMessage, on_delete=models.CASCADE)
    res_partner_id = models.ForeignKey(ResPartner, null=True, blank=True, on_delete=models.CASCADE)
    notification_type = models.CharField(max_length=8, choices=NOTIFICATION_TYPES, default='inbox')
    notification_status = models.CharField(max_length=16, choices=NOTIFICATION_STATUS, default='ready')
    is_read = models.BooleanField(default=False)
    read_date = models.DateTimeField(null=True, blank=True)
    failure_type = models.CharField(max_length=64, blank=True)
    failure_reason = models.TextField(blank=True)

    class Meta:
        db_table = 'mail_notification'

    def __str__(self):
        return f"Notification for {self.res_partner_id.name if self.res_partner_id else 'System'}"


class MailTrackingValue(models.Model):
    """Mail Tracking Value - Based on Odoo mail.tracking.value"""
    field = models.CharField(max_length=64)
    field_desc = models.CharField(max_length=128)
    field_type = models.CharField(max_length=16)
    old_value_integer = models.IntegerField(null=True, blank=True)
    old_value_float = models.DecimalField(max_digits=16, decimal_places=2, null=True, blank=True)
    old_value_monetary = models.DecimalField(max_digits=16, decimal_places=2, null=True, blank=True)
    old_value_char = models.CharField(max_length=256, blank=True)
    old_value_text = models.TextField(blank=True)
    old_value_datetime = models.DateTimeField(null=True, blank=True)
    new_value_integer = models.IntegerField(null=True, blank=True)
    new_value_float = models.DecimalField(max_digits=16, decimal_places=2, null=True, blank=True)
    new_value_monetary = models.DecimalField(max_digits=16, decimal_places=2, null=True, blank=True)
    new_value_char = models.CharField(max_length=256, blank=True)
    new_value_text = models.TextField(blank=True)
    new_value_datetime = models.DateTimeField(null=True, blank=True)
    mail_message_id = models.ForeignKey(MailMessage, on_delete=models.CASCADE)

    class Meta:
        db_table = 'mail_tracking_value'

    def __str__(self):
        return f"Tracking: {self.field_desc}"


# ============================================================================
# LOCALIZATION & COMPLIANCE (Country-specific)
# ============================================================================

class ResCountry(models.Model):
    """Country - Based on Odoo res.country"""
    name = models.CharField(max_length=128)
    code = models.CharField(max_length=2, unique=True)
    address_format = models.TextField(blank=True)
    currency_id = models.CharField(max_length=3, blank=True)
    phone_code = models.IntegerField(null=True, blank=True)

    class Meta:
        db_table = 'res_country'
        verbose_name_plural = "Countries"

    def __str__(self):
        return self.name


class ResCountryState(models.Model):
    """Country State - Based on Odoo res.country.state"""
    country_id = models.ForeignKey(ResCountry, on_delete=models.CASCADE)
    name = models.CharField(max_length=128)
    code = models.CharField(max_length=3)

    class Meta:
        db_table = 'res_country_state'

    def __str__(self):
        return f"{self.name} ({self.country_id.name})"


class ResCurrency(models.Model):
    """Currency - Based on Odoo res.currency"""
    name = models.CharField(max_length=3, unique=True)
    symbol = models.CharField(max_length=4)
    rate = models.DecimalField(max_digits=12, decimal_places=6, default=1.0)
    rounding = models.DecimalField(max_digits=12, decimal_places=6, default=0.01)
    decimal_places = models.IntegerField(default=2)
    active = models.BooleanField(default=True)
    position = models.CharField(max_length=8, choices=[('after', 'After Amount'), ('before', 'Before Amount')], default='after')

    class Meta:
        db_table = 'res_currency'
        verbose_name_plural = "Currencies"

    def __str__(self):
        return self.name
