"""
Comprehensive Accounting Models for ERP System
"""
from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from decimal import Decimal
import uuid


class Company(models.Model):
    """Company/Organization Information"""
    name = models.CharField(max_length=200)
    legal_name = models.CharField(max_length=200, blank=True)
    tax_id = models.CharField(max_length=50, unique=True)
    registration_number = models.CharField(max_length=50, blank=True)
    address = models.TextField()
    phone = models.CharField(max_length=20)
    email = models.EmailField()
    website = models.URLField(blank=True)
    currency = models.CharField(max_length=3, default='PKR')
    fiscal_year_start = models.DateField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Companies"

    def __str__(self):
        return self.name


class AccountType(models.Model):
    """Chart of Accounts Types"""
    ACCOUNT_TYPES = [
        ('ASSET', 'Asset'),
        ('LIABILITY', 'Liability'),
        ('EQUITY', 'Equity'),
        ('REVENUE', 'Revenue'),
        ('EXPENSE', 'Expense'),
    ]
    
    name = models.CharField(max_length=100)
    type = models.CharField(max_length=20, choices=ACCOUNT_TYPES)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.name} ({self.type})"


class Account(models.Model):
    """Chart of Accounts"""
    code = models.CharField(max_length=20, unique=True)
    name = models.CharField(max_length=200)
    account_type = models.ForeignKey(AccountType, on_delete=models.CASCADE)
    parent = models.ForeignKey('self', null=True, blank=True, on_delete=models.CASCADE)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    is_reconcilable = models.BooleanField(default=False)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"

    @property
    def balance(self):
        """Calculate account balance"""
        from django.db.models import Sum
        debit_sum = self.journal_entries.aggregate(
            total=Sum('debit_amount'))['total'] or Decimal('0')
        credit_sum = self.journal_entries.aggregate(
            total=Sum('credit_amount'))['total'] or Decimal('0')
        
        if self.account_type.type in ['ASSET', 'EXPENSE']:
            return debit_sum - credit_sum
        else:
            return credit_sum - debit_sum


class Customer(models.Model):
    """Customer Master Data"""
    name = models.CharField(max_length=200)
    company_name = models.CharField(max_length=200, blank=True)
    email = models.EmailField(blank=True)
    phone = models.CharField(max_length=20, blank=True)
    address = models.TextField(blank=True)
    city = models.CharField(max_length=100, blank=True)
    country = models.CharField(max_length=100, default='Pakistan')
    tax_id = models.CharField(max_length=50, blank=True)
    credit_limit = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    payment_terms = models.IntegerField(default=30, help_text="Payment terms in days")
    is_active = models.BooleanField(default=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    @property
    def outstanding_balance(self):
        """Calculate outstanding customer balance"""
        from django.db.models import Sum
        invoices = self.invoices.filter(status__in=['SENT', 'PARTIAL'])
        total_amount = invoices.aggregate(total=Sum('total_amount'))['total'] or Decimal('0')
        total_paid = invoices.aggregate(total=Sum('paid_amount'))['total'] or Decimal('0')
        return total_amount - total_paid


class Vendor(models.Model):
    """Vendor/Supplier Master Data"""
    name = models.CharField(max_length=200)
    company_name = models.CharField(max_length=200, blank=True)
    email = models.EmailField(blank=True)
    phone = models.CharField(max_length=20, blank=True)
    address = models.TextField(blank=True)
    city = models.CharField(max_length=100, blank=True)
    country = models.CharField(max_length=100, default='Pakistan')
    tax_id = models.CharField(max_length=50, blank=True)
    payment_terms = models.IntegerField(default=30, help_text="Payment terms in days")
    is_active = models.BooleanField(default=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    @property
    def outstanding_balance(self):
        """Calculate outstanding vendor balance"""
        from django.db.models import Sum
        bills = self.bills.filter(status__in=['RECEIVED', 'PARTIAL'])
        total_amount = bills.aggregate(total=Sum('total_amount'))['total'] or Decimal('0')
        total_paid = bills.aggregate(total=Sum('paid_amount'))['total'] or Decimal('0')
        return total_amount - total_paid


class TaxRate(models.Model):
    """Tax Configuration"""
    name = models.CharField(max_length=100)
    rate = models.DecimalField(max_digits=5, decimal_places=2, 
                              validators=[MinValueValidator(0), MaxValueValidator(100)])
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.name} ({self.rate}%)"


class Journal(models.Model):
    """Journal/Book Configuration"""
    JOURNAL_TYPES = [
        ('GENERAL', 'General Journal'),
        ('SALES', 'Sales Journal'),
        ('PURCHASE', 'Purchase Journal'),
        ('CASH', 'Cash Journal'),
        ('BANK', 'Bank Journal'),
    ]
    
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=10, unique=True)
    type = models.CharField(max_length=20, choices=JOURNAL_TYPES)
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.code} - {self.name}"


class JournalEntry(models.Model):
    """Journal Entry Header"""
    ENTRY_STATES = [
        ('DRAFT', 'Draft'),
        ('POSTED', 'Posted'),
        ('CANCELLED', 'Cancelled'),
    ]
    
    reference = models.CharField(max_length=50, unique=True)
    journal = models.ForeignKey(Journal, on_delete=models.CASCADE)
    date = models.DateField()
    description = models.TextField()
    state = models.CharField(max_length=20, choices=ENTRY_STATES, default='DRAFT')
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Journal Entries"
        ordering = ['-date', '-created_at']

    def __str__(self):
        return f"{self.reference} - {self.date}"

    @property
    def total_debit(self):
        return sum(line.debit_amount for line in self.lines.all())

    @property
    def total_credit(self):
        return sum(line.credit_amount for line in self.lines.all())

    @property
    def is_balanced(self):
        return self.total_debit == self.total_credit

    def post(self):
        """Post the journal entry"""
        if self.is_balanced and self.state == 'DRAFT':
            self.state = 'POSTED'
            self.save()
            return True
        return False


class JournalEntryLine(models.Model):
    """Journal Entry Line Items"""
    entry = models.ForeignKey(JournalEntry, related_name='lines', on_delete=models.CASCADE)
    account = models.ForeignKey(Account, related_name='journal_entries', on_delete=models.CASCADE)
    description = models.CharField(max_length=200)
    debit_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    credit_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    partner = models.CharField(max_length=200, blank=True, help_text="Customer/Vendor name")

    def __str__(self):
        return f"{self.entry.reference} - {self.account.code}"

    def clean(self):
        from django.core.exceptions import ValidationError
        if self.debit_amount > 0 and self.credit_amount > 0:
            raise ValidationError("A line cannot have both debit and credit amounts")
        if self.debit_amount == 0 and self.credit_amount == 0:
            raise ValidationError("A line must have either debit or credit amount")


class Invoice(models.Model):
    """Customer Invoices"""
    INVOICE_STATES = [
        ('DRAFT', 'Draft'),
        ('SENT', 'Sent'),
        ('PARTIAL', 'Partially Paid'),
        ('PAID', 'Paid'),
        ('CANCELLED', 'Cancelled'),
    ]

    number = models.CharField(max_length=50, unique=True)
    customer = models.ForeignKey(Customer, related_name='invoices', on_delete=models.CASCADE)
    date = models.DateField()
    due_date = models.DateField()
    description = models.TextField(blank=True)
    subtotal = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    paid_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    status = models.CharField(max_length=20, choices=INVOICE_STATES, default='DRAFT')
    journal_entry = models.ForeignKey(JournalEntry, null=True, blank=True, on_delete=models.SET_NULL)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-date', '-created_at']

    def __str__(self):
        return f"INV-{self.number}"

    @property
    def outstanding_amount(self):
        return self.total_amount - self.paid_amount

    @property
    def is_overdue(self):
        from django.utils import timezone
        return self.due_date < timezone.now().date() and self.outstanding_amount > 0


class InvoiceLine(models.Model):
    """Invoice Line Items"""
    invoice = models.ForeignKey(Invoice, related_name='lines', on_delete=models.CASCADE)
    description = models.CharField(max_length=200)
    quantity = models.DecimalField(max_digits=10, decimal_places=2, default=1)
    unit_price = models.DecimalField(max_digits=15, decimal_places=2)
    tax_rate = models.ForeignKey(TaxRate, null=True, blank=True, on_delete=models.SET_NULL)
    line_total = models.DecimalField(max_digits=15, decimal_places=2, default=0)

    def save(self, *args, **kwargs):
        # Calculate line total
        subtotal = self.quantity * self.unit_price
        tax_amount = subtotal * (self.tax_rate.rate / 100) if self.tax_rate else 0
        self.line_total = subtotal + tax_amount
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.invoice.number} - {self.description}"


class Bill(models.Model):
    """Vendor Bills"""
    BILL_STATES = [
        ('DRAFT', 'Draft'),
        ('RECEIVED', 'Received'),
        ('PARTIAL', 'Partially Paid'),
        ('PAID', 'Paid'),
        ('CANCELLED', 'Cancelled'),
    ]

    number = models.CharField(max_length=50, unique=True)
    vendor = models.ForeignKey(Vendor, related_name='bills', on_delete=models.CASCADE)
    vendor_reference = models.CharField(max_length=50, blank=True)
    date = models.DateField()
    due_date = models.DateField()
    description = models.TextField(blank=True)
    subtotal = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    tax_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    total_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    paid_amount = models.DecimalField(max_digits=15, decimal_places=2, default=0)
    status = models.CharField(max_length=20, choices=BILL_STATES, default='DRAFT')
    journal_entry = models.ForeignKey(JournalEntry, null=True, blank=True, on_delete=models.SET_NULL)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-date', '-created_at']

    def __str__(self):
        return f"BILL-{self.number}"

    @property
    def outstanding_amount(self):
        return self.total_amount - self.paid_amount


class Payment(models.Model):
    """Payments (Customer/Vendor)"""
    PAYMENT_TYPES = [
        ('CUSTOMER', 'Customer Payment'),
        ('VENDOR', 'Vendor Payment'),
    ]

    PAYMENT_METHODS = [
        ('CASH', 'Cash'),
        ('BANK', 'Bank Transfer'),
        ('CHECK', 'Check'),
        ('CARD', 'Credit/Debit Card'),
    ]

    reference = models.CharField(max_length=50, unique=True)
    type = models.CharField(max_length=20, choices=PAYMENT_TYPES)
    customer = models.ForeignKey(Customer, null=True, blank=True, on_delete=models.CASCADE)
    vendor = models.ForeignKey(Vendor, null=True, blank=True, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=15, decimal_places=2)
    date = models.DateField()
    method = models.CharField(max_length=20, choices=PAYMENT_METHODS)
    description = models.TextField(blank=True)
    journal_entry = models.ForeignKey(JournalEntry, null=True, blank=True, on_delete=models.SET_NULL)
    company = models.ForeignKey(Company, on_delete=models.CASCADE)
    created_by = models.ForeignKey(User, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"PAY-{self.reference}"
