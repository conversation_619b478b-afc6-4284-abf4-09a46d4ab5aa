<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.NavigableList">
        <div class="o-mail-NavigableList bg-view m-0 p-0" t-ref="root" t-att-class="props.class">
            <div t-if="show" class="o-open border" t-on-mousedown.prevent="">
                <div t-if="props.isLoading" class="o-mail-NavigableList-item">
                    <a href="#" class="d-flex align-items-center w-100 py-2 px-4 gap-1">
                        <i class="fa fa-spin fa-circle-o-notch"/>
                        <t t-esc="props.placeholder"/>
                    </a>
                </div>
                <t t-else="">
                    <div
                        t-foreach="props.options" t-as="option" t-key="option_index"
                        class="o-mail-NavigableList-item"
                        t-att-class="option.classList"
                        t-on-mouseenter="() => this.onOptionMouseEnter(option_index)"
                        t-on-click="(ev) => this.selectOption(ev, option_index)"
                    >
                        <a href="#" class="d-flex align-items-center w-100 py-2 px-4" t-att-class="{ 'o-mail-NavigableList-active bg-300': state.activeIndex === option_index }">
                            <t t-if="props.optionTemplate" t-call="{{ props.optionTemplate }}"/>
                            <t t-else="" t-esc="option.label"/>
                        </a>
                    </div>
                </t>
            </div>
        </div>
    </t>

</templates>
