#!/usr/bin/env python3
import xmlrpc.client

def final_uom_fix():
    try:
        # Connection details
        url = 'http://localhost:8069'
        db = 'dataicraft'
        username = '<EMAIL>'
        password = 'admin123'
        
        # Connect to Odoo
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            print("❌ Authentication failed!")
            return
        
        print(f"✅ Connected to Odoo as user ID: {uid}")
        
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        print("\n🔧 === Final Comprehensive UoM Fix === 🔧")
        
        # Step 1: Get all UoM categories and their UoMs
        print("📊 Current UoM structure:")
        
        categories = models.execute_kw(
            db, uid, password,
            'uom.category', 'search_read',
            [[]],
            {'fields': ['id', 'name']}
        )
        
        unit_cat_id = None
        working_time_cat_id = None
        
        for cat in categories:
            uoms = models.execute_kw(
                db, uid, password,
                'uom.uom', 'search_read',
                [[['category_id', '=', cat['id']]]],
                {'fields': ['id', 'name', 'uom_type']}
            )
            
            print(f"  📂 {cat['name']} (ID: {cat['id']})")
            for uom in uoms:
                print(f"    📏 {uom['name']} (ID: {uom['id']}) - {uom['uom_type']}")
            
            if cat['name'] == 'Unit':
                unit_cat_id = cat['id']
            elif cat['name'] == 'Working Time':
                working_time_cat_id = cat['id']
        
        # Step 2: Standardize on Units UoM for everything except true time tracking
        print(f"\n🔧 Standardizing UoMs...")
        
        # Get Units UoM (from Unit category)
        units_uom = models.execute_kw(
            db, uid, password,
            'uom.uom', 'search_read',
            [[['name', '=', 'Units'], ['category_id', '=', unit_cat_id]]],
            {'fields': ['id', 'name']}
        )
        
        if not units_uom:
            print("❌ Units UoM not found!")
            return
        
        units_id = units_uom[0]['id']
        print(f"📏 Using Units UoM (ID: {units_id}) for most products")
        
        # Step 3: Update ALL products to use Units UoM initially
        print(f"\n🔧 Setting all products to use Units UoM...")
        
        all_products = models.execute_kw(
            db, uid, password,
            'product.template', 'search',
            [[]]
        )
        
        if all_products:
            models.execute_kw(
                db, uid, password,
                'product.template', 'write',
                [all_products, {
                    'uom_id': units_id,
                    'uom_po_id': units_id
                }]
            )
            print(f"✅ Updated {len(all_products)} products to use Units UoM")
        
        # Step 4: Remove conflicting Hour UoM from Unit category
        print(f"\n🔧 Removing conflicting Hour UoM...")
        
        hour_unit = models.execute_kw(
            db, uid, password,
            'uom.uom', 'search',
            [[['name', '=', 'Hour'], ['category_id', '=', unit_cat_id]]]
        )
        
        if hour_unit:
            try:
                models.execute_kw(
                    db, uid, password,
                    'uom.uom', 'unlink',
                    [hour_unit]
                )
                print("✅ Removed conflicting Hour UoM from Unit category")
            except Exception as e:
                print(f"⚠️ Could not remove Hour UoM: {e}")
        
        # Step 5: Clear all draft orders to avoid conflicts
        print(f"\n🔧 Clearing all draft orders...")
        
        try:
            # Clear sales orders
            draft_so = models.execute_kw(
                db, uid, password,
                'sale.order', 'search',
                [[['state', 'in', ['draft', 'sent']]]]
            )
            
            for so_id in draft_so:
                try:
                    models.execute_kw(
                        db, uid, password,
                        'sale.order', 'unlink',
                        [[so_id]]
                    )
                except:
                    pass
            
            print(f"✅ Cleared {len(draft_so)} draft sales orders")
            
            # Clear purchase orders
            draft_po = models.execute_kw(
                db, uid, password,
                'purchase.order', 'search',
                [[['state', 'in', ['draft', 'sent']]]]
            )
            
            for po_id in draft_po:
                try:
                    models.execute_kw(
                        db, uid, password,
                        'purchase.order', 'unlink',
                        [[po_id]]
                    )
                except:
                    pass
            
            print(f"✅ Cleared {len(draft_po)} draft purchase orders")
            
        except Exception as e:
            print(f"⚠️ Error clearing orders: {e}")
        
        # Step 6: Update company settings
        print(f"\n🔧 Updating company settings...")
        
        try:
            company_ids = models.execute_kw(
                db, uid, password,
                'res.company', 'search', [[]]
            )
            
            if company_ids:
                # Find Hours UoM in Working Time category for timesheet encoding
                hours_working_time = models.execute_kw(
                    db, uid, password,
                    'uom.uom', 'search_read',
                    [[['name', '=', 'Hours'], ['category_id', '=', working_time_cat_id]]],
                    {'fields': ['id']}
                )
                
                if hours_working_time:
                    models.execute_kw(
                        db, uid, password,
                        'res.company', 'write',
                        [company_ids, {
                            'timesheet_encode_uom_id': hours_working_time[0]['id'],
                        }]
                    )
                    print("✅ Company timesheet encoding set to Hours (Working Time)")
        
        except Exception as e:
            print(f"⚠️ Company settings: {e}")
        
        # Step 7: Restart Odoo server to clear any cached UoM data
        print(f"\n🔄 Recommendation: Restart Odoo server...")
        print("The server should be restarted to clear any cached UoM data")
        
        print(f"\n🎉 Final UoM Fix Complete!")
        print(f"✅ All products now use Units UoM (Unit category)")
        print(f"✅ Removed conflicting Hour UoM")
        print(f"✅ Cleared all draft orders")
        print(f"✅ Company settings updated")
        
        print(f"\n📋 Current UoM Strategy:")
        print(f"  📦 ALL products → Units UoM (Unit category)")
        print(f"  ⏰ Timesheet encoding → Hours (Working Time category)")
        print(f"  🚫 No mixing of UoM categories")
        
        print(f"\n🔄 Next Steps:")
        print("1. Restart Odoo server (recommended)")
        print("2. Refresh your browser")
        print("3. Try creating orders - should work now")
        print("4. All products will use 'Units' as UoM")
        print("5. For time tracking, use quantity decimals (0.5 = 30 minutes)")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    final_uom_fix()
