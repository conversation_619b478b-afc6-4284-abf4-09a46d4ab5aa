#!/usr/bin/env python3
import psycopg2

def nuclear_uom_investigation():
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='dataicraft',
            user='odoo',
            password='odoo123'
        )
        cur = conn.cursor()
        
        print("💥 === NUCLEAR UoM INVESTIGATION === 💥")
        print("Finding EVERY possible source of Hours UoM")
        
        # Step 1: Find ALL UoMs with "Hours" or similar
        print("\n🔍 ALL UoMs containing 'hour' (case insensitive):")
        
        cur.execute("""
            SELECT u.id, u.name, c.id as cat_id, c.name as category, u.active, u.uom_type
            FROM uom_uom u
            JOIN uom_category c ON u.category_id = c.id
            WHERE LOWER(u.name) LIKE '%hour%'
            ORDER BY u.id;
        """)
        
        hours_uoms = cur.fetchall()
        
        if hours_uoms:
            print(f"Found {len(hours_uoms)} Hours-related UoMs:")
            for uom in hours_uoms:
                uom_id, name, cat_id, category, active, uom_type = uom
                print(f"  🕐 ID:{uom_id} | {name} | Category: {category} (ID:{cat_id}) | Active:{active} | Type:{uom_type}")
        else:
            print("✅ No Hours UoMs found")
        
        # Step 2: Check ALL product templates for ANY non-Units UoM
        print(f"\n📦 ALL products with non-Units UoM:")
        
        cur.execute("""
            SELECT 
                pt.id,
                pt.name,
                pt.uom_id,
                u1.name as uom_name,
                c1.name as uom_category,
                pt.uom_po_id,
                u2.name as po_uom_name,
                c2.name as po_uom_category,
                pt.type,
                pt.service_type
            FROM product_template pt
            LEFT JOIN uom_uom u1 ON pt.uom_id = u1.id
            LEFT JOIN uom_category c1 ON u1.category_id = c1.id
            LEFT JOIN uom_uom u2 ON pt.uom_po_id = u2.id
            LEFT JOIN uom_category c2 ON u2.category_id = c2.id
            WHERE pt.uom_id != 1 OR pt.uom_po_id != 1
            ORDER BY pt.id;
        """)
        
        non_units_products = cur.fetchall()
        
        if non_units_products:
            print(f"Found {len(non_units_products)} products NOT using Units:")
            for product in non_units_products:
                pid, name, uom_id, uom_name, uom_cat, po_uom_id, po_uom_name, po_uom_cat, ptype, service_type = product
                print(f"  📦 ID:{pid} | {name}")
                print(f"     Type: {ptype} | Service: {service_type}")
                print(f"     UoM: {uom_name} (ID:{uom_id}) - {uom_cat}")
                print(f"     PO UoM: {po_uom_name} (ID:{po_uom_id}) - {po_uom_cat}")
                print()
        else:
            print("✅ ALL products use Units UoM")
        
        # Step 3: Check product variants (product.product)
        print(f"\n🔍 Checking product variants:")
        
        cur.execute("""
            SELECT COUNT(*) FROM product_product;
        """)
        total_variants = cur.fetchone()[0]
        print(f"Total product variants: {total_variants}")
        
        # Step 4: Check for ANY existing orders with UoM conflicts
        print(f"\n📋 Checking ALL existing orders:")
        
        cur.execute("""
            SELECT 
                'SALE' as order_type,
                sol.id as line_id,
                so.id as order_id,
                so.name as order_name,
                so.state,
                pt.id as product_id,
                pt.name as product_name,
                sol.product_uom as line_uom_id,
                u1.name as line_uom_name,
                c1.name as line_uom_category,
                pt.uom_id as product_uom_id,
                u2.name as product_uom_name,
                c2.name as product_uom_category
            FROM sale_order_line sol
            JOIN sale_order so ON sol.order_id = so.id
            LEFT JOIN product_product pp ON sol.product_id = pp.id
            LEFT JOIN product_template pt ON pp.product_tmpl_id = pt.id
            LEFT JOIN uom_uom u1 ON sol.product_uom = u1.id
            LEFT JOIN uom_category c1 ON u1.category_id = c1.id
            LEFT JOIN uom_uom u2 ON pt.uom_id = u2.id
            LEFT JOIN uom_category c2 ON u2.category_id = c2.id
            
            UNION ALL
            
            SELECT 
                'PURCHASE' as order_type,
                pol.id as line_id,
                po.id as order_id,
                po.name as order_name,
                po.state,
                pt.id as product_id,
                pt.name as product_name,
                pol.product_uom as line_uom_id,
                u1.name as line_uom_name,
                c1.name as line_uom_category,
                pt.uom_po_id as product_uom_id,
                u2.name as product_uom_name,
                c2.name as product_uom_category
            FROM purchase_order_line pol
            JOIN purchase_order po ON pol.order_id = po.id
            LEFT JOIN product_product pp ON pol.product_id = pp.id
            LEFT JOIN product_template pt ON pp.product_tmpl_id = pt.id
            LEFT JOIN uom_uom u1 ON pol.product_uom = u1.id
            LEFT JOIN uom_category c1 ON u1.category_id = c1.id
            LEFT JOIN uom_uom u2 ON pt.uom_po_id = u2.id
            LEFT JOIN uom_category c2 ON u2.category_id = c2.id
            
            ORDER BY order_type, line_id DESC;
        """)
        
        all_order_lines = cur.fetchall()
        
        if all_order_lines:
            print(f"Found {len(all_order_lines)} order lines:")
            conflicts_found = False
            
            for line in all_order_lines:
                order_type, line_id, order_id, order_name, state, product_id, product_name, line_uom_id, line_uom_name, line_uom_category, product_uom_id, product_uom_name, product_uom_category = line
                
                print(f"  📋 {order_type} Line {line_id} | Order: {order_name} ({state})")
                print(f"     Product: {product_name} (ID:{product_id})")
                print(f"     Line UoM: {line_uom_name} (ID:{line_uom_id}) - {line_uom_category}")
                print(f"     Product UoM: {product_uom_name} (ID:{product_uom_id}) - {product_uom_category}")
                
                if line_uom_category != product_uom_category:
                    print(f"     🔴 CONFLICT FOUND: {line_uom_category} != {product_uom_category}")
                    conflicts_found = True
                else:
                    print(f"     ✅ OK")
                print()
            
            if conflicts_found:
                print("🔴 UoM CONFLICTS FOUND IN EXISTING ORDERS!")
            else:
                print("✅ No UoM conflicts in existing orders")
        else:
            print("✅ No order lines found")
        
        # Step 5: Check for cached/default UoM settings
        print(f"\n⚙️ Checking system UoM settings:")
        
        cur.execute("""
            SELECT 
                rc.id,
                rc.name,
                rc.timesheet_encode_uom_id,
                u.name as timesheet_uom_name,
                c.name as timesheet_uom_category
            FROM res_company rc
            LEFT JOIN uom_uom u ON rc.timesheet_encode_uom_id = u.id
            LEFT JOIN uom_category c ON u.category_id = c.id;
        """)
        
        company_settings = cur.fetchall()
        
        for company in company_settings:
            comp_id, comp_name, timesheet_uom_id, timesheet_uom_name, timesheet_uom_category = company
            print(f"  🏢 Company: {comp_name} (ID:{comp_id})")
            print(f"     Timesheet UoM: {timesheet_uom_name} (ID:{timesheet_uom_id}) - {timesheet_uom_category}")
        
        # Step 6: NUCLEAR SOLUTION
        print(f"\n💥 NUCLEAR SOLUTION:")
        print("1. Delete ALL Hours UoMs from ALL categories:")
        for uom in hours_uoms:
            print(f"   DELETE FROM uom_uom WHERE id = {uom[0]};")
        
        print("\n2. Force ALL products to Units:")
        print("   UPDATE product_template SET uom_id = 1, uom_po_id = 1;")
        
        print("\n3. Delete ALL existing orders:")
        print("   DELETE FROM sale_order_line;")
        print("   DELETE FROM purchase_order_line;")
        print("   DELETE FROM sale_order;")
        print("   DELETE FROM purchase_order;")
        
        print("\n4. Set company timesheet to Units:")
        print("   UPDATE res_company SET timesheet_encode_uom_id = 1;")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    nuclear_uom_investigation()
