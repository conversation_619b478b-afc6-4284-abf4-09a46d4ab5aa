<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.ImStatus">
        <div class="o-mail-ImStatus d-flex justify-content-center flex-shrink-0 align-items-center rounded-circle bg-inherit" t-att-class="props.className" t-att-style="props.style">
            <span class="d-flex flex-column" name="icon">
                <t t-if="!props.thread or !typingService.hasTypingMembers(props.thread)">
                    <i t-if="props.persona.im_status === 'online'" class="fa fa-circle text-success" title="Online" role="img" aria-label="User is online"/>
                    <i t-elif="props.persona.im_status === 'away'" class="fa fa-circle o-away" title="Idle" role="img" aria-label="User is idle"/>
                    <i t-elif="props.persona.im_status === 'offline'" class="fa fa-circle-o text-700" title="Offline" role="img" aria-label="User is offline"/>
                    <i t-elif="props.persona.im_status === 'bot'" class="fa fa-heart text-success" title="Bot" role="img" aria-label="User is a bot"/>
                    <i t-else="" class="fa fa-fw fa-question-circle" title="No IM status available"/>
                </t>
                <Typing t-if="props.thread" channel="props.thread" size="'medium'" displayText="false" />
            </span>
        </div>
    </t>

</templates>
