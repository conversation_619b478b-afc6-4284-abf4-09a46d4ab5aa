# Generated by Django 4.2.21 on 2025-07-15 16:32

import datetime
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('accounting', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountAnalyticAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('code', models.CharField(blank=True, max_length=32)),
                ('active', models.BooleanField(default=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
            ],
            options={
                'db_table': 'account_analytic_account',
            },
        ),
        migrations.CreateModel(
            name='AccountFiscalPosition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('active', models.BooleanField(default=True)),
                ('auto_apply', models.BooleanField(default=False)),
                ('vat_required', models.BooleanField(default=False)),
                ('note', models.TextField(blank=True)),
                ('sequence', models.IntegerField(default=10)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
            ],
            options={
                'db_table': 'account_fiscal_position',
            },
        ),
        migrations.CreateModel(
            name='AccountFullReconcile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
            ],
            options={
                'db_table': 'account_full_reconcile',
            },
        ),
        migrations.CreateModel(
            name='AccountMove',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='/', max_length=64)),
                ('ref', models.CharField(blank=True, max_length=64)),
                ('date', models.DateField(db_index=True)),
                ('move_type', models.CharField(choices=[('entry', 'Journal Entry'), ('out_invoice', 'Customer Invoice'), ('out_refund', 'Customer Credit Note'), ('in_invoice', 'Vendor Bill'), ('in_refund', 'Vendor Credit Note'), ('out_receipt', 'Sales Receipt'), ('in_receipt', 'Purchase Receipt')], db_index=True, default='entry', max_length=16)),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('posted', 'Posted'), ('cancel', 'Cancelled')], db_index=True, default='draft', max_length=8)),
                ('currency_id', models.CharField(default='PKR', max_length=3)),
                ('invoice_date', models.DateField(blank=True, db_index=True, null=True)),
                ('invoice_date_due', models.DateField(blank=True, db_index=True, null=True)),
                ('amount_untaxed', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('amount_tax', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('amount_total', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('amount_residual', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('payment_state', models.CharField(choices=[('not_paid', 'Not Paid'), ('in_payment', 'In Payment'), ('paid', 'Paid'), ('partial', 'Partially Paid'), ('reversed', 'Reversed'), ('invoicing_legacy', 'Invoicing App Legacy')], db_index=True, default='not_paid', max_length=16)),
                ('payment_reference', models.CharField(blank=True, max_length=64)),
                ('narration', models.TextField(blank=True)),
                ('sequence_number', models.IntegerField(default=0)),
                ('sequence_prefix', models.CharField(blank=True, max_length=64)),
                ('auto_post', models.BooleanField(default=False)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('commercial_partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='commercial_moves', to='accounting.respartner')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('create_uid', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_moves', to=settings.AUTH_USER_MODEL)),
                ('journal_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.accountjournal')),
                ('partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, to='accounting.respartner')),
                ('reversed_entry_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountmove')),
                ('write_uid', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='modified_moves', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'account_move',
                'ordering': ['-date', '-name', '-invoice_date', '-id'],
            },
        ),
        migrations.CreateModel(
            name='AccountMoveLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Label', max_length=64)),
                ('debit', models.DecimalField(db_index=True, decimal_places=2, default=0, max_digits=16)),
                ('credit', models.DecimalField(db_index=True, decimal_places=2, default=0, max_digits=16)),
                ('balance', models.DecimalField(db_index=True, decimal_places=2, default=0, max_digits=16)),
                ('currency_id', models.CharField(blank=True, max_length=3)),
                ('amount_currency', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('quantity', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('price_unit', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('price_subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('price_total', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('discount', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('tax_base_amount', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('reconciled', models.BooleanField(db_index=True, default=False)),
                ('matching_number', models.CharField(blank=True, db_index=True, max_length=32)),
                ('date', models.DateField(db_index=True)),
                ('date_maturity', models.DateField(blank=True, db_index=True, null=True)),
                ('ref', models.CharField(blank=True, max_length=64)),
                ('sequence', models.IntegerField(default=10)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('account_id', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='move_line_ids', to='accounting.accountaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('full_reconcile_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountfullreconcile')),
                ('move_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_ids', to='accounting.accountmove')),
                ('partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, to='accounting.respartner')),
            ],
            options={
                'db_table': 'account_move_line',
            },
        ),
        migrations.CreateModel(
            name='AccountPaymentTerm',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('active', models.BooleanField(default=True)),
                ('note', models.TextField(blank=True)),
                ('sequence', models.IntegerField(default=10)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
            ],
            options={
                'db_table': 'account_payment_term',
            },
        ),
        migrations.CreateModel(
            name='AccountTax',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('type_tax_use', models.CharField(choices=[('sale', 'Sales'), ('purchase', 'Purchase'), ('none', 'None')], max_length=8)),
                ('amount_type', models.CharField(choices=[('group', 'Group of Taxes'), ('fixed', 'Fixed'), ('percent', 'Percentage of Price'), ('division', 'Percentage of Price Tax Included')], default='percent', max_length=8)),
                ('amount', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=1)),
                ('description', models.CharField(blank=True, max_length=16)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
            ],
            options={
                'db_table': 'account_tax',
            },
        ),
        migrations.CreateModel(
            name='AccountTaxRepartitionLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('factor_percent', models.DecimalField(decimal_places=4, default=100, max_digits=16)),
                ('repartition_type', models.CharField(choices=[('base', 'Base'), ('tax', 'Tax')], default='tax', max_length=8)),
                ('sequence', models.IntegerField(default=1)),
                ('account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('invoice_tax_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='invoice_repartition_lines', to='accounting.accounttax')),
                ('refund_tax_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='refund_repartition_lines', to='accounting.accounttax')),
            ],
            options={
                'db_table': 'account_tax_repartition_line',
            },
        ),
        migrations.AddField(
            model_name='accounttax',
            name='invoice_repartition_line_ids',
            field=models.ManyToManyField(blank=True, to='accounting.accounttaxrepartitionline'),
        ),
        migrations.AddField(
            model_name='accounttax',
            name='refund_repartition_line_ids',
            field=models.ManyToManyField(blank=True, related_name='refund_tax_ids', to='accounting.accounttaxrepartitionline'),
        ),
        migrations.CreateModel(
            name='AccountPaymentTermLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('value', models.CharField(choices=[('balance', 'Balance'), ('percent', 'Percent'), ('fixed', 'Fixed Amount')], default='balance', max_length=8)),
                ('value_amount', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('days', models.IntegerField(default=0)),
                ('sequence', models.IntegerField(default=10)),
                ('payment_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_ids', to='accounting.accountpaymentterm')),
            ],
            options={
                'db_table': 'account_payment_term_line',
            },
        ),
        migrations.CreateModel(
            name='AccountPartialReconcile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=16)),
                ('amount_currency', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('currency_id', models.CharField(blank=True, max_length=3)),
                ('credit_move_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='credit_partial_reconcile_ids', to='accounting.accountmoveline')),
                ('debit_move_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='debit_partial_reconcile_ids', to='accounting.accountmoveline')),
                ('full_reconcile_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.accountfullreconcile')),
            ],
            options={
                'db_table': 'account_partial_reconcile',
            },
        ),
        migrations.AddField(
            model_name='accountmoveline',
            name='tax_ids',
            field=models.ManyToManyField(blank=True, related_name='move_line_tax_ids', to='accounting.accounttax'),
        ),
        migrations.AddField(
            model_name='accountmoveline',
            name='tax_line_id',
            field=models.ForeignKey(blank=True, help_text='Tax line', null=True, on_delete=django.db.models.deletion.RESTRICT, related_name='tax_line_move_ids', to='accounting.accounttax'),
        ),
        migrations.AddField(
            model_name='accountfullreconcile',
            name='exchange_move_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountmove'),
        ),
        migrations.AddField(
            model_name='accountfullreconcile',
            name='reconciled_line_ids',
            field=models.ManyToManyField(related_name='full_reconcile_ids', to='accounting.accountmoveline'),
        ),
        migrations.CreateModel(
            name='AccountFiscalPositionTax',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('position_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='tax_ids', to='accounting.accountfiscalposition')),
                ('tax_dest_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='fiscal_position_taxes_dest', to='accounting.accounttax')),
                ('tax_src_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fiscal_position_taxes_src', to='accounting.accounttax')),
            ],
            options={
                'db_table': 'account_fiscal_position_tax',
            },
        ),
        migrations.CreateModel(
            name='AccountFiscalPositionAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('account_dest_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fiscal_position_accounts_dest', to='accounting.accountaccount')),
                ('account_src_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='fiscal_position_accounts_src', to='accounting.accountaccount')),
                ('position_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='account_ids', to='accounting.accountfiscalposition')),
            ],
            options={
                'db_table': 'account_fiscal_position_account',
            },
        ),
        migrations.CreateModel(
            name='AccountAnalyticLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=256)),
                ('date', models.DateField(db_index=True)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=16)),
                ('unit_amount', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('account_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_ids', to='accounting.accountanalyticaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('general_account_id', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='accounting.accountaccount')),
                ('move_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.accountmove')),
                ('partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
            ],
            options={
                'db_table': 'account_analytic_line',
            },
        ),
        migrations.CreateModel(
            name='AccountPayment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='/', max_length=64)),
                ('payment_type', models.CharField(choices=[('outbound', 'Send Money'), ('inbound', 'Receive Money')], max_length=8)),
                ('partner_type', models.CharField(choices=[('customer', 'Customer'), ('supplier', 'Vendor')], max_length=8)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=16)),
                ('currency_id', models.CharField(default='PKR', max_length=3)),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('posted', 'Validated'), ('sent', 'Sent'), ('reconciled', 'Reconciled'), ('cancelled', 'Cancelled')], default='draft', max_length=16)),
                ('reconciled_invoices_count', models.IntegerField(default=0)),
                ('ref', models.CharField(blank=True, max_length=64)),
                ('date', models.DateField(default=datetime.date.today)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('destination_account_id', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='accounting.accountaccount')),
                ('journal_id', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='accounting.accountjournal')),
                ('move_id', models.OneToOneField(help_text='Journal Entry', on_delete=django.db.models.deletion.CASCADE, to='accounting.accountmove')),
                ('outstanding_account_id', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='outstanding_payments', to='accounting.accountaccount')),
                ('partner_id', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, to='accounting.respartner')),
                ('reconciled_invoice_ids', models.ManyToManyField(blank=True, help_text='Invoices reconciled with this payment', related_name='reconciled_payments', to='accounting.accountmove')),
            ],
            options={
                'db_table': 'account_payment',
                'indexes': [models.Index(fields=['partner_id', 'state'], name='account_pay_partner_41ca57_idx'), models.Index(fields=['date', 'journal_id'], name='account_pay_date_fc7baf_idx')],
            },
        ),
        migrations.AddIndex(
            model_name='accountmoveline',
            index=models.Index(fields=['account_id', 'date'], name='account_mov_account_eeaeb4_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmoveline',
            index=models.Index(fields=['partner_id', 'account_id'], name='account_mov_partner_6a4ecd_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmoveline',
            index=models.Index(fields=['reconciled', 'account_id'], name='account_mov_reconci_5c6b3e_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmoveline',
            index=models.Index(fields=['date', 'move_id'], name='account_mov_date_ed03df_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmove',
            index=models.Index(fields=['date', 'journal_id'], name='account_mov_date_586ef6_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmove',
            index=models.Index(fields=['state', 'move_type'], name='account_mov_state_c8b985_idx'),
        ),
        migrations.AddIndex(
            model_name='accountmove',
            index=models.Index(fields=['partner_id', 'state'], name='account_mov_partner_6c4f1b_idx'),
        ),
    ]
