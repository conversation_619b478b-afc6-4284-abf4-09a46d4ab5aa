2025-07-15 00:02:51,422 23488 INFO ? odoo: Odoo version 17.0 
2025-07-15 00:02:51,422 23488 INFO ? odoo: Using configuration file at D:\odoo_erp\odoo.conf 
2025-07-15 00:02:51,422 23488 INFO ? odoo: addons paths: ['D:\\odoo_erp\\odoo\\addons', 'd:\\odoo_erp\\filestore\\addons\\17.0', 'd:\\odoo_erp\\addons', 'd:\\odoo_erp\\odoo\\addons'] 
2025-07-15 00:02:51,422 23488 INFO ? odoo: database: odoo@localhost:5432 
2025-07-15 00:02:51,422 23488 WARNING ? odoo: Python 3.13 is not officially supported, please use Python 3.12 instead 
2025-07-15 00:02:51,748 23488 INFO ? odoo.addons.base.models.ir_actions_report: You need Wkhtmltopdf to print a pdf version of the reports. 
2025-07-15 00:02:52,280 23488 INFO ? odoo.service.server: HTTP service (werkzeug) running on DESKTOP-H0NBJJT:8069 
2025-07-15 00:02:52,358 23488 INFO dataicraft_final odoo.modules.loading: loading 1 modules... 
2025-07-15 00:02:52,366 23488 INFO dataicraft_final odoo.modules.loading: 1 modules loaded in 0.01s, 0 queries (+0 extra) 
2025-07-15 00:02:52,455 23488 INFO dataicraft_final odoo.modules.loading: loading 70 modules... 
2025-07-15 00:02:54,960 23488 INFO dataicraft_final odoo.modules.loading: 70 modules loaded in 2.51s, 0 queries (+0 extra) 
2025-07-15 00:02:55,221 23488 INFO dataicraft_final odoo.modules.loading: Modules loaded. 
2025-07-15 00:02:55,230 23488 INFO dataicraft_final odoo.modules.registry: Registry loaded in 2.957s 
2025-07-15 00:06:55,413 23488 INFO dataicraft_final odoo.sql_db: Connection to the database failed 
2025-07-15 00:06:55,413 23488 WARNING dataicraft_final odoo.addons.base.models.ir_cron: Exception in cron: 
Traceback (most recent call last):
  File "D:\odoo_erp\odoo\addons\base\models\ir_cron.py", line 119, in _process_jobs
    with db.cursor() as cron_cr:
         ~~~~~~~~~^^
  File "D:\odoo_erp\odoo\sql_db.py", line 764, in cursor
    return Cursor(self.__pool, self.__dbname, self.__dsn)
  File "D:\odoo_erp\odoo\sql_db.py", line 267, in __init__
    self._cnx = pool.borrow(dsn)
                ~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "D:\odoo_erp\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
  File "D:\odoo_erp\odoo\sql_db.py", line 692, in borrow
    result = psycopg2.connect(
        connection_factory=PsycoConnection,
        **connection_info)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psycopg2\__init__.py", line 135, in connect
    conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
psycopg2.OperationalError: connection to server at "localhost" (::1), port 5432 failed: FATAL:  database "dataicraft_final" does not exist

2025-07-15 00:06:59,634 23488 INFO dataicraft_final odoo.sql_db: Connection to the database failed 
2025-07-15 00:06:59,634 23488 WARNING dataicraft_final odoo.addons.base.models.ir_cron: Exception in cron: 
Traceback (most recent call last):
  File "D:\odoo_erp\odoo\addons\base\models\ir_cron.py", line 119, in _process_jobs
    with db.cursor() as cron_cr:
         ~~~~~~~~~^^
  File "D:\odoo_erp\odoo\sql_db.py", line 764, in cursor
    return Cursor(self.__pool, self.__dbname, self.__dsn)
  File "D:\odoo_erp\odoo\sql_db.py", line 267, in __init__
    self._cnx = pool.borrow(dsn)
                ~~~~~~~~~~~^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\decorator.py", line 235, in fun
    return caller(func, *(extras + args), **kw)
  File "D:\odoo_erp\odoo\tools\func.py", line 87, in locked
    return func(inst, *args, **kwargs)
  File "D:\odoo_erp\odoo\sql_db.py", line 692, in borrow
    result = psycopg2.connect(
        connection_factory=PsycoConnection,
        **connection_info)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Lib\site-packages\psycopg2\__init__.py", line 135, in connect
    conn = _connect(dsn, connection_factory=connection_factory, **kwasync)
psycopg2.OperationalError: connection to server at "localhost" (::1), port 5432 failed: FATAL:  database "dataicraft_final" does not exist

