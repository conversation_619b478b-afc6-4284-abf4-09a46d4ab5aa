{"version": 12, "sheets": [{"id": "sheet1", "name": "Dashboard", "colNumber": 10, "rowNumber": 61, "rows": {"6": {"size": 40}, "18": {"size": 40}, "19": {"size": 40}}, "cols": {"0": {"size": 100}, "1": {"size": 75}, "2": {"size": 125}, "3": {"size": 125}, "4": {"size": 125}, "5": {"size": 75}, "6": {"size": 50}, "7": {"size": 125}, "8": {"size": 100}, "9": {"size": 100}}, "merges": ["H34:H35", "I34:I35", "J34:J35", "H49:H50", "I49:I50", "J49:J50", "H32:J33", "H47:J48"], "cells": {"A7": {"style": 1, "content": "[Top Vendors by Amount](odoo://view/{\"viewType\":\"graph\",\"action\":{\"domain\":[],\"context\":{\"group_by\":[\"partner_id\"],\"graph_measure\":\"untaxed_total\",\"graph_mode\":\"bar\",\"graph_groupbys\":[\"partner_id\"]},\"modelName\":\"purchase.report\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Top Vendors by Amount\"})", "border": 1}, "A19": {"style": 1, "content": "[Top Orders](odoo://view/{\"viewType\":\"list\",\"action\":{\"domain\":[[\"state\",\"in\",[\"purchase\",\"done\"]]],\"context\":{\"group_by\":[]},\"modelName\":\"purchase.order\",\"views\":[[false,\"list\"],[false,\"kanban\"],[false,\"form\"],[false,\"pivot\"],[false,\"graph\"],[false,\"calendar\"],[false,\"activity\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Top Orders\"})", "border": 1}, "A20": {"style": 2, "content": "=_t(\"Order\")", "border": 2}, "A21": {"style": 3, "content": "=ODOO.LIST(1,1,\"name\")"}, "A22": {"style": 4, "content": "=ODOO.LIST(1,2,\"name\")"}, "A23": {"style": 3, "content": "=ODOO.LIST(1,3,\"name\")"}, "A24": {"style": 4, "content": "=ODOO.LIST(1,4,\"name\")"}, "A25": {"style": 3, "content": "=ODOO.LIST(1,5,\"name\")"}, "A26": {"style": 4, "content": "=ODOO.LIST(1,6,\"name\")"}, "A27": {"style": 3, "content": "=ODOO.LIST(1,7,\"name\")"}, "A28": {"style": 4, "content": "=ODOO.LIST(1,8,\"name\")"}, "A29": {"style": 3, "content": "=ODOO.LIST(1,9,\"name\")"}, "A30": {"style": 4, "content": "=ODOO.LIST(1,10,\"name\")"}, "A31": {"style": 3, "content": "=ODOO.LIST(1,11,\"name\")"}, "A32": {"style": 4, "content": "=ODOO.LIST(1,12,\"name\")"}, "A33": {"style": 3, "content": "=ODOO.LIST(1,13,\"name\")"}, "A34": {"style": 4, "content": "=ODOO.LIST(1,14,\"name\")"}, "A35": {"style": 3, "content": "=ODOO.LIST(1,15,\"name\")"}, "A36": {"style": 4, "content": "=ODOO.LIST(1,16,\"name\")"}, "A37": {"style": 3, "content": "=ODOO.LIST(1,17,\"name\")"}, "A38": {"style": 4, "content": "=ODOO.LIST(1,18,\"name\")"}, "A39": {"style": 3, "content": "=ODOO.LIST(1,19,\"name\")"}, "A40": {"style": 4, "content": "=ODOO.LIST(1,20,\"name\")"}, "A41": {"style": 3, "content": "=ODOO.LIST(1,21,\"name\")"}, "A42": {"style": 4, "content": "=ODOO.LIST(1,22,\"name\")"}, "A43": {"style": 3, "content": "=ODOO.LIST(1,23,\"name\")"}, "A44": {"style": 4, "content": "=ODOO.LIST(1,24,\"name\")"}, "A45": {"style": 3, "content": "=ODOO.LIST(1,25,\"name\")"}, "A46": {"style": 4, "content": "=ODOO.LIST(1,26,\"name\")"}, "A47": {"style": 3, "content": "=ODOO.LIST(1,27,\"name\")"}, "A48": {"style": 4, "content": "=ODOO.LIST(1,28,\"name\")"}, "A49": {"style": 3, "content": "=ODOO.LIST(1,29,\"name\")"}, "A50": {"style": 4, "content": "=ODOO.LIST(1,30,\"name\")"}, "A51": {"style": 3, "content": "=ODOO.LIST(1,31,\"name\")"}, "A52": {"style": 4, "content": "=ODOO.LIST(1,32,\"name\")"}, "A53": {"style": 3, "content": "=ODOO.LIST(1,33,\"name\")"}, "A54": {"style": 4, "content": "=ODOO.LIST(1,34,\"name\")"}, "A55": {"style": 3, "content": "=ODOO.LIST(1,35,\"name\")"}, "A56": {"style": 4, "content": "=ODOO.LIST(1,36,\"name\")"}, "A57": {"style": 3, "content": "=ODOO.LIST(1,37,\"name\")"}, "A58": {"style": 4, "content": "=ODOO.LIST(1,38,\"name\")"}, "A59": {"style": 3, "content": "=ODOO.LIST(1,39,\"name\")"}, "A60": {"style": 4, "content": "=ODOO.LIST(1,40,\"name\")"}, "A61": {"style": 5}, "B20": {"style": 2, "content": "=_t(\"Ordered\")", "border": 2}, "B21": {"style": 6, "format": 1, "content": "=ODOO.LIST(1,1,\"date_approve\")"}, "B22": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,2,\"date_approve\")"}, "B23": {"style": 6, "format": 1, "content": "=ODOO.LIST(1,3,\"date_approve\")"}, "B24": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,4,\"date_approve\")"}, "B25": {"style": 6, "format": 1, "content": "=ODOO.LIST(1,5,\"date_approve\")"}, "B26": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,6,\"date_approve\")"}, "B27": {"style": 6, "format": 1, "content": "=ODOO.LIST(1,7,\"date_approve\")"}, "B28": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,8,\"date_approve\")"}, "B29": {"style": 6, "format": 1, "content": "=ODOO.LIST(1,9,\"date_approve\")"}, "B30": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,10,\"date_approve\")"}, "B31": {"style": 6, "format": 1, "content": "=ODOO.LIST(1,11,\"date_approve\")"}, "B32": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,12,\"date_approve\")"}, "B33": {"style": 6, "format": 1, "content": "=ODOO.LIST(1,13,\"date_approve\")"}, "B34": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,14,\"date_approve\")"}, "B35": {"style": 6, "format": 1, "content": "=ODOO.LIST(1,15,\"date_approve\")"}, "B36": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,16,\"date_approve\")"}, "B37": {"style": 6, "format": 1, "content": "=ODOO.LIST(1,17,\"date_approve\")"}, "B38": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,18,\"date_approve\")"}, "B39": {"style": 6, "format": 1, "content": "=ODOO.LIST(1,19,\"date_approve\")"}, "B40": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,20,\"date_approve\")"}, "B41": {"style": 6, "format": 1, "content": "=ODOO.LIST(1,21,\"date_approve\")"}, "B42": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,22,\"date_approve\")"}, "B43": {"style": 6, "format": 1, "content": "=ODOO.LIST(1,23,\"date_approve\")"}, "B44": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,24,\"date_approve\")"}, "B45": {"style": 6, "format": 1, "content": "=ODOO.LIST(1,25,\"date_approve\")"}, "B46": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,26,\"date_approve\")"}, "B47": {"style": 6, "format": 1, "content": "=ODOO.LIST(1,27,\"date_approve\")"}, "B48": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,28,\"date_approve\")"}, "B49": {"style": 6, "format": 1, "content": "=ODOO.LIST(1,29,\"date_approve\")"}, "B50": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,30,\"date_approve\")"}, "B51": {"style": 6, "format": 1, "content": "=ODOO.LIST(1,31,\"date_approve\")"}, "B52": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,32,\"date_approve\")"}, "B53": {"style": 6, "format": 1, "content": "=ODOO.LIST(1,33,\"date_approve\")"}, "B54": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,34,\"date_approve\")"}, "B55": {"style": 6, "format": 1, "content": "=ODOO.LIST(1,35,\"date_approve\")"}, "B56": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,36,\"date_approve\")"}, "B57": {"style": 6, "format": 1, "content": "=ODOO.LIST(1,37,\"date_approve\")"}, "B58": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,38,\"date_approve\")"}, "B59": {"style": 6, "format": 1, "content": "=ODOO.LIST(1,39,\"date_approve\")"}, "B60": {"style": 7, "format": 1, "content": "=ODOO.LIST(1,40,\"date_approve\")"}, "B61": {"style": 5}, "C20": {"style": 2, "content": "=_t(\"Buyer\")", "border": 2}, "C21": {"style": 8, "content": "=ODOO.LIST(1,1,\"user_id\")"}, "C22": {"content": "=ODOO.LIST(1,2,\"user_id\")"}, "C23": {"style": 8, "content": "=ODOO.LIST(1,3,\"user_id\")"}, "C24": {"content": "=ODOO.LIST(1,4,\"user_id\")"}, "C25": {"style": 8, "content": "=ODOO.LIST(1,5,\"user_id\")"}, "C26": {"content": "=ODOO.LIST(1,6,\"user_id\")"}, "C27": {"style": 8, "content": "=ODOO.LIST(1,7,\"user_id\")"}, "C28": {"content": "=ODOO.LIST(1,8,\"user_id\")"}, "C29": {"style": 8, "content": "=ODOO.LIST(1,9,\"user_id\")"}, "C30": {"content": "=ODOO.LIST(1,10,\"user_id\")"}, "C31": {"style": 8, "content": "=ODOO.LIST(1,11,\"user_id\")"}, "C32": {"content": "=ODOO.LIST(1,12,\"user_id\")"}, "C33": {"style": 8, "content": "=ODOO.LIST(1,13,\"user_id\")"}, "C34": {"content": "=ODOO.LIST(1,14,\"user_id\")"}, "C35": {"style": 8, "content": "=ODOO.LIST(1,15,\"user_id\")"}, "C36": {"content": "=ODOO.LIST(1,16,\"user_id\")"}, "C37": {"style": 8, "content": "=ODOO.LIST(1,17,\"user_id\")"}, "C38": {"content": "=ODOO.LIST(1,18,\"user_id\")"}, "C39": {"style": 8, "content": "=ODOO.LIST(1,19,\"user_id\")"}, "C40": {"content": "=ODOO.LIST(1,20,\"user_id\")"}, "C41": {"style": 8, "content": "=ODOO.LIST(1,21,\"user_id\")"}, "C42": {"content": "=ODOO.LIST(1,22,\"user_id\")"}, "C43": {"style": 8, "content": "=ODOO.LIST(1,23,\"user_id\")"}, "C44": {"content": "=ODOO.LIST(1,24,\"user_id\")"}, "C45": {"style": 8, "content": "=ODOO.LIST(1,25,\"user_id\")"}, "C46": {"content": "=ODOO.LIST(1,26,\"user_id\")"}, "C47": {"style": 8, "content": "=ODOO.LIST(1,27,\"user_id\")"}, "C48": {"content": "=ODOO.LIST(1,28,\"user_id\")"}, "C49": {"style": 8, "content": "=ODOO.LIST(1,29,\"user_id\")"}, "C50": {"content": "=ODOO.LIST(1,30,\"user_id\")"}, "C51": {"style": 8, "content": "=ODOO.LIST(1,31,\"user_id\")"}, "C52": {"content": "=ODOO.LIST(1,32,\"user_id\")"}, "C53": {"style": 8, "content": "=ODOO.LIST(1,33,\"user_id\")"}, "C54": {"content": "=ODOO.LIST(1,34,\"user_id\")"}, "C55": {"style": 8, "content": "=ODOO.LIST(1,35,\"user_id\")"}, "C56": {"content": "=ODOO.LIST(1,36,\"user_id\")"}, "C57": {"style": 8, "content": "=ODOO.LIST(1,37,\"user_id\")"}, "C58": {"content": "=ODOO.LIST(1,38,\"user_id\")"}, "C59": {"style": 8, "content": "=ODOO.LIST(1,39,\"user_id\")"}, "C60": {"content": "=ODOO.LIST(1,40,\"user_id\")"}, "C61": {"style": 5}, "D20": {"style": 2, "content": "=_t(\"Source\")", "border": 2}, "D21": {"style": 8, "content": "=ODOO.LIST(1,1,\"origin\")"}, "D22": {"content": "=ODOO.LIST(1,2,\"origin\")"}, "D23": {"style": 8, "content": "=ODOO.LIST(1,3,\"origin\")"}, "D24": {"content": "=ODOO.LIST(1,4,\"origin\")"}, "D25": {"style": 8, "content": "=ODOO.LIST(1,5,\"origin\")"}, "D26": {"content": "=ODOO.LIST(1,6,\"origin\")"}, "D27": {"style": 8, "content": "=ODOO.LIST(1,7,\"origin\")"}, "D28": {"content": "=ODOO.LIST(1,8,\"origin\")"}, "D29": {"style": 8, "content": "=ODOO.LIST(1,9,\"origin\")"}, "D30": {"content": "=ODOO.LIST(1,10,\"origin\")"}, "D31": {"style": 8, "content": "=ODOO.LIST(1,11,\"origin\")"}, "D32": {"content": "=ODOO.LIST(1,12,\"origin\")"}, "D33": {"style": 8, "content": "=ODOO.LIST(1,13,\"origin\")"}, "D34": {"content": "=ODOO.LIST(1,14,\"origin\")"}, "D35": {"style": 8, "content": "=ODOO.LIST(1,15,\"origin\")"}, "D36": {"content": "=ODOO.LIST(1,16,\"origin\")"}, "D37": {"style": 8, "content": "=ODOO.LIST(1,17,\"origin\")"}, "D38": {"content": "=ODOO.LIST(1,18,\"origin\")"}, "D39": {"style": 8, "content": "=ODOO.LIST(1,19,\"origin\")"}, "D40": {"content": "=ODOO.LIST(1,20,\"origin\")"}, "D41": {"style": 8, "content": "=ODOO.LIST(1,21,\"origin\")"}, "D42": {"content": "=ODOO.LIST(1,22,\"origin\")"}, "D43": {"style": 8, "content": "=ODOO.LIST(1,23,\"origin\")"}, "D44": {"content": "=ODOO.LIST(1,24,\"origin\")"}, "D45": {"style": 8, "content": "=ODOO.LIST(1,25,\"origin\")"}, "D46": {"content": "=ODOO.LIST(1,26,\"origin\")"}, "D47": {"style": 8, "content": "=ODOO.LIST(1,27,\"origin\")"}, "D48": {"content": "=ODOO.LIST(1,28,\"origin\")"}, "D49": {"style": 8, "content": "=ODOO.LIST(1,29,\"origin\")"}, "D50": {"content": "=ODOO.LIST(1,30,\"origin\")"}, "D51": {"style": 8, "content": "=ODOO.LIST(1,31,\"origin\")"}, "D52": {"content": "=ODOO.LIST(1,32,\"origin\")"}, "D53": {"style": 8, "content": "=ODOO.LIST(1,33,\"origin\")"}, "D54": {"content": "=ODOO.LIST(1,34,\"origin\")"}, "D55": {"style": 8, "content": "=ODOO.LIST(1,35,\"origin\")"}, "D56": {"content": "=ODOO.LIST(1,36,\"origin\")"}, "D57": {"style": 8, "content": "=ODOO.LIST(1,37,\"origin\")"}, "D58": {"content": "=ODOO.LIST(1,38,\"origin\")"}, "D59": {"style": 8, "content": "=ODOO.LIST(1,39,\"origin\")"}, "D60": {"content": "=ODOO.LIST(1,40,\"origin\")"}, "D61": {"style": 5}, "E20": {"style": 2, "content": "=_t(\"Vendor\")", "border": 2}, "E21": {"style": 8, "content": "=ODOO.LIST(1,1,\"partner_id\")"}, "E22": {"content": "=ODOO.LIST(1,2,\"partner_id\")"}, "E23": {"style": 8, "content": "=ODOO.LIST(1,3,\"partner_id\")"}, "E24": {"content": "=ODOO.LIST(1,4,\"partner_id\")"}, "E25": {"style": 8, "content": "=ODOO.LIST(1,5,\"partner_id\")"}, "E26": {"content": "=ODOO.LIST(1,6,\"partner_id\")"}, "E27": {"style": 8, "content": "=ODOO.LIST(1,7,\"partner_id\")"}, "E28": {"content": "=ODOO.LIST(1,8,\"partner_id\")"}, "E29": {"style": 8, "content": "=ODOO.LIST(1,9,\"partner_id\")"}, "E30": {"content": "=ODOO.LIST(1,10,\"partner_id\")"}, "E31": {"style": 8, "content": "=ODOO.LIST(1,11,\"partner_id\")"}, "E32": {"content": "=ODOO.LIST(1,12,\"partner_id\")"}, "E33": {"style": 8, "content": "=ODOO.LIST(1,13,\"partner_id\")"}, "E34": {"content": "=ODOO.LIST(1,14,\"partner_id\")"}, "E35": {"style": 8, "content": "=ODOO.LIST(1,15,\"partner_id\")"}, "E36": {"content": "=ODOO.LIST(1,16,\"partner_id\")"}, "E37": {"style": 8, "content": "=ODOO.LIST(1,17,\"partner_id\")"}, "E38": {"content": "=ODOO.LIST(1,18,\"partner_id\")"}, "E39": {"style": 8, "content": "=ODOO.LIST(1,19,\"partner_id\")"}, "E40": {"content": "=ODOO.LIST(1,20,\"partner_id\")"}, "E41": {"style": 8, "content": "=ODOO.LIST(1,21,\"partner_id\")"}, "E42": {"content": "=ODOO.LIST(1,22,\"partner_id\")"}, "E43": {"style": 8, "content": "=ODOO.LIST(1,23,\"partner_id\")"}, "E44": {"content": "=ODOO.LIST(1,24,\"partner_id\")"}, "E45": {"style": 8, "content": "=ODOO.LIST(1,25,\"partner_id\")"}, "E46": {"content": "=ODOO.LIST(1,26,\"partner_id\")"}, "E47": {"style": 8, "content": "=ODOO.LIST(1,27,\"partner_id\")"}, "E48": {"content": "=ODOO.LIST(1,28,\"partner_id\")"}, "E49": {"style": 8, "content": "=ODOO.LIST(1,29,\"partner_id\")"}, "E50": {"content": "=ODOO.LIST(1,30,\"partner_id\")"}, "E51": {"style": 8, "content": "=ODOO.LIST(1,31,\"partner_id\")"}, "E52": {"content": "=ODOO.LIST(1,32,\"partner_id\")"}, "E53": {"style": 8, "content": "=ODOO.LIST(1,33,\"partner_id\")"}, "E54": {"content": "=ODOO.LIST(1,34,\"partner_id\")"}, "E55": {"style": 8, "content": "=ODOO.LIST(1,35,\"partner_id\")"}, "E56": {"content": "=ODOO.LIST(1,36,\"partner_id\")"}, "E57": {"style": 8, "content": "=ODOO.LIST(1,37,\"partner_id\")"}, "E58": {"content": "=ODOO.LIST(1,38,\"partner_id\")"}, "E59": {"style": 8, "content": "=ODOO.LIST(1,39,\"partner_id\")"}, "E60": {"content": "=ODOO.LIST(1,40,\"partner_id\")"}, "E61": {"style": 5}, "F20": {"style": 9, "content": "=_t(\"Amount\")", "border": 2}, "F21": {"style": 8, "content": "=ODOO.LIST(1,1,\"amount_untaxed\")"}, "F22": {"content": "=ODOO.LIST(1,2,\"amount_untaxed\")"}, "F23": {"style": 8, "content": "=ODOO.LIST(1,3,\"amount_untaxed\")"}, "F24": {"content": "=ODOO.LIST(1,4,\"amount_untaxed\")"}, "F25": {"style": 8, "content": "=ODOO.LIST(1,5,\"amount_untaxed\")"}, "F26": {"content": "=ODOO.LIST(1,6,\"amount_untaxed\")"}, "F27": {"style": 8, "content": "=ODOO.LIST(1,7,\"amount_untaxed\")"}, "F28": {"content": "=ODOO.LIST(1,8,\"amount_untaxed\")"}, "F29": {"style": 8, "content": "=ODOO.LIST(1,9,\"amount_untaxed\")"}, "F30": {"content": "=ODOO.LIST(1,10,\"amount_untaxed\")"}, "F31": {"style": 8, "content": "=ODOO.LIST(1,11,\"amount_untaxed\")"}, "F32": {"content": "=ODOO.LIST(1,12,\"amount_untaxed\")"}, "F33": {"style": 8, "content": "=ODOO.LIST(1,13,\"amount_untaxed\")"}, "F34": {"content": "=ODOO.LIST(1,14,\"amount_untaxed\")"}, "F35": {"style": 8, "content": "=ODOO.LIST(1,15,\"amount_untaxed\")"}, "F36": {"content": "=ODOO.LIST(1,16,\"amount_untaxed\")"}, "F37": {"style": 8, "content": "=ODOO.LIST(1,17,\"amount_untaxed\")"}, "F38": {"content": "=ODOO.LIST(1,18,\"amount_untaxed\")"}, "F39": {"style": 8, "content": "=ODOO.LIST(1,19,\"amount_untaxed\")"}, "F40": {"content": "=ODOO.LIST(1,20,\"amount_untaxed\")"}, "F41": {"style": 8, "content": "=ODOO.LIST(1,21,\"amount_untaxed\")"}, "F42": {"content": "=ODOO.LIST(1,22,\"amount_untaxed\")"}, "F43": {"style": 8, "content": "=ODOO.LIST(1,23,\"amount_untaxed\")"}, "F44": {"content": "=ODOO.LIST(1,24,\"amount_untaxed\")"}, "F45": {"style": 8, "content": "=ODOO.LIST(1,25,\"amount_untaxed\")"}, "F46": {"content": "=ODOO.LIST(1,26,\"amount_untaxed\")"}, "F47": {"style": 8, "content": "=ODOO.LIST(1,27,\"amount_untaxed\")"}, "F48": {"content": "=ODOO.LIST(1,28,\"amount_untaxed\")"}, "F49": {"style": 8, "content": "=ODOO.LIST(1,29,\"amount_untaxed\")"}, "F50": {"content": "=ODOO.LIST(1,30,\"amount_untaxed\")"}, "F51": {"style": 8, "content": "=ODOO.LIST(1,31,\"amount_untaxed\")"}, "F52": {"content": "=ODOO.LIST(1,32,\"amount_untaxed\")"}, "F53": {"style": 8, "content": "=ODOO.LIST(1,33,\"amount_untaxed\")"}, "F54": {"content": "=ODOO.LIST(1,34,\"amount_untaxed\")"}, "F55": {"style": 8, "content": "=ODOO.LIST(1,35,\"amount_untaxed\")"}, "F56": {"content": "=ODOO.LIST(1,36,\"amount_untaxed\")"}, "F57": {"style": 8, "content": "=ODOO.LIST(1,37,\"amount_untaxed\")"}, "F58": {"content": "=ODOO.LIST(1,38,\"amount_untaxed\")"}, "F59": {"style": 8, "content": "=ODOO.LIST(1,39,\"amount_untaxed\")"}, "F60": {"content": "=ODOO.LIST(1,40,\"amount_untaxed\")"}, "F61": {"style": 5}, "G57": {"style": 5}, "G58": {"style": 5}, "G59": {"style": 5}, "G60": {"style": 5}, "G61": {"style": 5}, "H19": {"style": 1, "content": "[Sourcing by Country](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[[\"country_id\",\"!=\",false]],\"context\":{\"group_by\":[\"country_id\"],\"pivot_measures\":[\"order_id\",\"untaxed_total\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"country_id\"]},\"modelName\":\"purchase.report\",\"views\":[[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Sourcing by Country\"})", "border": 1}, "H20": {"style": 2, "content": "=_t(\"Country\")", "border": 2}, "H21": {"style": 10, "content": "=ODOO.PIVOT.HEADER(1,\"#country_id\",1)"}, "H22": {"style": 11, "content": "=ODOO.PIVOT.HEADER(1,\"#country_id\",2)"}, "H23": {"style": 10, "content": "=ODOO.PIVOT.HEADER(1,\"#country_id\",3)"}, "H24": {"style": 11, "content": "=ODOO.PIVOT.HEADER(1,\"#country_id\",4)"}, "H25": {"style": 10, "content": "=ODOO.PIVOT.HEADER(1,\"#country_id\",5)"}, "H26": {"style": 11, "content": "=ODOO.PIVOT.HEADER(1,\"#country_id\",6)"}, "H27": {"style": 10, "content": "=ODOO.PIVOT.HEADER(1,\"#country_id\",7)"}, "H28": {"style": 11, "content": "=ODOO.PIVOT.HEADER(1,\"#country_id\",8)"}, "H29": {"style": 10, "content": "=ODOO.PIVOT.HEADER(1,\"#country_id\",9)"}, "H30": {"style": 11, "content": "=ODOO.PIVOT.HEADER(1,\"#country_id\",10)"}, "H32": {"style": 1, "content": "[Top Vendors](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[],\"context\":{\"group_by\":[\"partner_id\"],\"pivot_measures\":[\"order_id\",\"untaxed_total\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"partner_id\"]},\"modelName\":\"purchase.report\",\"views\":[[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Top Vendors\"})"}, "H33": {"style": 1}, "H34": {"style": 2, "content": "=_t(\"Vendor\")"}, "H35": {"style": 2}, "H36": {"style": 10, "content": "=ODOO.PIVOT.HEADER(2,\"#partner_id\",1)"}, "H37": {"style": 11, "content": "=ODOO.PIVOT.HEADER(2,\"#partner_id\",2)"}, "H38": {"style": 10, "content": "=ODOO.PIVOT.HEADER(2,\"#partner_id\",3)"}, "H39": {"style": 11, "content": "=ODOO.PIVOT.HEADER(2,\"#partner_id\",4)"}, "H40": {"style": 10, "content": "=ODOO.PIVOT.HEADER(2,\"#partner_id\",5)"}, "H41": {"style": 11, "content": "=ODOO.PIVOT.HEADER(2,\"#partner_id\",6)"}, "H42": {"style": 10, "content": "=ODOO.PIVOT.HEADER(2,\"#partner_id\",7)"}, "H43": {"style": 11, "content": "=ODOO.PIVOT.HEADER(2,\"#partner_id\",8)"}, "H44": {"style": 10, "content": "=ODOO.PIVOT.HEADER(2,\"#partner_id\",9)"}, "H45": {"style": 11, "content": "=ODOO.PIVOT.HEADER(2,\"#partner_id\",10)"}, "H47": {"style": 1, "content": "[Top Buyers](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[[\"user_id\",\"!=\",false]],\"context\":{\"group_by\":[\"user_id\"],\"pivot_measures\":[\"order_id\",\"untaxed_total\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"user_id\"]},\"modelName\":\"purchase.report\",\"views\":[[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Top Buyers\"})"}, "H48": {"style": 1, "border": 1}, "H49": {"style": 2, "content": "=_t(\"Buyer\")", "border": 2}, "H50": {"style": 2}, "H51": {"style": 10, "content": "=ODOO.PIVOT.HEADER(3,\"#user_id\",1)"}, "H52": {"style": 11, "content": "=ODOO.PIVOT.HEADER(3,\"#user_id\",2)"}, "H53": {"style": 10, "content": "=ODOO.PIVOT.HEADER(3,\"#user_id\",3)"}, "H54": {"style": 11, "content": "=ODOO.PIVOT.HEADER(3,\"#user_id\",4)"}, "H55": {"style": 10, "content": "=ODOO.PIVOT.HEADER(3,\"#user_id\",5)"}, "H56": {"style": 11, "content": "=ODOO.PIVOT.HEADER(3,\"#user_id\",6)"}, "H57": {"style": 10, "content": "=ODOO.PIVOT.HEADER(3,\"#user_id\",7)"}, "H58": {"style": 11, "content": "=ODOO.PIVOT.HEADER(3,\"#user_id\",8)"}, "H59": {"style": 10, "content": "=ODOO.PIVOT.HEADER(3,\"#user_id\",9)"}, "H60": {"style": 11, "content": "=ODOO.PIVOT.HEADER(3,\"#user_id\",10)"}, "I19": {"style": 12, "border": 1}, "I20": {"style": 9, "content": "=_t(\"Orders\")", "border": 2}, "I21": {"style": 8, "content": "=ODOO.PIVOT(1,\"order_id\",\"#country_id\",1)"}, "I22": {"content": "=ODOO.PIVOT(1,\"order_id\",\"#country_id\",2)"}, "I23": {"style": 8, "content": "=ODOO.PIVOT(1,\"order_id\",\"#country_id\",3)"}, "I24": {"content": "=ODOO.PIVOT(1,\"order_id\",\"#country_id\",4)"}, "I25": {"style": 8, "content": "=ODOO.PIVOT(1,\"order_id\",\"#country_id\",5)"}, "I26": {"content": "=ODOO.PIVOT(1,\"order_id\",\"#country_id\",6)"}, "I27": {"style": 8, "content": "=ODOO.PIVOT(1,\"order_id\",\"#country_id\",7)"}, "I28": {"content": "=ODOO.PIVOT(1,\"order_id\",\"#country_id\",8)"}, "I29": {"style": 8, "content": "=ODOO.PIVOT(1,\"order_id\",\"#country_id\",9)"}, "I30": {"content": "=ODOO.PIVOT(1,\"order_id\",\"#country_id\",10)"}, "I32": {"style": 1}, "I33": {"style": 1}, "I34": {"style": 9, "content": "=_t(\"Orders\")"}, "I35": {"style": 9}, "I36": {"style": 8, "content": "=ODOO.PIVOT(2,\"order_id\",\"#partner_id\",1)"}, "I37": {"content": "=ODOO.PIVOT(2,\"order_id\",\"#partner_id\",2)"}, "I38": {"style": 8, "content": "=ODOO.PIVOT(2,\"order_id\",\"#partner_id\",3)"}, "I39": {"content": "=ODOO.PIVOT(2,\"order_id\",\"#partner_id\",4)"}, "I40": {"style": 8, "content": "=ODOO.PIVOT(2,\"order_id\",\"#partner_id\",5)"}, "I41": {"content": "=ODOO.PIVOT(2,\"order_id\",\"#partner_id\",6)"}, "I42": {"style": 8, "content": "=ODOO.PIVOT(2,\"order_id\",\"#partner_id\",7)"}, "I43": {"content": "=ODOO.PIVOT(2,\"order_id\",\"#partner_id\",8)"}, "I44": {"style": 8, "content": "=ODOO.PIVOT(2,\"order_id\",\"#partner_id\",9)"}, "I45": {"content": "=ODOO.PIVOT(2,\"order_id\",\"#partner_id\",10)"}, "I47": {"style": 1}, "I48": {"style": 1, "border": 1}, "I49": {"style": 9, "content": "=_t(\"Orders\")", "border": 2}, "I50": {"style": 9}, "I51": {"style": 8, "content": "=ODOO.PIVOT(3,\"order_id\",\"#user_id\",1)"}, "I52": {"content": "=ODOO.PIVOT(3,\"order_id\",\"#user_id\",2)"}, "I53": {"style": 8, "content": "=ODOO.PIVOT(3,\"order_id\",\"#user_id\",3)"}, "I54": {"content": "=ODOO.PIVOT(3,\"order_id\",\"#user_id\",4)"}, "I55": {"style": 8, "content": "=ODOO.PIVOT(3,\"order_id\",\"#user_id\",5)"}, "I56": {"content": "=ODOO.PIVOT(3,\"order_id\",\"#user_id\",6)"}, "I57": {"style": 8, "content": "=ODOO.PIVOT(3,\"order_id\",\"#user_id\",7)"}, "I58": {"content": "=ODOO.PIVOT(3,\"order_id\",\"#user_id\",8)"}, "I59": {"style": 8, "content": "=ODOO.PIVOT(3,\"order_id\",\"#user_id\",9)"}, "I60": {"content": "=ODOO.PIVOT(3,\"order_id\",\"#user_id\",10)"}, "J19": {"style": 12, "border": 1}, "J20": {"style": 9, "content": "=_t(\"Amount\")", "border": 2}, "J21": {"style": 8, "format": 3, "content": "=ODOO.PIVOT(1,\"untaxed_total\",\"#country_id\",1)"}, "J22": {"format": 3, "content": "=ODOO.PIVOT(1,\"untaxed_total\",\"#country_id\",2)"}, "J23": {"style": 8, "format": 3, "content": "=ODOO.PIVOT(1,\"untaxed_total\",\"#country_id\",3)"}, "J24": {"format": 3, "content": "=ODOO.PIVOT(1,\"untaxed_total\",\"#country_id\",4)"}, "J25": {"style": 8, "format": 3, "content": "=ODOO.PIVOT(1,\"untaxed_total\",\"#country_id\",5)"}, "J26": {"format": 3, "content": "=ODOO.PIVOT(1,\"untaxed_total\",\"#country_id\",6)"}, "J27": {"style": 8, "format": 3, "content": "=ODOO.PIVOT(1,\"untaxed_total\",\"#country_id\",7)"}, "J28": {"format": 3, "content": "=ODOO.PIVOT(1,\"untaxed_total\",\"#country_id\",8)"}, "J29": {"style": 8, "format": 3, "content": "=ODOO.PIVOT(1,\"untaxed_total\",\"#country_id\",9)"}, "J30": {"format": 3, "content": "=ODOO.PIVOT(1,\"untaxed_total\",\"#country_id\",10)"}, "J32": {"style": 1}, "J33": {"style": 1}, "J34": {"style": 9, "content": "=_t(\"Amount\")"}, "J35": {"style": 9}, "J36": {"style": 8, "format": 3, "content": "=ODOO.PIVOT(2,\"untaxed_total\",\"#partner_id\",1)"}, "J37": {"format": 3, "content": "=ODOO.PIVOT(2,\"untaxed_total\",\"#partner_id\",2)"}, "J38": {"style": 8, "format": 3, "content": "=ODOO.PIVOT(2,\"untaxed_total\",\"#partner_id\",3)"}, "J39": {"format": 3, "content": "=ODOO.PIVOT(2,\"untaxed_total\",\"#partner_id\",4)"}, "J40": {"style": 8, "format": 3, "content": "=ODOO.PIVOT(2,\"untaxed_total\",\"#partner_id\",5)"}, "J41": {"format": 3, "content": "=ODOO.PIVOT(2,\"untaxed_total\",\"#partner_id\",6)"}, "J42": {"style": 8, "format": 3, "content": "=ODOO.PIVOT(2,\"untaxed_total\",\"#partner_id\",7)"}, "J43": {"format": 3, "content": "=ODOO.PIVOT(2,\"untaxed_total\",\"#partner_id\",8)"}, "J44": {"style": 8, "format": 3, "content": "=ODOO.PIVOT(2,\"untaxed_total\",\"#partner_id\",9)"}, "J45": {"format": 3, "content": "=ODOO.PIVOT(2,\"untaxed_total\",\"#partner_id\",10)"}, "J47": {"style": 1}, "J48": {"style": 1, "border": 1}, "J49": {"style": 9, "content": "=_t(\"Amount\")", "border": 2}, "J50": {"style": 9}, "J51": {"style": 8, "format": 3, "content": "=ODOO.PIVOT(3,\"untaxed_total\",\"#user_id\",1)"}, "J52": {"format": 3, "content": "=ODOO.PIVOT(3,\"untaxed_total\",\"#user_id\",2)"}, "J53": {"style": 8, "format": 3, "content": "=ODOO.PIVOT(3,\"untaxed_total\",\"#user_id\",3)"}, "J54": {"format": 3, "content": "=ODOO.PIVOT(3,\"untaxed_total\",\"#user_id\",4)"}, "J55": {"style": 8, "format": 3, "content": "=ODOO.PIVOT(3,\"untaxed_total\",\"#user_id\",5)"}, "J56": {"format": 3, "content": "=ODOO.PIVOT(3,\"untaxed_total\",\"#user_id\",6)"}, "J57": {"style": 8, "format": 3, "content": "=ODOO.PIVOT(3,\"untaxed_total\",\"#user_id\",7)"}, "J58": {"format": 3, "content": "=ODOO.PIVOT(3,\"untaxed_total\",\"#user_id\",8)"}, "J59": {"style": 8, "format": 3, "content": "=ODOO.PIVOT(3,\"untaxed_total\",\"#user_id\",9)"}, "J60": {"format": 3, "content": "=ODOO.PIVOT(3,\"untaxed_total\",\"#user_id\",10)"}, "A8": {"border": 2}, "B7": {"border": 1}, "B8": {"border": 2}, "B19": {"border": 1}, "C7": {"border": 1}, "C8": {"border": 2}, "C19": {"border": 1}, "D7": {"border": 1}, "D8": {"border": 2}, "D19": {"border": 1}, "E7": {"border": 1}, "E8": {"border": 2}, "E19": {"border": 1}, "F7": {"border": 1}, "F8": {"border": 2}, "F19": {"border": 1}, "G7": {"border": 1}, "G8": {"border": 2}, "H7": {"border": 1}, "H8": {"border": 2}, "I7": {"border": 1}, "I8": {"border": 2}, "J7": {"border": 1}, "J8": {"border": 2}}, "conditionalFormats": [], "figures": [{"id": "155096a3-ad7f-4e64-bf38-051fcfc90ee0", "x": 0, "y": 178, "width": 1000, "height": 230, "tag": "chart", "data": {"title": "", "background": "#FFFFFF", "legendPosition": "none", "metaData": {"groupBy": ["partner_id"], "measure": "untaxed_total", "order": "DESC", "resModel": "purchase.report"}, "searchParams": {"comparison": null, "context": {}, "domain": [], "groupBy": ["partner_id"], "orderBy": []}, "type": "odoo_bar", "verticalAxisPosition": "left", "stacked": true}}, {"id": "53bdb059-e5ce-47d9-bb63-d5f2e665c05f", "x": 0, "y": 0, "width": 192, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "percentage", "title": "Purchased", "type": "scorecard", "background": "", "baseline": "Data!E2", "baselineDescr": "since last period", "keyValue": "Data!D2"}}, {"id": "1e548a1e-ef9f-47f8-a9c7-b42709559c74", "x": 202, "y": 0, "width": 192, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "percentage", "title": "Orders", "type": "scorecard", "background": "", "baseline": "Data!E4", "baselineDescr": "since last period", "keyValue": "Data!D4"}}, {"id": "d161aa79-1991-4d86-9b51-df4e66f9ae7e", "x": 404, "y": 0, "width": 192, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "percentage", "title": "Lines", "type": "scorecard", "background": "", "baseline": "Data!E3", "baselineDescr": "since last period", "keyValue": "Data!D3"}}, {"id": "3b318ab5-0677-4790-8446-d37a0b4c6ac6", "x": 808, "y": 0, "width": 192, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "percentage", "title": "Days to receive", "type": "scorecard", "background": "", "baseline": "Data!E6", "baselineDescr": "last period", "keyValue": "Data!D6"}}, {"id": "7bd14ede-d636-44cc-ac07-bc3fe6c565c0", "x": 606, "y": 0, "width": 192, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "percentage", "title": "Quantity ordered", "type": "scorecard", "background": "", "baseline": "Data!E5", "baselineDescr": "since last period", "keyValue": "Data!D5"}}], "areGridLinesVisible": true, "isVisible": true}, {"id": "e37f2922-708e-4af7-8960-c964384ba95a", "name": "Data", "colNumber": 19, "rowNumber": 97, "rows": {}, "cols": {}, "merges": [], "cells": {"A1": {"style": 2, "content": "=_t(\"KPI\")"}, "A2": {"content": "=_t(\"Untaxed total\")"}, "A3": {"content": "=_t(\"Lines\")"}, "A4": {"content": "=_t(\"Orders\")"}, "A5": {"content": "=_t(\"Qty ordered\")"}, "A6": {"content": "=_t(\"Days to receive\")"}, "B1": {"style": 2, "content": "=_t(\"Current\")"}, "B2": {"content": "=ODOO.PIVOT(4,\"untaxed_total\")"}, "B3": {"content": "=ODOO.PIVOT(4,\"nbr_lines\")"}, "B4": {"content": "=ODOO.PIVOT(4,\"order_id\")"}, "B5": {"content": "=ODOO.PIVOT(4,\"qty_ordered\")"}, "B6": {"content": "=ODOO.PIVOT(4,\"delay_pass\")"}, "C1": {"style": 2, "content": "=_t(\"Previous\")"}, "C2": {"content": "=ODOO.PIVOT(5,\"untaxed_total\")"}, "C3": {"content": "=ODOO.PIVOT(5,\"nbr_lines\")"}, "C4": {"content": "=ODOO.PIVOT(5,\"order_id\")"}, "C5": {"content": "=ODOO.PIVOT(5,\"qty_ordered\")"}, "C6": {"content": "=ODOO.PIVOT(5,\"delay_pass\")"}, "D1": {"style": 2, "content": "=_t(\"Current\")"}, "D2": {"style": 8, "content": "=FORMAT.LARGE.NUMBER(B2)"}, "D3": {"style": 8, "content": "=FORMAT.LARGE.NUMBER(B3)"}, "D4": {"style": 8, "content": "=FORMAT.LARGE.NUMBER(B4)"}, "D5": {"style": 8, "content": "=FORMAT.LARGE.NUMBER(B5)"}, "D6": {"style": 13, "content": "=CONCATENATE(ROUNDUP(B6),_t(\" days\"))"}, "E1": {"style": 2, "content": "=_t(\"Previous\")"}, "E2": {"style": 8, "content": "=FORMAT.LARGE.NUMBER(C2)"}, "E3": {"style": 8, "content": "=FORMAT.LARGE.NUMBER(C3)"}, "E4": {"style": 8, "content": "=FORMAT.LARGE.NUMBER(C4)"}, "E5": {"style": 8, "content": "=FORMAT.LARGE.NUMBER(C5)"}, "E6": {"style": 8, "format": 3, "content": "=ROUNDUP(C6)"}}, "conditionalFormats": [], "figures": [], "areGridLinesVisible": true, "isVisible": true}], "entities": {}, "styles": {"1": {"textColor": "#01666b", "fontSize": 16, "bold": true}, "2": {"bold": true}, "3": {"fillColor": "#f2f2f2", "textColor": "#01666b"}, "4": {"textColor": "#01666b"}, "5": {"fillColor": ""}, "6": {"align": "left", "fillColor": "#f2f2f2"}, "7": {"align": "left"}, "8": {"fillColor": "#f2f2f2"}, "9": {"align": "right", "bold": true}, "10": {"fillColor": "#f2f2f2", "textColor": "#741b47"}, "11": {"textColor": "#741b47"}, "12": {"fontSize": 16, "bold": true}, "13": {"align": "right", "fillColor": "#f2f2f2"}}, "formats": {"1": "m/d/yyyy", "2": "[$$]#,##0", "3": "#,##0"}, "borders": {"1": {"bottom": ["thin", "#000"]}, "2": {"top": ["thin", "#000"]}}, "revisionId": "ebff854f-062d-403f-9f92-a495d68f3b39", "settings": {"locale": {"name": "English (US)", "code": "en_US", "thousandsSeparator": ",", "decimalSeparator": ".", "dateFormat": "mm/dd/yyyy", "timeFormat": "hh:mm:ss", "formulaArgSeparator": ","}}, "chartOdooMenusReferences": {"8b6dfcc3-d418-4b74-80ea-f6bdcdc3b229": "purchase.menu_purchase_root", "155096a3-ad7f-4e64-bf38-051fcfc90ee0": "purchase.menu_purchase_root", "53bdb059-e5ce-47d9-bb63-d5f2e665c05f": "purchase.purchase_report", "1e548a1e-ef9f-47f8-a9c7-b42709559c74": "purchase.menu_purchase_form_action", "d161aa79-1991-4d86-9b51-df4e66f9ae7e": "purchase.menu_purchase_form_action", "7bd14ede-d636-44cc-ac07-bc3fe6c565c0": "purchase.purchase_report", "3b318ab5-0677-4790-8446-d37a0b4c6ac6": "purchase.purchase_report"}, "odooVersion": 4, "lists": {"1": {"columns": ["name", "date_approve", "partner_id", "user_id", "amount_untaxed"], "domain": [["state", "in", ["purchase", "done"]]], "model": "purchase.order", "context": {}, "orderBy": [{"name": "amount_untaxed", "asc": false}], "id": "1", "name": "Purchase Orders by Untaxed Amount"}}, "listNextId": 2, "pivots": {"1": {"colGroupBys": [], "context": {}, "domain": ["&", ["country_id", "!=", false], ["state", "in", ["purchase", "done"]]], "id": "1", "measures": [{"field": "order_id"}, {"field": "untaxed_total"}], "model": "purchase.report", "rowGroupBys": ["country_id"], "name": "Purchase Analysis by Partner Country", "sortedColumn": {"groupId": [[], []], "measure": "untaxed_total", "order": "desc"}}, "2": {"colGroupBys": [], "context": {}, "domain": [["state", "in", ["purchase", "done"]]], "id": "2", "measures": [{"field": "order_id"}, {"field": "untaxed_total"}], "model": "purchase.report", "rowGroupBys": ["partner_id"], "name": "Purchase Analysis by <PERSON><PERSON><PERSON>", "sortedColumn": {"groupId": [[], []], "measure": "untaxed_total", "order": "desc"}}, "3": {"colGroupBys": [], "context": {}, "domain": ["&", ["user_id", "!=", false], ["state", "in", ["purchase", "done"]]], "id": "3", "measures": [{"field": "order_id"}, {"field": "untaxed_total"}], "model": "purchase.report", "rowGroupBys": ["user_id"], "name": "Purchase Analysis by Purchase Representative", "sortedColumn": {"groupId": [[], []], "measure": "untaxed_total", "order": "desc"}}, "4": {"colGroupBys": [], "context": {}, "domain": [["state", "in", ["purchase", "done"]]], "id": "4", "measures": [{"field": "untaxed_total"}, {"field": "nbr_lines"}, {"field": "order_id"}, {"field": "qty_ordered"}, {"field": "delay_pass"}], "model": "purchase.report", "rowGroupBys": [], "name": "stats - current", "sortedColumn": {"groupId": [[], []], "measure": "untaxed_total", "order": "desc"}}, "5": {"colGroupBys": [], "context": {}, "domain": [["state", "in", ["purchase", "done"]]], "id": "5", "measures": [{"field": "untaxed_total"}, {"field": "nbr_lines"}, {"field": "order_id"}, {"field": "qty_ordered"}, {"field": "delay_pass"}], "model": "purchase.report", "rowGroupBys": [], "name": "stats - previous", "sortedColumn": {"groupId": [[], []], "measure": "untaxed_total", "order": "desc"}}}, "pivotNextId": 6, "globalFilters": [{"id": "cf8f7a37-d54f-41ee-9fd8-2e5ad98720c9", "type": "date", "label": "Period", "defaultValue": "last_three_months", "rangeType": "relative", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "date_order", "type": "datetime", "offset": 0}, "2": {"field": "date_order", "type": "datetime", "offset": 0}, "3": {"field": "date_order", "type": "datetime", "offset": 0}, "4": {"field": "date_order", "type": "datetime", "offset": 0}, "5": {"field": "date_order", "type": "datetime", "offset": -1}}, "listFields": {"1": {"field": "date_order", "type": "datetime", "offset": 0}}, "graphFields": {"155096a3-ad7f-4e64-bf38-051fcfc90ee0": {"field": "date_order", "type": "datetime", "offset": 0}}}, {"id": "fbf748f9-596f-42f0-be3d-e0d7c01ef9be", "type": "relation", "label": "Country", "modelName": "res.country", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "country_id", "type": "many2one"}, "2": {"field": "country_id", "type": "many2one"}, "3": {"field": "country_id", "type": "many2one"}, "4": {"field": "country_id", "type": "many2one"}, "5": {"field": "country_id", "type": "many2one"}}, "listFields": {"1": {"field": "partner_id.country_id", "type": "many2one"}}, "graphFields": {"155096a3-ad7f-4e64-bf38-051fcfc90ee0": {"field": "country_id", "type": "many2one"}}}, {"id": "36330b95-00de-4d79-9ad9-3d67b5685993", "type": "relation", "label": "<PERSON><PERSON><PERSON>", "modelName": "res.partner", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "partner_id", "type": "many2one"}, "2": {"field": "partner_id", "type": "many2one"}, "3": {"field": "partner_id", "type": "many2one"}, "4": {"field": "partner_id", "type": "many2one"}, "5": {"field": "partner_id", "type": "many2one"}}, "listFields": {"1": {"field": "partner_id", "type": "many2one"}}, "graphFields": {"155096a3-ad7f-4e64-bf38-051fcfc90ee0": {"field": "partner_id", "type": "many2one"}}}, {"id": "46501b8a-08a2-4d8c-abb5-7c52f9d1ada2", "type": "relation", "label": "Buyer", "modelName": "res.users", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "user_id", "type": "many2one"}, "2": {"field": "user_id", "type": "many2one"}, "3": {"field": "user_id", "type": "many2one"}, "4": {"field": "user_id", "type": "many2one"}, "5": {"field": "user_id", "type": "many2one"}}, "listFields": {"1": {"field": "user_id", "type": "many2one"}}, "graphFields": {"155096a3-ad7f-4e64-bf38-051fcfc90ee0": {"field": "user_id", "type": "many2one"}}}]}