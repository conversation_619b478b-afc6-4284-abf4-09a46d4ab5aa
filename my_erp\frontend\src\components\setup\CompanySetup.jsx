/**
 * Company Setup Component - Following Odoo's Company Configuration Flow
 * Professional ERP Company Setup with Modern UX/UI
 */
import React, { useState, useEffect } from 'react';
import {
  Steps,
  Card,
  Form,
  Input,
  Select,
  Upload,
  Button,
  Row,
  Col,
  Typography,
  Space,
  Divider,
  Alert,
  Progress,
  Avatar,
  message,
  Tooltip,
  Switch
} from 'antd';
import {
  BuildingOutlined,
  GlobalOutlined,
  DollarOutlined,
  FileTextOutlined,
  UploadOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined,
  SaveOutlined,
  ArrowRightOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons';
import { useAuth } from '../shared/auth/AuthProvider';
import { useNavigate } from 'react-router-dom';
import './CompanySetup.css';

const { Step } = Steps;
const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

// Countries list (simplified)
const COUNTRIES = [
  { code: 'PK', name: 'Pakistan', currency: 'PKR' },
  { code: 'US', name: 'United States', currency: 'USD' },
  { code: 'GB', name: 'United Kingdom', currency: 'GBP' },
  { code: 'IN', name: 'India', currency: 'INR' },
  { code: 'AE', name: 'United Arab Emirates', currency: 'AED' },
  { code: 'SA', name: 'Saudi Arabia', currency: 'SAR' },
  { code: 'CA', name: 'Canada', currency: 'CAD' },
  { code: 'AU', name: 'Australia', currency: 'AUD' },
  { code: 'DE', name: 'Germany', currency: 'EUR' },
  { code: 'FR', name: 'France', currency: 'EUR' }
];

// Industries list
const INDUSTRIES = [
  'Technology',
  'Manufacturing',
  'Healthcare',
  'Finance',
  'Retail',
  'Construction',
  'Education',
  'Transportation',
  'Real Estate',
  'Consulting',
  'Other'
];

// Company sizes
const COMPANY_SIZES = [
  { value: 'small', label: '1-10 employees' },
  { value: 'medium', label: '11-50 employees' },
  { value: 'large', label: '51-200 employees' },
  { value: 'enterprise', label: '200+ employees' }
];

const CompanySetup = () => {
  const [form] = Form.useForm();
  const [currentStep, setCurrentStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({});
  const [logoFile, setLogoFile] = useState(null);
  const [logoPreview, setLogoPreview] = useState('');
  
  const { currentCompany, isSuperUser, isAdmin } = useAuth();
  const navigate = useNavigate();

  // Setup steps
  const steps = [
    {
      title: 'Company Information',
      icon: <BuildingOutlined />,
      description: 'Basic company details'
    },
    {
      title: 'Location & Contact',
      icon: <GlobalOutlined />,
      description: 'Address and contact information'
    },
    {
      title: 'Financial Settings',
      icon: <DollarOutlined />,
      description: 'Currency and fiscal settings'
    },
    {
      title: 'Review & Complete',
      icon: <CheckCircleOutlined />,
      description: 'Review and finalize setup'
    }
  ];

  useEffect(() => {
    // Load existing company data if editing
    if (currentCompany) {
      form.setFieldsValue({
        name: currentCompany.name,
        email: currentCompany.email,
        phone: currentCompany.phone,
        website: currentCompany.website,
        street: currentCompany.street,
        city: currentCompany.city,
        state: currentCompany.state,
        zip: currentCompany.zip,
        country_id: currentCompany.country_id,
        currency_id: currentCompany.currency_id,
        industry: currentCompany.industry,
        company_size: currentCompany.company_size,
        tax_id: currentCompany.tax_id,
        description: currentCompany.description
      });
      setFormData(currentCompany);
    }
  }, [currentCompany, form]);

  const handleNext = async () => {
    try {
      const values = await form.validateFields();
      setFormData({ ...formData, ...values });
      setCurrentStep(currentStep + 1);
    } catch (error) {
      message.error('Please fill in all required fields');
    }
  };

  const handlePrevious = () => {
    setCurrentStep(currentStep - 1);
  };

  const handleFinish = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();
      const finalData = { ...formData, ...values };

      // Add logo if uploaded
      if (logoFile) {
        finalData.logo = logoFile;
      }

      // API call to save company
      // await companyAPI.createOrUpdate(finalData);

      message.success('Company setup completed successfully!');
      navigate('/dashboard');
    } catch (error) {
      message.error('Failed to save company settings');
    } finally {
      setLoading(false);
    }
  };

  const handleLogoUpload = (file) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('You can only upload image files!');
      return false;
    }

    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('Image must be smaller than 2MB!');
      return false;
    }

    setLogoFile(file);
    
    // Create preview
    const reader = new FileReader();
    reader.onload = (e) => setLogoPreview(e.target.result);
    reader.readAsDataURL(file);

    return false; // Prevent auto upload
  };

  const handleCountryChange = (countryCode) => {
    const country = COUNTRIES.find(c => c.code === countryCode);
    if (country) {
      form.setFieldsValue({ currency_id: country.currency });
    }
  };

  // Step 1: Company Information
  const renderStep1 = () => (
    <Card title="Company Information" className="setup-card">
      <Row gutter={24}>
        <Col span={24}>
          <div className="logo-upload-section">
            <Text strong>Company Logo</Text>
            <div className="logo-upload-container">
              <Upload
                name="logo"
                listType="picture-card"
                className="logo-uploader"
                showUploadList={false}
                beforeUpload={handleLogoUpload}
              >
                {logoPreview ? (
                  <img src={logoPreview} alt="logo" style={{ width: '100%' }} />
                ) : (
                  <div>
                    <UploadOutlined />
                    <div style={{ marginTop: 8 }}>Upload Logo</div>
                  </div>
                )}
              </Upload>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                Recommended: 200x200px, PNG or JPG, max 2MB
              </Text>
            </div>
          </div>
        </Col>
      </Row>

      <Row gutter={24}>
        <Col xs={24} md={12}>
          <Form.Item
            name="name"
            label="Company Name"
            rules={[{ required: true, message: 'Please enter company name' }]}
          >
            <Input placeholder="Enter company name" />
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            name="industry"
            label="Industry"
            rules={[{ required: true, message: 'Please select industry' }]}
          >
            <Select placeholder="Select industry">
              {INDUSTRIES.map(industry => (
                <Option key={industry} value={industry}>{industry}</Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={24}>
        <Col xs={24} md={12}>
          <Form.Item
            name="company_size"
            label="Company Size"
            rules={[{ required: true, message: 'Please select company size' }]}
          >
            <Select placeholder="Select company size">
              {COMPANY_SIZES.map(size => (
                <Option key={size.value} value={size.value}>{size.label}</Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            name="tax_id"
            label="Tax ID / Registration Number"
          >
            <Input placeholder="Enter tax ID or registration number" />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={24}>
        <Col span={24}>
          <Form.Item
            name="description"
            label="Company Description"
          >
            <TextArea 
              rows={4} 
              placeholder="Brief description of your company (optional)"
            />
          </Form.Item>
        </Col>
      </Row>
    </Card>
  );

  // Step 2: Location & Contact
  const renderStep2 = () => (
    <Card title="Location & Contact Information" className="setup-card">
      <Row gutter={24}>
        <Col xs={24} md={12}>
          <Form.Item
            name="email"
            label="Email Address"
            rules={[
              { required: true, message: 'Please enter email address' },
              { type: 'email', message: 'Please enter valid email address' }
            ]}
          >
            <Input placeholder="<EMAIL>" />
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            name="phone"
            label="Phone Number"
            rules={[{ required: true, message: 'Please enter phone number' }]}
          >
            <Input placeholder="+****************" />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={24}>
        <Col span={24}>
          <Form.Item
            name="website"
            label="Website"
          >
            <Input placeholder="https://www.company.com" />
          </Form.Item>
        </Col>
      </Row>

      <Divider>Address Information</Divider>

      <Row gutter={24}>
        <Col span={24}>
          <Form.Item
            name="street"
            label="Street Address"
            rules={[{ required: true, message: 'Please enter street address' }]}
          >
            <Input placeholder="123 Business Street" />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={24}>
        <Col xs={24} md={8}>
          <Form.Item
            name="city"
            label="City"
            rules={[{ required: true, message: 'Please enter city' }]}
          >
            <Input placeholder="City" />
          </Form.Item>
        </Col>
        <Col xs={24} md={8}>
          <Form.Item
            name="state"
            label="State/Province"
          >
            <Input placeholder="State/Province" />
          </Form.Item>
        </Col>
        <Col xs={24} md={8}>
          <Form.Item
            name="zip"
            label="ZIP/Postal Code"
          >
            <Input placeholder="12345" />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={24}>
        <Col span={24}>
          <Form.Item
            name="country_id"
            label="Country"
            rules={[{ required: true, message: 'Please select country' }]}
          >
            <Select 
              placeholder="Select country"
              onChange={handleCountryChange}
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {COUNTRIES.map(country => (
                <Option key={country.code} value={country.code}>
                  {country.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>
    </Card>
  );

  // Step 3: Financial Settings
  const renderStep3 = () => (
    <Card title="Financial Settings" className="setup-card">
      <Alert
        message="Financial Configuration"
        description="These settings will affect how financial data is processed in your ERP system."
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Row gutter={24}>
        <Col xs={24} md={12}>
          <Form.Item
            name="currency_id"
            label="Base Currency"
            rules={[{ required: true, message: 'Please select base currency' }]}
          >
            <Select placeholder="Select currency">
              <Option value="PKR">PKR - Pakistani Rupee</Option>
              <Option value="USD">USD - US Dollar</Option>
              <Option value="EUR">EUR - Euro</Option>
              <Option value="GBP">GBP - British Pound</Option>
              <Option value="INR">INR - Indian Rupee</Option>
              <Option value="AED">AED - UAE Dirham</Option>
              <Option value="SAR">SAR - Saudi Riyal</Option>
              <Option value="CAD">CAD - Canadian Dollar</Option>
              <Option value="AUD">AUD - Australian Dollar</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            name="fiscal_year_start"
            label="Fiscal Year Start"
            initialValue="01-01"
          >
            <Select placeholder="Select fiscal year start">
              <Option value="01-01">January 1st</Option>
              <Option value="04-01">April 1st</Option>
              <Option value="07-01">July 1st</Option>
              <Option value="10-01">October 1st</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={24}>
        <Col xs={24} md={12}>
          <Form.Item
            name="decimal_precision"
            label="Decimal Precision"
            initialValue={2}
          >
            <Select>
              <Option value={0}>0 (No decimals)</Option>
              <Option value={2}>2 (Standard)</Option>
              <Option value={3}>3 (High precision)</Option>
              <Option value={4}>4 (Very high precision)</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            name="date_format"
            label="Date Format"
            initialValue="DD/MM/YYYY"
          >
            <Select>
              <Option value="DD/MM/YYYY">DD/MM/YYYY</Option>
              <Option value="MM/DD/YYYY">MM/DD/YYYY</Option>
              <Option value="YYYY-MM-DD">YYYY-MM-DD</Option>
              <Option value="DD-MM-YYYY">DD-MM-YYYY</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Divider>Tax Settings</Divider>

      <Row gutter={24}>
        <Col xs={24} md={12}>
          <Form.Item
            name="default_tax_rate"
            label="Default Tax Rate (%)"
            initialValue={0}
          >
            <Input type="number" min={0} max={100} placeholder="0" />
          </Form.Item>
        </Col>
        <Col xs={24} md={12}>
          <Form.Item
            name="tax_calculation_rounding"
            label="Tax Calculation Rounding"
            initialValue="round_per_line"
          >
            <Select>
              <Option value="round_per_line">Round per Line</Option>
              <Option value="round_globally">Round Globally</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>
    </Card>
  );

  // Step 4: Review & Complete
  const renderStep4 = () => (
    <Card title="Review & Complete Setup" className="setup-card">
      <Alert
        message="Review Your Settings"
        description="Please review all the information below before completing the setup."
        type="success"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <div className="review-section">
        <Title level={4}>Company Information</Title>
        <Row gutter={24}>
          <Col xs={24} md={6}>
            {logoPreview && (
              <Avatar size={80} src={logoPreview} icon={<BuildingOutlined />} />
            )}
          </Col>
          <Col xs={24} md={18}>
            <Space direction="vertical" size="small">
              <Text><strong>Name:</strong> {formData.name}</Text>
              <Text><strong>Industry:</strong> {formData.industry}</Text>
              <Text><strong>Size:</strong> {COMPANY_SIZES.find(s => s.value === formData.company_size)?.label}</Text>
              {formData.tax_id && <Text><strong>Tax ID:</strong> {formData.tax_id}</Text>}
            </Space>
          </Col>
        </Row>

        <Divider />

        <Title level={4}>Contact Information</Title>
        <Space direction="vertical" size="small">
          <Text><strong>Email:</strong> {formData.email}</Text>
          <Text><strong>Phone:</strong> {formData.phone}</Text>
          {formData.website && <Text><strong>Website:</strong> {formData.website}</Text>}
          <Text><strong>Address:</strong> {formData.street}, {formData.city}, {formData.state} {formData.zip}</Text>
          <Text><strong>Country:</strong> {COUNTRIES.find(c => c.code === formData.country_id)?.name}</Text>
        </Space>

        <Divider />

        <Title level={4}>Financial Settings</Title>
        <Space direction="vertical" size="small">
          <Text><strong>Currency:</strong> {formData.currency_id}</Text>
          <Text><strong>Fiscal Year Start:</strong> {formData.fiscal_year_start}</Text>
          <Text><strong>Decimal Precision:</strong> {formData.decimal_precision}</Text>
          <Text><strong>Date Format:</strong> {formData.date_format}</Text>
        </Space>
      </div>
    </Card>
  );

  const renderStepContent = () => {
    switch (currentStep) {
      case 0: return renderStep1();
      case 1: return renderStep2();
      case 2: return renderStep3();
      case 3: return renderStep4();
      default: return renderStep1();
    }
  };

  return (
    <div className="company-setup-container">
      <div className="setup-header">
        <Title level={2}>
          <BuildingOutlined /> Company Setup
        </Title>
        <Paragraph type="secondary">
          Configure your company information to get started with your ERP system.
        </Paragraph>
      </div>

      <Card className="setup-progress-card">
        <Steps current={currentStep} className="setup-steps">
          {steps.map((step, index) => (
            <Step
              key={index}
              title={step.title}
              description={step.description}
              icon={step.icon}
            />
          ))}
        </Steps>
        <Progress 
          percent={((currentStep + 1) / steps.length) * 100} 
          showInfo={false}
          strokeColor="#1890ff"
          style={{ marginTop: 16 }}
        />
      </Card>

      <Form
        form={form}
        layout="vertical"
        className="setup-form"
      >
        {renderStepContent()}
      </Form>

      <Card className="setup-actions">
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Button
            onClick={handlePrevious}
            disabled={currentStep === 0}
            icon={<ArrowLeftOutlined />}
          >
            Previous
          </Button>
          
          <Space>
            <Text type="secondary">
              Step {currentStep + 1} of {steps.length}
            </Text>
          </Space>

          {currentStep < steps.length - 1 ? (
            <Button
              type="primary"
              onClick={handleNext}
              icon={<ArrowRightOutlined />}
            >
              Next
            </Button>
          ) : (
            <Button
              type="primary"
              onClick={handleFinish}
              loading={loading}
              icon={<SaveOutlined />}
            >
              Complete Setup
            </Button>
          )}
        </Space>
      </Card>
    </div>
  );
};

export default CompanySetup;
