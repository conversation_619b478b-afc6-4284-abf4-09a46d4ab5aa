#!/usr/bin/env python3
import psycopg2

def show_accounting_menus():
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='dataicraft',
            user='odoo',
            password='odoo123'
        )
        cur = conn.cursor()
        
        print("=== ACCOUNTING MENU STRUCTURE ===")
        
        # Get the main Invoicing/Accounting app
        cur.execute("""
            SELECT id, name 
            FROM ir_ui_menu 
            WHERE parent_id IS NULL 
            AND (name::text LIKE '%Invoice%' OR name::text LIKE '%Account%')
            AND active = true;
        """)
        
        main_apps = cur.fetchall()
        print("Main Accounting Apps:")
        for app in main_apps:
            print(f"  {app[0]}: {app[1]}")
        
        # Get all accounting-related menus
        cur.execute("""
            SELECT m.id, m.name, m.parent_id, p.name as parent_name
            FROM ir_ui_menu m
            LEFT JOIN ir_ui_menu p ON m.parent_id = p.id
            WHERE m.active = true 
            AND (
                m.name::text LIKE '%Invoice%' OR 
                m.name::text LIKE '%Account%' OR 
                m.name::text LIKE '%Journal%' OR 
                m.name::text LIKE '%Bill%' OR
                m.name::text LIKE '%Payment%' OR
                m.name::text LIKE '%Report%' OR
                m.name::text LIKE '%Chart%' OR
                m.name::text LIKE '%Tax%'
            )
            ORDER BY m.parent_id, m.sequence, m.id;
        """)
        
        menus = cur.fetchall()
        
        print(f"\nAll Accounting Menus ({len(menus)} found):")
        print("=" * 80)
        
        current_parent = None
        for menu in menus:
            menu_id, menu_name, parent_id, parent_name = menu
            
            if parent_id != current_parent:
                current_parent = parent_id
                if parent_name:
                    print(f"\n📁 {parent_name} (ID: {parent_id})")
                else:
                    print(f"\n📁 ROOT MENU")
                print("-" * 40)
            
            print(f"  📄 {menu_name} (ID: {menu_id})")
        
        # Check what the user can access
        print(f"\n=== USER ACCESS CHECK ===")
        cur.execute("""
            SELECT DISTINCT m.id, m.name
            FROM ir_ui_menu m
            JOIN ir_ui_menu_group_rel mgr ON m.id = mgr.menu_id
            JOIN res_groups g ON mgr.gid = g.id
            JOIN res_groups_users_rel gur ON g.id = gur.gid
            WHERE gur.uid = 2  -- admin user
            AND m.active = true
            AND (
                m.name::text LIKE '%Invoice%' OR 
                m.name::text LIKE '%Account%' OR 
                m.name::text LIKE '%Journal%'
            )
            ORDER BY m.id;
        """)
        
        user_menus = cur.fetchall()
        print(f"Menus accessible to admin user: {len(user_menus)}")
        for menu in user_menus:
            print(f"  ✅ {menu[1]} (ID: {menu[0]})")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    show_accounting_menus()
