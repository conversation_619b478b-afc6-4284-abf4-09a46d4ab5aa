<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.MessageReactionMenu">
        <Dialog size="'md'" header="false" footer="false" contentClass="'o-mail-MessageReactionMenu h-50 d-flex'">
            <div class="d-flex h-100" t-on-keydown="onKeydown" t-ref="root">
                <div class="d-flex overflow-auto flex-column bg-100 p-2 h-100 border-end">
                    <t t-foreach="props.message.reactions" t-as="reaction" t-key="reaction.content">
                        <button class="btn p-1 rounded-2 mx-2 py-0 d-flex align-items-center" t-att-class="{ 'bg-200 border-primary': reaction.eq(state.reaction) }" t-att-title="getEmojiShortcode(reaction)" t-on-click="() => state.reaction = reaction">
                            <span class="mx-1 fs-2" t-esc="reaction.content"/>
                            <span class="mx-1 pe-2" t-att-class="{ 'text-primary': reaction.eq(state.reaction) }" t-esc="reaction.count"/>
                        </button>
                    </t>
                </div>
                <div class="d-flex overflow-auto flex-column flex-grow-1 bg-view p-2 h-100">
                    <div t-foreach="state.reaction.personas" t-as="persona" t-key="persona.id" class="o-mail-MessageReactionMenu-persona d-flex p-1 align-items-center" t-att-class="{ 'o-isDeviceSmall': ui.isSmall }">
                        <img class="rounded o_object_fit_cover o-mail-MessageReactionMenu-avatar" t-att-src="threadService.avatarUrl(persona, props.message.originThread)"/>
                        <span class="d-flex flex-grow-1 align-items-center">
                            <span class="mx-2 text-truncate fs-6" t-esc="persona.name"/>
                            <div class="flex-grow-1"/>
                            <button t-if="persona.eq(store.self)" class="btn btn-light fa fa-trash rounded-pill bg-inherit border-0" title="Remove" t-on-click.stop="() => this.messageService.removeReaction(state.reaction)"/>
                        </span>
                    </div>
                </div>
            </div>
        </Dialog>
    </t>

</templates>
