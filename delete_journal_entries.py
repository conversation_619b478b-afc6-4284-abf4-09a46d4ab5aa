#!/usr/bin/env python3
import psycopg2

def delete_journal_entries():
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='dataicraft',
            user='odoo',
            password='odoo123'
        )
        cur = conn.cursor()
        
        print("=== DELETING JOURNAL ENTRIES TO ALLOW CURRENCY CHANGE ===")
        
        # First, check what we have
        cur.execute("SELECT COUNT(*) FROM account_move WHERE company_id = 1;")
        move_count = cur.fetchone()[0]
        print(f"Journal entries to delete: {move_count}")
        
        cur.execute("SELECT COUNT(*) FROM account_move_line WHERE company_id = 1;")
        line_count = cur.fetchone()[0]
        print(f"Journal entry lines to delete: {line_count}")
        
        # Delete in the correct order (lines first, then moves)
        print("\nStep 1: Deleting journal entry lines...")
        cur.execute("DELETE FROM account_move_line WHERE company_id = 1;")
        deleted_lines = cur.rowcount
        print(f"Deleted {deleted_lines} journal entry lines")
        
        print("\nStep 2: Deleting journal entries...")
        cur.execute("DELETE FROM account_move WHERE company_id = 1;")
        deleted_moves = cur.rowcount
        print(f"Deleted {deleted_moves} journal entries")
        
        # Also delete any reconciliation records
        print("\nStep 3: Cleaning up reconciliation records...")
        cur.execute("DELETE FROM account_partial_reconcile WHERE company_id = 1;")
        deleted_reconcile = cur.rowcount
        print(f"Deleted {deleted_reconcile} reconciliation records")
        
        # Delete any payment records
        print("\nStep 4: Cleaning up payment records...")
        cur.execute("DELETE FROM account_payment WHERE company_id = 1;")
        deleted_payments = cur.rowcount
        print(f"Deleted {deleted_payments} payment records")
        
        # Reset journal sequences
        print("\nStep 5: Resetting journal sequences...")
        cur.execute("UPDATE account_journal SET sequence_number = 1 WHERE company_id = 1;")
        
        # Commit all changes
        conn.commit()
        
        print("\n✅ SUCCESS! All journal entries deleted.")
        print("You can now change the company currency to PKR.")
        
        # Verify deletion
        cur.execute("SELECT COUNT(*) FROM account_move WHERE company_id = 1;")
        remaining_moves = cur.fetchone()[0]
        
        cur.execute("SELECT COUNT(*) FROM account_move_line WHERE company_id = 1;")
        remaining_lines = cur.fetchone()[0]
        
        print(f"\nVerification:")
        print(f"Remaining journal entries: {remaining_moves}")
        print(f"Remaining journal entry lines: {remaining_lines}")
        
        if remaining_moves == 0 and remaining_lines == 0:
            print("✅ All entries successfully deleted!")
        else:
            print("⚠️ Some entries may still remain")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    print("⚠️  WARNING: This will delete ALL journal entries for your company!")
    print("This is necessary to change the currency.")
    print("Make sure you have a backup if needed.")
    
    confirm = input("\nDo you want to proceed? (yes/no): ")
    if confirm.lower() == 'yes':
        delete_journal_entries()
    else:
        print("Operation cancelled.")
