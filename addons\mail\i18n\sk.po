# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* mail
# 
# Translators:
# <PERSON><PERSON><PERSON> <rast<PERSON>.<EMAIL>>, 2023
# <PERSON><PERSON><PERSON>, 2023
# ka<PERSON><PERSON><PERSON> schus<PERSON> <karol<PERSON>.schustero<PERSON>@vdp.sk>, 2023
# <PERSON><PERSON> <<EMAIL>>, 2023
# <PERSON>, 2024
# <PERSON><PERSON>, 2024
# <PERSON> <brencic<PERSON><EMAIL>>, 2025
# <PERSON><PERSON><PERSON>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-11 01:02+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON>, 2025\n"
"Language-Team: Slovak (https://app.transifex.com/odoo/teams/41243/sk/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: sk\n"
"Plural-Forms: nplurals=4; plural=(n % 1 == 0 && n == 1 ? 0 : n % 1 == 0 && n >= 2 && n <= 4 ? 1 : n % 1 != 0 ? 2: 3);\n"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_gateway_allowed.py:0
#, python-format
msgid ""
"\n"
"            <p class=\"o_view_nocontent_smiling_face\">\n"
"                Add addresses to the Allowed List\n"
"            </p><p>\n"
"                To protect you from spam and reply loops, Odoo automatically blocks emails\n"
"                coming to your gateway past a threshold of <b>%(threshold)i</b> emails every <b>%(minutes)i</b>\n"
"                minutes. If there are some addresses from which you need to receive very frequent\n"
"                updates, you can however add them below and Odoo will let them go through.\n"
"            </p>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_document_unfollowed
msgid "\" no longer followed"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "\"%(activity_name)s: %(summary)s\" assigned to you"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
#, python-format
msgid "\"%(hostname)s\" needs to access your microphone"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "\"%(hostname)s\" requires microphone access"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "%(activity)s, assigned to %(name)s, due on the %(deadline)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/out_of_focus_service.js:0
#, python-format
msgid "%(author name)s from %(channel name)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.js:0
#, python-format
msgid "%(candidateType)s (%(protocol)s)"
msgstr "%(candidateType)s (%(protocol)s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid ""
"%(email)s is not recognized as a valid email. This is required to create a "
"new customer."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "%(name)s: %(message)s)"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"%(new_line)s%(new_line)sType %(bold_start)s@username%(bold_end)s to mention "
"someone, and grab their attention.%(new_line)sType "
"%(bold_start)s#channel%(bold_end)s to mention a channel.%(new_line)sType "
"%(bold_start)s/command%(bold_end)s to execute a command."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid ""
"%(open_button)s%(icon)s%(open_em)sDiscard "
"editing%(close_em)s%(close_button)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid ""
"%(open_samp)sEscape%(close_samp)s %(open_em)sto "
"%(open_cancel)scancel%(close_cancel)s%(close_em)s, %(open_samp)sCTRL-"
"Enter%(close_samp)s %(open_em)sto "
"%(open_save)ssave%(close_save)s%(close_em)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid ""
"%(open_samp)sEscape%(close_samp)s %(open_em)sto "
"%(open_cancel)scancel%(close_cancel)s%(close_em)s, "
"%(open_samp)sEnter%(close_samp)s %(open_em)sto "
"%(open_save)ssave%(close_save)s%(close_em)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_core_web_service.js:0
#, python-format
msgid "%(user)s connected. This is their first connection. Wish them luck."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow %(document)s document: %(title)s"
msgstr ""
"%(user_name)s vás pozval na sledovanie %(document)s dokumentu: %(title)s"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "%(user_name)s invited you to follow a new document."
msgstr "%(user_name)s vás pozval na sledovanie nového dokumentu."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "%(user_name)s pinned a message to this channel."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_mail_server.py:0
#, python-format
msgid "%s (Email Template)"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "%s (copy)"
msgstr "%s (kópia)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/out_of_focus_service_patch.js:0
#, python-format
msgid "%s Message"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/out_of_focus_service_patch.js:0
#, python-format
msgid "%s Messages"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
#, python-format
msgid "%s and %s are typing..."
msgstr "%s a %s píšu..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.js:0
#, python-format
msgid "%s and %s have reacted with %s"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "%s created"
msgstr "%s vytvorené"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#, python-format
msgid "%s days overdue"
msgstr "%s dní po splatnosti"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "%s has a request"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "%s has a suggestion"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.js:0
#, python-format
msgid "%s has reacted with %s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
#, python-format
msgid "%s is typing..."
msgstr "%s píše..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.js:0
#, python-format
msgid "%s messages found"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "%s raised their hand"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid "%s started a live conference"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "%s\" requires \"camera\" access"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "%s\" requires \"screen recording\" access"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/typing/common/typing.js:0
#, python-format
msgid "%s, %s and more are typing..."
msgstr "%s, %s a ďalší píšu..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.js:0
#, python-format
msgid "%s, %s, %s and %s other persons have reacted with %s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.js:0
#, python-format
msgid "%s, %s, %s and 1 other person have reacted with %s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reactions.js:0
#, python-format
msgid "%s, %s, %s have reacted with %s"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
msgid "&amp;nbsp;&amp;nbsp;"
msgstr "&amp;nbsp;&amp;nbsp;"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#, python-format
msgid "(Translated from: %(language)s)"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#, python-format
msgid "(Translation Failure: %(error)s)"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#, python-format
msgid "(from"
msgstr "(od"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "(originally assigned to"
msgstr "(pôvodne priradené k"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid ""
",\n"
"    <br/><br/>"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid ". Narrow your search to see more choices."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid ""
"<b invisible=\"not no_record\" class=\"text-warning\">No record for this "
"model</b>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "<i class=\"fa fa-globe\" aria-label=\"Document url\"/>"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "<samp>%(send_keybind)s</samp><i> to send</i>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"d-block w-75 py-2\">Button Color</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"d-block w-75 py-2\">Header Color</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "<span class=\"me-1 oe_inline\">@</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "<span class=\"me-1\">@</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "<span class=\"o_stat_text\">Open Document</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "<span class=\"o_stat_text\">Open Parent Document</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "<span class=\"text-bg-danger\">Archived</span>"
msgstr "<span class=\"text-bg-danger\">Archivované</span>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid ""
"<span invisible=\"mail_post_method != 'email'\">\n"
"                                The message will be sent as an email to the recipients of the\n"
"                                template and will not appear in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'note'\">\n"
"                                The message will be posted as an internal note visible to internal\n"
"                                users in the messaging history.\n"
"                            </span>\n"
"                            <span invisible=\"mail_post_method != 'comment'\">\n"
"                                The message will be posted as a message on the record,\n"
"                                notifying all followers. It will appear in the messaging history.\n"
"                            </span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid ""
"<span name=\"document_followers_text\" invisible=\"not model or "
"composition_mode == 'mass_mail'\">Followers of the document and</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>If this was done by you:</span><br/>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>If this was not done by you:</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "<span>Open Record</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "<span>We suggest you start by</span>"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
msgid ""
"<strong>Internal communication</strong>: Replying will post an internal "
"note. Followers won't receive any email notification."
msgstr ""
"<strong>Interná komunikácia</strong>: odpoveď vytvorí internú poznámku. "
"Odberatelia nedostanú žiadne emailové upozornenie."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
msgid "<strong>Original note:</strong>"
msgstr "<strong>Pôvodná poznámka:</strong>"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"<strong>Save</strong> this page and come back here to set up the feature."
msgstr ""
"<strong>Uložte</strong> stránku a vráťte sa na nastavenie tejto funkcie."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_document_unfollowed
msgid "<strong>You are no longer following the document:</strong>"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Python slovník, bude použitý na poskytnutie predvolených hodnôt pri "
"vytváraní nových záznamov pre tento alias."

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_bus_presence_partner_or_guest_exists
msgid "A bus presence must have a user or a guest."
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_member_partner_or_guest_exists
msgid "A channel member must be a partner or a guest."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "A channel of type 'chat' cannot have more than two users."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"A chat should not be created with more than 2 persons. Create a group "
"instead."
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_message_reaction_partner_or_guest_exists
msgid "A message reaction must be from a partner or from a guest."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "A next activity can only be planned on models that use activities."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__google_translate_api_key
msgid ""
"A valid Google API key is required to enable message translation. "
"https://cloud.google.com/translate/docs/setup"
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_settings_volumes_partner_or_guest_exists
msgid "A volume setting must have a partner or a guest."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#, python-format
msgid "Accept"
msgstr "Akceptovať"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Access Denied"
msgstr "Prístup zamietnutý"

#. module: mail
#: model:ir.model,name:mail.model_res_groups
msgid "Access Groups"
msgstr "Prístupové skupiny"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__access_token
msgid "Access Token"
msgstr "Prístupový token"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_model.js:0
#, python-format
msgid "Access restricted to group \"%(groupFullName)s\""
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Account"
msgstr "Účet"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_category
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__category
msgid "Action"
msgstr "Akcia"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction
msgid "Action Needed"
msgstr "Potrebná akcia"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_act_window_view
msgid "Action Window View"
msgstr "Okno pohľadu aktivít"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#, python-format
msgid "Actions"
msgstr "Akcie"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_schedule__activity_category
#: model:ir.model.fields,help:mail.field_mail_activity_type__category
msgid ""
"Actions may trigger specific behavior like opening calendar view or "
"automatically mark as done when a document is uploaded"
msgstr ""
"Akcie môžu spustiť určité správanie, napríklad otvoriť zobrazenie kalendára "
"alebo automaticky označiť ako vykonané pri odovzdávaní dokumentu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Actions to Perform on Incoming Mails"
msgstr "Akcie na vykonanie na prichádzajúce emaily"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__default
msgid "Activated by default when subscribing."
msgstr "Aktivované automaticky pri predplatnom."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__active
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__active
#: model:ir.model.fields,field_description:mail.field_mail_activity__active
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__active
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__active
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__active
#: model:ir.model.fields,field_description:mail.field_mail_template__active
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Active"
msgstr "Aktívne"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_domain
msgid "Active domain"
msgstr "Aktívna doména"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#: model:ir.actions.act_window,name:mail.mail_activity_action
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__template_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_ids
#: model:ir.model.fields,field_description:mail.field_res_users__activity_ids
#: model:ir.ui.menu,name:mail.menu_mail_activities
#: model:mail.message.subtype,name:mail.mt_activities
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
#, python-format
msgid "Activities"
msgstr "Aktivity"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Activities To Generate"
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_activity_check_res_id_is_set
msgid "Activities have to be linked to records with a not null res_id."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model,name:mail.model_mail_activity
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_act_window_view__view_mode__activity
#: model:ir.model.fields.selection,name:mail.selection__ir_ui_view__type__activity
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_template_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_calendar
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#, python-format
msgid "Activity"
msgstr "Aktivita"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Označenie výnimky v aktivite"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_mixin
msgid "Activity Mixin"
msgstr "Aktivita Mixin"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_plan
msgid "Activity Plan"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_plan_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_plan
msgid "Activity Plans"
msgstr "Plány aktivít"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Activity Settings"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_state
#: model:ir.model.fields,field_description:mail.field_res_users__activity_state
msgid "Activity State"
msgstr "Stav aktivity"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_type
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_type_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_type_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Activity Type"
msgstr "Typ aktivity"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_icon
msgid "Activity Type Icon"
msgstr "Ikona typu aktivity"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_activity_type_action
#: model:ir.ui.menu,name:mail.menu_mail_activity_type
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Activity Types"
msgstr "Typy aktivít"

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_plan_template
msgid "Activity plan template"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_activity_schedule
msgid "Activity schedule plan Wizard"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Activity type"
msgstr "Typ aktivity"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "Activity: %s"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Add Context Action"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Add Email Blacklist"
msgstr "Pridaj čiernu listinu mailov"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__followers
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#, python-format
msgid "Add Followers"
msgstr "Pridať odberateľov"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Add Users"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_reaction_button.xml:0
#: code:addons/mail/static/src/core/common/message_reaction_button.xml:0
#, python-format
msgid "Add a Reaction"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add a Tenor GIF API key to enable GIFs support."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_api_key
msgid ""
"Add a Tenor GIF API key to enable GIFs support. "
"https://developers.google.com/tenor/guides/quickstart#setup"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.xml:0
#, python-format
msgid "Add a description"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Add a description to your activity..."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Add a new %(document)s or send an email to %(email_link)s"
msgstr "Pridaj nový %(document)s alebo odošli email: %(email_link)s"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_plan_action
msgid "Add a new plan"
msgstr "Pridajte nový plán"

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid "Add an email address to the blacklist"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient.js:0
#, python-format
msgid "Add as recipient and follower (reason: %s)"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
msgid "Add contacts to notify..."
msgstr "Pridať kontakty pre notifikáciu..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss_app_model.js:0
#, python-format
msgid "Add or join a channel"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_add_signature
msgid "Add signature"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Add your twilio credentials for ICE servers"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"Adding followers on channels is not possible. Consider adding members "
"instead."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid ""
"Adding more members to this chat isn't possible; it's designed for just two "
"people."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__partner_ids
msgid "Additional Contacts"
msgstr "Ďalšie kontakty"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Advanced"
msgstr "Pokročilé"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Advanced Options"
msgstr "Dalšie možnosti"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__warning
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_exception_decoration__warning
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_exception_decoration__warning
msgid "Alert"
msgstr "Upozornenie"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Alias"
msgstr "Alias"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Alias %(matching_name)s (%(current_id)s) is already linked with "
"%(alias_model_name)s (%(matching_id)s) and used by the %(parent_name)s "
"%(parent_model_name)s."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Alias %(matching_name)s (%(current_id)s) is already linked with "
"%(alias_model_name)s (%(matching_id)s)."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_contact
msgid "Alias Contact Security"
msgstr "Alias ​​kontakt bezpečnosť"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_mail_message__record_alias_domain_id
#: model:ir.model.fields,field_description:mail.field_res_config_settings__alias_domain_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Alias Domain"
msgstr "Alias doména"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_domain
#: model:ir.model.fields,field_description:mail.field_res_company__alias_domain_name
msgid "Alias Domain Name"
msgstr "Názov alias domény"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_alias_domain_action
#: model:ir.ui.menu,name:mail.mail_alias_domain_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_tree
msgid "Alias Domains"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_full_name
msgid "Alias Email"
msgstr "Alias e-mailu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_name
msgid "Alias Name"
msgstr "Alias názov"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_status
msgid "Alias Status"
msgstr "Stav aliasu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_domain
msgid "Alias domain name"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_status
msgid "Alias status assessed on the last message received."
msgstr "Stav aliasu posúdený pri poslednej doručenej správe."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_model_id
msgid "Aliased Model"
msgstr "Aliasovaný model"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_alias_action
#: model:ir.ui.menu,name:mail.mail_alias_menu
msgid "Aliases"
msgstr "Alias"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Aliases %(alias_names)s is already used as bounce or catchall address. "
"Please choose another alias."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "All"
msgstr "Všetko"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "All Messages"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__custom_notifications
msgid "All Messages if not specified"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_resend_message.py:0
#, python-format
msgid "All partners must belong to the same message"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__allow_public_upload
msgid "Allow Public Upload"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid ""
"An SSL exception occurred. Check SSL/TLS configuration on server port.\n"
" %s"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid "An access token must be provided for each attachment."
msgstr "Pre každú prílohu musí byť poskytnutý prístupový token."

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid "An email is required for find_or_create to work"
msgstr "Na to, aby find_or_create fungoval, je potrebný e-mail"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/failure_model.js:0
#, python-format
msgid "An error occurred when sending an email"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "An error occurred while fetching messages."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_service.js:0
#, python-format
msgid "An unexpected error occurred during the creation of the chat."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "And"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "And 1 other member."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_model_id
#: model:ir.model.fields,field_description:mail.field_mail_template__model_id
msgid "Applies to"
msgstr "Aplikované na"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.xml:0
#, python-format
msgid "Apply"
msgstr "Použiť"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Archived"
msgstr "Archivovaný"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid ""
"Archived because %(user_name)s (#%(user_id)s) deleted the portal account"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
msgid "Are you sure you want to delete this Mail Template?"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/common/message.js:0
#, python-format
msgid "Are you sure you want to delete this message?"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
msgid ""
"Are you sure you want to reset these email templates to their original "
"configuration? Changes and translations will be lost."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_blacklist.py:0
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid "Are you sure you want to unblacklist this Email Address?"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__responsible_type__on_demand
msgid "Ask at launch"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "Assign to ..."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "Assign to me"
msgstr "Priradiť mne"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_on_demand_user_id
msgid "Assigned To"
msgstr "Priradený"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__user_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__responsible_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__activity_user_id
#, python-format
msgid "Assigned to"
msgstr "Priradený"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid ""
"Assigned user %s has no access to the document and is not able to handle "
"this activity."
msgstr ""
"Priradený užívateľ  %s nemá prístup k dokumentu a nemôže spracovať túto "
"aktivitu."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__responsible_type
msgid "Assignment"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "At this point lang should be correctly set"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Attach a file"
msgstr "Priložiť súbor"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#: code:addons/mail/static/src/core/common/composer.xml:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Attach files"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_attachment
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__attachment_id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__attachment_ids
msgid "Attachment"
msgstr "Príloha"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_partner__message_attachment_count
#: model:ir.model.fields,field_description:mail.field_res_users__message_attachment_count
msgid "Attachment Count"
msgstr "Počet príloh"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Attachment counter loading..."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_activity__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__attachment_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__attachment_ids
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Attachments"
msgstr "Prílohy"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Audio player:"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__partners
msgid "Authenticated Partners"
msgstr "Overení partneri"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__author_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Author"
msgstr "Autor"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__author_id
#: model:ir.model.fields,help:mail.field_mail_mail__author_id
#: model:ir.model.fields,help:mail.field_mail_message__author_id
msgid ""
"Author of the message. If not set, email_from may hold an email address that"
" did not match any partner."
msgstr ""
"Autor správy. Pokiaľ nie je uvedený, email_from môže obsahovať emailovú "
"adresu ktorá nemusí zodpovedať žiadnemu partnerovi."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_avatar
#: model:ir.model.fields,field_description:mail.field_mail_message__author_avatar
msgid "Author's avatar"
msgstr "Avatar autora"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__group_public_id
msgid "Authorized Group"
msgstr "Autorizovaná skupina"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,field_description:mail.field_mail_template__auto_delete
msgid "Auto Delete"
msgstr "Automatické vymazanie"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Auto Subscribe Groups"
msgstr "Auto odberateľské skupiny"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__group_ids
msgid "Auto Subscription"
msgstr "Automatické predplatné"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Auto subscription"
msgstr "Automatické predplatné"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__auto_comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__auto_comment
msgid "Automated Targeted Notification"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__automated
msgid "Automated activity"
msgstr "Automatizované aktivity"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#, python-format
msgid "Automated message"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__triggered_next_type_id
msgid ""
"Automatically schedule this activity once the current one is marked as done."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Available for all Companies"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.xml:0
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__avatar_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1920
#, python-format
msgid "Avatar"
msgstr "Avatar"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_1024
msgid "Avatar 1024"
msgstr "Avatar 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_128
msgid "Avatar 128"
msgstr "Avatar 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_256
msgid "Avatar 256"
msgstr "Avatar 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__avatar_512
msgid "Avatar 512"
msgstr "Avatar 512"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#, python-format
msgid "Avatar of user"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#, python-format
msgid "Away"
msgstr "Neprítomný"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Background blur intensity"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_base
msgid "Base"
msgstr "Základ"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__base_template
msgid "Base Template"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Base Templates"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__sfu_server_key
msgid "Base64 encoded key"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_batch
msgid "Batch composition"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Batch log cannot support attachments or tracking values on more than 1 "
"document"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,field_description:mail.field_res_users__is_blacklisted
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Blacklist"
msgstr "Čierna listina"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Blacklist Date"
msgstr "Dátum čiernej listiny"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_bl
msgid "Blacklisted Address"
msgstr "Blokovaná adresa"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_blacklist_action
msgid "Blacklisted Email Addresses"
msgstr "Blokované emailové adresy"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid ""
"Blocked by deletion of portal account %(portal_user_name)s by %(user_name)s "
"(#%(user_id)s)"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Blur video background"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__body_html
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__body_html
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Body"
msgstr "Telo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body_has_template_value
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body_has_template_value
msgid "Body content is the same as the template"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "Bot"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_company__bounce_formatted
#: model:ir.model.fields,field_description:mail.field_res_partner__message_bounce
#: model:ir.model.fields,field_description:mail.field_res_users__message_bounce
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_bounce
msgid "Bounce"
msgstr "Odskočiť"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__bounce_alias
msgid "Bounce Alias"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__bounce_email
#: model:ir.model.fields,field_description:mail.field_res_company__bounce_email
msgid "Bounce Email"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
#, python-format
msgid ""
"Bounce alias %(bounce)s is already used for another domain with same name. "
"Use another bounce or simply use the other alias domain."
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_domain_bounce_email_uniques
msgid "Bounce emails should be unique"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
#, python-format
msgid ""
"Bounce/Catchall '%(matching_alias_name)s' is already used by "
"%(document_name)s. Choose another alias or change it on the other document."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
#, python-format
msgid ""
"Bounce/Catchall '%(matching_alias_name)s' is already used. Choose another "
"alias or change it on the linked model."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__bounce
#, python-format
msgid "Bounced"
msgstr "Odmietnuté"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/webclient/web/webclient.js:0
#, python-format
msgid ""
"Brave: enable 'Google Services for Push Messaging' to enable push "
"notifications"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Browser default"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__endpoint
msgid "Browser endpoint"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__keys
msgid "Browser keys"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_cc.py:0
#, python-format
msgid "CC Email"
msgstr "CC Email"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "CTRL-Enter"
msgstr ""

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_call
msgid "Call"
msgstr "Telefonát"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "Camera is off"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__can_cancel
msgid "Can Cancel"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__can_edit_body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__can_edit_body
msgid "Can Edit Body"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__can_resend
msgid "Can Resend"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__can_write
#: model:ir.model.fields,field_description:mail.field_mail_template__can_write
msgid "Can Write"
msgstr "Môže písať"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "Can not update the message or recipient of a notification."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_confirm_dialog.xml:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#: model_terms:ir.ui.view,arch_db:mail.mail_wizard_invite_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Cancel"
msgstr "Zrušené"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Cancel Email"
msgstr "Zrušiť email"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__canceled
#, python-format
msgid "Canceled"
msgstr "Zrušené"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__cancel
msgid "Cancelled"
msgstr "Zrušené"

#. module: mail
#: model:ir.model,name:mail.model_mail_shortcode
msgid "Canned Response / Shortcode"
msgstr "Zabalená odpoveď / Krátky kód"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_shortcode_action
msgid "Canned Responses"
msgstr "Konzervované odpovede"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_search
msgid "Canned Responses Search"
msgstr ""

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid ""
"Canned responses allow you to insert prewritten responses in\n"
"                your messages by typing <i>:shortcut</i>. The shortcut is\n"
"                replaced directly in your message, so that you can still edit\n"
"                it before sending."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "Cannot change the channel type of: %(channel_names)s"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_cc
msgid "Carbon copy message recipients"
msgstr "Príjemcovia Cc správy"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_cc
msgid "Carbon copy recipients"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_cc
msgid "Carbon copy recipients (placeholders may be used here)"
msgstr "Príjemcovia Cc (môže tu byť použitý dočasný prvok)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_formatted
msgid "Catchall"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__catchall_alias
msgid "Catchall Alias"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__catchall_email
#: model:ir.model.fields,field_description:mail.field_res_company__catchall_email
msgid "Catchall Email"
msgstr "Catchall Email"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
#, python-format
msgid ""
"Catchall alias %(catchall)s is already used for another domain with same "
"name. Use another catchall or simply use the other alias domain."
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_alias_domain_catchall_email_uniques
msgid "Catchall emails should be unique"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template__email_cc
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_cc
msgid "Cc"
msgstr "Cc"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__chaining_type
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__chaining_type
msgid "Chaining Type"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,help:mail.field_mail_activity_type__decoration_type
msgid "Change the background color of the related activities of this type."
msgstr "Zmeň farbu pozadia príslušných aktivít tohto typu."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__channel_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__channel_id
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__channel
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_search
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
#, python-format
msgid "Channel"
msgstr "Kanál"

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel_member
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__channel_member_id
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_member_view_form
msgid "Channel Member"
msgstr "Člen kanála"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_type
msgid "Channel Type"
msgstr "Typ kanálu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Channel full"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid "Channel members cannot include public users."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "Channel settings"
msgstr "Nastavenia kanála"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss_app_model.js:0
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_guest__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__channel_ids
#: model:ir.model.fields,field_description:mail.field_res_users__channel_ids
#: model:ir.ui.menu,name:mail.discuss_channel_menu_settings
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_member_view_tree
#, python-format
msgid "Channels"
msgstr "Kanály"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_member_action
#: model:ir.ui.menu,name:mail.discuss_channel_member_menu
msgid "Channels/Members"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__chat
#, python-format
msgid "Chat"
msgstr "Chat"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__channel_type
msgid ""
"Chat is private and unique between 2 persons. Group is private among invited"
" persons. Channel can be freely joined (depending on its configuration)."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "Chats"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__use_exclusion_list
msgid "Check Exclusion List"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__child_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__child_ids
msgid "Child Messages"
msgstr "Podriadené správy"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Choose a template..."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Choose a user..."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "Choose another value or change it on the other document."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_schedule__plan_on_demand_user_id
msgid "Choose assignation for activities with on demand assignation."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "Click here to retry"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
#, python-format
msgid "Click to see the attachments"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
#, python-format
msgid "Close"
msgstr "Zatvoriť"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#, python-format
msgid "Close Chat Window"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#, python-format
msgid "Close Search"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "Close button"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/action_panel.xml:0
#, python-format
msgid "Close panel"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "Close search"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__closed
msgid "Closed"
msgstr "Zavreté"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__new
msgid "Collect replies on a specific email address"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "Come here often? Install Odoo on your device!"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated carbon copy recipients addresses"
msgstr "Čiarkou oddelené Cc adresy prijímateľov"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated ids of recipient partners"
msgstr "Čiarkou oddelené ID partnerov prijímateľa"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__partner_to
msgid ""
"Comma-separated ids of recipient partners (placeholders may be used here)"
msgstr ""
"Čiarkou oddelené ID partnerov prijímateľa (môže tu byť použitý dočasný "
"prvok)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_to
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Comma-separated recipient addresses"
msgstr "Čiarkou oddelené adresy prijímateľa"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_to
msgid "Comma-separated recipient addresses (placeholders may be used here)"
msgstr ""
"Čiarkou oddelené adresy prijímateľa (môže tu byť použitý dočasný prvok)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__comment
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__comment
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Comment"
msgstr "Komentár"

#. module: mail
#: model:ir.model,name:mail.model_res_company
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__company_ids
msgid "Companies"
msgstr "Spoločnosti"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__company_ids
msgid "Companies using this domain as default for sending mails"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__company_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__company_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__company_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_company_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_company_id
#: model:ir.model.fields,field_description:mail.field_mail_message__record_company_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
msgid "Company"
msgstr "Spoločnosť"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/web/activity_mail_template.js:0
#: code:addons/mail/wizard/mail_compose_message.py:0
#: model:ir.actions.act_window,name:mail.action_email_compose_message_wizard
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Compose Email"
msgstr "Vytvoriť email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__composition_mode
msgid "Composition mode"
msgstr "Kompozičný režim"

#. module: mail
#: model:ir.model,name:mail.model_res_config_settings
msgid "Config Settings"
msgstr "Nastavenia konfigurácie"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__configuration
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Configuration"
msgstr "Konfigurácia"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your ICE server list for webRTC"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your activity types"
msgstr "Nastavenie typov aktivít"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Configure your own email servers"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_confirm_dialog.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
#, python-format
msgid "Confirm"
msgstr "Potvrdiť"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#: code:addons/mail/static/src/core/common/message_confirm_dialog.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#, python-format
msgid "Confirmation"
msgstr "Potvrdenie"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__state__done
msgid "Confirmed"
msgstr "Potvrdené"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#, python-format
msgid "Congratulations, you're done with your activities."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "Congratulations, your inbox is empty"
msgstr "Gratulujeme, váš inbox je prázdny"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.js:0
#, python-format
msgid "Congratulations, your inbox is empty!"
msgstr "Gratulujeme, váš inbox je prázdny!"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_smtp
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_smtp
msgid "Connection failed (outgoing mail server problem)"
msgstr "Spojenie nedostupné (problém servera odchádzajúcich emailov)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid "Connection test failed: %s"
msgstr "Test pripojenia zlyhal: %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Connection to SFU server closed by the server"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Connection type:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Connection:"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__is_ssl
msgid ""
"Connections are encrypted with SSL/TLS through a dedicated port (default: "
"IMAPS=993, POP3S=995)"
msgstr ""
"Spojenia sú šifrované pomocou SSL/TLS cez dedikovaný port (predvoľba: IMAPs "
"= 993, POP3S = 995)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_force_new
msgid "Considers answers as new thread"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_res_partner
msgid "Contact"
msgstr "Kontakt"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "Contact your administrator"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_activity
msgid "Contacts"
msgstr "Kontakty"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Container Model"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__last_interest_dt
msgid ""
"Contains the date and time of the last interesting event that happened in "
"this channel for this partner. This includes: creating, joining, pinning, "
"and new message posted."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__content
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Content"
msgstr "Obsah"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__substitution
msgid ""
"Content that will automatically replace the shortcut of your choosing. This "
"content can still be adapted before sending your message."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__body
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__body
#: model:ir.model.fields,field_description:mail.field_mail_mail__body
#: model:ir.model.fields,field_description:mail.field_mail_message__body
msgid "Contents"
msgstr "Obsahy"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__fold_state
msgid "Conversation Fold State"
msgstr "Stav zloženia konverácie"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_minimized
msgid "Conversation is minimized"
msgstr "Konverzácia je zminimalizovaná"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_bounce
#: model:ir.model.fields,help:mail.field_res_partner__message_bounce
#: model:ir.model.fields,help:mail.field_res_users__message_bounce
msgid "Counter of the number of bounced emails for this contact"
msgstr "Počítadlo počtu odmietnutých emailov pre tento kontakt"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__country_id
msgid "Country"
msgstr "Štát"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
#, python-format
msgid "Create"
msgstr "Vytvoriť"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__next_activity
msgid "Create Activity"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__create_date
msgid "Create Date"
msgstr "Dátum vytvorenia"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Create Group Chat"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_uid
msgid "Create Uid"
msgstr "Vytvoriť Uid"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__object_id
msgid "Create a New Record"
msgstr "Vytvoriť nový záznam"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Create a new Mail Template"
msgstr ""

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_shortcode_action
msgid "Create a new canned response"
msgstr "Vytvorte novú konzervovanú odpoveď"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create new %(document)s"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Create new %(document)s by sending an email to %(email_link)s"
msgstr "Vytvor nový %(document)s zaslaním e-mailu na adresu %(email_link)s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.xml:0
#, python-format
msgid "Create: #"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Created"
msgstr "Vytvorené"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Created By"
msgstr "Vytvoril"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__create_uid
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__create_uid
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_uid
msgid "Created by"
msgstr "Vytvoril"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__create_date
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__create_date
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__create_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__create_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__create_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__create_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__create_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__create_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__create_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__create_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__create_date
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__create_date
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__create_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__create_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__create_date
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__create_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__create_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__create_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__create_date
msgid "Created on"
msgstr "Vytvorené"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_service_patch.js:0
#, python-format
msgid "Creating a new record..."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Creation Date"
msgstr "Dátum vytvorenia"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Creator"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__credential
msgid "Credential"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_notification_web_push
msgid "Cron data used for web push notification"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__currency_id
msgid "Currency"
msgstr "Mena"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__starred
#: model:ir.model.fields,help:mail.field_mail_message__starred
msgid "Current user has a starred notification linked to this message"
msgstr ""
"Aktuálny užívateľ má aktivovanú notifikáciu nalinkovanú na túto správu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Vlastná vrátená správa"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Custom ICE server list"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__custom_template
msgid "Custom Template"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Custom Templates"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__custom_channel_name
msgid "Custom channel name"
msgstr "Názov vlastného kanála"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_notification_notification_partner_required
msgid "Customer is required for inbox / email notification"
msgstr "Od zákazníka je vyžadovaná notifikácia doručenej pošty alebo e-mailom"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Customize the look and feel of automated emails"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__custom_notifications
msgid "Customized Notifications"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "DTLS:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Data channel:"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__date
#: model:ir.model.fields,field_description:mail.field_mail_message__date
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Date"
msgstr "Dátum"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_schedule__scheduled_datetime
msgid "Datetime at which notification should be sent."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__pinned_at
#: model:ir.model.fields,help:mail.field_mail_message__pinned_at
msgid "Datetime at which the message has been pinned"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__days
msgid "Days"
msgstr "Dni"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
msgid "Deadline"
msgstr "Uzávierka"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Deadline:"
msgstr "Uzávierka:"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "Deadline: %s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Deafen"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "Dear"
msgstr "Drahý"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "Dear Sender"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "Dear Sender,"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__activity_decoration
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__decoration_type
msgid "Decoration Type"
msgstr "Typ dekorácie"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__default
msgid "Default"
msgstr "Predvolený"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__default_display_mode
msgid "Default Display Mode"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__default_from_email
#: model:ir.model.fields,field_description:mail.field_res_company__default_from_email
msgid "Default From"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__default_from
msgid "Default From Alias"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_note
msgid "Default Note"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__summary
msgid "Default Summary"
msgstr "Predvolený súhrn"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__default_user_id
msgid "Default User"
msgstr "Predvolený používateľ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_defaults
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_defaults
msgid "Default Values"
msgstr "Predvolené hodnoty"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__default_from
msgid ""
"Default from when it does not match outgoing server filters. Can be either a"
" local-part e.g. 'notifications' either a complete email address e.g. "
"'<EMAIL>' to override all outgoing emails."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__use_default_to
msgid "Default recipients"
msgstr "Predvolený príjemcovia"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__use_default_to
msgid ""
"Default recipients of the record:\n"
"- partner (using id on a partner or the partner_id field) OR\n"
"- email (using email_from or email field)"
msgstr ""
"Predvolený adresáti pre záznam:\n"
"- partner (using id on a partner or the partner_id field) OR\n"
"- email (using email_from or email field)"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_plan_template__responsible_type__other
msgid "Default user"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__priority
msgid "Defines the order of processing, lower values mean higher priority"
msgstr "Definuje poradie spracovania, nižšie hodnoty znamenjú vyššiu prioritu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_label
msgid "Delay Label"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_from
msgid "Delay Type"
msgstr "Typ oneskorenia"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Delay after releasing push-to-talk"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_unit
msgid "Delay units"
msgstr "Jednotky oneskorenia"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.xml:0
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_view_form_confirm_delete
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
#, python-format
msgid "Delete"
msgstr "Zmazať"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete
msgid "Delete Emails"
msgstr "Vymazať správy"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#, python-format
msgid "Delete all previews"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__sent
#, python-format
msgid "Delivered"
msgstr "Doručené"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__exception
msgid "Delivery Failed"
msgstr "Neúspešné dodanie"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#, python-format
msgid "Delivery failure"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Deprecated usage of 'default_res_id', should use 'default_res_ids'."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__description
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_description
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__description
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__description
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Description"
msgstr "Popis"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__description
msgid ""
"Description that will be added in the message posted for this subtype. If "
"void, the name will be added instead."
msgstr ""
"Popis, ktorý bude pridaný do správy zverejnenej k tomuto podtypu. Ak "
"neplatné, bude namiesto toho pridaný názov."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__default_display_mode
msgid ""
"Determines how the channel will be displayed by default when opening it from"
" its invitation link. No value means display text (no voice/video)."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss_app_model.js:0
#, python-format
msgid "Direct messages"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
#, python-format
msgid "Discard"
msgstr "Zrušiť"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_tree
#, python-format
msgid "Disconnect"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Disconnected from the RTC call by the server"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/thread_actions.js:0
#: model:ir.actions.client,name:mail.action_discuss
#: model:ir.ui.menu,name:mail.mail_menu_technical
#: model:ir.ui.menu,name:mail.menu_root_discuss
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
#, python-format
msgid "Discuss"
msgstr "Diskusie"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Discuss sidebar"
msgstr ""

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_discuss_channel_member_unmute_ir_actions_server
msgid "Discuss: channel member unmute"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel
msgid "Discussion Channel"
msgstr "Diskusný kanál"

#. module: mail
#: model:mail.message.subtype,name:mail.mt_comment
msgid "Discussions"
msgstr "Diskusie"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "Dismiss"
msgstr "Zahodiť"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__display_name
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__display_name
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__display_name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias__display_name
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__display_name
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__display_name
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_followers__display_name
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__display_name
#: model:ir.model.fields,field_description:mail.field_mail_guest__display_name
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__display_name
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_mail__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__display_name
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification__display_name
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__display_name
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__display_name
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__display_name
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__display_name
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__display_name
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__display_name
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__display_name
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__display_name
msgid "Display Name"
msgstr "Zobrazovaný názov"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Display an option on related documents to open a composition wizard with "
"this template"
msgstr ""
"Zobraziť možnosť na súvisiacom dokumente otvoriť kompozitného sprievodcu s "
"touto šablónou"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/many2one_avatar_user_field/many2one_avatar_user_field.js:0
#, python-format
msgid "Display avatar name"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#, python-format
msgid "Do you really want to delete \"%s\"?"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/link_preview_confirm_delete.xml:0
#, python-format
msgid "Do you really want to delete this preview?"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Document"
msgstr "Dokument"

#. module: mail
#: model:ir.model,name:mail.model_mail_followers
msgid "Document Followers"
msgstr "Dokument odberateľov"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_ids
msgid "Document IDs"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model_id
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Document Model"
msgstr "Model dokumentu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_name
msgid "Document Name"
msgstr "Názov dokumentu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Document: \""
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "Domain"
msgstr "Doména"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__done
#, python-format
msgid "Done"
msgstr "Hotové"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Done & Launch Next"
msgstr "Hotovo & spusťte nové"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#, python-format
msgid "Done & Schedule Next"
msgstr "Hotovo & plánujte nové"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_done
msgid "Done Date"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#, python-format
msgid "Done and Schedule Next"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
#, python-format
msgid "Download"
msgstr "Stiahnuť"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Download Files"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Download logs"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Download:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/dropzone.xml:0
#, python-format
msgid "Drag Files Here"
msgstr "Sem presuňte súbory"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__date_deadline
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__date_deadline
msgid "Due Date"
msgstr "Dátum splatnosti"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range
msgid "Due Date In"
msgstr "Dátum splatnosti za"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Due in"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#, python-format
msgid "Due in %s days"
msgstr "Splatné v %s dňoch"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Due on"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_date_deadline_range_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_date_deadline_range_type
msgid "Due type"
msgstr "Typ splatnosti"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_dup
msgid "Duplicated Email"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__voice_active_duration
msgid "Duration of voice activity in ms"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__report_template_ids
msgid "Dynamic Reports"
msgstr "Dynamické reporty"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__generic
msgid "Dynamic User (based on record)"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Edge blur intensity"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Edit"
msgstr "Upraviť"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Edit Partners"
msgstr "Editovať partnerov"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.js:0
#, python-format
msgid "Edit Subscription of %(name)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#, python-format
msgid "Edit subscription"
msgstr "Oditovať predplatné"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/many2many_tags_email/many2many_tags_email.js:0
#, python-format
msgid "Edit: %s"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__email
#: model:ir.model.fields,field_description:mail.field_mail_followers__email
#: model:ir.model.fields,field_description:mail.field_res_partner__email
#: model:ir.model.fields,field_description:mail.field_res_users__email
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__email
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__email
#: model:mail.activity.type,name:mail.mail_activity_data_email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email"
msgstr "Email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_add_signature
#: model:ir.model.fields,field_description:mail.field_mail_message__email_add_signature
msgid "Email Add Signature"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__email
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__email
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__email
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Email Address"
msgstr "Emailová adresa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin__alias_email
#: model:ir.model.fields,field_description:mail.field_mail_alias_mixin_optional__alias_email
msgid "Email Alias"
msgstr "Emailový alias"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias
msgid "Email Aliases"
msgstr "Emailové aliasy"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin
msgid "Email Aliases Mixin"
msgstr "Aliasy emailu mixin"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_mixin_optional
msgid "Email Aliases Mixin (light)"
msgstr ""

#. module: mail
#: model:ir.ui.menu,name:mail.mail_blacklist_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_tree
msgid "Email Blacklist"
msgstr "Čierna listina mailov"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_secondary_color
#: model:ir.model.fields,field_description:mail.field_res_config_settings__email_secondary_color
msgid "Email Button Color"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_cc
msgid "Email CC management"
msgstr "Správa CC emailom"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Email Configuration"
msgstr "Nastavenia Email-u"

#. module: mail
#: model:ir.model,name:mail.model_mail_alias_domain
#: model:ir.model.fields,field_description:mail.field_res_company__alias_domain_id
msgid "Email Domain"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_primary_color
#: model:ir.model.fields,field_description:mail.field_res_config_settings__email_primary_color
msgid "Email Header Color"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__mass_mail
msgid "Email Mass Mailing"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_template__email_layout_xmlid
msgid "Email Notification Layout"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Email Preview"
msgstr "Náhľad emailu"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Email Search"
msgstr "Vyhľadať email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__template_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__template_id
msgid "Email Template"
msgstr "Šablóna emailu"

#. module: mail
#: model:ir.model,name:mail.model_mail_template_preview
msgid "Email Template Preview"
msgstr "Náhľad šablóny emailu"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_template_tree_all
#: model:ir.model,name:mail.model_mail_template
#: model:ir.ui.menu,name:mail.menu_email_templates
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Email Templates"
msgstr "Emailové šablóny"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread
msgid "Email Thread"
msgstr "Emailové vlákno"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_blacklist_unique_email
msgid "Email address already exists!"
msgstr "Emailová adresa už existuje!"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,help:mail.field_mail_mail__email_from
#: model:ir.model.fields,help:mail.field_mail_message__email_from
msgid ""
"Email address of the sender. This field is set when no matching partner is "
"found and replaces the author_id field in the chatter."
msgstr ""
"Emailová adresa odosielateľa. Toto pole je nastavené keď sa nenájde žiaden "
"vyhovujúci partner a zamení pole author_id v chate."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__reply_to
msgid ""
"Email address to which replies will be redirected when sending emails in "
"mass; only used when the reply is not logged in the original discussion "
"thread."
msgstr ""

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_blacklist_action
msgid ""
"Email addresses that are blacklisted won't receive Email mailings anymore."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Email aliases %(alias_name)s cannot be used on several records at the same "
"time. Please update records one by one."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__email_cc
msgid "Email cc"
msgstr "E-mailová kópia"

#. module: mail
#: model:ir.model,name:mail.model_mail_compose_message
msgid "Email composition wizard"
msgstr "Sprievodca zostavovaním emailov"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_domain
#: model:ir.model.fields,help:mail.field_mail_alias_domain__name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_domain
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_domain
#: model:ir.model.fields,help:mail.field_res_company__alias_domain_name
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr "E-mail doména, napr. 'example.com' in '<EMAIL>'"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_message_subtype_form
msgid "Email message"
msgstr "Emailová správa"

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_message
msgid "Email resend wizard"
msgstr "Sprievodca opätovným zaslaním"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__mail_template_ids
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__mail_template_ids
msgid "Email templates"
msgstr "E-mailové šablóny"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_mail
#: model:ir.ui.menu,name:mail.menu_mail_mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_tree
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Emails"
msgstr "Email"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
#, python-format
msgid "Emoji"
msgstr "Emoji"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#: code:addons/mail/static/src/views/web/fields/emojis_field_common/emojis_field_common.xml:0
#, python-format
msgid "Emojis"
msgstr "Emoji"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_internal
#: model:ir.model.fields,field_description:mail.field_mail_message__is_internal
msgid "Employee Only"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model_fields__tracking
msgid "Enable Ordered Tracking"
msgstr "Povoliť objednané sledovanie"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Enable Push-to-talk"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "Enable desktop notifications to chat"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Enter"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Enter Full Screen"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__error
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__decoration_type__danger
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_exception_decoration__danger
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_exception_decoration__danger
#, python-format
msgid "Error"
msgstr "Chyba"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__error_msg
msgid "Error Message"
msgstr "Chybová správa"

#. module: mail
#. odoo-python
#: code:addons/mail/models/update.py:0
#, python-format
msgid "Error during communication with the publisher warranty server."
msgstr "Chyba počas komunikácie so serverom vydavateľa záruky"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__message
msgid "Error message"
msgstr "Chybová správa"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due to concurrent access update of "
"notification records. Please see with an administrator."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid ""
"Error without exception. Probably due to sending an email without computed "
"recipients."
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_followers_mail_followers_res_partner_res_model_id_uniq
msgid "Error, a partner cannot follow twice the same object."
msgstr "Chyba, partner nemôže sledovať rovnaký objekt dva krát."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__everyone
msgid "Everyone"
msgstr "Každý"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__exception
#: model:mail.activity.type,name:mail.mail_activity_data_warning
msgid "Exception"
msgstr "Výnimka"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Exit Full Screen"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#, python-format
msgid "Expand"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__expiration_time
msgid "Expiration Token Date"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Extended Filters..."
msgstr "Rozšírené filtre..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__fail_counter
msgid "Fail Mail"
msgstr "Zlyhanie pošty"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Failed"
msgstr "Nepodarilo sa"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/webclient/web/webclient.js:0
#, python-format
msgid "Failed to enable push notifications"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "Failed to load gifs..."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Failed to load the SFU server, falling back to peer-to-peer"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Failed to render QWeb template: %(template_src)s\n"
"\n"
"%(template_traceback)s)"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render inline_template template: %(template_txt)s"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid "Failed to render template: %(view_ref)s"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_reason
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__failure_reason
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Failure Reason"
msgstr "Dôvod zlyhania"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_reason
msgid "Failure reason"
msgstr "Dôvod zlyhania"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__failure_reason
msgid ""
"Failure reason. This is usually the exception thrown by the email server, "
"stored to ease the debugging of mailing issues."
msgstr ""
"Dôvod zlyhania. Toto je zvyčajne výnimka z emailového serveru, uložená pre "
"zjednodušenie debugovania zasielania emailov."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__failure_type
#: model:ir.model.fields,field_description:mail.field_mail_notification__failure_type
msgid "Failure type"
msgstr "Typ zlyhania"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__starred_partner_ids
msgid "Favorited By"
msgstr "Má v obľúbených"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "Favorites"
msgstr "Obľúbené"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Fetch Now"
msgstr "Načítať teraz"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_gif_limit
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Fetch up to the specified number of GIF."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_id
msgid "Field"
msgstr "Pole"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Activity\" cannot be changed to \"False\"."
msgstr "Pole\"Maliová aktivita\" nieje možné zmeniť na\"False\"."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Blacklist\" cannot be changed to \"False\"."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Field \"Mail Thread\" cannot be changed to \"False\"."
msgstr "Pole \"mailové vlákno\" nemožno zmeniť na \"False\" "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_groups
msgid "Field Groups"
msgstr "Skupina poľa"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Field details"
msgstr "Detaily poľa"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__relation_field
msgid ""
"Field used to link the related model to the subtype model when using "
"automatic subscription on a related document. The field is used to compute "
"getattr(related_document.relation_field)."
msgstr ""
"Pole slúžiace k prepojeniu súvisiaceho modelu k modelu podtypu pri použití "
"automatického odberu príbuzneho dokumentu. Pole sa používa na výpočet "
"getattr(related_document.relation_field)."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_tracking_duration_mixin.py:0
#, python-format
msgid ""
"Field “%(field)s” on model “%(model)s” must be of type Many2one and have "
"tracking=True for the computation of duration."
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_model_fields
msgid "Fields"
msgstr "Polia"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__template_fs
#: model:ir.model.fields,help:mail.field_template_reset_mixin__template_fs
msgid ""
"File from where the template originates. Used to reset broken template."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_upload_service.js:0
#, python-format
msgid "File too large"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#, python-format
msgid "File upload is disabled for external users"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#, python-format
msgid "File upload is enabled for external users"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Files"
msgstr "Súbory"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#, python-format
msgid "Fold"
msgstr "Zložiť"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__folded
msgid "Folded"
msgstr "Zložené"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Follow"
msgstr "Odoberať"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_followers
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_follower_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_follower_ids
#: model:ir.ui.menu,name:mail.menu_email_followers
#: model_terms:ir.ui.view,arch_db:mail.view_followers_tree
msgid "Followers"
msgstr "Odberatelia"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_partner_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_partner_ids
msgid "Followers (Partners)"
msgstr "Odberatelia (partneri)"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_subscription_form
msgid "Followers Form"
msgstr "Formulár odberateľa"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_contact__followers
msgid "Followers only"
msgstr "Iba odberatelia"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Followers to add"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "Followers to remove"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.js:0
#, python-format
msgid "Following"
msgstr "Odberajúci"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__icon
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_type_icon
#: model:ir.model.fields,help:mail.field_mail_activity_type__icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_type_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Úžasná ikona fronty napr. fa-tasks"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"For %(channels)s, channel_type should be 'channel' to have the group-based "
"authorization or group auto-subscription."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "For 1 hour"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "For 15 minutes"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "For 24 hours"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "For 3 hours"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "For 8 hours"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_form
msgid "Force Send"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Force a Language:"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_company__email_formatted
msgid "Formatted Email"
msgstr "Formátovaný email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_from
#: model:ir.model.fields,field_description:mail.field_mail_message__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template__email_from
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_from
msgid "From"
msgstr "Od"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "From peer:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#: code:addons/mail/static/src/core/common/composer.xml:0
#, python-format
msgid "Full composer"
msgstr "Plný skladateľ"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__default_display_mode__video_full_screen
msgid "Full screen video"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#, python-format
msgid "Future"
msgstr "Budúcnosť"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Future Activities"
msgstr "Budúce aktivity"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "GIF"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "GIF Category"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "GIF Favorites"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_gif_favorite_action
#: model:ir.ui.menu,name:mail.discuss_gif_favorite_menu
#: model_terms:ir.ui.view,arch_db:mail.discuss_gif_favorite_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_gif_favorite_view_tree
msgid "GIF favorite"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__tenor_gif_id
msgid "GIF id from Tenor"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/composer_patch.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
#, python-format
msgid "GIFs"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Gateway"
msgstr "Brána"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Go to conversation"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Google Translate Integration"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel__channel_type__group
msgid "Group"
msgstr "Skupina"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_search
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Group By"
msgstr "Zoskupiť podľa"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Group Name"
msgstr "Meno skupiny"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_group_public_id_check
msgid ""
"Group authorization and group auto-subscription are only supported on "
"channels."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Group by..."
msgstr "Zoskupiť podľa..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#, python-format
msgid "Grouped Chat"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_tree
msgid "Groups"
msgstr "Skupiny"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/controllers/discuss/public_page.py:0
#: code:addons/mail/static/src/discuss/core/public/welcome_page.js:0
#: model:ir.model,name:mail.model_mail_guest
#: model:ir.model.fields,field_description:mail.field_bus_presence__guest_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__guest_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__guest_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__author_guest_id
#: model:ir.model.fields,field_description:mail.field_mail_message__author_guest_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__guest_id
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_form
#, python-format
msgid "Guest"
msgstr "Hosť"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/mail_guest.py:0
#, python-format
msgid "Guest's name cannot be empty."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/mail_guest.py:0
#, python-format
msgid "Guest's name is too long."
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_guest_action
#: model:ir.ui.menu,name:mail.mail_guest_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_guest_view_tree
msgid "Guests"
msgstr "Hostia"

#. module: mail
#: model:ir.model,name:mail.model_ir_http
msgid "HTTP Routing"
msgstr "HTTP smerovanie"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__email
msgid "Handle by Emails"
msgstr "Spracovať emailom"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_users__notification_type__inbox
msgid "Handle in Odoo"
msgstr "Spracovať v Odoo"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__has_error
msgid "Has Error"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_activity
msgid "Has Mail Activity"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_blacklist
msgid "Has Mail Blacklist"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_model__is_mail_thread
msgid "Has Mail Thread"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Has Mentions"
msgstr "Má zmienku"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__has_message
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__has_message
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__has_message
#: model:ir.model.fields,field_description:mail.field_res_partner__has_message
#: model:ir.model.fields,field_description:mail.field_res_users__has_message
msgid "Has Message"
msgstr "Má správu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_deaf
msgid "Has disabled incoming sound"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__has_error
#: model:ir.model.fields,field_description:mail.field_mail_message__has_error
msgid "Has error"
msgstr "Má chybu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__has_user_on_demand
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_has_user_on_demand
msgid "Has on demand responsible"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__headers
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "Headers"
msgstr "Hlavičky"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Hello"
msgstr "Dobrý deň, "

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Hello,"
msgstr "Ahoj,"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__hidden
msgid "Hidden"
msgstr "Skryté"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_template__template_category__hidden_template
msgid "Hidden Template"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Hide Attachments"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
#, python-format
msgid "Hide Call Settings"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Hide Member List"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/thread_actions.js:0
#, python-format
msgid "Hide Pinned Messages"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.xml:0
#, python-format
msgid "Hide sidebar"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__hidden
msgid "Hide the subtype in the follower options"
msgstr "Skryť podtyp v možnostiach odberateľa"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_internal
#: model:ir.model.fields,help:mail.field_mail_message__is_internal
msgid ""
"Hide to public / portal users, independently from subtype configuration."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__high
msgid "High"
msgstr "Vysoká"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/messaging_service.js:0
#: code:addons/mail/static/src/core/common/thread.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#, python-format
msgid "History"
msgstr "História"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__server
msgid "Hostname or IP of the mail server"
msgstr "Názov hostiteľa alebo IP adresa mail servera"

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__voice_active_duration
msgid ""
"How long the audio broadcast will remain active after passing the volume "
"threshold"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "ICE Servers"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "ICE gathering:"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_ice_server
#: model_terms:ir.ui.view,arch_db:mail.view_ice_server_form
msgid "ICE server"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_ice_servers
#: model:ir.ui.menu,name:mail.ice_servers_menu
msgid "ICE servers"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "ICE:"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__id
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__id
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__id
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__id
#: model:ir.model.fields,field_description:mail.field_mail_activity__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__id
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__id
#: model:ir.model.fields,field_description:mail.field_mail_alias__id
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__id
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__id
#: model:ir.model.fields,field_description:mail.field_mail_followers__id
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__id
#: model:ir.model.fields,field_description:mail.field_mail_guest__id
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__id
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_mail__id
#: model:ir.model.fields,field_description:mail.field_mail_message__id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__id
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__id
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__id
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__id
#: model:ir.model.fields,field_description:mail.field_mail_notification__id
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__id
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__id
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__id
#: model:ir.model.fields,field_description:mail.field_mail_template__id
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__id
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__id
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__id
msgid "ID"
msgstr "ID"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"ID nadradeného záznamu obsahujúceho alias (príklad: projekt obsahujúci alias"
" vytvorenia úlohy)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__im_status
msgid "IM Status"
msgstr "IM status"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "IMAP"
msgstr "IMAP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__imap
msgid "IMAP Server"
msgstr "IMAP Server"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__icon
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,field_description:mail.field_res_users__activity_exception_icon
msgid "Icon"
msgstr "Ikona"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_icon
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Ikona indikujúca výnimočnú aktivitu."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__res_id
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_id
msgid "Id of the followed resource"
msgstr "Id sledujúceho zdroja"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
msgid "Identity"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "Idle"
msgstr "Nečinný"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "If SSL required."
msgstr "Ak je SSL vyžadované."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_needaction
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_needaction
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction
#: model:ir.model.fields,help:mail.field_res_users__message_needaction
msgid "If checked, new messages require your attention."
msgstr "Ak označené, potom nové správy vyžadujú vašu pozornosť."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_has_error
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_has_error
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error
#: model:ir.model.fields,help:mail.field_res_users__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Ak označené, potom majú niektoré správy chybu dodania."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "If not set, shared with all users."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_model_fields__tracking
msgid ""
"If set every modification done to this field is tracked. Value is used to "
"order tracking values."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel_member__mute_until_dt
msgid ""
"If set, the member will not receive notifications from the channel until "
"this date."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. Unless a timezone is specified, "
"it is considered as being in UTC timezone."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__scheduled_date
msgid ""
"If set, the queue manager will send the email after the date. If not set, "
"the email will be send as soon as possible. You can use dynamic expression."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Ak je nastavený, namiesto predvolenej správy sa tento obsah automaticky "
"odošle neoprávneným užívateľom."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid ""
"If set, will restrict the template to this specific user."
"                                                   If not set, shared with "
"all users."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_partner__is_blacklisted
#: model:ir.model.fields,help:mail.field_res_users__is_blacklisted
msgid ""
"If the email address is on the blacklist, the contact won't receive mass "
"mailing anymore, from any list"
msgstr ""
"Ak je adresa na čiernej listine, kontakt neobdrží žiadny hromadný mail, zo "
"žiadneho zoznamu"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,help:mail.field_mail_message__reply_to_force_new
msgid ""
"If true, answers do not go in the original document discussion thread. "
"Instead, it will check for the reply_to in tracking message-id and "
"redirected accordingly. This has an impact on the generated message-id."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__alias_domain_id
msgid ""
"If you have setup a catch-all email domain redirected to the Odoo server, "
"enter the domain name here."
msgstr ""
"Ak máte nastavenú catch-all emailovú doménu presmerovanú na server Odoo, tu "
"zadajte názov domény."

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "If you want to use twilio as TURN/STUN server provider"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Ignore all"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__image_128
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1920
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_image
msgid "Image"
msgstr "Obrázok"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_1024
msgid "Image 1024"
msgstr "Obrázok 1024"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_128
msgid "Image 128"
msgstr "Obrázok 128"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_256
msgid "Image 256"
msgstr "Obrázok 256"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__image_512
msgid "Image 512"
msgstr "Obrázok 512"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__image_mimetype
msgid "Image MIME type"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_document_file_kanban
msgid "Image is a link"
msgstr "Obrázok je odkaz"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__scheduled_date
msgid ""
"In comment mode: if set, postpone notifications sending. In mass mail mode: "
"if sent, send emails after that date. This date is considered as being in "
"UTC timezone."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "Inactive Alias"
msgstr "Neaktívny alias"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__fetchmail_server_id
msgid "Inbound Mail Server"
msgstr "Server prichádzajúcej pošty"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/messaging_service.js:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_type__inbox
#, python-format
msgid "Inbox"
msgstr "Inbox"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#, python-format
msgid "Incoming Call..."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Incoming Email"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Incoming Email Servers"
msgstr "Servery prichádzajúcej pošty"

#. module: mail
#: model:ir.model,name:mail.model_fetchmail_server
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Incoming Mail Server"
msgstr "Server prichádzajúcej pošty"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_email_server_tree
#: model:ir.ui.menu,name:mail.menu_action_fetchmail_server_tree
msgid "Incoming Mail Servers"
msgstr "Servery prichádzajúcej pošty"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity__automated
msgid ""
"Indicates this activity has been created automatically and not by any user."
msgstr "Udáva, že táto aktivita bola vytvorená automaticky a nie užívateľom."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Info"
msgstr "Informácia"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__initial_res_model
msgid "Initial model"
msgstr "Počiatočný model"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__contact_address_inline
#: model:ir.model.fields,field_description:mail.field_res_users__contact_address_inline
msgid "Inlined Complete Address"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Input device"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "Install"
msgstr "Nainštalovať"

#. module: mail
#: model:ir.ui.menu,name:mail.discuss_channel_integrations_menu
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Integrations"
msgstr "Integrácie"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__internal
msgid "Internal Only"
msgstr "Iba interné"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.notification_preview
msgid "Internal communication:"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__invalid
msgid "Invalid"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Invalid domain %(domain)r (type %(domain_type)s)"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_invalid
msgid "Invalid email address"
msgstr "Neplatná emailová adresa"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_blacklist.py:0
#, python-format
msgid "Invalid email address %r"
msgstr "Neplatná emailová adresa %r"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Invalid expression, it must be a literal python dictionary definition e.g. "
"\"{'field': 'value'}\""
msgstr ""
"Chybný výraz, musí byť ako slovník jazyka Python, napr.: \"{'field': "
"'value'}\""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "Invalid field “%(field_name)s” when creating a channel with members."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_from_invalid
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_from_invalid
msgid "Invalid from address"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid "Invalid primary email field on model %s"
msgstr "Neplatné primárne pole emailu v modeli %s"

#. module: mail
#. odoo-python
#: code:addons/mail/tools/parser.py:0
#, python-format
msgid "Invalid res_ids %(res_ids_str)s (type %(res_ids_type)s)"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid ""
"Invalid server name!\n"
" %s"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Invalid template or view source %(svalue)s (type %(stype)s), should be a "
"record or an XMLID"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Invalid template or view source Xml ID %(source_ref)s does not exist anymore"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Invalid template or view source record %(svalue)s, is %(model)s instead"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Invalid template or view source reference %(svalue)s, is %(model)s instead"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"Invalid value when creating a channel with members, only 4 or 6 are allowed."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"Invalid value when creating a channel with memberships, only 0 is allowed."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__invitation_url
msgid "Invitation URL"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Invitation to follow %(document_model)s: %(document_name)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Invite"
msgstr "Pozvať"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.js:0
#, python-format
msgid "Invite Follower"
msgstr "Pozvať sledujúceho"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "Invite a User"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Invite people"
msgstr "Pozvať ľudí"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Invite to Channel"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Invite to Group Chat"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_wizard_invite
msgid "Invite wizard"
msgstr "Sprievodca pozvania"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__is_active
msgid "Is Active"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_current_user_or_guest_author
#: model:ir.model.fields,field_description:mail.field_mail_message__is_current_user_or_guest_author
msgid "Is Current User Or Guest Author"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_editable
msgid "Is Editable"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__is_mail_template_editor
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__is_mail_template_editor
msgid "Is Editor"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_is_follower
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_partner__message_is_follower
#: model:ir.model.fields,field_description:mail.field_res_users__message_is_follower
msgid "Is Follower"
msgstr "Odberateľ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_member
msgid "Is Member"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__is_read
msgid "Is Read"
msgstr "Je prečítaný"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_self
msgid "Is Self"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__is_template_editor
msgid "Is Template Editor"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__is_chat
msgid "Is a chat"
msgstr "Je chat"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_is_log
msgid "Is a log"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_channel_open
msgid "Is discuss sidebar category channel open?"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__is_discuss_sidebar_category_chat_open
msgid "Is discuss sidebar category chat open?"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_muted
msgid "Is microphone muted"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__is_pinned
msgid "Is pinned on the interface"
msgstr "Je pripnutý na interface"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_camera_on
msgid "Is sending user video"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__is_screen_sharing_on
msgid "Is sharing the screen"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid ""
"It appears you're trying to create a channel member, but it seems like you "
"forgot to specify the related channel. To move forward, please make sure to "
"provide the necessary channel information."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_partner_device__keys
msgid ""
"It's refer to browser keys used by the notification: \n"
"- p256dh: It's the subscription public key generated by the browser. The browser will \n"
"          keep the private key secret and use it for decrypting the payload\n"
"- auth: The auth value should be treated as a secret and not shared outside of Odoo"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_duration_mixin__duration_tracking
msgid "JSON that maps ids from a many2one field to seconds spent"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Join"
msgstr "Pripojiť"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Join Call"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "Join Channel"
msgstr "Pripojiť sa ku kanálu"

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_action_view
msgid "Join a group"
msgstr "Pridať sa do skupiny"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.xml:0
#, python-format
msgid "Jump"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "Jump to Present"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__attach
msgid "Keep Attachments"
msgstr "Uchovať prílohy"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__keep_done
msgid "Keep Done"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__auto_delete_keep_log
msgid "Keep Message Copy"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__original
msgid "Keep Original"
msgstr "Uchovať originál"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete_keep_log
msgid ""
"Keep a copy of the email content if emails are removed (mass mailing only)"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__keep_done
msgid "Keep activities marked as done in the activity view"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Key"
msgstr "Kľúč"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#: code:addons/mail/models/mail_alias.py:0
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
#, python-format
msgid "Kind Regards"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "LIVE"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__lang
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_guest__lang
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,field_description:mail.field_mail_template__lang
msgid "Language"
msgstr "Jazyk"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__date
msgid "Last Fetch Date"
msgstr "Dátum posledného načítania"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__fetched_message_id
msgid "Last Fetched"
msgstr "Naposledy načítané"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__last_interest_dt
msgid "Last Interest"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__seen_message_id
msgid "Last Seen"
msgstr "Naposledy viden"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__write_date
msgid "Last Updated On"
msgstr "Posledná aktualizácia dňa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__write_uid
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__write_uid
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_uid
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_uid
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_uid
msgid "Last Updated by"
msgstr "Naposledy upravoval"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_gif_favorite__write_date
#: model:ir.model.fields,field_description:mail.field_discuss_voice_metadata__write_date
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__write_date
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias__write_date
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__write_date
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__write_date
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__write_date
#: model:ir.model.fields,field_description:mail.field_mail_guest__write_date
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__write_date
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_mail__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__write_date
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__write_date
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__write_date
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__write_date
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__write_date
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__write_date
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__write_date
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__write_date
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__write_date
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__write_date
msgid "Last Updated on"
msgstr "Naposledy upravované"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__last_used
msgid "Last Used"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__last_seen_dt
msgid "Last seen date"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__last_used
msgid "Last time this shortcode was used"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#, python-format
msgid "Late"
msgstr "Meškajúci"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Late Activities"
msgstr "Omeškané aktivity"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "Launch Plans"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_layout_xmlid
#: model:ir.model.fields,field_description:mail.field_mail_message__email_layout_xmlid
msgid "Layout"
msgstr "Usporiadanie"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_kanban
msgid "Leave"
msgstr "Voľno"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.js:0
#, python-format
msgid "Leave Conversation"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "Leave this channel"
msgstr "Opustiť tento kanál"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_link_preview_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__link_preview_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__link_preview_ids
#: model:ir.ui.menu,name:mail.mail_link_preview_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_link_preview_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_link_preview_view_tree
msgid "Link Previews"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Link copied!"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
#, python-format
msgid "List users in the current channel"
msgstr "Zoznam užívateľov aktuálneho kanála"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "Load More"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: code:addons/mail/static/src/core/web/recipient_list.xml:0
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "Load more"
msgstr "Načítať viac"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Load template"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
#, python-format
msgid "Loading"
msgstr "Nahrávanie"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__local
msgid "Local Server"
msgstr "Lokálny server"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__catchall_alias
msgid ""
"Local-part of email used for Reply-To to catch answers e.g. 'catchall' in "
"'<EMAIL>'"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias_domain__bounce_alias
msgid ""
"Local-part of email used for Return-Path used when emails bounce e.g. "
"'bounce' in '<EMAIL>'"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Log"
msgstr "Pridať"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Log RTC events"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Log a note..."
msgstr "Pridať poznámku..."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
msgid "Log an Activity"
msgstr "Zaznamenaj  aktivitu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/composer_patch.js:0
#, python-format
msgid "Log an internal note…"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Log note"
msgstr "Pridať poznámku"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Log step:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.js:0
#, python-format
msgid "Logged in as %s"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Login Information"
msgstr "Prihlasovacie údaje"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__low
msgid "Low"
msgstr "Nízky"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Lower Hand"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_mimetype
msgid "MIME type"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_mail_id
#, python-format
msgid "Mail"
msgstr "Správa"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Activity"
msgstr "Aktivita mailu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_activity_type_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_activity_type_id
msgid "Mail Activity Type"
msgstr "Typ aktivity mailu"

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Blacklist"
msgstr "Čierna listina mailov"

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_blacklist
msgid "Mail Blacklist mixin"
msgstr "Čierna listina mailov mixin"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Mail Channel Form"
msgstr "Formulár mailového kanálu"

#. module: mail
#: model:ir.model,name:mail.model_mail_composer_mixin
msgid "Mail Composer Mixin"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "Mail Failures"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_gateway_allowed_action
#: model:ir.model,name:mail.model_mail_gateway_allowed
#: model:ir.ui.menu,name:mail.mail_gateway_allowed_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_gateway_allowed_view_tree
msgid "Mail Gateway Allowed"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_config_settings.py:0
#, python-format
msgid "Mail Layout"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_thread_main_attachment
msgid "Mail Main Attachment management"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id_int
msgid "Mail Message Id Int"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_discuss_channel_rtc_session
msgid "Mail RTC session"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_render_mixin
msgid "Mail Render Mixin"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_mail_server
msgid "Mail Server"
msgstr "Mailový server"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__template_id
msgid "Mail Template"
msgstr "Mailová šablóna"

#. module: mail
#: model:res.groups,name:mail.group_mail_template_editor
msgid "Mail Template Editor"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_template_reset
msgid "Mail Template Reset"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_template_reset.py:0
#, python-format
msgid "Mail Templates have been reset"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.model_search_view
msgid "Mail Thread"
msgstr "Vlákno mailu"

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_value
msgid "Mail Tracking Value"
msgstr "Hodnota sledovania mailu"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid ""
"Mail composer in comment mode should run on at least one record. No records "
"found (model %(model_name)s)."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__is_notification
msgid "Mail has been created to notify people of an existing mail.message"
msgstr "Mail  bol vytvorený na notifikáciu osôb existujúceho mail.message"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "Mail template model of %(action_name)s does not match action model."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_mail_server__mail_template_ids
msgid "Mail template using this mail server"
msgstr ""

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_scheduler_action_ir_actions_server
msgid "Mail: Email Queue Manager"
msgstr "Mail: správa správcovi fronty"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_mail_gateway_action_ir_actions_server
msgid "Mail: Fetchmail Service"
msgstr "Služba Fetchmail"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_web_push_notification_ir_actions_server
msgid "Mail: send web push notification"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Mailbox unavailable - %s"
msgstr "Schránka nedostupná - %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.js:0
#, python-format
msgid "Mailboxes"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Mailing or posting with a source should not be called with an empty "
"%(source_type)s"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_ids
msgid "Mails"
msgstr "Maily"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_main_attachment_id
msgid "Main Attachment"
msgstr "Hlavná príloha"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/js/tools/debug_manager.js:0
#, python-format
msgid "Manage Messages"
msgstr "Správa správ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_force_new
msgid ""
"Manage answers as new incoming emails instead of replies going to the same "
"thread."
msgstr ""
"Spravovať odpovede ako nové prichádzajúce e-maily namiesto toho, aby "
"odpovede smerovali do rovnakého vlákna."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/notification_item.xml:0
#, python-format
msgid "Mark As Read"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#, python-format
msgid "Mark Done"
msgstr "Označenie hotovo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
#, python-format
msgid "Mark all read"
msgstr "Označiť všetko ako prečítané"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree_without_record_access
msgid "Mark as Done"
msgstr "Označiť ako hotovo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Mark as Read"
msgstr "Označiť ako prečítané"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Mark as Todo"
msgstr "Označiť ako Úloha"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Mark as Unread"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
#, python-format
msgid "Mark as done"
msgstr "Označiť ako hotovo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.js:0
#, python-format
msgid "Media devices unobtainable. SSL might not be set up properly."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__medium
msgid "Medium"
msgstr "Médium"

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_meeting
msgid "Meeting"
msgstr "Stretnutie"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__member_count
msgid "Member Count"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.js:0
#, python-format
msgid "Member List"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_member_ids
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Members"
msgstr "Členovia"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__group_ids
msgid ""
"Members of those groups will automatically added as followers. Note that "
"they will be able to manage their subscription manually if necessary."
msgstr ""
"Členovia týchto skupín budú automaticky pridaný ako odberatelia. Vprípade "
"potreby budú schopní spravovať ich odberanie manuálne."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/command_category.js:0
#, python-format
msgid "Mentions"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__mentions
#, python-format
msgid "Mentions Only"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_base_partner_merge_automatic_wizard
msgid "Merge Partner Wizard"
msgstr "Pomocník zlučovania partnerov"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/base_partner_merge_automatic_wizard.py:0
#, python-format
msgid "Merged with the following partners:"
msgstr "Zlúčené s následovnými partnermi:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model,name:mail.model_mail_message
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__message_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__message_id
#: model:ir.model.fields,field_description:mail.field_mail_notification__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__mail_message_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__message
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__comment
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
#, python-format
msgid "Message"
msgstr "Správa"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Message #%(thread name)s…"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Message %(thread name)s…"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error
msgid "Message Delivery error"
msgstr "Chyba zobrazovania správ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__mail_message_id
msgid "Message ID"
msgstr "ID správy"

#. module: mail
#: model:ir.model,name:mail.model_mail_notification
msgid "Message Notifications"
msgstr "Upozornenie na správu"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_reaction
msgid "Message Reaction"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_message_reaction_action
#: model:ir.ui.menu,name:mail.mail_message_reaction_menu
msgid "Message Reactions"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__record_name
#: model:ir.model.fields,field_description:mail.field_mail_message__record_name
msgid "Message Record Name"
msgstr "Názov záznamu správy"

#. module: mail
#: model:ir.model,name:mail.model_mail_message_translation
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Message Translation"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__google_translate_api_key
msgid "Message Translation API Key"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__name
msgid "Message Type"
msgstr "Typ správy"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__description
#: model:ir.model.fields,help:mail.field_mail_message__description
msgid "Message description: either the subject, or the beginning of the body"
msgstr "Popis správy: môže byť subjekt, alebo začiatok tela správy"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Message posted on \"%s\""
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__email_to
msgid "Message recipients (emails)"
msgstr "Odberatelia správy (email)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__references
msgid "Message references, such as identifiers of previous messages"
msgstr ""
"Odkazy na správu, ako napíklad identifikátory v predchádzajúcich správach"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Message should be a valid EmailMessage instance"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__name
msgid ""
"Message subtype gives a more precise type on the message, especially for "
"system notifications. For example, it can be a notification related to a new"
" record (New), or to a stage change in a process (Stage change). Message "
"subtypes allow to precisely tune the notifications the user want to receive "
"on its wall."
msgstr ""
"Podtyp správy dáva precíznejší typ správe, obzvlášť pre systémové "
"notifikácie. Napríklad, môže to byť notifikácia vzťahujúca sa na nový záznam"
" (Nové) , alebo k zmene fázy v procese (Zmena fázy). Podtypy správ umožňujú "
"presne vyladiť notifikácie ktoré chce používateľ dostávať na svoju nástenku."

#. module: mail
#: model:ir.model,name:mail.model_mail_message_subtype
msgid "Message subtypes"
msgstr "Podtypy správy"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_followers__subtype_ids
msgid ""
"Message subtypes followed, meaning subtypes that will be pushed onto the "
"user's Wall."
msgstr ""
"Nasledované podtypy správy, znamená podtypy zobrazené na užívateľovej "
"nástenke."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__message_type
msgid ""
"Message type: email for email message, notification for system message, "
"comment for other messages such as user replies"
msgstr ""
"Typ správy: email pre zaslanie emailu, notifikácia ako systémová správa, "
"koment pre ostatné správy ako napr. odpovede od užívateľov"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_id
#: model:ir.model.fields,help:mail.field_mail_message__message_id
msgid "Message unique identifier"
msgstr "Unikátny identifikátor správy"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_id
#: model:ir.model.fields,field_description:mail.field_mail_message__message_id
msgid "Message-Id"
msgstr "ID správy"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#: model:ir.actions.act_window,name:mail.act_server_history
#: model:ir.actions.act_window,name:mail.action_view_mail_message
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_ids
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_ids
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_ids
#: model:ir.model.fields,field_description:mail.field_res_partner__message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__message_ids
#: model:ir.ui.menu,name:mail.menu_mail_message
#: model_terms:ir.ui.view,arch_db:mail.view_message_tree
#, python-format
msgid "Messages"
msgstr "Správy"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Messages Search"
msgstr "Vyhľadávanie správ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "Messages marked as read will appear in the history."
msgstr "Správy označené ako prečítané sa objavia v histórii."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__internal
msgid ""
"Messages with internal subtypes will be visible only by employees, aka "
"members of base_user group"
msgstr ""
"Správy s interným podtypom budú viditeľné iba zamestnancami, aka členovia "
"skupiny base_user"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Messages with tracking values cannot be modified"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_discuss_voice_metadata
msgid "Metadata for voice attachments"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_email_missing
msgid "Missing email"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_email_missing
msgid "Missing email address"
msgstr "Chýba emailová adresa"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_from_missing
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__mail_from_missing
msgid "Missing from address"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_tracking_duration_mixin
msgid ""
"Mixin to compute the time a record has spent in each value a many2one field "
"can take"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__res_model
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__res_model
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Model"
msgstr "Model"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__res_model_change
msgid "Model has change"
msgstr "Model sa zmenil"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_wizard_invite__res_model
msgid "Model of the followed resource"
msgstr "Model sledovaného zdroja"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__res_model
msgid ""
"Model the subtype applies to. If False, this subtype applies to all models."
msgstr ""
"Model pre ktorý platí tento podtyp. Ak je Nepravda, tento podtyp platí pre "
"všetky modely."

#. module: mail
#: model:ir.model,name:mail.model_ir_model
msgid "Models"
msgstr "Modely"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid ""
"Modifying the model can have an impact on existing activities using this "
"activity type, be careful."
msgstr ""
"Budte opatrní, zmena modelu môže mať vplyv na existujúce aktivity "
"používajúce tento typ aktivity."

#. module: mail
#: model:ir.model,name:mail.model_base_module_uninstall
msgid "Module Uninstall"
msgstr "Odinštalovať modul"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__months
msgid "Months"
msgstr "Mesiace"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "More"
msgstr "Viac"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Mute"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
#, python-format
msgid "Mute Channel"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__mute_until_dt
msgid "Mute notifications until"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__my_activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Termín mojej aktivity"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "My Templates"
msgstr "Moje šablóny"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__name
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__name
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__name
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__name
#: model:ir.model.fields,field_description:mail.field_mail_followers__name
#: model:ir.model.fields,field_description:mail.field_mail_guest__name
#: model:ir.model.fields,field_description:mail.field_mail_template__name
#: model:ir.model.fields,field_description:mail.field_res_partner__name
#: model:ir.model.fields,field_description:mail.field_res_users__name
msgid "Name"
msgstr "Meno"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__needaction
#: model:ir.model.fields,field_description:mail.field_mail_message__needaction
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Need Action"
msgstr "Vyžaduje akciu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
#, python-format
msgid "New Channel"
msgstr "Nový kanál"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
#, python-format
msgid "New Message"
msgstr "Nová správa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_char
msgid "New Value Char"
msgstr "Nová  hodnota Char"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_datetime
msgid "New Value Datetime"
msgstr "Nová hodnota Datetime"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_float
msgid "New Value Float"
msgstr "Nová hodnota float"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_integer
msgid "New Value Integer"
msgstr "Nová hodnota Integer"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__new_value_text
msgid "New Value Text"
msgstr "Nová hodnota text"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window_model.js:0
#: code:addons/mail/static/src/core/common/out_of_focus_service.js:0
#, python-format
msgid "New message"
msgstr "Nová správa"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "New messages"
msgstr "Nové správy"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "New messages appear here."
msgstr "Nové správy sa zobrazia tu."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "New values"
msgstr "Nové hodnoty"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_tree
msgid "Next Activities"
msgstr "Ďalšie aktivity"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "Next Activity"
msgstr "Ďalšia aktivita"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_date_deadline
#: model:ir.model.fields,field_description:mail.field_res_users__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Ďalší konečný termín aktivity"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_summary
#: model:ir.model.fields,field_description:mail.field_res_users__activity_summary
msgid "Next Activity Summary"
msgstr "Zhrnutie ďalšej aktivity"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_type_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_type_id
msgid "Next Activity Type"
msgstr "Typ ďalšej aktivity"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__has_recommended_activities
msgid "Next activities available"
msgstr "Dostupné nasledovné aktivity"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
#, python-format
msgid "No"
msgstr "Nie"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
#, python-format
msgid "No Error"
msgstr "Bez chýb"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#, python-format
msgid "No Followers"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "No IM status available"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__no_record
msgid "No Record"
msgstr ""

#. module: mail
#: model_terms:ir.actions.act_window,help:mail.mail_activity_without_access_action
msgid "No activities."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "No channel found"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.xml:0
#, python-format
msgid "No conversation selected."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "No conversation yet..."
msgstr "Zatiaľ žiadna konverzácia..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "No history messages"
msgstr "Žiadne správy z histórie"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_resend_message.py:0
#, python-format
msgid "No message_id found in context"
msgstr "V kontexte sa nenašiel žiadna správa_id"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.js:0
#, python-format
msgid "No messages found"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.js:0
#, python-format
msgid "No recipient"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "No recipient found."
msgstr "Nenašli sa žiadni príjemcovia."

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid ""
"No response received. Check server information.\n"
" %s"
msgstr ""
"Neprišlia žiadna odpoveď. Skontrolujte info servera.\n"
"%s"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
#, python-format
msgid ""
"No responsible specified for %(activity_type_name)s: %(activity_summary)s."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
#, python-format
msgid "No results found"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "No starred messages"
msgstr "Žiadne správy označené hviezdičkou"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to_force_new
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to_force_new
msgid "No threading for answers"
msgstr "Žiadne vlákna v odpovediach"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "No user found"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid "No user found that is not already a member of this channel."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "No users found"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/mail.py:0
#, python-format
msgid "Non existing record or wrong token."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__default
#, python-format
msgid "None"
msgstr "Žiadne"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_gateway_allowed__email_normalized
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_partner__email_normalized
#: model:ir.model.fields,field_description:mail.field_res_users__email_normalized
msgid "Normalized Email"
msgstr "Normalizovaný email"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__state__draft
msgid "Not Confirmed"
msgstr "Nepotvrdené"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__not_tested
msgid "Not Tested"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_note
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_note
#: model:ir.model.fields,field_description:mail.field_mail_activity__note
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__note
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__note
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__mail_post_method__note
#: model:mail.message.subtype,name:mail.mt_note
#, python-format
msgid "Note"
msgstr "Poznámka"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__custom_notifications__no_notif
#, python-format
msgid "Nothing"
msgstr "Nič"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__notification_id
#: model:ir.model.fields,field_description:mail.field_res_users__notification_type
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Notification"
msgstr "Notifikácia"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__is_notification
msgid "Notification Email"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/notification_item.xml:0
#, python-format
msgid "Notification Item Image"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__notification_parameters
msgid "Notification Parameter"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Notification Settings"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_type
msgid "Notification Type"
msgstr "Typ Notifikácie"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Notification should receive attachments as a list of list or tuples "
"(received %(aids)s)"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Notification should receive attachments records as a list of IDs (received "
"%(aids)s)"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Notification should receive partners given as a list of IDs (received "
"%(pids)s)"
msgstr ""

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_delete_notification_ir_actions_server
msgid "Notification: Delete Notifications older than 6 Month"
msgstr "Oznámenie: Odstrániť upozornenia staršie ako 6 mesiacov"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_send_scheduled_message_ir_actions_server
msgid "Notification: Send scheduled message notifications"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_notification_action
#: model:ir.model.fields,field_description:mail.field_mail_mail__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notification_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__notification_ids
#: model:ir.ui.menu,name:mail.mail_notification_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_tree
msgid "Notifications"
msgstr "Notifikácie"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
#, python-format
msgid "Notifications allowed"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
#, python-format
msgid "Notifications blocked"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_needaction_counter
msgid "Number of Actions"
msgstr "Počet akcií"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_count
msgid ""
"Number of days/week/month before executing the action. It allows to plan the"
" action deadline."
msgstr ""
"Počet dní/týždňov/mesiacov pred vykonaním akcie. Umožňuje plánovať záverečný"
" termín akcie."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_mail_thread_main_attachment__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,field_description:mail.field_res_users__message_has_error_counter
msgid "Number of errors"
msgstr "Počet chýb"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_needaction_counter
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_needaction_counter
#: model:ir.model.fields,help:mail.field_res_users__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Počet správ, ktoré vyžadujú akciu"

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_cc__message_has_error_counter
#: model:ir.model.fields,help:mail.field_mail_thread_main_attachment__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_partner__message_has_error_counter
#: model:ir.model.fields,help:mail.field_res_users__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Počet doručených správ s chybou"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Odoo"
msgstr "Odoo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
#, python-format
msgid "Odoo will not send notifications on this device."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_permission_service.js:0
#, python-format
msgid "Odoo will send notifications on this device!"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__res_config_settings__tenor_content_filter__off
msgid "Off"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "Offline"
msgstr "Offline"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "Offline -"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_char
msgid "Old Value Char"
msgstr "Stará hodnota Char"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_datetime
msgid "Old Value DateTime"
msgstr "Stará hodnota DateTime"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_float
msgid "Old Value Float"
msgstr "Stará hodnota Float"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_integer
msgid "Old Value Integer"
msgstr "Stará hodnota Integer"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__old_value_text
msgid "Old Value Text"
msgstr "Text"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Old values"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "Online"
msgstr "Online"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "Online -"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Only administrators are allowed to export mail message"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_model.py:0
#, python-format
msgid "Only custom models can be modified."
msgstr "Upraviť možno iba zákaznícky model"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_res_users_notification_type
msgid "Only internal user can receive notifications in Odoo"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Only messages type comment can have their content updated"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"Only messages type comment can have their content updated on model "
"'discuss.channel'"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Only users belonging to the \"%(group_name)s\" group can modify dynamic "
"templates."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#: model:ir.model.fields.selection,name:mail.selection__discuss_channel_member__fold_state__open
#, python-format
msgid "Open"
msgstr "Otvorené"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.js:0
#, python-format
msgid "Open Actions Menu"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Open Document"
msgstr "Otvoriť dokument"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
#, python-format
msgid "Open Form View"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#, python-format
msgid "Open Link"
msgstr "Otvoriť odkaz"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_tree
msgid "Open Owner"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
#, python-format
msgid "Open card"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/thread_actions.js:0
#, python-format
msgid "Open in Discuss"
msgstr "Otvorené v diskusii"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Operation not supported"
msgstr "Operácia nie je podporovaná"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__mail_optout
msgid "Opted Out"
msgstr "Odhlásené"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Voliteľné ID záznamy, na ktoré budú pripojené všetky prichádzajúce správy, "
"aj keď na to neodpovedali. Ak je nastavené, úplne to zakáže vytváranie "
"nových záznamov."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_notification__mail_mail_id
msgid "Optional mail_mail ID. Used mainly to optimize searches."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__mail_server_id
msgid ""
"Optional preferred server for outgoing mails. If not set, the highest "
"priority one will be used."
msgstr ""
"Preferovaný server pre odchádzajúcu poštu. Pokiaľ nie je nastavené inak, "
"použitý bude server s najväčšou prioritou."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__lang
#: model:ir.model.fields,help:mail.field_mail_composer_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_render_mixin__lang
#: model:ir.model.fields,help:mail.field_mail_template__lang
msgid ""
"Optional translation language (ISO code) to select when sending out an "
"email. If not set, the english version will be used. This should usually be "
"a placeholder expression that provides the appropriate language, e.g. {{ "
"object.partner_id.lang }}."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to_mode
msgid ""
"Original Discussion: Answers go in the original document discussion thread. \n"
" Another Email Address: Answers go to the email address mentioned in the tracking message-id instead of original document discussion thread. \n"
" This has an impact on the generated message-id."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_in_reply.xml:0
#, python-format
msgid "Original message was deleted"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#: model:ir.actions.act_window,name:mail.mail_activity_without_access_action
#, python-format
msgid "Other activities"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing"
msgstr "Odchádzajúce"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__email_outgoing
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Outgoing Email"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Outgoing Email Servers"
msgstr "Servery odchádzajúcej pošty"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__mail_server_id
msgid "Outgoing Mail Server"
msgstr "Server odchádzajúcej pošty"

#. module: mail
#: model:ir.model,name:mail.model_mail_mail
msgid "Outgoing Mails"
msgstr "Odchádzajúca pošta"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__mail_server_id
#: model:ir.model.fields,field_description:mail.field_mail_message__mail_server_id
msgid "Outgoing mail server"
msgstr "Server odchádzajúcej pošty"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_state__overdue
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_state__overdue
#, python-format
msgid "Overdue"
msgstr "Oneskorené"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Override author's email"
msgstr "Prepísať autorov email"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "POP"
msgstr "POP"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__fetchmail_server__server_type__pop
msgid "POP Server"
msgstr "POP Server"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_tree
msgid "POP/IMAP Servers"
msgstr "POP/IMAP Servery"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Packets received:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Packets sent:"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__parent_id
msgid "Parent"
msgstr "Nadradené"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__parent_id
#: model:ir.model.fields,field_description:mail.field_mail_message__parent_id
msgid "Parent Message"
msgstr "Nadradená správa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_model_id
msgid "Parent Model"
msgstr "Nadradený model"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "ID vlákna nadradeného záznamu"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Nadradený model drží alias. Model drží referenciu aliasu nie je nevyhnutne "
"model ktorý zadal alias_model_id (príklad: projekt (parent_model) a "
"požiadavky (model))"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__parent_id
msgid ""
"Parent subtype, used for automatic subscription. This field is not correctly"
" named. For example on a project, the parent_id of project subtypes refers "
"to task-related subtypes."
msgstr ""
"Nadradený podtyp, pouźívaný pre automatické odbery. Toto pole nie je správne"
" pomenované. Napríklad na projekte, parent_id podtypov projektu odkazuje na "
"podtypy spojené s úlohami."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__partner_id
#: model:ir.model.fields,field_description:mail.field_discuss_channel_rtc_session__partner_id
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__partner_ids
#: model:ir.model.fields,field_description:mail.field_ir_cron__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_partner_device__partner_id
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_id
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "Partner"
msgstr "Partner"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_partner.py:0
#, python-format
msgid "Partner Profile"
msgstr "Profil  partnera"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_readonly
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__partner_readonly
msgid "Partner Readonly"
msgstr "Partner iba na čítanie"

#. module: mail
#: model:ir.model,name:mail.model_mail_partner_device
msgid "Partner Web Push Device"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_resend_partner
msgid "Partner with additional information for mail resend"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__channel_partner_ids
msgid "Partners"
msgstr "Partneri"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__notified_partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__notified_partner_ids
msgid "Partners with Need Action"
msgstr "Partneri, ktorí potrebujú akciu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__password
msgid "Password"
msgstr "Heslo"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Paste your API key"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
#, python-format
msgid "Pause"
msgstr "Pozastavenie"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__payload
msgid "Payload"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Permanently delete this template"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__phone
#: model:ir.model.fields,field_description:mail.field_res_users__phone
msgid "Phone"
msgstr "Telefón"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__phonecall
msgid "Phonecall"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_patch.js:0
#, python-format
msgid "Pin"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid "Pin It"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__pinned_at
#: model:ir.model.fields,field_description:mail.field_mail_message__pinned_at
msgid "Pinned"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
#: code:addons/mail/static/src/discuss/message_pin/common/thread_actions.js:0
#: model:ir.model.fields,field_description:mail.field_discuss_channel__pinned_message_ids
#, python-format
msgid "Pinned Messages"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__plan_id
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_id
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_search
msgid "Plan"
msgstr "Plán"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_available_ids
msgid "Plan Available"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_date_deadline
msgid "Plan Date"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "Plan Name"
msgstr "Názov plánu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__assignation_summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__plan_assignation_summary
msgid "Plan Summary"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Plan summary"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__planned
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_state__planned
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_state__planned
#, python-format
msgid "Planned"
msgstr "Plánované"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Planned Activities"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Planned in"
msgstr "Plánované v"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_tree
msgid "Planning"
msgstr "Plánovanie"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
#, python-format
msgid "Play"
msgstr "Spustiť"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient.js:0
#, python-format
msgid "Please complete customer's information"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Please contact us instead using"
msgstr "Kontaktujte nás, prosím, odoslaním správy na adresu: "

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Please wait while the file is uploading."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users__notification_type
msgid ""
"Policy on how to handle Chatter notifications:\n"
"- Handle by Emails: notifications are sent to your email address\n"
"- Handle in Odoo: notifications appear in your Odoo Inbox"
msgstr ""
"Pravidlá ako spracovať upozornenia chatteru:\n"
"- spracovať mailom: upozornenia budú zaslané na vašu mailovú adresu\n"
"- spracovať v Odoo: upozornenia sa zobrazia vo vašej schránke Odoo Inbox"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Metóda pre zverejnenie správy na dokumente pri použití mailovej brány.\n"
"- všeci: všetci môžu zverejňovať\n"
"- partneri: iba overený partneri\n"
"- odberatelia: iba odberatelia pridruženého dokumentu alebo členovia príslušných kanálov\n"
"\n"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__port
msgid "Port"
msgstr "Port"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Portal Access Granted"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Portal Access Revoked"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__composition_mode__comment
msgid "Post on a document"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message should be done on a business document. Use message_notify "
"to send a notification to an user."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message should receive attachments as a list of list or tuples "
"(received %(aids)s)"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message should receive attachments records as a list of IDs "
"(received %(aids)s)"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Posting a message should receive partners as a list of IDs (received "
"%(pids)s)"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Powered by"
msgstr "Prinášané"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__previous_type_ids
msgid "Preceding Activities"
msgstr "Predchádzajúce aktivity"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__reply_to
msgid "Preferred response address"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/channel_selector.js:0
#, python-format
msgid "Press Enter to start"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Press a key to register it as the push-to-talk shortcut."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__preview
#: model:ir.model.fields,field_description:mail.field_mail_message__preview
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#, python-format
msgid "Preview"
msgstr "Náhľad"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Preview of"
msgstr "Náhľad "

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__previous_activity_type_id
msgid "Previous Activity Type"
msgstr "Typ predošlej aktivity"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Privacy"
msgstr "Súkromný"

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__object_id
msgid ""
"Process each incoming mail as part of a conversation corresponding to this "
"document type. This will create new documents for new conversations, or "
"attach follow-up emails to the existing conversations (documents)."
msgstr ""
"Spracovanie každej prichádzajúcej pošty v rámci konverzácie zodpovedajúcej "
"tomuto typu dokumentu. To bude vytvárať nové dokumenty pre nové konverzácie,"
" alebo priloží následné emaily na existujúce konverzácie (dokumenty)."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__process
#, python-format
msgid "Processing"
msgstr "Spracovanie"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_icon.xml:0
#, python-format
msgid "Public Channel"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.js:0
#, python-format
msgid "Public Channels"
msgstr "Verejné kanály"

#. module: mail
#: model:ir.model,name:mail.model_publisher_warranty_contract
msgid "Publisher Warranty Contract"
msgstr "Zmluva o záruke vydavateľov"

#. module: mail
#: model:ir.actions.server,name:mail.ir_cron_module_update_notification_ir_actions_server
msgid "Publisher: Update Notification"
msgstr "Vydavateľ: aktualizácia oznámenia"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__push_to_talk_key
msgid "Push-To-Talk shortcut"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Push-to-talk key"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "Quick search..."
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_qweb
msgid "Qweb"
msgstr "Qweb"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_tree
msgid "RTC Session"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "RTC Session ID:"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__rtc_session_ids
msgid "RTC Sessions"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.discuss_channel_rtc_session_action
#: model:ir.ui.menu,name:mail.discuss_channel_rtc_session_menu
msgid "RTC sessions"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Raise Hand"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings_volumes__volume
msgid ""
"Ranges between 0.0 and 1.0, scale depends on the browser implementation"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__guest_id
msgid "Reacting Guest"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_reaction__partner_id
msgid "Reacting Partner"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reaction_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__reaction_ids
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_message_reaction_view_tree
msgid "Reactions"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__read_date
msgid "Read Date"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#, python-format
msgid "Ready"
msgstr "Pripravené"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__ready
msgid "Ready to Send"
msgstr "Pripravené na odoslanie"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_blacklist_remove__reason
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "Reason"
msgstr "Dôvod "

#. module: mail
#: model:res.groups,name:mail.group_mail_notification_type_inbox
msgid "Receive notifications in Odoo"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__received
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Received"
msgstr "Prijaté"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/command_category.js:0
#, python-format
msgid "Recent"
msgstr "Nedávny"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification__res_partner_id
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Recipient"
msgstr "Príjemca"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__name
msgid "Recipient Name"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_resend_message__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__partner_ids
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__partner_ids
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Recipients"
msgstr "Príjemcovia"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Recommended Activities"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__recommended_activity_type_id
msgid "Recommended Activity Type"
msgstr "Odporúčané typy aktivity"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__resource_ref
msgid "Record"
msgstr "Záznam"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__record_name
msgid "Record Name"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_alias__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Zaznamenať ID vlákna"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Records:"
msgstr "Záznamov:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__references
msgid "References"
msgstr "Referencie"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_invitation.xml:0
#, python-format
msgid "Refuse"
msgstr "Odmietnuť"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "Regards,"
msgstr "S pozdravom,"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Register new key"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Reject"
msgstr "Zamietnuť"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__parent_id
#: model:ir.model.fields,field_description:mail.field_res_users__parent_id
msgid "Related Company"
msgstr "Súvisiaca spoločnosť"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_id
#: model:ir.model.fields,field_description:mail.field_mail_mail__res_id
#: model:ir.model.fields,field_description:mail.field_mail_message__res_id
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_id
msgid "Related Document ID"
msgstr "Súvisiace ID dokumentu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_ids
msgid "Related Document IDs"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__res_model
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model
#: model:ir.model.fields,field_description:mail.field_mail_mail__model
#: model:ir.model.fields,field_description:mail.field_mail_message__model
#: model:ir.model.fields,field_description:mail.field_mail_template__model
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__res_model
msgid "Related Document Model"
msgstr "Súvisiaci model dokumentu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__res_model
msgid "Related Document Model Name"
msgstr "Názov súvisiaceho modelu dokumentu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__mail_template_id
msgid "Related Mail Template"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
msgid "Related Message"
msgstr "Súvisiaca správa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_followers__partner_id
msgid "Related Partner"
msgstr "Súvisiaci partner"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__relation_field
msgid "Relation field"
msgstr "Pole vzťahu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.js:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/link_preview.xml:0
#: code:addons/mail/static/src/core/common/message_reaction_menu.xml:0
#, python-format
msgid "Remove"
msgstr "Odstrániť"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove Context Action"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__remove_followers
msgid "Remove Followers"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_blacklist_remove
msgid "Remove email from blacklist wizard"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Remove the contextual action to use this template on related documents"
msgstr ""
"Odstrániť kontextuálnu akciu pre použitie šablóny súvisiacich dokumentov"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#: code:addons/mail/static/src/core/web/follower_list.xml:0
#, python-format
msgid "Remove this follower"
msgstr "Odstániť tohto sledujúceho"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_value__field_info
msgid "Removed field information"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#, python-format
msgid "Rename"
msgstr "Premenovať"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__render_model
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_render_mixin__render_model
#: model:ir.model.fields,field_description:mail.field_mail_template__render_model
msgid "Rendering Model"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_composer_mixin.py:0
#, python-format
msgid ""
"Rendering of %(field_name)s is not possible as no counterpart on template."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_composer_mixin.py:0
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Rendering of %(field_name)s is not possible as not defined on template."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_player.xml:0
#, python-format
msgid "Repeat"
msgstr "Opakovať"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to_mode
msgid "Replies"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "Reply"
msgstr "Odpoveď"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template__reply_to
msgid "Reply To"
msgstr "Odpovedať "

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__reply_to
#: model:ir.model.fields,help:mail.field_mail_mail__reply_to
#: model:ir.model.fields,help:mail.field_mail_message__reply_to
msgid ""
"Reply email address. Setting the reply_to bypasses the automatic thread "
"creation."
msgstr ""
"Emailová adresa pre odpoveď. Nastavením reply_to vypnete automatické "
"vytváranie vlákien."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_message__reply_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__reply_to
msgid "Reply-To"
msgstr "Odpoveď-pre"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Reply-to Address"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#, python-format
msgid "Replying to"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Report"
msgstr "Report"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__request_partner_id
msgid "Requesting Partner"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_partner_view_form
msgid "Resend"
msgstr "Znovu odoslať"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_partner_action
msgid "Resend Email"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend_wizard_id
msgid "Resend wizard"
msgstr "Sprievodca opätovným zaslaním"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Reset Confirmation"
msgstr "Resetovať povrdenie"

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_reset_action
msgid "Reset Mail Template"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_template_reset_view_form
msgid "Reset Template"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "Resetting Your Password"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_id
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_id
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__res_domain_user_id
msgid "Responsible"
msgstr "Zodpovedný"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_mixin__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_partner__activity_user_id
#: model:ir.model.fields,field_description:mail.field_res_users__activity_user_id
msgid "Responsible User"
msgstr "Zodpovedný užívateľ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__restrict_template_rendering
msgid "Restrict Template Rendering"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Restrict mail templates edition and QWEB placeholders usage."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__restricted_attachment_count
msgid "Restricted attachments"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__source_lang
msgid "Result of the language detection based on its content."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "Retry"
msgstr "Skúsiť znova"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Revert"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Review All Templates"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_content
msgid "Rich-text Contents"
msgstr "Rich-text obsah"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__body_html
msgid "Rich-text/HTML message"
msgstr "Rich-text/HTML správa"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__rtc_inviting_session_id
msgid "Ringing session"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__rtc_session_ids
msgid "Rtc Session"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__sfu_server_url
msgid "SFU Server URL"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__sfu_server_key
msgid "SFU Server key"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "SFU server"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "SMTP Server"
msgstr "SMTP server"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "SSL"
msgstr "SSL"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__is_ssl
msgid "SSL/TLS"
msgstr "SSL/TLS"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__user_id
#: model:ir.model.fields,field_description:mail.field_res_users__user_id
msgid "Salesperson"
msgstr "Obchodník"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
msgid "Save"
msgstr "Uložiť"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save Template"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Save as a new template"
msgstr "Uložiť ako novú šablónu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#, python-format
msgid "Save editing"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_discuss_gif_favorite
msgid "Save favorite GIF from Tenor API"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__delay_count
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
msgid "Schedule"
msgstr "Rozvrh"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
msgid "Schedule & Mark as Done"
msgstr ""

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/web/activity_service.js:0
#: code:addons/mail/static/src/core/web/activity_service.js:0
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "Schedule Activity"
msgstr "Naplánuj aktivity"

#. module: mail
#. odoo-javascript
#. odoo-python
#: code:addons/mail/static/src/core/web/activity_service.js:0
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "Schedule Activity On Selected Records"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#, python-format
msgid "Schedule activities to help you get things done."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
#, python-format
msgid "Schedule activity"
msgstr "Naplánuj aktivity"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity.py:0
#, python-format
msgid "Schedule an Activity"
msgstr "Naplánuj aktivitu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#, python-format
msgid "Schedule an activity"
msgstr "Naplánuj aktivitu"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#, python-format
msgid "Schedule an activity on selected records"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__scheduled_date
msgid "Scheduled Date"
msgstr "Naplánovaný dátum"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_form
msgid "Scheduled Message"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_message_schedule_action
#: model:ir.model,name:mail.model_mail_message_schedule
#: model:ir.ui.menu,name:mail.mail_message_schedule_menu
#: model_terms:ir.ui.view,arch_db:mail.mail_message_schedule_view_search
msgid "Scheduled Messages"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__scheduled_date
#: model:ir.model.fields,field_description:mail.field_mail_message_schedule__scheduled_datetime
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Scheduled Send Date"
msgstr "Naplánovaný dátum zaslania"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__script
msgid "Script"
msgstr "Skript"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "Search"
msgstr "Vyhľadávanie"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_search
msgid "Search Alias"
msgstr "Alias vyhľadávania"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_search
msgid "Search Groups"
msgstr "Skupiny vyľadávania"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Search Incoming Mail Servers"
msgstr "Vyhľadať server prichádzajúcej pošty"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#: code:addons/mail/static/src/core/common/thread_actions.js:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Search Messages"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_search
msgid "Search RTC session"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "Search button"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "Search for a GIF"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "Search for a channel..."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "Search for a user..."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "Search in progress"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.js:0
#, python-format
msgid "Search messages"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/mention_list.js:0
#: code:addons/mail/static/src/core/web/mention_list.xml:0
#: code:addons/mail/static/src/core/web/mention_list.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "Search..."
msgstr "Hľadať..."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_controller.js:0
#, python-format
msgid "Search: %s"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Security Update: Email Changed"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Security Update: Login Changed"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Security Update: Password Changed"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "See Error Details"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "See all pinned messages."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Select a language"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "Select a user..."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Select the content filter used for filtering GIFs"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid "Selected users:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.js:0
#: code:addons/mail/static/src/core/common/composer.xml:0
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#, python-format
msgid "Send"
msgstr "Poslať"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_resend_message_view_form
msgid "Send & close"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__state__mail_post
msgid "Send Email"
msgstr "Poslať email"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__mail_post_method
#: model:ir.model.fields,field_description:mail.field_ir_cron__mail_post_method
msgid "Send Email As"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Send Mail (%s)"
msgstr "Odoslať Mail (%s)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_wizard_invite__notify
msgid "Send Notification"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
#, python-format
msgid "Send Now"
msgstr "Odoslať teraz"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/composer_patch.js:0
#, python-format
msgid "Send a message to followers…"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Send and receive emails through your Gmail account."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Send and receive emails through your Outlook account."
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.action_partner_mass_mail
msgid "Send email"
msgstr "Poslať mail"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__force_send
msgid "Send mailing or notifications directly"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "Send message"
msgstr "Pošli správu"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__email_from
msgid "Sender address"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__email_from
msgid ""
"Sender address (placeholders may be used here). If not set, the default "
"value will be the author's email alias if configured, or email address."
msgstr ""
"Adresa odosielateľa (zástupný prvok tu môže byť použitý). Pokiaľ nie je "
"nastavené, prednastavená hodnota bude autorov emailový alias (pokiaľ je "
"nastavený), alebo emailová adresa."

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_resend_message_action
msgid "Sending Failures"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/notification_model.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__state__sent
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__notification_status__pending
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
#, python-format
msgid "Sent"
msgstr "Poslané"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__sequence
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__sequence
#: model:ir.model.fields,field_description:mail.field_mail_alias_domain__sequence
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__sequence
msgid "Sequence"
msgstr "Postupnosť"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Server & Login"
msgstr "Server & Prihlásenie"

#. module: mail
#: model:ir.model,name:mail.model_ir_actions_server
msgid "Server Action"
msgstr "Serverová akcia"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Server Information"
msgstr "Informácia servera"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server
msgid "Server Name"
msgstr "Názov servera"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__priority
msgid "Server Priority"
msgstr "Priorita servera"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server_type
msgid "Server Type"
msgstr "Typ servera"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__server_type_info
msgid "Server Type Info"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_upload_service.js:0
#, python-format
msgid "Server error"
msgstr "Chyba servera"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid ""
"Server replied with following exception:\n"
" %s"
msgstr ""
"Server odpovedal s nasledujúcou výnimkou:\n"
" %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Server type IMAP."
msgstr "Typ serveru IMAP."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_search
msgid "Server type POP."
msgstr "Typ serveru POP."

#. module: mail
#: model:ir.model.fields,help:mail.field_discuss_channel__active
msgid "Set active to false to hide the channel without removing it."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "Settings"
msgstr "Nastavenia"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sfu_channel_uuid
msgid "Sfu Channel Uuid"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__sfu_server_url
msgid "Sfu Server Url"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Share Screen"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__description
#: model:ir.model.fields,field_description:mail.field_mail_message__description
msgid "Short description"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_shortcode_view_tree
msgid "Shortcodes"
msgstr "Skratky"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__source
msgid "Shortcut"
msgstr "Skratka"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_shortcode__source
msgid ""
"Shortcut that will automatically be substituted with longer content in your "
"messages. Type ':' followed by the name of your shortcut (e.g. :hello) to "
"use in your messages."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__target_lang
msgid ""
"Shortened language code used as the target for the translation request."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Show Attachments"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
#, python-format
msgid "Show Call Settings"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.js:0
#, python-format
msgid "Show Followers"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Show Member List"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_commands.js:0
#, python-format
msgid "Show a helper message"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_button.js:0
#, python-format
msgid "Show activities"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "Show all recipients"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Show all records which has next action date is before today"
msgstr "Zobraz všetky záznamy, ktorých následná aktivita je pred dnešným dňom"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient_list.xml:0
#, python-format
msgid "Show less"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/suggested_recipient_list.xml:0
#, python-format
msgid "Show more"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.xml:0
#, python-format
msgid "Show sidebar"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Show video participants only"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid "Showing"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__ref_ir_act_window
msgid "Sidebar action"
msgstr "Akcia postranného panelu"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__ref_ir_act_window
msgid ""
"Sidebar action to make this template available on records of the related "
"document model"
msgstr ""
"Akcia postranného panelu spraví túto šablónu dostupnú záznamom súvisiaceho "
"modelu dokumentu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_site_name
msgid "Site name"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "So uhh... maybe go favorite some GIFs?"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
msgid "Source"
msgstr "Zdroj"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__source_lang
msgid "Source Language"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_user_type__specific
msgid "Specific User"
msgstr "Špecifický užívateľ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_plan__res_model
#: model:ir.model.fields,help:mail.field_mail_activity_plan_template__res_model
#: model:ir.model.fields,help:mail.field_mail_activity_type__res_model
msgid ""
"Specify a model if the activity should be specific to a model and not "
"available when managing activities for other models."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/messaging_service.js:0
#: model:ir.model.fields,field_description:mail.field_mail_mail__starred
#: model:ir.model.fields,field_description:mail.field_mail_message__starred
#, python-format
msgid "Starred"
msgstr "Obľúbené"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__starred_message_ids
#: model:ir.model.fields,field_description:mail.field_res_users__starred_message_ids
msgid "Starred Message"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/thread_actions.js:0
#, python-format
msgid "Start a Call"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.js:0
#, python-format
msgid "Start a Conversation"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss_app_model.js:0
#: code:addons/mail/static/src/discuss/core/web/messaging_menu_patch.xml:0
#, python-format
msgid "Start a conversation"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/web/discuss_sidebar_start_meeting.xml:0
#, python-format
msgid "Start a meeting"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__state
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_rtc_session_view_form
msgid "State"
msgstr "Štát"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__state
#: model:ir.model.fields,field_description:mail.field_mail_mail__state
#: model:ir.model.fields,field_description:mail.field_mail_notification__notification_status
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_view_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Status"
msgstr "Stav"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_state
#: model:ir.model.fields,help:mail.field_res_partner__activity_state
#: model:ir.model.fields,help:mail.field_res_users__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Status založený na aktivitách\n"
"Zmeškané: dátum už vypršal\n"
"Dnes: dátum aktivity je dnes\n"
"Plán: budúce aktivity"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_tracking_duration_mixin__duration_tracking
msgid "Status time"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/fields/statusbar_duration/statusbar_duration_field.js:0
#, python-format
msgid "Status with time"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan__steps_count
msgid "Steps Count"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_actions.js:0
#, python-format
msgid "Stop Adding Users"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.xml:0
#, python-format
msgid "Stop Recording"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.js:0
#, python-format
msgid "Stop Sharing Screen"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Stop camera"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#, python-format
msgid "Stop replying"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__reply_to_mode__update
msgid "Store email and replies in the chatter of each record"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_mail_link_preview
msgid "Store link preview data"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_users_settings__push_to_talk_key
msgid ""
"String formatted to represent a key with modifiers following this pattern: "
"shift.ctrl.alt.key, e.g: truthy.1.true.b"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_translation__body
msgid "String received from the translation request."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_composer_mixin__subject
#: model:ir.model.fields,field_description:mail.field_mail_mail__subject
#: model:ir.model.fields,field_description:mail.field_mail_message__subject
#: model:ir.model.fields,field_description:mail.field_mail_template__subject
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__subject
msgid "Subject"
msgstr "Predmet"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__subject
msgid "Subject (placeholders may be used here)"
msgstr "Subjekt (zástupné objekty môžu byť použité)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#, python-format
msgid "Subject:"
msgstr "Subjekt:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__mail_post_autofollow
#: model:ir.model.fields,field_description:mail.field_ir_cron__mail_post_autofollow
msgid "Subscribe Recipients"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_shortcode__substitution
msgid "Substitution"
msgstr "Náhrada"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_followers__subtype_ids
#: model:ir.model.fields,field_description:mail.field_mail_mail__subtype_id
#: model:ir.model.fields,field_description:mail.field_mail_message__subtype_id
#: model_terms:ir.ui.view,arch_db:mail.view_message_subtype_tree
msgid "Subtype"
msgstr "Podtyp"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_message_subtype
#: model:ir.ui.menu,name:mail.menu_message_subtype
msgid "Subtypes"
msgstr "Podtypy"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__suggest
msgid "Suggest Next Activity"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__suggested_next_type_ids
msgid "Suggest these activities once the current one is marked as done."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity__summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_plan_template__summary
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__summary
msgid "Summary"
msgstr "Zhrnutie"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "Summary:"
msgstr "Zhrnutie:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__module_google_gmail
msgid "Support Gmail Authentication"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__module_microsoft_outlook
msgid "Support Outlook Authentication"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_config_parameter
msgid "System Parameter"
msgstr "Systémový parameter"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.js:0
#: model:ir.model.fields.selection,name:mail.selection__mail_compose_message__message_type__notification
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__notification
#, python-format
msgid "System notification"
msgstr "Systémové upozornenia"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__target_lang
msgid "Target Language"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__model_id
msgid "Targeted model"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_partner__vat
#: model:ir.model.fields,field_description:mail.field_res_users__vat
msgid "Tax ID"
msgstr "DIČ"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Technical Settings"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__initial_res_model
msgid ""
"Technical field to keep track of the model at the start of editing to "
"support UX related behaviour"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_reset__template_ids
msgid "Template"
msgstr "Šablóna"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__template_category
msgid "Template Category"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__template_fs
#: model:ir.model.fields,field_description:mail.field_template_reset_mixin__template_fs
msgid "Template Filename"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.mail_template_preview_action
msgid "Template Preview"
msgstr "Náhľad šablóny"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__lang
msgid "Template Preview Language"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_template_reset_mixin
msgid "Template Reset Mixin"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_compose_message.py:0
#, python-format
msgid "Template creation from composer requires a valid model."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__description
msgid "Template description"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Template rendering should be called only using on a list of IDs; received "
"%(res_ids)r instead."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Template rendering supports only inline_template, qweb, or qweb_view (view "
"or raw); received %(engine)s instead."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.email_template_tree
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
#: model_terms:ir.ui.view,arch_db:mail.view_email_template_search
msgid "Templates"
msgstr "Šablóny"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_api_key
msgid "Tenor API key"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF API key"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF content filter"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Tenor GIF limits"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_gif_limit
msgid "Tenor Gif Limit"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__tenor_content_filter
msgid "Tenor content filter"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_email_server_form
msgid "Test & Confirm"
msgstr "Otestovať & Potvrdiť"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_template_preview_view_form
msgid "Test Record:"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__body_html
msgid "Text Contents"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The"
msgstr "  "

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "The 'Due Date In' value can't be negative."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call.js:0
#, python-format
msgid "The Fullscreen mode was denied by the browser"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__vat
#: model:ir.model.fields,help:mail.field_res_users__vat
msgid ""
"The Tax Identification Number. Values here will be validated based on the "
"country format. You can use '/' to indicate that the partner is not subject "
"to tax."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "The activity cannot be launched:"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
#, python-format
msgid ""
"The activity type \"%(activity_type_name)s\" is not compatible with the plan"
" \"%(plan_name)s\" because it is limited to the model "
"\"%(activity_type_model)s\"."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid ""
"The attachment %s does not exist or you do not have the rights to access it."
msgstr "Príloha %s neexistuje alebo nemáte k nej prístupové práva."

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_attachment.py:0
#, python-format
msgid "The attachment %s does not exist."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.js:0
#, python-format
msgid "The avatar has been updated!"
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_uuid_unique
msgid "The channel UUID must be unique"
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_channel_type_not_null
msgid "The channel type cannot be empty"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__can_write
msgid "The current user can edit the template."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
#, python-format
msgid "The duration of voice messages is limited to 1 minute."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "The email sent to"
msgstr "Mail odoslaný na adresu"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_mail_partner_device_endpoint_unique
msgid "The endpoint must be unique !"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/template_reset_mixin.py:0
#, python-format
msgid ""
"The following email templates could not be reset because their related source files could not be found:\n"
"- %s"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_partner__user_id
#: model:ir.model.fields,help:mail.field_res_users__user_id
msgid "The internal user in charge of this contact."
msgstr "Interný užívateľ, ktorý má na starosti komunikáciu s týmto kontaktom."

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
msgid "The last message received on this alias has caused an error."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid "The message below could not be accepted by the address"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"                 Only %(contact_description)s are allowed to contact it.<br /><br />\n"
"                 Please make sure you are using the correct address or contact us at %(default_email)s instead."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"The message below could not be accepted by the address %(alias_display_name)s.\n"
"Please try again later or contact %(company_name)s instead."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Model (Odoo druh dokumentu), ktorému odpovedá tento alias. Každý "
"prichádzajúci email ktorý neodpovie na existujúci záznam, spôsobí vytvorenie"
" nového záznamu tohto modelu (napr požiadavku projektu)"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_alias__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin__alias_name
#: model:ir.model.fields,help:mail.field_mail_alias_mixin_optional__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"Meno emailového alias, napr. \"práce\", ak chcete zachytiť emaily pre "
"<<EMAIL>>"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "The plan \"%(plan_name)s\" cannot be launched:"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "The plan \"%(plan_name)s\" has been started"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template_preview__scheduled_date
msgid "The queue manager will send the email after the date"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_activity_schedule.py:0
#, python-format
msgid "The records must belong to the same company."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid ""
"The requested operation cannot be completed due to security restrictions. Please contact your system administrator.\n"
"\n"
"(Document type: %s, Operation: %s)"
msgstr ""
"Vyžiadaná operácia nemôže byť dokončená kôli bezpečnostným opatreniam. Prosím konaktujte vášho systémového administrátora.\n"
"\n"
"(Typ dokumentu: %s, operácia: %s)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid "The server \"%s\" cannot be used because it is archived."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/follower_subtype_dialog.js:0
#, python-format
msgid "The subscription preferences were successfully applied."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__user_id
msgid "The template belongs to this user"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__preview
#: model:ir.model.fields,help:mail.field_mail_message__preview
msgid "The text-only beginning of the body used as email preview."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "There are no messages in this conversation."
msgstr ""

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_channel_rtc_session_channel_member_unique
msgid "There can only be one rtc session per channel member"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "This"
msgstr "Tento"

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "This action can only be done on a mail thread models"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/ir_actions_server.py:0
#, python-format
msgid "This action cannot be done on transient models."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.xml:0
#, python-format
msgid "This action will send an email."
msgstr "Táto akcia zašle email."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#, python-format
msgid "This channel doesn't have any attachments."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
#, python-format
msgid "This channel doesn't have any pinned messages."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/attachment_panel.xml:0
#, python-format
msgid "This conversation doesn't have any attachments."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/pinned_messages_panel.js:0
#, python-format
msgid "This conversation doesn't have any pinned messages."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_form_inherit_mail
msgid "This email is blacklisted for mass mailings. Click to unblacklist."
msgstr ""
"Tento e-mail je na čiernej listine pre hromadné zasielanie pošty. Kliknutím "
"zrušíte zoznam zakázaných položiek."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_blacklist__email
msgid "This field is case insensitive."
msgstr "Toto pole nerozlišuje veľkosť písmen."

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__description
msgid "This field is used for internal description of the template's usage."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_thread_blacklist__email_normalized
#: model:ir.model.fields,help:mail.field_res_partner__email_normalized
#: model:ir.model.fields,help:mail.field_res_users__email_normalized
msgid ""
"This field is used to search on email address as the primary email field can"
" contain more than strictly an email address."
msgstr ""
"Toto pole sa používa na vyhľadávanie emailovej adresy, pretože emailové pole"
" môže obsahovať viac ako len striktne emailovú adresu."

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_config_settings.py:0
#, python-format
msgid "This layout seems to no longer exist."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__auto_delete
#: model:ir.model.fields,help:mail.field_mail_mail__auto_delete
#: model:ir.model.fields,help:mail.field_mail_template__auto_delete
msgid ""
"This option permanently removes any track of email after it's been sent, "
"including from the Technical menu in the Settings, in order to preserve "
"storage space of your Odoo database."
msgstr ""
"Táto možnosť natrvalo odstráni všetky stopy e-mailov po ich odoslaní, a to "
"aj z ponuky technické v ponuke nastavenia, aby sa zachoval úložný priestor "
"vašej databázy Odoo."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/activity_exception/activity_exception.xml:0
#, python-format
msgid "This record has an exception activity."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_render_mixin.py:0
#, python-format
msgid ""
"Those values are not supported as options when rendering: %(param_names)s"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"Those values are not supported when posting or notifying: %(param_names)s"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_search
msgid "Thread"
msgstr "Vlákno"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/chat_window.xml:0
#: code:addons/mail/static/src/core/common/discuss.xml:0
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "Thread Image"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__model_is_thread
msgid "Thread-Enabled"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_guest__timezone
msgid "Timezone"
msgstr "Časová zóna"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_summary
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_summary
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_title
msgid "Title"
msgstr "Titul"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__email_to
#: model:ir.model.fields,field_description:mail.field_mail_template_preview__email_to
msgid "To"
msgstr "Do"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_template__email_to
msgid "To (Emails)"
msgstr "Komu (emaily)"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__recipient_ids
#: model:ir.model.fields,field_description:mail.field_mail_template__partner_to
msgid "To (Partners)"
msgstr "Komu (partneri)"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/chat_window_patch.xml:0
#, python-format
msgid "To :"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "To peer:"
msgstr ""

#. module: mail
#: model:mail.activity.type,name:mail.mail_activity_data_todo
msgid "To-Do"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/chatter.xml:0
#, python-format
msgid "To:"
msgstr "Komu:"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_model.js:0
#: code:addons/mail/static/src/core/web/activity_list_popover.xml:0
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#: code:addons/mail/static/src/core/web/activity_menu.xml:0
#: model:ir.model.fields.selection,name:mail.selection__account_journal__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__account_move__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__account_payment__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__crm_lead__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_booth__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_event__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_registration__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_sponsor__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__event_track__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_contract__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__fleet_vehicle_log_services__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_applicant__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_contract__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_employee__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_expense__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_expense_sheet__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_leave__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__hr_leave_allocation__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__lunch_supplier__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity__state__today
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_mixin__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mailing_mailing__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__maintenance_equipment__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__maintenance_request__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mrp_production__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__mrp_unbuild__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__pos_session__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__product_pricelist__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__product_product__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__product_template__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__project_project__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__project_task__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__project_update__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__purchase_order__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__purchase_requisition__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__repair_order__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__res_partner_bank__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__sale_order__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__slide_channel__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_landed_cost__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_lot__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_picking__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__stock_picking_batch__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__survey_survey__activity_state__today
#: model:ir.model.fields.selection,name:mail.selection__survey_user_input__activity_state__today
#, python-format
msgid "Today"
msgstr "Dnes"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_search
#: model_terms:ir.ui.view,arch_db:mail.res_partner_view_search_inherit_mail
msgid "Today Activities"
msgstr "Dnešné aktivity"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Today:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#, python-format
msgid "Tomorrow"
msgstr "Zajtra"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Tomorrow:"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "Topics discussed in this group..."
msgstr "Témy diskutované v tejto skupine..."

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_subtype__track_recipients
msgid "Track Recipients"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,help:mail.field_mail_message__tracking_value_ids
msgid ""
"Tracked values are stored in a separate model. This field allow to "
"reconstruct the tracking and to generate statistics on the model."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_message_view_form
msgid "Tracking"
msgstr "Sledovanie"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tracking_value_tree
msgid "Tracking Value"
msgstr "Sledovaná hodnota"

#. module: mail
#: model:ir.actions.act_window,name:mail.action_view_mail_tracking_value
#: model:ir.ui.menu,name:mail.menu_mail_tracking_value
msgid "Tracking Values"
msgstr "Sledované hodnoty"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__tracking_value_ids
#: model:ir.model.fields,field_description:mail.field_mail_message__tracking_value_ids
msgid "Tracking values"
msgstr "Sledované hodnoty"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "Translate"
msgstr "Preložiť"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_message_translation__body
msgid "Translation Body"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#, python-format
msgid "Translation Failure"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_type__triggered_next_type_id
msgid "Trigger"
msgstr "Spúštač"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__chaining_type__trigger
msgid "Trigger Next Activity"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_resend_partner__resend
msgid "Try Again"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Turn camera on"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_token
msgid "Twilio Account Auth Token"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__twilio_account_sid
msgid "Twilio Account SID"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__state
#: model:ir.model.fields,field_description:mail.field_ir_cron__state
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__message_type
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__server_type
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__og_type
#: model:ir.model.fields,field_description:mail.field_mail_mail__message_type
#: model:ir.model.fields,field_description:mail.field_mail_message__message_type
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_tree
msgid "Type"
msgstr "Typ"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_from
msgid "Type of delay"
msgstr "Typ oneskorenia"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__state
#: model:ir.model.fields,help:mail.field_ir_cron__state
msgid ""
"Type of server action. The following values are available:\n"
"- 'Update a Record': update the values of a record\n"
"- 'Create Activity': create an activity (Discuss)\n"
"- 'Send Email': post a message, a note or send an email (Discuss)\n"
"- 'Send SMS': send SMS, log them on documents (SMS)- 'Add/Remove Followers': add or remove followers to a record (Discuss)\n"
"- 'Create Record': create a new record with new values\n"
"- 'Execute Code': a block of Python code that will be executed\n"
"- 'Send Webhook Notification': send a POST request to an external system, also known as a Webhook\n"
"- 'Execute Existing Actions': define an action that triggers several other server actions\n"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_mixin__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_partner__activity_exception_decoration
#: model:ir.model.fields,help:mail.field_res_users__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Typ výnimočnej aktivity v zázname."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid "Type the name of a person"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__uri
msgid "URI"
msgstr "URI"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_link_preview__source_url
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "URL"
msgstr "URL"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel__uuid
msgid "UUID"
msgstr "UUID"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_mail.py:0
#, python-format
msgid "Unable to connect to SMTP Server"
msgstr "Nemožno sa pripojiť na SMTP server"

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_wizard_invite.py:0
#, python-format
msgid "Unable to post message, please configure the sender's email address."
msgstr "Nemožno odoslať email, prosím nastavte emailovú adresu odosielateľa."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Unable to send message, please configure the sender's email address."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "Unassign"
msgstr "Zrušiť priradenie"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#: code:addons/mail/static/src/views/web/fields/assign_user_command_hook.js:0
#, python-format
msgid "Unassign from me"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_view_form
msgid "Unblacklist"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/wizard/mail_blacklist_remove.py:0
#, python-format
msgid "Unblock Reason: %(reason)s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Undeafen"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#: code:addons/mail/static/src/core/web/chatter.js:0
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_layout
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
#, python-format
msgid "Unfollow"
msgstr "Prestať sledovať"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_activity_type__delay_unit
msgid "Unit of delay"
msgstr "Jednotka oneskorenia"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_tracking_value.py:0
#, python-format
msgid "Unknown"
msgstr "Neznáme"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_notification.py:0
#: model:ir.model.fields.selection,name:mail.selection__mail_mail__failure_type__unknown
#: model:ir.model.fields.selection,name:mail.selection__mail_notification__failure_type__unknown
#, python-format
msgid "Unknown error"
msgstr "Neznáma chyba"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_action_list.xml:0
#, python-format
msgid "Unmute"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.xml:0
#, python-format
msgid "Unmute Channel"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_card_list.xml:0
#: code:addons/mail/static/src/discuss/message_pin/common/message_patch.js:0
#, python-format
msgid "Unpin"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "Unpin Conversation"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid "Unpin Message"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_discuss_channel_member__message_unread_counter
msgid "Unread Messages Counter"
msgstr "Počítadlo neprečítaných správ"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_message_search
msgid "Unread messages"
msgstr "Neprečítané správy"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_mail__unrestricted_attachment_ids
msgid "Unrestricted Attachments"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/thread_actions.js:0
#, python-format
msgid "Unstar all"
msgstr "Odstrániť všetko z obľúbených"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "Unsupported report type %s found."
msgstr "Našiel sa nepodporovaný typ reportu %s."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/notification_settings.js:0
#, python-format
msgid "Until "
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/thread_model_patch.js:0
#, python-format
msgid "Until I turn it back on"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Update Mail Layout"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/discuss.xml:0
#, python-format
msgid "Upload Avatar"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__category__upload_file
#: model:mail.activity.type,name:mail.mail_activity_data_upload_document
msgid "Upload Document"
msgstr "Nahrať dokument"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
#, python-format
msgid "Upload File"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.xml:0
#, python-format
msgid "Upload file"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Upload:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#, python-format
msgid "Uploaded"
msgstr "Nahrané"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#: code:addons/mail/static/src/core/common/attachment_list.xml:0
#, python-format
msgid "Uploading"
msgstr "Nahrávam"

#. module: mail
#: model:ir.model.fields,help:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,help:mail.field_ir_cron__activity_user_type
msgid ""
"Use 'Specific User' to always assign the same user on the next activity. Use"
" 'Dynamic User' to specify the field name of the user to choose on the "
"record."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__external_email_server_default
msgid "Use Custom Email Servers"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_config_settings__use_twilio_rtc_servers
msgid "Use Twilio ICE servers"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use a Gmail Server"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/fetchmail.py:0
#, python-format
msgid "Use a local script to fetch your emails and create new records."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use an Outlook Server"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "Use different domains for your mail aliases"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_activity_schedule__is_batch_mode
msgid "Use in batch"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "Use message_notify to send a notification to an user."
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_compose_message__template_id
msgid "Use template"
msgstr "Použiť šablónu"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__use_push_to_talk
msgid "Use the push to talk feature"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "Used In"
msgstr "Použité v"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_compose_message__res_domain_user_id
msgid "Used as context used to evaluate composer domain"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_mail__message_type
#: model:ir.model.fields,help:mail.field_mail_message__message_type
msgid ""
"Used to categorize message generator\n"
"'email': generated by an incoming email e.g. mailgateway\n"
"'comment': generated by user input e.g. through discuss or composer\n"
"'email_outgoing': generated by a mailing\n"
"'notification': generated by system e.g. tracking messages\n"
"'auto_comment': generated by automated notification mechanism e.g. acknowledgment\n"
"'user_notification': generated for a specific recipient"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_tracking_value__currency_id
msgid "Used to display the currency when tracking monetary values"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__sequence
msgid "Used to order subtypes."
msgstr "Požité na zoradenie subtypov."

#. module: mail
#: model:ir.model,name:mail.model_res_users
#: model:ir.model.fields,field_description:mail.field_mail_template__user_id
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#: model_terms:ir.ui.view,arch_db:mail.view_mail_tree
msgid "User"
msgstr "Užívateľ"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_field_name
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_field_name
msgid "User Field"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_bus_presence
msgid "User Presence"
msgstr "Užívateľova prítomnosť"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__user_setting_id
msgid "User Setting"
msgstr ""

#. module: mail
#: model:ir.actions.act_window,name:mail.res_users_settings_action
#: model:ir.model,name:mail.model_res_users_settings
#: model:ir.ui.menu,name:mail.res_users_settings_menu
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_tree
msgid "User Settings"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_res_users_settings_volumes
msgid "User Settings Volumes"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_message__message_type__user_notification
msgid "User Specific Notification"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_server__activity_user_type
#: model:ir.model.fields,field_description:mail.field_ir_cron__activity_user_type
msgid "User Type"
msgstr "Typ užívateľa"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "User is a bot"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "User is idle"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "User is offline"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/im_status.xml:0
#: code:addons/mail/static/src/discuss/web/avatar_card/avatar_card_popover.xml:0
#, python-format
msgid "User is online"
msgstr "Používateľ je online"

#. module: mail
#: model:ir.model.constraint,message:mail.constraint_discuss_gif_favorite_user_gif_favorite
msgid "User should not have duplicated favorite GIF"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "User:"
msgstr "Používateľ:"

#. module: mail
#: model:ir.model.fields,field_description:mail.field_fetchmail_server__user
#: model:ir.model.fields,field_description:mail.field_mail_ice_server__username
msgid "Username"
msgstr "Používateľské meno"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "Users in this channel: %(members)s %(dots)s and you."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__restrict_template_rendering
msgid ""
"Users will still be able to render templates.\n"
"However only Mail Template Editors will be able to create new dynamic templates or modify existing ones."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid ""
"Using your own email server is required to send/receive emails in Community "
"and Enterprise versions. Online users already benefit from a ready-to-use "
"email server (@mycompany.odoo.com)."
msgstr ""
"Použitie vlastného emailového servera je nevyhnutné pre príjem/odosielanie "
"mailov vo verzii Community a Enterprise. Užívatelia online profitujú z "
"pripraveného servera (@moja_spolocnost.odoo.com)."

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_alias__alias_status__valid
msgid "Valid"
msgstr "Platný"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"Value %(allowed_domains)s for `mail.catchall.domain.allowed` cannot be validated.\n"
"It should be a comma separated list of domains e.g. example.com,example.org."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Video Settings"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "Video player:"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#: model:ir.model,name:mail.model_ir_ui_view
#, python-format
msgid "View"
msgstr "Náhľad"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "View %s"
msgstr "Pohľad %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_actions.js:0
#, python-format
msgid "View Reactions"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_actions_act_window_view__view_mode
#: model:ir.model.fields,field_description:mail.field_ir_ui_view__type
msgid "View Type"
msgstr "Typ zobrazenia"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.xml:0
#, python-format
msgid "View or join channels"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_ir_attachment__voice_ids
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Voice"
msgstr "Hlas"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.xml:0
#, python-format
msgid "Voice Message"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.js:0
#, python-format
msgid "Voice Settings"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "Voice detection threshold"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/voice_message/common/voice_recorder.js:0
#, python-format
msgid "Voice recording stopped"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings_volumes__volume
msgid "Volume"
msgstr "Objem"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "Volume per partner"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_res_users_settings__volume_settings_ids
msgid "Volumes of other partners"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/picker_content_patch.xml:0
#, python-format
msgid ""
"Want to spice up your conversations with GIFs? Activate the feature in the "
"settings!"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_button.js:0
#: code:addons/mail/static/src/views/web/fields/list_activity/list_activity.js:0
#, python-format
msgid "Warning"
msgstr "Varovanie"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"We could not create alias %(alias_name)s because domain "
"%(alias_domain_name)s belongs to company %(alias_company_names)s while the "
"owner document belongs to company %(company_name)s."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"We could not create alias %(alias_name)s because domain "
"%(alias_domain_name)s belongs to company %(alias_company_names)s while the "
"target document belongs to company %(company_name)s."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__ir_actions_server__activity_date_deadline_range_type__weeks
msgid "Weeks"
msgstr "Týždne"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Welcome to MyCompany!"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid ""
"Well, nothing lasts forever, but are you sure you want to unpin this "
"message?"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "What's your name?"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_plan_template.py:0
#, python-format
msgid ""
"When selecting \"Default user\" assignment, you must specify a responsible."
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__original
msgid ""
"Whether a full original copy of each email should be kept for reference and "
"attached to each processed message. This will usually double the size of "
"your message database."
msgstr ""
"Či sa má úplná originálna kópia každého e-mailu uchovať pre referenciu a "
"priložiť ku každej spracovanej správe. Zvyčajne to zdvojnásobí veľkosť "
"databázy správ."

#. module: mail
#: model:ir.model.fields,help:mail.field_fetchmail_server__attach
msgid ""
"Whether attachments should be downloaded. If not enabled, incoming emails "
"will be stripped of any attachments before being processed"
msgstr ""
"Či by sa mali sťahovať prílohy. Ak to nie je povolené, prichádzajúce e-maily"
" budú pred spracovaním zbavené všetkých príloh"

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_message_subtype__track_recipients
msgid "Whether to display all the recipients or only the important ones."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_markasdone_popover.xml:0
#, python-format
msgid "Write Feedback"
msgstr "Napíšte spätnú väzbu "

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "Write your message here..."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_message.py:0
#, python-format
msgid "Wrong operation name (%s)"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "YYYY-MM-DD HH:MM:SS"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid "Yeah, pin it!"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/message_patch.js:0
#, python-format
msgid "Yes"
msgstr "Áno"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid "Yes, remove it please"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_list_popover_item.js:0
#, python-format
msgid "Yesterday"
msgstr "Včera"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "Yesterday:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.js:0
#, python-format
msgid ""
"You are about to leave this group conversation and will no longer have "
"access to it unless you are invited again. Are you sure you want to "
"continue?"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "You are alone in a private conversation."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "You are alone in this channel."
msgstr "V tomto kanáli ste sám."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "You are in a private conversation with %(member_names)s."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "You are in channel %(bold_start)s#%(channel_name)s%(bold_end)s."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message_service.js:0
#, python-format
msgid "You are no longer following \"%(thread_name)s\"."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/attachment.py:0
#, python-format
msgid "You are not allowed to upload an attachment here."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/controllers/attachment.py:0
#, python-format
msgid "You are not allowed to upload attachments on this channel."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/discuss_sidebar_categories.js:0
#, python-format
msgid ""
"You are the administrator of this channel. Are you sure you want to leave?"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid ""
"You can mark any message as 'starred', and it shows up in this mailbox."
msgstr ""
"Akúkoľvek správu môžete označiť ako 'obľúbenú', tá sa potom zobrazí v tejto "
"mailovej schránke."

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid "You can not write on %(field_name)s."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_service.js:0
#, python-format
msgid "You can only chat with existing users."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread_service.js:0
#, python-format
msgid "You can only chat with partners that have a dedicated user."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.account_security_setting_update
msgid "You can safely ignore this message"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_type.py:0
#, python-format
msgid ""
"You cannot delete %(activity_names)s as it is required in various apps."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid ""
"You cannot delete those groups, as the Whole Company group is required by "
"other modules."
msgstr ""
"Nemôžete odstrániť túto skupinu, pretož skupina Celá spoločnosť je "
"vyžadovaná ostatnými modulmi."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_activity_type.py:0
#, python-format
msgid ""
"You cannot modify %(activities_names)s target model as they are are required"
" in various apps."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid ""
"You cannot use anything else than unaccented latin characters in the alias "
"address %(alias_name)s."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias_domain.py:0
#, python-format
msgid ""
"You cannot use anything else than unaccented latin characters in the domain "
"name %(domain_name)s."
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "You do not have access to"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread_blacklist.py:0
#, python-format
msgid ""
"You do not have the access right to unblacklist emails. Please contact your "
"administrator."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "You have been assigned to %s"
msgstr "Boli ste priradení k %s"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_user_assigned
msgid "You have been assigned to the"
msgstr "Boli ste pridelení k"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
#, python-format
msgid "You have been invited to #%s"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_mail_template__attachment_ids
msgid ""
"You may attach files to this template, to be added to all emails created "
"from this template"
msgstr ""
"Môžte priložiť súbory k tejto šablóne, tie potom budú pridané ku všetkým "
"emailom vytvorených z tejto šablóny"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_template.py:0
#, python-format
msgid "You may not define a template on an abstract model: %s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/message_pin/common/message_pin_service.js:0
#, python-format
msgid ""
"You sure want this message pinned to %(conversation)s forever and ever?"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
#, python-format
msgid "You unpinned your conversation with %s"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/discuss_core_common_service.js:0
#, python-format
msgid "You unsubscribed from %s."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/thread.xml:0
#, python-format
msgid "You're viewing older messages"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "You've been invited to a chat!"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "You've been invited to a meeting!"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/messaging_menu.xml:0
#, python-format
msgid "You:"
msgstr "Vy:"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_notification_light
msgid "Your"
msgstr "Vaša"

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid ""
"Your account email has been changed from %(old_email)s to %(new_email)s."
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Your account login has been updated"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/res_users.py:0
#, python-format
msgid "Your account password has been updated"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "Your browser does not support videoconference"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Your browser does not support voice activation"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/rtc_service.js:0
#, python-format
msgid "Your browser does not support webRTC."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/public/welcome_page.xml:0
#, python-format
msgid "Your name"
msgstr "Vaše meno"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/recipient_list.js:0
#, python-format
msgid "[%(name)s] (no email address)"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "addresses linked to registered partners"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__current_date
msgid "after completion date"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_from__previous_activity
msgid "after previous activity deadline"
msgstr "po termíne predchádzajúcej aktivity"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "alias %(name)s: %(error)s"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
msgid "attachment(s) of this email."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "available bitrate:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "back"
msgstr "späť"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_notification_limit_email
msgid ""
"because you have\n"
"                contacted it too many times in the last few minutes.\n"
"                <br/>\n"
"                Please try again later."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "by"
msgstr "pomocou"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "camera"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid ""
"cannot be processed. This address\n"
"    is used to collect replies and should not be used to directly contact"
msgstr ""
"nie je možné spracovať. Táto adresa\n"
"    sa používa na zber odpovedí a nemá sa používať na priame kontaktovanie spoločnosti."

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "channels"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "clock rate:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/search_messages_panel.xml:0
#, python-format
msgid "close"
msgstr "zatvoriť"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "codec:"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "created this channel."
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__days
msgid "days"
msgstr "dni"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "days overdue:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "days:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "deaf"
msgstr ""

#. module: mail
#: model:ir.model.fields,field_description:mail.field_mail_notification_web_push__user_device
msgid "devices"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "document"
msgstr "dokument"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/views/web/activity/activity_renderer.js:0
#: model_terms:ir.ui.view,arch_db:mail.message_activity_done
#, python-format
msgid "done"
msgstr "dokončené"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "down DTLS:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "down ICE:"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. \"Discuss proposal\""
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. \"Go over the offer and discuss details\""
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. \"Welcome email\""
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
#: model_terms:ir.ui.view,arch_db:mail.mail_compose_message_view_form_template_save
msgid "e.g. \"Welcome to MyCompany\" or \"Nice to meet you, {{ object.name }}\""
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"bounce\""
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"catchall\""
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"mycompany.com\""
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_domain_view_form
msgid "e.g. \"notifications\""
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. 65ea4f9e948b693N5156F350256bd152"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. ACd5543a0b450ar4c7t95f1b6e8a39t543"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "e.g. Contact"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_schedule_view_form
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_popup
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_view_form_without_record_access
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. Discuss proposal"
msgstr "napr. porediskutovať návrh"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_plan_view_form
msgid "e.g. Onboarding"
msgstr "napr. onboarding"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_activity_type_view_form
msgid "e.g. Schedule a meeting"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_alias_view_form
#: model_terms:ir.ui.view,arch_db:mail.res_config_settings_view_form
msgid "e.g. domain.com"
msgstr "napr. domain.com"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.discuss_channel_view_form
msgid "e.g. support"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.res_users_settings_view_form
msgid "e.g. true.true..f"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.view_server_action_form_template
msgid "e.g. user_id"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_compose_message_wizard_form
msgid "e.g: \"<EMAIL>\""
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity.xml:0
#, python-format
msgid "for"
msgstr "pre"

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been created from:"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_origin_link
msgid "has been modified from:"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.message_activity_assigned
msgid "has just assigned you the following activity:"
msgstr ""

#. module: mail
#: model:ir.model.fields,help:mail.field_res_config_settings__tenor_content_filter
msgid "https://developers.google.com/tenor/guides/content-filtering"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
#, python-format
msgid "incorrectly configured alias"
msgstr "nesprávne nakonfigurovaný alias"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
#, python-format
msgid "incorrectly configured alias (unknown reference record)"
msgstr "nesprávne nakonfigurovaný alias (neznáma referencia záznamu)"

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "invited %s to the channel"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "joined the channel"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel.py:0
#, python-format
msgid "left the channel"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "list"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#: code:addons/mail/static/src/discuss/gif_picker/common/gif_picker.xml:0
#, python-format
msgid "list-item"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "live"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_blacklist_remove_view_form
msgid "mail_blacklist_removal"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "media player Error"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "microphone"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "model %s does not accept document creation"
msgstr "model %s nepovoľuje  tvorbu dokumentu"

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__months
msgid "months"
msgstr "mesiace"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "ms"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "muted"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "new"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.js:0
#, python-format
msgid "no connection"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/relative_time.js:0
#, python-format
msgid "now"
msgstr "teraz"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/message.xml:0
#: model_terms:ir.ui.view,arch_db:mail.view_mail_form
#, python-format
msgid "on"
msgstr "na"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/common/composer.xml:0
#, python-format
msgid "on:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/core/web/activity_mail_template.xml:0
#, python-format
msgid "or"
msgstr "alebo"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_member_list.xml:0
#, python-format
msgid "other members."
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_participant_card.xml:0
#, python-format
msgid "raising hand"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"reply to missing document (%(model)s,%(thread)s), fall back on document "
"creation"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid ""
"reply to model %s that does not accept document update, fall back on "
"document creation"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
#, python-format
msgid "restricted to followers"
msgstr "obmedzené na odberateľov"

#. module: mail
#. odoo-python
#: code:addons/mail/models/models.py:0
#, python-format
msgid "restricted to known authors"
msgstr "obmedzené na známych autorov"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/common/channel_invitation.xml:0
#, python-format
msgid "results out of"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "screen"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "some specific addresses"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__stun
msgid "stun:"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "target model unspecified"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.mail_bounce_catchall
msgid "team."
msgstr "tím."

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "template"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#: code:addons/mail/static/src/discuss/call/common/call_settings.xml:0
#, python-format
msgid "toggle push-to-talk"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_ice_server__server_type__turn
msgid "turn:"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown error"
msgstr "neznáma chyba"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "unknown target model %s"
msgstr "neznámy cieľový model %s"

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "up DTLS:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/call/common/call_context_menu.xml:0
#, python-format
msgid "up ICE:"
msgstr ""

#. module: mail
#. odoo-javascript
#: code:addons/mail/static/src/discuss/core/web/command_palette.js:0
#, python-format
msgid "users"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_thread.py:0
#, python-format
msgid "view"
msgstr ""

#. module: mail
#: model:ir.model,name:mail.model_ir_websocket
msgid "websocket message handling"
msgstr ""

#. module: mail
#: model:ir.model.fields.selection,name:mail.selection__mail_activity_type__delay_unit__weeks
msgid "weeks"
msgstr "týždne"

#. module: mail
#. odoo-python
#: code:addons/mail/models/mail_alias.py:0
#, python-format
msgid "your alias"
msgstr ""

#. module: mail
#: model_terms:ir.ui.view,arch_db:mail.email_template_form
msgid "{{ object.partner_id.lang }}"
msgstr ""

#. module: mail
#. odoo-python
#: code:addons/mail/models/discuss/discuss_channel_member.py:0
#, python-format
msgid "“%(member_name)s” in “%(channel_name)s”"
msgstr ""
