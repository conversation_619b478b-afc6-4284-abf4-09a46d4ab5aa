# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* maintenance
# 
# Translators:
# <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>, 2024
# <AUTHOR> <EMAIL>, 2024
# <PERSON><PERSON><PERSON> <<EMAIL>>, 2025
# 
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 17.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-08-13 10:26+0000\n"
"PO-Revision-Date: 2023-10-26 23:09+0000\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>, 2025\n"
"Language-Team: Azerbaijani (https://app.transifex.com/odoo/teams/41243/az/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Language: az\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
msgid "<b>Category:</b>"
msgstr "<b>Kateqoriya:</b>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "<b>Model Number:</b>"
msgstr "<b>Modelin nömrəsi:</b>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
msgid "<b>Request to:</b>"
msgstr "<b>Sorğunun göndərildiyi şəxs:</b>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "<b>Serial Number:</b>"
msgstr "<b>Seriya nömrəsi:</b>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "<span class=\"badge text-bg-warning float-end\">Canceled</span>"
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
msgid "<span class=\"badge text-bg-warning float-end\">Cancelled</span>"
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "<span class=\"ml8\">hours</span>"
msgstr "<span class=\"ml8\">Saatlar</span>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "<span>Reporting</span>"
msgstr "<span>Mühasibat uçotu</span>"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "<span>Requests</span>"
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.view_maintenance_equipment_category_kanban
msgid "<strong>Equipment:</strong>"
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.view_maintenance_equipment_category_kanban
msgid "<strong>Maintenance:</strong>"
msgstr "<strong>Texniki Xidmət:</strong>"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_defaults
msgid ""
"A Python dictionary that will be evaluated to provide default values when "
"creating new records for this alias."
msgstr ""
"Bu qoşma ad üzrə yeni qeydlər yaradarkən susmaya görə dəyərləri təmin etmək "
"üçün qiymətləndiriləcək Python lüğəti."

#. module: maintenance
#: model:maintenance.equipment,name:maintenance.equipment_computer3
#: model:maintenance.equipment,name:maintenance.equipment_computer5
msgid "Acer Laptop"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_needaction
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_needaction
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_needaction
msgid "Action Needed"
msgstr "Lazımi Hərəkət"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__active
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__active
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Active"
msgstr "Aktiv"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_ids
msgid "Activities"
msgstr "Fəaliyyətlər"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_exception_decoration
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Faəliyyət istisnaetmə İşarəsi"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_state
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_state
msgid "Activity State"
msgstr "Fəaliyyət Statusu"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_type_icon
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_type_icon
msgid "Activity Type Icon"
msgstr "Fəaliyyət Növü ikonu"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.mail_activity_type_action_config_maintenance
#: model:ir.ui.menu,name:maintenance.maintenance_menu_config_activity_type
msgid "Activity Types"
msgstr "Fəaliyyət Növləri"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_action
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_action_from_category_form
msgid "Add a new equipment"
msgstr "Yeni avadanlıq əlavə et"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_category_action
msgid "Add a new equipment category"
msgstr "Yeni avadanlıq kateqoriyası əlavə et"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_cal
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_from_equipment
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_link
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_todo_request_action_from_dashboard
#: model_terms:ir.actions.act_window,help:maintenance.maintenance_request_action_reports
msgid "Add a new maintenance request"
msgstr "Yeni texniki xidmət sorğusu əlavə et"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_stage_action
#: model_terms:ir.actions.act_window,help:maintenance.maintenance_dashboard_action
msgid "Add a new stage in the maintenance request"
msgstr "Texniki xidmət sorğusu sahəsində yeni mərhələ əlavə et"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.maintenance_team_action_settings
msgid "Add a team in the maintenance request"
msgstr "Texniki xidmət sorğusuna komanda əlavə edin"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_id
msgid "Alias"
msgstr "Qoşma Ad"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_contact
msgid "Alias Contact Security"
msgstr "Kontakt Təhlükəsizliyinə Alternativ Ad"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_domain_id
msgid "Alias Domain"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_domain
msgid "Alias Domain Name"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_full_name
msgid "Alias Email"
msgstr "Alias E-poçt"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_name
msgid "Alias Name"
msgstr "Alternativ Ad"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_status
msgid "Alias Status"
msgstr "Alias Vəziyyəti"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_status
msgid "Alias status assessed on the last message received."
msgstr "Alınan son mesajda alias statusu qiymətləndirildi."

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_model_id
msgid "Aliased Model"
msgstr "Alternativ Model adı"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "All"
msgstr "BÜTÜN"

#. module: maintenance
#: model:ir.model.constraint,message:maintenance.constraint_maintenance_equipment_serial_no
msgid "Another asset already exists with this serial number!"
msgstr "Bu seriya nömrəsi ilə başqa bir aktiv artıq mövcuddur!"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__archive
msgid "Archive"
msgstr "Arxiv"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_search
msgid "Archived"
msgstr "Arxivləndi"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_tree
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_tree
msgid "Assign To User"
msgstr "İstifadəçiyə təyin edin"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Assigned"
msgstr "Təyin edilmiş"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__assign_date
msgid "Assigned Date"
msgstr "Təyin edilmiş Tarix"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Assigned to"
msgstr "Təyin olunub"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_attachment_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_attachment_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_attachment_count
msgid "Attachment Count"
msgstr "Qoşma Sayı"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__kanban_state__blocked
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Blocked"
msgstr "Bloklanıb"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Cancel"
msgstr "Ləğv edin"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Cancelled"
msgstr "Ləğv olundu"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__category_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Category"
msgstr "Kateqoriya"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__name
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_search
msgid "Category Name"
msgstr "Kateqoriyanın adı"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__close_date
msgid "Close Date"
msgstr "Tarixi Bağlayın"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__color
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__color
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__color
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__color
msgid "Color Index"
msgstr "Rəng İndeksi"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__note
msgid "Comments"
msgstr "şərhlər"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__company_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__company_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__company_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__company_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__company_id
msgid "Company"
msgstr "Şirkət"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__estimated_next_failure
#: model:ir.model.fields,help:maintenance.field_maintenance_mixin__estimated_next_failure
msgid "Computed as Latest Failure Date + MTBF"
msgstr ""

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_computer
msgid "Computers"
msgstr ""

#. module: maintenance
#: model:ir.model,name:maintenance.model_res_config_settings
msgid "Config Settings"
msgstr "Parametrləri Konfiqurasiya edin"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_maintenance_configuration
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Configuration"
msgstr "Konfiqurasiya"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__maintenance_type__corrective
msgid "Corrective"
msgstr "Düzəldici"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__cost
msgid "Cost"
msgstr "Maya dəyəri"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.res_config_settings_view_form
msgid "Create custom worksheet templates"
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Created By"
msgstr "Tərəfindən yaradılıb"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__create_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__create_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__create_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__create_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__create_uid
msgid "Created by"
msgstr "Tərəfindən yaradılıb"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__owner_user_id
msgid "Created by User"
msgstr "İstifadəçi tərəfindən yaradılıb"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__create_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__create_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__create_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__create_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__create_date
msgid "Created on"
msgstr "Tarixdə yaradıldı"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__maintenance_open_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__maintenance_open_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__maintenance_open_count
msgid "Current Maintenance"
msgstr "Cari Xidmət"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_bounced_content
msgid "Custom Bounced Message"
msgstr "Xüsusi Qaytarılmış Mesaj"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_res_config_settings__module_maintenance_worksheet
msgid "Custom Maintenance Worksheets"
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.res_config_settings_view_form
msgid "Custom Worksheets"
msgstr ""

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_m_dashboard
msgid "Dashboard"
msgstr "Şəxsi kabinet"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__request_date
msgid "Date requested for the maintenance to happen"
msgstr "Texniki xidmətin həyata keçirilməsi üçün tələb olunan tarix"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__schedule_date
msgid ""
"Date the maintenance team plans the maintenance.  It should not differ much "
"from the Request Date. "
msgstr ""
"Texniki xidmət qrupunun texniki xidmətin keçirilməsini planlaşdırdığı tarix"
"  Sorğu tarixindən çox fərqlənməli deyil. "

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__close_date
msgid "Date the maintenance was finished. "
msgstr "Texnikixidmətin başa çatdığı tarix "

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_unit__day
msgid "Days"
msgstr "günlər"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_defaults
msgid "Default Values"
msgstr "Defolt Dəyərlər"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "Delete"
msgstr "Silin"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__description
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Description"
msgstr "Təsvir"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__display_name
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__display_name
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__display_name
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__display_name
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__display_name
msgid "Display Name"
msgstr "Ekran Adı"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Done"
msgstr "Hazırdır"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__duration
msgid "Duration"
msgstr "Müddət"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__duration
msgid "Duration in hours."
msgstr "Saat müddəti."

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "Edit..."
msgstr "Redaktə edin..."

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__effective_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__effective_date
msgid "Effective Date"
msgstr "Qüvvəyə minmə Tarixi"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_email
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
msgid "Email Alias"
msgstr "Alternativ Email Adı"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_id
msgid ""
"Email alias for this equipment category. New emails will automatically "
"create a new equipment under this category."
msgstr ""
"Bu avadanlıq kateqoriyası üçün email təxəllüsü. Yeni emaillər avtomatik "
"olaraq  bu kateqoriya əsasında  yeni avadanlıqlar yaradacaq."

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__email_cc
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Email cc"
msgstr "Elektron Poçt cc"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_domain
msgid "Email domain e.g. 'example.com' in '<EMAIL>'"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__repeat_until
msgid "End Date"
msgstr "Bitmə Tarixi"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.hr_equipment_action
#: model:ir.actions.act_window,name:maintenance.hr_equipment_action_from_category_form
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__equipment_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__equipment_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__equipment_ids
#: model:ir.ui.menu,name:maintenance.menu_equipment_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Equipment"
msgstr "Avadanlıq"

#. module: maintenance
#: model:mail.message.subtype,description:maintenance.mt_mat_assign
#: model:mail.message.subtype,name:maintenance.mt_cat_mat_assign
#: model:mail.message.subtype,name:maintenance.mt_mat_assign
msgid "Equipment Assigned"
msgstr "Təyin edilmiş Avadanlıq"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.hr_equipment_category_action
#: model:ir.ui.menu,name:maintenance.menu_maintenance_cat
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
msgid "Equipment Categories"
msgstr "Avadanlıq Kateqoriyaları"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__category_id
msgid "Equipment Category"
msgstr "Avadanlıq Kateqoriyası"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__equipment_count
msgid "Equipment Count"
msgstr ""

#. module: maintenance
#: model:res.groups,name:maintenance.group_equipment_manager
msgid "Equipment Manager"
msgstr "Avadanlıq Meneceri"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__name
msgid "Equipment Name"
msgstr "Avadanlığın Adı "

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Estimated Next Failure"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__estimated_next_failure
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__estimated_next_failure
msgid "Estimated time before next failure (in days)"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__expected_mtbf
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__expected_mtbf
msgid "Expected MTBF"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__expected_mtbf
#: model:ir.model.fields,help:maintenance.field_maintenance_mixin__expected_mtbf
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Expected Mean Time Between Failure"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__fold
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__fold
msgid "Folded in Maintenance Pipe"
msgstr "Texniki boruda bağlanmış"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_cal
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_from_equipment
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_request_action_link
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_todo_request_action_from_dashboard
#: model_terms:ir.actions.act_window,help:maintenance.maintenance_request_action_reports
msgid ""
"Follow the process of the request and communicate with the collaborator."
msgstr "Tələbin prosesini izləyin  və əməkdaşlarla əlaqə saxlayın."

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_follower_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_follower_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_follower_ids
msgid "Followers"
msgstr "İzləyicilər"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_partner_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_partner_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_partner_ids
msgid "Followers (Partners)"
msgstr "İzləyicilər (Tərəfdaşlar)"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__activity_type_icon
#: model:ir.model.fields,help:maintenance.field_maintenance_request__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Gözəl şriftli ikon, məsələn fa-tapşırıqlar"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_type__forever
msgid "Forever"
msgstr "Davamlı"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Future Activities"
msgstr "Gələcək Fəaliyyətlər"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__instruction_google_slide
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__instruction_type__google_slide
msgid "Google Slide"
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Google Slide Link"
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Group by..."
msgstr "Qruplaşdır..."

#. module: maintenance
#: model:maintenance.equipment,name:maintenance.equipment_printer1
msgid "HP Inkjet printer"
msgstr ""

#. module: maintenance
#: model:maintenance.equipment,name:maintenance.equipment_computer11
#: model:maintenance.equipment,name:maintenance.equipment_computer9
msgid "HP Laptop"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__has_message
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__has_message
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__has_message
msgid "Has Message"
msgstr "Mesajı Var"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__priority__3
msgid "High"
msgstr "Yüksək"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "High-priority"
msgstr "Yüksək prioritetli"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__id
msgid "ID"
msgstr "ID"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_parent_thread_id
msgid ""
"ID of the parent record holding the alias (example: project holding the task"
" creation alias)"
msgstr ""
"Təxəllüsün olduğu əsas qeydin ID nömrəsi (məsələn: tapşırığın yaradılması "
"təxəllüsünün olduğu layihə)"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_exception_icon
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_exception_icon
msgid "Icon"
msgstr "Simvol"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__activity_exception_icon
#: model:ir.model.fields,help:maintenance.field_maintenance_request__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "İstisna fəaliyyəti göstərən simvol."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_needaction
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_needaction
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_needaction
msgid "If checked, new messages require your attention."
msgstr "İşarələnibsə, yeni mesajlara baxmalısınız."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_has_error
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_has_error
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_has_error
msgid "If checked, some messages have a delivery error."
msgstr "Yoxlanılıbsa, bəzi mesajların çatdırılmasında xəta var."

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_bounced_content
msgid ""
"If set, this content will automatically be sent out to unauthorized users "
"instead of the default message."
msgstr ""
"Qurulubsa, bu məzmun avtomatik olaraq standart mesaj yerinə icazəsiz "
"istifadəçilərə göndəriləcək."

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__kanban_state__normal
#: model:maintenance.stage,name:maintenance.stage_1
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "In Progress"
msgstr "Həyata Keçirilməkdə"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__instruction_type
msgid "Instruction"
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Instructions"
msgstr ""

#. module: maintenance
#: model:maintenance.team,name:maintenance.equipment_team_maintenance
msgid "Internal Maintenance"
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Internal Notes"
msgstr "Daxili Qeydlər"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_is_follower
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_is_follower
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_is_follower
msgid "Is Follower"
msgstr "İzləyicidir"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__kanban_state
msgid "Kanban State"
msgstr "Kanban Metodu"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__write_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__write_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__write_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__write_uid
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__write_uid
msgid "Last Updated by"
msgstr "Son Yeniləyən"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__write_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__write_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__write_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__write_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__write_date
msgid "Last Updated on"
msgstr "Son Yenilənmə tarixi"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Late Activities"
msgstr "Ən son Əməliyyatlar"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Latest Failure"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__latest_failure_date
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__latest_failure_date
msgid "Latest Failure Date"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_incoming_local
msgid "Local-part based incoming detection"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__location
msgid "Location"
msgstr "Məkan"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_m_reports_losses
msgid "Losses Analysis"
msgstr "İtkilərin Analizi"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__priority__1
msgid "Low"
msgstr "Aşağı"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__mtbf
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__mtbf
msgid "MTBF"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__mttr
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__mttr
msgid "MTTR"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__maintenance_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__maintenance_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__maintenance_ids
#: model:ir.ui.menu,name:maintenance.menu_m_request
#: model:ir.ui.menu,name:maintenance.menu_maintenance_title
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.res_config_settings_view_form
msgid "Maintenance"
msgstr "Texniki xidmət"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_m_request_calendar
msgid "Maintenance Calendar"
msgstr "Texniki xidmət Təqvimi"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__maintenance_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__maintenance_count
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__maintenance_count
msgid "Maintenance Count"
msgstr "Texniki xidmət Hesabı"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_equipment
msgid "Maintenance Equipment"
msgstr "Texniki xidmət Avadanlığı"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_equipment_category
msgid "Maintenance Equipment Category"
msgstr "Texniki xidmət Avadanlığı Kateqoriyası"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_mixin
msgid "Maintenance Maintained Item"
msgstr ""

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_request
#: model:mail.activity.type,name:maintenance.mail_act_maintenance_request
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_request_view_activity
msgid "Maintenance Request"
msgstr "Texniki xidmət üzrə Sorğu"

#. module: maintenance
#: model:mail.message.subtype,name:maintenance.mt_cat_req_created
msgid "Maintenance Request Created"
msgstr "Texniki xidmət üzrə Yaradılmış Sorğu"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Maintenance Request Search"
msgstr "Texniki xidmət üzrə Sorğu Axtarışı"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_stage_view_tree
msgid "Maintenance Request Stage"
msgstr "Texniki xidmət üzrə Sorğu Mərhələsi"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_stage_view_search
msgid "Maintenance Request Stages"
msgstr "Texniki xidmət üzrə Sorğu Mərhələləri"

#. module: maintenance
#: model:mail.message.subtype,description:maintenance.mt_req_created
msgid "Maintenance Request created"
msgstr "Texniki xidmət üzrə Yaradılmış Sorğu"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.hr_equipment_request_action
#: model:ir.actions.act_window,name:maintenance.hr_equipment_request_action_cal
#: model:ir.actions.act_window,name:maintenance.hr_equipment_request_action_from_equipment
#: model:ir.actions.act_window,name:maintenance.hr_equipment_request_action_link
#: model:ir.actions.act_window,name:maintenance.hr_equipment_todo_request_action_from_dashboard
#: model:ir.actions.act_window,name:maintenance.maintenance_request_action_reports
#: model:ir.ui.menu,name:maintenance.maintenance_request_reporting
#: model:ir.ui.menu,name:maintenance.menu_m_request_form
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Maintenance Requests"
msgstr "Texniki xidmət üzrə Sorğular"

#. module: maintenance
#: model:ir.model,name:maintenance.model_maintenance_stage
msgid "Maintenance Stage"
msgstr "Texniki xidmət Mərhələsi"

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_maintenance_stage_configuration
msgid "Maintenance Stages"
msgstr "Texniki xidmət Mərhələləri"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__maintenance_team_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__maintenance_team_id
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_tree
msgid "Maintenance Team"
msgstr "Texniki xidmət Qrupu"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.maintenance_dashboard_action
#: model:ir.model,name:maintenance.model_maintenance_team
#: model:ir.ui.menu,name:maintenance.menu_maintenance_teams
msgid "Maintenance Teams"
msgstr "Texniki xidmət Qrupları"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__maintenance_type
msgid "Maintenance Type"
msgstr "Texniki xidmət Növü"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Mean Time Between Failure"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__mtbf
#: model:ir.model.fields,help:maintenance.field_maintenance_mixin__mtbf
msgid ""
"Mean Time Between Failure, computed based on done corrective maintenances."
msgstr ""

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__mttr
#: model:ir.model.fields,help:maintenance.field_maintenance_mixin__mttr
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Mean Time To Repair"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_has_error
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_has_error
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_has_error
msgid "Message Delivery error"
msgstr "Mesajın Çatdırılmasında xəta"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_ids
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_ids
msgid "Messages"
msgstr "Mesajlar"

#. module: maintenance
#: model:maintenance.team,name:maintenance.equipment_team_metrology
msgid "Metrology"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__model
msgid "Model"
msgstr "Model"

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_monitor
msgid "Monitors"
msgstr ""

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_unit__month
msgid "Months"
msgstr "Aylar"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__my_activity_date_deadline
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mənim Fəaliyyətlərimin Son Tarixi "

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "My Equipment"
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "My Maintenances"
msgstr "Mənim Təminatlarım"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__name
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_tree
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Name"
msgstr "Ad"

#. module: maintenance
#: model:maintenance.stage,name:maintenance.stage_0
msgid "New Request"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_calendar_event_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_calendar_event_id
msgid "Next Activity Calendar Event"
msgstr "Növbəti Fəaliyyət Təqvimi Tədbiri"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_date_deadline
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Növbəti Fəaliyyətin Son Tarixi"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_summary
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_summary
msgid "Next Activity Summary"
msgstr "Növbəti Fəaliyyət Xülasəsi"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_type_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_type_id
msgid "Next Activity Type"
msgstr "Yeni Fəaliyyət Növü"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__priority__2
msgid "Normal"
msgstr "Normal"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__note
msgid "Note"
msgstr "Qeyd"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Notes"
msgstr "Qeydlər"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_needaction_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_needaction_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_needaction_counter
msgid "Number of Actions"
msgstr "Hərəkətlərin sayı"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count
msgid "Number of Requests"
msgstr "İstəklərin Sayı"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count_block
msgid "Number of Requests Blocked"
msgstr "Blok olunmuş İstəklərin Sayı"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count_date
msgid "Number of Requests Scheduled"
msgstr "Planlaşdırılmış İstəklərin Sayı"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count_unscheduled
msgid "Number of Requests Unscheduled"
msgstr "Planlaşdırılmamış İstəklərin Sayı"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_count_high_priority
msgid "Number of Requests in High Priority"
msgstr "Yüksək Prioritetli İstəklərin Sayı"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__message_has_error_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__message_has_error_counter
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__message_has_error_counter
msgid "Number of errors"
msgstr "Xətaların sayı"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_needaction_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_needaction_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Əməliyyat tələb edən mesajların sayı"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__message_has_error_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__message_has_error_counter
#: model:ir.model.fields,help:maintenance.field_maintenance_request__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Çatdırılma xətası olan mesajların sayı"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_force_thread_id
msgid ""
"Optional ID of a thread (record) to which all incoming messages will be "
"attached, even if they did not reply to it. If set, this will disable the "
"creation of new records completely."
msgstr ""
"Cavab verməsələr belə, gələn bütün mesajların əlavə ediləcəyi email "
"zəncirinin (qeyd) istəyə bağlı ID-si. Quraşdırılarsa, bu, yeni qeydlərin "
"yaradılmasını tamamilə dayandıracaq."

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.menu_m_reports_oee
msgid "Overall Equipment Effectiveness (OEE)"
msgstr "Ümumi Avadanlıq Effektivliyi (OEE)"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__owner_user_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_tree
msgid "Owner"
msgstr "Sahibi"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__instruction_pdf
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__instruction_type__pdf
msgid "PDF"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_parent_model_id
msgid "Parent Model"
msgstr "Əsas Model"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_parent_thread_id
msgid "Parent Record Thread ID"
msgstr "Əsas Qeyd Zəncirinin ID-si"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_parent_model_id
msgid ""
"Parent model holding the alias. The model holding the alias reference is not"
" necessarily the model given by alias_model_id (example: project "
"(parent_model) and task (model))"
msgstr ""
"Qoşma adı özündə saxlayan əsas model Qoşma ad istinadını özündə saxlayan "
"model  alias_model_id tərəfindən verilən model olmaya bilər (məsələn: layihə"
" (parent_model) və tapşırıq (model))"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__instruction_google_slide
msgid ""
"Paste the url of your Google Slide. Make sure the access to the document is "
"public."
msgstr ""
"Google Slaydınızın URL-sini yapışdırın. Sənədə girişin açıq olduğundan əmin "
"olun."

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_phone
msgid "Phones"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_contact
msgid ""
"Policy to post a message on the document using the mailgateway.\n"
"- everyone: everyone can post\n"
"- partners: only authenticated partners\n"
"- followers: only followers of the related document or members of following channels\n"
msgstr ""
"Mail şlüzündən istifadə edərək sənəddəki mesajı göndərmək qaydası.\n"
"- hər kəs: hərkəs göndərə bilər\n"
"- tərəfdaşlar: yalnız təsdiqlənmiş tərəfdaşlar\n"
"- izləyicilər: yalnız müvafiq sənədin izləyiciləri və ya aşağıdakı kanalların üzvləri\n"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__maintenance_type__preventive
msgid "Preventive"
msgstr "Profilaktik"

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_printer
msgid "Printers"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__priority
msgid "Priority"
msgstr "Prioritet"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Product Information"
msgstr "Məhsul haqqında məlumat"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Ready"
msgstr "Hazır"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__kanban_state__done
msgid "Ready for next stage"
msgstr "Növbəti Mərhələyə Hazır"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "Record Colour"
msgstr "Rəngi qeyd et"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__alias_force_thread_id
msgid "Record Thread ID"
msgstr "Qeyd Hissəciyinin ID-si"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__recurring_maintenance
msgid "Recurrent"
msgstr "Təkrarlanan"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Reopen Request"
msgstr "Yenidən açılmış Təklif"

#. module: maintenance
#: model:maintenance.stage,name:maintenance.stage_3
msgid "Repaired"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__repeat_interval
msgid "Repeat Every"
msgstr "Hər birini Təkrarlayın"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__repeat_unit
msgid "Repeat Unit"
msgstr ""

#. module: maintenance
#: model:ir.ui.menu,name:maintenance.maintenance_reporting
#: model:ir.ui.menu,name:maintenance.menu_m_reports
msgid "Reporting"
msgstr "Hesabatlıq"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__request_ids
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_kanban
msgid "Request"
msgstr "Sorğu"

#. module: maintenance
#: model:mail.message.subtype,name:maintenance.mt_req_created
msgid "Request Created"
msgstr "Sorğu Yaradıldı"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__request_date
msgid "Request Date"
msgstr "Sorğu Tarixi"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__done
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__done
msgid "Request Done"
msgstr "Sorğu İcra edildi"

#. module: maintenance
#. odoo-python
#: code:addons/maintenance/models/maintenance.py:0
#, python-format
msgid "Request planned for %s"
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Requested By"
msgstr "Sorğu edilib"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_kanban
msgid "Requested by:"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__todo_request_ids
msgid "Requests"
msgstr "Sorğular"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment_category__technician_user_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Responsible"
msgstr "Məsul"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__activity_user_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__activity_user_id
msgid "Responsible User"
msgstr "Məsul İstifadəçi"

#. module: maintenance
#: model:maintenance.equipment,name:maintenance.equipment_monitor1
#: model:maintenance.equipment,name:maintenance.equipment_monitor4
#: model:maintenance.equipment,name:maintenance.equipment_monitor6
msgid "Samsung Monitor 15\""
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Scheduled"
msgstr "Planlaşdırılmış "

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__schedule_date
msgid "Scheduled Date"
msgstr "Planlaşdırılmış Tarix"

#. module: maintenance
#: model:maintenance.stage,name:maintenance.stage_4
msgid "Scrap"
msgstr "İstifadəyə yararsız"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__scrap_date
msgid "Scrap Date"
msgstr "İstifadəyə Yararsızlıq Tarixi"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_search
msgid "Search"
msgstr "Axtarın"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_stage__sequence
msgid "Sequence"
msgstr "Ardıcıllıq"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__serial_no
msgid "Serial Number"
msgstr "Seriya Nömrəsi"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_request__archive
msgid ""
"Set archive to true to hide the maintenance request without deleting it."
msgstr ""
"Texniki xidmət tələbini silmədən gizlətmək üçün arxivi \"doğru\" olaraq "
"təyin edin."

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.action_maintenance_configuration
#: model:ir.ui.menu,name:maintenance.menu_maintenance_config
msgid "Settings"
msgstr "Parametrlər"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Show all records which has next action date is before today"
msgstr "Növbəti fəaliyyət tarixi bu günə qədər olan bütün qeydləri göstərin"

#. module: maintenance
#: model:maintenance.equipment.category,name:maintenance.equipment_software
msgid "Software"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__stage_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
msgid "Stage"
msgstr "Mərhələ"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.hr_equipment_stage_action
msgid "Stages"
msgstr "Mərhələlər"

#. module: maintenance
#: model:mail.message.subtype,name:maintenance.mt_req_status
msgid "Status Changed"
msgstr "Status Dəyişdi"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__activity_state
#: model:ir.model.fields,help:maintenance.field_maintenance_request__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Fəaliyyətlərə əsaslanan status\n"
"Gecikmiş: Gözlənilən tarixdən keçib\n"
"Bu gün: Fəaliyyət tarixi bu gündür\n"
"Planlaşdırılıb: Gələcək fəaliyyətlər."

#. module: maintenance
#: model:mail.message.subtype,description:maintenance.mt_req_status
msgid "Status changed"
msgstr "Status Dəyişdi"

#. module: maintenance
#: model:maintenance.team,name:maintenance.equipment_team_subcontractor
msgid "Subcontractor"
msgstr ""

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__name
msgid "Subjects"
msgstr "Mövzular"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__maintenance_team_id
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_search
msgid "Team"
msgstr "Komanda"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__member_ids
msgid "Team Members"
msgstr "Komanda Üzvləri"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_team__name
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_form
msgid "Team Name"
msgstr "Komandanın Adı"

#. module: maintenance
#: model:ir.actions.act_window,name:maintenance.maintenance_team_action_settings
msgid "Teams"
msgstr "Komandalar"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__technician_user_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_mixin__technician_user_id
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__user_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Technician"
msgstr "Texnik"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__instruction_text
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__instruction_type__text
msgid "Text"
msgstr "Mətn"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_model_id
msgid ""
"The model (Odoo Document Kind) to which this alias corresponds. Any incoming"
" email that does not reply to an existing record will cause the creation of "
"a new record of this model (e.g. a Project Task)"
msgstr ""
"Bu alternativ adın uyğun gəldiyi model (Odoo Document Kind). Mövcud qeydə "
"cavab verməyən hər hansı gələn email bu modelin yeni bir qeydinin "
"yaradılmasına səbəb olacaq (məsələn, Layihə Tapşırığı)"

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment_category__alias_name
msgid ""
"The name of the email alias, e.g. 'jobs' if you want to catch emails for "
"<<EMAIL>>"
msgstr ""
"<<EMAIL>> ünvanından olan mailləri görmək istəyirsinizsə, "
"email alternativininin (məs: işlər) adı"

#. module: maintenance
#: model:res.groups,comment:maintenance.group_equipment_manager
msgid "The user will be able to manage equipment."
msgstr ""

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__effective_date
#: model:ir.model.fields,help:maintenance.field_maintenance_mixin__effective_date
msgid "This date will be used to compute the Mean Time Between Failure."
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "To Do"
msgstr "Görülməli iş"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Today Activities"
msgstr "Bugünkü Fəaliyyətlər"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Top Priorities"
msgstr "Ən Məşhur Üstünlüklər"

#. module: maintenance
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_action
#: model_terms:ir.actions.act_window,help:maintenance.hr_equipment_action_from_category_form
msgid ""
"Track equipment and link it to an employee or department.\n"
"                You will be able to manage allocations, issues and maintenance of your equipment."
msgstr ""

#. module: maintenance
#: model:ir.model.fields,help:maintenance.field_maintenance_equipment__activity_exception_decoration
#: model:ir.model.fields,help:maintenance.field_maintenance_request__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Qeyddəki istisna fəaliyyət növü."

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Unassigned"
msgstr "Təyin olunmamış"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Under Maintenance"
msgstr "Təmir"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Unread Messages"
msgstr "Oxunmamış Mesajlar"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_search
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_kanban
msgid "Unscheduled"
msgstr "Planlaşdırılmayıb"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_request__repeat_type
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_type__until
msgid "Until"
msgstr "Qeyd edilən tarixədək"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Upload your PDF file."
msgstr "PDF faylınızı yükləyin"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "Used in location"
msgstr "Yerdə istifadə olunur"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__partner_id
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_search
msgid "Vendor"
msgstr "Podratçı"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__partner_ref
msgid "Vendor Reference"
msgstr "Təchizatçı İstinadı"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__priority__0
msgid "Very Low"
msgstr "Çox Aşağı"

#. module: maintenance
#: model:ir.model.fields,field_description:maintenance.field_maintenance_equipment__warranty_date
msgid "Warranty Expiration Date"
msgstr "Zəmanətin Bitmə Tarixi"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_unit__week
msgid "Weeks"
msgstr "Həftələr"

#. module: maintenance
#: model:ir.model.fields.selection,name:maintenance.selection__maintenance_request__repeat_unit__year
msgid "Years"
msgstr "İllər"

#. module: maintenance
#. odoo-python
#: code:addons/maintenance/models/maintenance.py:0
#, python-format
msgid ""
"You cannot delete an equipment category containing equipment or maintenance "
"requests."
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "Your instructions"
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "days"
msgstr "Gün"

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.maintenance_team_view_form
msgid "e.g. Internal Maintenance"
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_view_form
msgid "e.g. LED Monitor"
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
msgid "e.g. Monitors"
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_form
msgid "e.g. Screen not working"
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_category_view_form
msgid "e.g. domain.com"
msgstr ""

#. module: maintenance
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_graph
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_pivot
#: model_terms:ir.ui.view,arch_db:maintenance.hr_equipment_request_view_tree
msgid "maintenance Request"
msgstr "Texniki xidmət üzrə Sorğu"
