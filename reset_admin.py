#!/usr/bin/env python3
import psycopg2
import hashlib
import base64

def reset_admin_password():
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='dataicraft',
            user='odoo',
            password='odoo123'
        )
        cur = conn.cursor()
        
        # Update admin user
        cur.execute("""
            UPDATE res_users 
            SET login = '<EMAIL>',
                password = 'admin123',
                active = true
            WHERE id = 2;
        """)
        
        conn.commit()
        
        print("✅ Admin user updated successfully!")
        print("Login credentials:")
        print("Email: <EMAIL>")
        print("Password: admin123")
        
        # Verify the update
        cur.execute("SELECT id, login, active FROM res_users WHERE id = 2;")
        user = cur.fetchone()
        print(f"\nVerification - ID: {user[0]}, Login: {user[1]}, Active: {user[2]}")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    reset_admin_password()
