Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Not Found: /
"GET / HTTP/1.1" 404 2541
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 2592
Not Found: /
"GET / HTTP/1.1" 404 2541
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Not Found: /
"GET / HTTP/1.1" 404 2541
"GET /admin HTTP/1.1" 301 0
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4177
"GET /static/admin/css/base.css HTTP/1.1" 200 21310
"GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2694
"GET /static/admin/css/login.css HTTP/1.1" 200 958
"GET /static/admin/css/responsive.css HTTP/1.1" 200 18559
"GET /static/admin/js/theme.js HTTP/1.1" 200 1943
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
"POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 200 7705
"GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
"GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
"GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
"GET /admin/accounting/accountjournal/ HTTP/1.1" 200 9774
"GET /static/admin/css/changelists.css HTTP/1.1" 200 6566
"GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
"GET /static/admin/js/core.js HTTP/1.1" 200 5682
"GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 8943
"GET /static/admin/js/actions.js HTTP/1.1" 200 7872
"GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
"GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
"GET /static/admin/img/search.svg HTTP/1.1" 200 458
"GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" **********
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" **********
"GET /static/admin/js/filters.js HTTP/1.1" 200 978
"GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
"GET /admin/accounting/rescompany/ HTTP/1.1" 200 9950
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /api/accounting/api/ HTTP/1.1" 200 6233
"GET /static/rest_framework/css/prettify.css HTTP/1.1" 200 817
"GET /static/rest_framework/css/bootstrap-tweaks.css HTTP/1.1" 200 3385
"GET /static/rest_framework/css/bootstrap.min.css HTTP/1.1" **********
"GET /static/rest_framework/css/default.css HTTP/1.1" 200 1152
"GET /static/rest_framework/js/ajax-form.js HTTP/1.1" 200 3597
"GET /static/rest_framework/js/csrf.js HTTP/1.1" 200 1719
"GET /static/rest_framework/js/jquery-3.5.1.min.js HTTP/1.1" 200 89476
"GET /static/rest_framework/js/bootstrap.min.js HTTP/1.1" 200 39680
"GET /static/rest_framework/js/default.js HTTP/1.1" 200 1268
"GET /static/rest_framework/js/prettify-min.js HTTP/1.1" 200 13632
"GET /static/rest_framework/img/grid.png HTTP/1.1" 200 1458
"GET /admin/accounting/respartner/ HTTP/1.1" 200 9817
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/auth/group/ HTTP/1.1" 200 8808
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/auth/user/ HTTP/1.1" 200 12013
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/img/icon-yes.svg HTTP/1.1" 200 436
"GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
"GET /admin/accounting/respartner/ HTTP/1.1" 200 9817
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/accountaccount/ HTTP/1.1" 200 11326
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/accountjournal/ HTTP/1.1" 200 9774
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/rescompany/ HTTP/1.1" 200 9950
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/respartner/ HTTP/1.1" 200 9817
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/accountjournal/ HTTP/1.1" 200 9774
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/accountjournal/add/ HTTP/1.1" 200 16077
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/css/forms.css HTTP/1.1" 200 9090
"GET /static/admin/img/icon-deletelink.svg HTTP/1.1" 200 392
"GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
"GET /static/admin/css/widgets.css HTTP/1.1" 200 11921
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
"GET /static/admin/js/change_form.js HTTP/1.1" 200 606
"GET /admin/accounting/accountaccount/ HTTP/1.1" 200 11326
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/accountaccount/add/ HTTP/1.1" 200 16760
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /api/accounting/api/companies/ HTTP/1.1" 200 25312
"GET /static/rest_framework/fonts/glyphicons-halflings-regular.woff2 HTTP/1.1" 200 18028
"GET /api/accounting/api/accounts/ HTTP/1.1" 200 26574
"GET /api/accounting/api/partners/ HTTP/1.1" 200 29398
"GET /admin/accounting/rescompany/ HTTP/1.1" 200 11763
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
Internal Server Error: /admin/accounting/accountaccount/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\options.py", line 681, in get_field
    return self.fields_map[field_name]
KeyError: 'balance_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 272, in lookup_field
    f = _get_non_gfk_field(opts, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 303, in _get_non_gfk_field
    field = opts.get_field(name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\options.py", line 683, in get_field
    raise FieldDoesNotExist(
django.core.exceptions.FieldDoesNotExist: AccountAccount has no field named 'balance_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\base.py", line 45, in render
    return super().render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\library.py", line 258, in render
    _dict = self.func(*resolved_args, **resolved_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 336, in result_list
    "results": list(results(cl)),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 312, in results
    yield ResultList(None, items_for_result(cl, res, None))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 303, in __init__
    super().__init__(*items)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 213, in items_for_result
    f, attr, value = lookup_field(field_name, result, cl.model_admin)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 281, in lookup_field
    value = attr(obj)
  File "D:\odoo_erp\my_erp\backend\accounting\admin.py", line 34, in balance_display
    balance = obj.balance
  File "D:\odoo_erp\my_erp\backend\accounting\models.py", line 197, in balance
    lines = self.move_line_ids.filter(move_id__state='posted')
AttributeError: 'AccountAccount' object has no attribute 'move_line_ids'
"GET /admin/accounting/accountaccount/ HTTP/1.1" **********
"GET /admin/accounting/rescompany/ HTTP/1.1" 200 11763
"GET /admin/accounting/accountjournal/ HTTP/1.1" 200 13941
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
Internal Server Error: /admin/accounting/accountaccount/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\options.py", line 681, in get_field
    return self.fields_map[field_name]
KeyError: 'balance_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 272, in lookup_field
    f = _get_non_gfk_field(opts, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 303, in _get_non_gfk_field
    field = opts.get_field(name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\options.py", line 683, in get_field
    raise FieldDoesNotExist(
django.core.exceptions.FieldDoesNotExist: AccountAccount has no field named 'balance_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\base.py", line 45, in render
    return super().render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\library.py", line 258, in render
    _dict = self.func(*resolved_args, **resolved_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 336, in result_list
    "results": list(results(cl)),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 312, in results
    yield ResultList(None, items_for_result(cl, res, None))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 303, in __init__
    super().__init__(*items)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 213, in items_for_result
    f, attr, value = lookup_field(field_name, result, cl.model_admin)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 281, in lookup_field
    value = attr(obj)
  File "D:\odoo_erp\my_erp\backend\accounting\admin.py", line 34, in balance_display
    balance = obj.balance
  File "D:\odoo_erp\my_erp\backend\accounting\models.py", line 197, in balance
    lines = self.move_line_ids.filter(move_id__state='posted')
AttributeError: 'AccountAccount' object has no attribute 'move_line_ids'
"GET /admin/accounting/accountaccount/ HTTP/1.1" 500 405214
Internal Server Error: /admin/accounting/accountaccount/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\options.py", line 681, in get_field
    return self.fields_map[field_name]
KeyError: 'balance_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 272, in lookup_field
    f = _get_non_gfk_field(opts, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 303, in _get_non_gfk_field
    field = opts.get_field(name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\options.py", line 683, in get_field
    raise FieldDoesNotExist(
django.core.exceptions.FieldDoesNotExist: AccountAccount has no field named 'balance_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\base.py", line 45, in render
    return super().render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\library.py", line 258, in render
    _dict = self.func(*resolved_args, **resolved_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 336, in result_list
    "results": list(results(cl)),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 312, in results
    yield ResultList(None, items_for_result(cl, res, None))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 303, in __init__
    super().__init__(*items)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 213, in items_for_result
    f, attr, value = lookup_field(field_name, result, cl.model_admin)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 281, in lookup_field
    value = attr(obj)
  File "D:\odoo_erp\my_erp\backend\accounting\admin.py", line 34, in balance_display
    balance = obj.balance
  File "D:\odoo_erp\my_erp\backend\accounting\models.py", line 197, in balance
    lines = self.move_line_ids.filter(move_id__state='posted')
AttributeError: 'AccountAccount' object has no attribute 'move_line_ids'
"GET /admin/accounting/accountaccount/ HTTP/1.1" 500 405351
"GET /admin/accounting/accountjournal/ HTTP/1.1" 200 13941
D:\odoo_erp\my_erp\backend\erp_backend\urls.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/accounting/respartner/ HTTP/1.1" 200 14799
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
D:\odoo_erp\my_erp\backend\erp_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 9816
"GET /api/accounting/api/companies/ HTTP/1.1" 200 655
"GET /api/accounting/api/accounts/ HTTP/1.1" 200 3581
"GET /api/accounting/api/partners/ HTTP/1.1" 200 3154
"GET /api/accounting/api/journals/ HTTP/1.1" 200 1381
"GET /api/accounting/api/ HTTP/1.1" 200 6233
"GET /api/accounting/api/accounts/ HTTP/1.1" 200 26574
"GET /api/accounting/api/journals/ HTTP/1.1" 200 18974
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\admin.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\admin.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 11108
"GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2694
"GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
"GET /static/admin/css/base.css HTTP/1.1" 200 21310
"GET /static/admin/css/dashboard.css HTTP/1.1" 200 441
"GET /static/admin/css/responsive.css HTTP/1.1" 200 18559
"GET /static/admin/js/theme.js HTTP/1.1" 200 1943
"GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 3063
"GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 380
"GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 331
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 11108
"GET /admin/ HTTP/1.1" 200 11108
Internal Server Error: /admin/accounting/accountaccount/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\options.py", line 681, in get_field
    return self.fields_map[field_name]
KeyError: 'balance_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 272, in lookup_field
    f = _get_non_gfk_field(opts, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 303, in _get_non_gfk_field
    field = opts.get_field(name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\options.py", line 683, in get_field
    raise FieldDoesNotExist(
django.core.exceptions.FieldDoesNotExist: AccountAccount has no field named 'balance_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\base.py", line 45, in render
    return super().render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\library.py", line 258, in render
    _dict = self.func(*resolved_args, **resolved_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 336, in result_list
    "results": list(results(cl)),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 312, in results
    yield ResultList(None, items_for_result(cl, res, None))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 303, in __init__
    super().__init__(*items)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 213, in items_for_result
    f, attr, value = lookup_field(field_name, result, cl.model_admin)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 281, in lookup_field
    value = attr(obj)
  File "D:\odoo_erp\my_erp\backend\accounting\admin.py", line 38, in balance_display
    return format_html(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\utils\html.py", line 112, in format_html
    return mark_safe(format_string.format(*args_safe, **kwargs_safe))
ValueError: Unknown format code 'f' for object of type 'SafeString'
"GET /admin/accounting/accountaccount/ HTTP/1.1" 500 450586
"GET /admin/ HTTP/1.1" 200 11108
"GET /admin/accounting/accountanalyticaccount/ HTTP/1.1" 200 14562
"GET /static/admin/css/changelists.css HTTP/1.1" 200 6566
"GET /static/admin/js/jquery.init.js HTTP/1.1" 200 347
"GET /static/admin/js/core.js HTTP/1.1" 200 5682
"GET /static/admin/js/admin/RelatedObjectLookups.js HTTP/1.1" 200 8943
"GET /static/admin/js/actions.js HTTP/1.1" 200 7872
"GET /static/admin/js/urlify.js HTTP/1.1" 200 7887
"GET /static/admin/js/vendor/jquery/jquery.js HTTP/1.1" **********
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/js/prepopulate.js HTTP/1.1" 200 1531
"GET /static/admin/js/filters.js HTTP/1.1" 200 978
"GET /static/admin/js/vendor/xregexp/xregexp.js HTTP/1.1" **********
"GET /static/admin/img/tooltag-add.svg HTTP/1.1" 200 331
"GET /static/admin/img/sorting-icons.svg HTTP/1.1" 200 1097
Internal Server Error: /admin/accounting/accountaccount/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\options.py", line 681, in get_field
    return self.fields_map[field_name]
KeyError: 'balance_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 272, in lookup_field
    f = _get_non_gfk_field(opts, name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 303, in _get_non_gfk_field
    field = opts.get_field(name)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\db\models\options.py", line 683, in get_field
    raise FieldDoesNotExist(
django.core.exceptions.FieldDoesNotExist: AccountAccount has no field named 'balance_display'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 1005, in <listcomp>
    return SafeString("".join([node.render_annotated(context) for node in self]))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\base.py", line 45, in render
    return super().render(context)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\template\library.py", line 258, in render
    _dict = self.func(*resolved_args, **resolved_kwargs)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 336, in result_list
    "results": list(results(cl)),
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 312, in results
    yield ResultList(None, items_for_result(cl, res, None))
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 303, in __init__
    super().__init__(*items)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\templatetags\admin_list.py", line 213, in items_for_result
    f, attr, value = lookup_field(field_name, result, cl.model_admin)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\contrib\admin\utils.py", line 281, in lookup_field
    value = attr(obj)
  File "D:\odoo_erp\my_erp\backend\accounting\admin.py", line 38, in balance_display
    return format_html(
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\site-packages\django\utils\html.py", line 112, in format_html
    return mark_safe(format_string.format(*args_safe, **kwargs_safe))
ValueError: Unknown format code 'f' for object of type 'SafeString'
"GET /admin/accounting/accountaccount/ HTTP/1.1" 500 450620
D:\odoo_erp\my_erp\backend\accounting\admin.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/accounting/accountaccount/ HTTP/1.1" 200 23385
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/img/icon-no.svg HTTP/1.1" 200 560
D:\odoo_erp\my_erp\backend\accounting\admin.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\admin.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 20184
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 20184
D:\odoo_erp\my_erp\backend\accounting\admin.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\admin.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 29334
D:\odoo_erp\my_erp\backend\accounting\admin.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 30806
D:\odoo_erp\my_erp\backend\erp_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4195
"GET /static/admin/css/login.css HTTP/1.1" 200 958
D:\odoo_erp\my_erp\backend\erp_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 302 0
"GET /admin/login/?next=/admin/ HTTP/1.1" 200 4198
"POST /admin/login/?next=/admin/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 200 31656
Forbidden (CSRF token from POST incorrect.): /admin/login/
"POST /admin/login/?next=/admin/ HTTP/1.1" 403 2518
"GET /admin/accounting/stockwarehouse/ HTTP/1.1" 200 26299
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/accounting/stockwarehouse/add/ HTTP/1.1" 200 42422
"GET /static/admin/img/icon-deletelink.svg HTTP/1.1" 200 392
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/css/forms.css HTTP/1.1" 200 9090
"GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
"GET /static/admin/css/widgets.css HTTP/1.1" 200 11921
"GET /static/admin/js/change_form.js HTTP/1.1" 200 606
"GET /admin/ HTTP/1.1" 200 31656
"GET /admin/sales/saleorderline/ HTTP/1.1" 200 27057
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/ HTTP/1.1" 200 31656
"GET /admin/sales/saleorder/ HTTP/1.1" 200 28199
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/ HTTP/1.1" 200 31656
"GET /admin/sales/salesteam/ HTTP/1.1" 200 29595
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/ HTTP/1.1" 200 31656
D:\odoo_erp\my_erp\backend\erp_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 33771
Watching for file changes with StatReloader
"GET /admin/login HTTP/1.1" 301 0
"GET /admin/login/ HTTP/1.1" 302 0
"GET /admin/ HTTP/1.1" 200 37865
"GET /admin/accounting/rescountry/ HTTP/1.1" 200 30337
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
D:\odoo_erp\my_erp\backend\erp_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\sales\models.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 39848
D:\odoo_erp\my_erp\backend\erp_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 45937
"GET /admin/auth/user/ HTTP/1.1" 200 39389
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
D:\odoo_erp\my_erp\backend\erp_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 50443
D:\odoo_erp\my_erp\backend\erp_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\accounting\models.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 54847
"GET /admin/financial_reports/accountagereportconfiguration/ HTTP/1.1" 200 45080
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/financial_reports/accountagereportconfiguration/1/change/ HTTP/1.1" 200 59616
"GET /static/admin/img/icon-viewlink.svg HTTP/1.1" 200 581
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /static/admin/js/prepopulate_init.js HTTP/1.1" 200 586
"GET /static/admin/css/forms.css HTTP/1.1" 200 9090
"GET /static/admin/css/widgets.css HTTP/1.1" 200 11921
"GET /static/admin/js/inlines.js HTTP/1.1" 200 15526
"GET /static/admin/js/change_form.js HTTP/1.1" 200 606
"GET /static/admin/img/icon-unknown.svg HTTP/1.1" 200 655
"POST /admin/financial_reports/accountagereportconfiguration/1/change/ HTTP/1.1" 302 0
"GET /admin/financial_reports/accountagereportconfiguration/ HTTP/1.1" 200 45360
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
D:\odoo_erp\my_erp\backend\erp_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\pos\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\pos\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\pos\admin.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\pos\admin.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\pos\admin.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\pos\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\pos\admin.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\expenses\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\pos\models.py changed, reloading.
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 64837
"GET /admin/ HTTP/1.1" 200 64837
Forbidden: /api/accounting/api/accounts/
"GET /api/accounting/api/accounts/ HTTP/1.1" 403 58
Forbidden: /api/accounting/api/accounts/
"GET /api/accounting/api/accounts/ HTTP/1.1" 403 58
"OPTIONS /api/accounting/api/accounts/ HTTP/1.1" 200 0
Forbidden: /api/accounting/api/accounts/
"POST /api/accounting/api/accounts/ HTTP/1.1" 403 58
Forbidden: /api/accounting/api/accounts/
"GET /api/accounting/api/accounts/ HTTP/1.1" 403 58
Forbidden: /api/accounting/api/accounts/
"GET /api/accounting/api/accounts/ HTTP/1.1" 403 58
Forbidden: /api/accounting/api/accounts/
"GET /api/accounting/api/accounts/ HTTP/1.1" 403 58
Forbidden: /api/accounting/api/accounts/
"GET /api/accounting/api/accounts/ HTTP/1.1" 403 58
Forbidden: /api/accounting/api/accounts/
"GET /api/accounting/api/accounts/ HTTP/1.1" 403 58
Forbidden: /api/accounting/api/accounts/
"GET /api/accounting/api/accounts/ HTTP/1.1" 403 58
D:\odoo_erp\my_erp\backend\erp_backend\settings.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\erp_backend\urls.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\setup\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\setup\models.py changed, reloading.
Watching for file changes with StatReloader
D:\odoo_erp\my_erp\backend\setup\migrations\__init__.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Forbidden: /api/setup/countries/
"GET /api/setup/countries/ HTTP/1.1" 403 58
D:\odoo_erp\my_erp\backend\setup\admin.py changed, reloading.
D:\odoo_erp\my_erp\backend\setup\admin.py changed, reloading.
D:\odoo_erp\my_erp\backend\setup\admin.py changed, reloading.
D:\odoo_erp\my_erp\backend\setup\admin.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /admin/ HTTP/1.1" 200 66787
"GET /admin/accounting/rescompany/ HTTP/1.1" 200 53920
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
"GET /admin/setup/companysetup/ HTTP/1.1" 200 54402
"GET /admin/jsi18n/ HTTP/1.1" 200 3343
