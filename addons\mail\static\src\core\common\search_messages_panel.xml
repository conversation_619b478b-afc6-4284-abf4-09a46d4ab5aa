<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="mail.SearchMessagesPanel">
        <ActionPanel title="env.inChatter ? undefined : title">
            <div class="d-flex pb-2">
                <div class="input-group">
                    <div class="o_searchview form-control d-flex align-items-center py-1" role="search" aria-autocomplete="list">
                        <div class="o_searchview_input_container d-flex flex-grow-1 flex-wrap gap-1">
                            <input type="text" class="o_searchview_input flex-grow-1 w-auto border-0" accesskey="Q" placeholder="Search" t-model="state.searchTerm" t-on-keydown="onKeydownSearch" t-ref="autofocus" role="searchbox"/>
                        </div>
                    </div>
                    <button class="btn" t-att-class="state.searchedTerm === state.searchTerm ? 'btn-outline-primary' : 'btn-primary'" t-on-click="() => this.search()" aria-label="Search button">
                        <i t-if="!messageSearch.searching" class="o_searchview_icon oi oi-search" role="img" aria-label="Search Messages" title="Search Messages"/>
                        <i t-else="" class="fa fa-spin fa-spinner" aria-label="Search in progress" title="Search in progress"/>
                    </button>
                </div>
                <button t-if="env.inChatter" class="btn btn-outline-secondary ms-3" t-on-click="() => this.clear()" aria-label="Close button">
                    <i class="o_searchview_icon oi oi-close cursor-pointer" role="img" aria-label="Close search" title="close"/>
                </button>
            </div>
            <p t-if="MESSAGES_FOUND" class="o-mail-SearchMessagesPanel-title py-1 mb-0 fw-bolder text-center text-uppercase text-700">
                <t t-out="MESSAGES_FOUND"/>
            </p>
            <MessageCardList messages="messageSearch.messages" thread="props.thread" mode="'search'" messageSearch="messageSearch" showEmpty="messageSearch.messages.length === 0 and messageSearch.searched" onClickJump="() => this.props.onClickJump?.()" loadMore="messageSearch.loadMore" onLoadMoreVisible="onLoadMoreVisible"/>
        </ActionPanel>
    </t>

</templates>
