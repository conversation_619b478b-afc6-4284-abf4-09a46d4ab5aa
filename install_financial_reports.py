#!/usr/bin/env python3
import xmlrpc.client

def install_financial_reports():
    try:
        # Connection details
        url = 'http://localhost:8069'
        db = 'dataicraft'
        username = '<EMAIL>'
        password = 'admin123'
        
        # Connect to Odoo
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            print("❌ Authentication failed!")
            return
        
        print(f"✅ Connected to Odoo as user ID: {uid}")
        
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        print("\n=== Installing Financial Reporting Features ===")
        
        # List of financial reporting related modules
        financial_modules = [
            'account_reports',
            'account_financial_report', 
            'account_asset',
            'account_budget',
            'analytic',
        ]
        
        for module_name in financial_modules:
            try:
                # Search for the module
                module_ids = models.execute_kw(
                    db, uid, password,
                    'ir.module.module', 'search',
                    [[['name', '=', module_name]]]
                )
                
                if not module_ids:
                    print(f"❌ {module_name}: Module not found")
                    continue
                
                # Get module info
                module_info = models.execute_kw(
                    db, uid, password,
                    'ir.module.module', 'read',
                    [module_ids], {'fields': ['name', 'state', 'shortdesc']}
                )
                
                module = module_info[0]
                state = module['state']
                desc = module['shortdesc']
                
                print(f"\n📦 {module_name}: {desc}")
                print(f"   Current state: {state}")
                
                if state == 'installed':
                    print(f"   ✅ Already installed")
                elif state == 'uninstalled':
                    print(f"   🔄 Installing...")
                    # Install the module
                    models.execute_kw(
                        db, uid, password,
                        'ir.module.module', 'button_immediate_install',
                        [module_ids]
                    )
                    print(f"   ✅ Installation triggered")
                elif state == 'uninstallable':
                    print(f"   ⚠️ Not available in Community Edition")
                else:
                    print(f"   ⚠️ State: {state}")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        print(f"\n=== Creating Financial Report Menu Items ===")
        
        # Create custom financial report menu items if they don't exist
        try:
            # Find the main Invoicing menu
            invoicing_menu_ids = models.execute_kw(
                db, uid, password,
                'ir.ui.menu', 'search',
                [[['name', 'ilike', 'Invoicing'], ['parent_id', '=', False]]]
            )
            
            if invoicing_menu_ids:
                invoicing_menu_id = invoicing_menu_ids[0]
                print(f"📱 Found Invoicing menu: {invoicing_menu_id}")
                
                # Find or create Reporting submenu
                reporting_menu_ids = models.execute_kw(
                    db, uid, password,
                    'ir.ui.menu', 'search',
                    [[['name', 'ilike', 'Reporting'], ['parent_id', '=', invoicing_menu_id]]]
                )
                
                if reporting_menu_ids:
                    reporting_menu_id = reporting_menu_ids[0]
                    print(f"📊 Found Reporting menu: {reporting_menu_id}")
                    
                    # Create financial statement actions and menus
                    financial_reports = [
                        {
                            'name': 'Balance Sheet',
                            'model': 'account.account',
                            'view_mode': 'tree,form',
                            'domain': "[]",
                            'context': "{'search_default_account_type': 'asset_receivable,asset_cash,asset_current,asset_non_current,asset_fixed,liability_payable,liability_current,liability_non_current,equity,equity_unaffected'}"
                        },
                        {
                            'name': 'Profit & Loss',
                            'model': 'account.account', 
                            'view_mode': 'tree,form',
                            'domain': "[]",
                            'context': "{'search_default_account_type': 'income,income_other,expense,expense_depreciation,expense_direct_cost'}"
                        },
                        {
                            'name': 'General Ledger',
                            'model': 'account.move.line',
                            'view_mode': 'tree,form',
                            'domain': "[('move_id.state', '=', 'posted')]",
                            'context': "{}"
                        },
                        {
                            'name': 'Trial Balance',
                            'model': 'account.account',
                            'view_mode': 'tree,form', 
                            'domain': "[]",
                            'context': "{'show_balance': True}"
                        }
                    ]
                    
                    for report in financial_reports:
                        try:
                            # Create action
                            action_data = {
                                'name': report['name'],
                                'type': 'ir.actions.act_window',
                                'res_model': report['model'],
                                'view_mode': report['view_mode'],
                                'domain': report['domain'],
                                'context': report['context'],
                                'target': 'current',
                            }
                            
                            action_id = models.execute_kw(
                                db, uid, password,
                                'ir.actions.act_window', 'create',
                                [action_data]
                            )
                            
                            # Create menu item
                            menu_data = {
                                'name': report['name'],
                                'parent_id': reporting_menu_id,
                                'action': f'ir.actions.act_window,{action_id}',
                                'sequence': 10,
                            }
                            
                            menu_id = models.execute_kw(
                                db, uid, password,
                                'ir.ui.menu', 'create',
                                [menu_data]
                            )
                            
                            print(f"   ✅ Created {report['name']} menu (ID: {menu_id})")
                            
                        except Exception as e:
                            print(f"   ⚠️ {report['name']}: {e}")
                
        except Exception as e:
            print(f"❌ Menu creation error: {e}")
        
        print(f"\n=== Summary ===")
        print("✅ Financial reporting setup completed")
        print("🔄 Please refresh your browser and check:")
        print("   1. Go to Invoicing app")
        print("   2. Look for 'Reporting' menu")
        print("   3. You should see:")
        print("      - Balance Sheet")
        print("      - Profit & Loss") 
        print("      - General Ledger")
        print("      - Trial Balance")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    install_financial_reports()
