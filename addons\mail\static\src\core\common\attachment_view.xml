<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.AttachmentView">
        <div t-if="state.thread.attachmentsInWebClientView.length > 0" class="o-mail-Attachment">
            <t t-if="state.thread.mainAttachment">
                <h3 t-if="!state.thread.mainAttachment.isPdf" class="mt0 mb8 ps-2 text-muted text-center"><t t-esc="displayName"/></h3>
                <div t-if="state.thread.mainAttachment.isImage" class="o-mail-Attachment-imgContainer">
                    <img id="attachment_img" class="img img-fluid d-block" t-att-src="state.thread.mainAttachment.defaultSource"/>
                </div>
                <iframe t-if="state.thread.mainAttachment.isPdf" class="mb48" t-att-src="state.thread.mainAttachment.defaultSource" t-ref="iframeViewerPdf"/>
                <t t-if="state.thread.attachmentsInWebClientView.length > 1 or (state.thread.attachmentsInWebClientView.length > 0 and !(state.thread.mainAttachment.isPdf or state.thread.mainAttachment.isImage))">
                    <a class="arrow o_move_previous text-center" href="#" t-on-click.prevent="onClickPrevious">
                        <span class="oi oi-chevron-left"/>
                    </a>
                    <a class="arrow o_move_next text-center" href="#" t-on-click.prevent="onClickNext">
                        <span class="oi oi-chevron-right"/>
                    </a>
                </t>
            </t>
        </div>
    </t>

</templates>
