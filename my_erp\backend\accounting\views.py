"""
API Views for Accounting Module
"""
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Sum, Q
from django.utils import timezone
from datetime import datetime, timedelta
from decimal import Decimal

from .models import (
    Company, AccountType, Account, Customer, Vendor, TaxRate,
    Journal, JournalEntry, JournalEntryLine, Invoice, InvoiceLine,
    Bill, Payment
)
from .serializers import (
    CompanySerializer, AccountTypeSerializer, AccountSerializer,
    CustomerSerializer, VendorSerializer, TaxRateSerializer,
    JournalSerializer, JournalEntrySerializer, InvoiceSerializer,
    BillSerializer, PaymentSerializer, DashboardStatsSerializer,
    TrialBalanceSerializer, ProfitLossSerializer, BalanceSheetSerializer
)


class CompanyViewSet(viewsets.ModelViewSet):
    queryset = Company.objects.all()
    serializer_class = CompanySerializer


class AccountTypeViewSet(viewsets.ModelViewSet):
    queryset = AccountType.objects.all()
    serializer_class = AccountTypeSerializer


class AccountViewSet(viewsets.ModelViewSet):
    queryset = Account.objects.all()
    serializer_class = AccountSerializer
    filterset_fields = ['account_type', 'is_active', 'company']
    search_fields = ['code', 'name']
    ordering_fields = ['code', 'name']


class CustomerViewSet(viewsets.ModelViewSet):
    queryset = Customer.objects.all()
    serializer_class = CustomerSerializer
    filterset_fields = ['is_active', 'company']
    search_fields = ['name', 'email', 'company_name']
    ordering_fields = ['name', 'created_at']


class VendorViewSet(viewsets.ModelViewSet):
    queryset = Vendor.objects.all()
    serializer_class = VendorSerializer
    filterset_fields = ['is_active', 'company']
    search_fields = ['name', 'email', 'company_name']
    ordering_fields = ['name', 'created_at']


class TaxRateViewSet(viewsets.ModelViewSet):
    queryset = TaxRate.objects.all()
    serializer_class = TaxRateSerializer
    filterset_fields = ['is_active', 'company']


class JournalViewSet(viewsets.ModelViewSet):
    queryset = Journal.objects.all()
    serializer_class = JournalSerializer
    filterset_fields = ['type', 'is_active', 'company']


class JournalEntryViewSet(viewsets.ModelViewSet):
    queryset = JournalEntry.objects.all()
    serializer_class = JournalEntrySerializer
    filterset_fields = ['state', 'journal', 'company']
    search_fields = ['reference', 'description']
    ordering_fields = ['date', 'created_at']

    @action(detail=True, methods=['post'])
    def post_entry(self, request, pk=None):
        """Post a journal entry"""
        entry = self.get_object()
        if entry.post():
            return Response({'status': 'Entry posted successfully'})
        return Response(
            {'error': 'Entry cannot be posted. Check if it is balanced.'},
            status=status.HTTP_400_BAD_REQUEST
        )


class InvoiceViewSet(viewsets.ModelViewSet):
    queryset = Invoice.objects.all()
    serializer_class = InvoiceSerializer
    filterset_fields = ['status', 'customer', 'company']
    search_fields = ['number', 'customer__name']
    ordering_fields = ['date', 'due_date', 'created_at']

    @action(detail=False)
    def overdue(self, request):
        """Get overdue invoices"""
        today = timezone.now().date()
        overdue_invoices = self.queryset.filter(
            due_date__lt=today,
            status__in=['SENT', 'PARTIAL']
        )
        serializer = self.get_serializer(overdue_invoices, many=True)
        return Response(serializer.data)


class BillViewSet(viewsets.ModelViewSet):
    queryset = Bill.objects.all()
    serializer_class = BillSerializer
    filterset_fields = ['status', 'vendor', 'company']
    search_fields = ['number', 'vendor__name', 'vendor_reference']
    ordering_fields = ['date', 'due_date', 'created_at']


class PaymentViewSet(viewsets.ModelViewSet):
    queryset = Payment.objects.all()
    serializer_class = PaymentSerializer
    filterset_fields = ['type', 'method', 'customer', 'vendor', 'company']
    ordering_fields = ['date', 'created_at']


class DashboardViewSet(viewsets.ViewSet):
    """Dashboard statistics and reports"""

    @action(detail=False)
    def stats(self, request):
        """Get dashboard statistics"""
        company_id = request.query_params.get('company')
        
        # Base queryset filters
        filters = {}
        if company_id:
            filters['company_id'] = company_id

        # Calculate statistics
        stats = {
            'total_customers': Customer.objects.filter(**filters, is_active=True).count(),
            'total_vendors': Vendor.objects.filter(**filters, is_active=True).count(),
            'total_invoices': Invoice.objects.filter(**filters).count(),
            'total_bills': Bill.objects.filter(**filters).count(),
            'outstanding_receivables': Invoice.objects.filter(
                **filters, status__in=['SENT', 'PARTIAL']
            ).aggregate(
                total=Sum('total_amount') - Sum('paid_amount')
            )['total'] or Decimal('0'),
            'outstanding_payables': Bill.objects.filter(
                **filters, status__in=['RECEIVED', 'PARTIAL']
            ).aggregate(
                total=Sum('total_amount') - Sum('paid_amount')
            )['total'] or Decimal('0'),
        }

        # Monthly revenue and expenses
        current_month = timezone.now().replace(day=1)
        revenue_accounts = Account.objects.filter(
            **filters, account_type__type='REVENUE'
        )
        expense_accounts = Account.objects.filter(
            **filters, account_type__type='EXPENSE'
        )

        monthly_revenue = JournalEntryLine.objects.filter(
            account__in=revenue_accounts,
            entry__date__gte=current_month,
            entry__state='POSTED'
        ).aggregate(total=Sum('credit_amount'))['total'] or Decimal('0')

        monthly_expenses = JournalEntryLine.objects.filter(
            account__in=expense_accounts,
            entry__date__gte=current_month,
            entry__state='POSTED'
        ).aggregate(total=Sum('debit_amount'))['total'] or Decimal('0')

        stats.update({
            'monthly_revenue': monthly_revenue,
            'monthly_expenses': monthly_expenses,
        })

        serializer = DashboardStatsSerializer(stats)
        return Response(serializer.data)

    @action(detail=False)
    def trial_balance(self, request):
        """Generate trial balance"""
        company_id = request.query_params.get('company')
        as_of_date = request.query_params.get('as_of_date', timezone.now().date())

        filters = {'is_active': True}
        if company_id:
            filters['company_id'] = company_id

        accounts = Account.objects.filter(**filters)
        account_balances = []
        total_debits = Decimal('0')
        total_credits = Decimal('0')

        for account in accounts:
            # Calculate balance up to the specified date
            entries = JournalEntryLine.objects.filter(
                account=account,
                entry__date__lte=as_of_date,
                entry__state='POSTED'
            )
            
            debit_sum = entries.aggregate(total=Sum('debit_amount'))['total'] or Decimal('0')
            credit_sum = entries.aggregate(total=Sum('credit_amount'))['total'] or Decimal('0')
            
            if account.account_type.type in ['ASSET', 'EXPENSE']:
                balance = debit_sum - credit_sum
                if balance > 0:
                    total_debits += balance
            else:
                balance = credit_sum - debit_sum
                if balance > 0:
                    total_credits += balance

            if balance != 0:  # Only include accounts with non-zero balances
                account_balances.append({
                    'account_code': account.code,
                    'account_name': account.name,
                    'account_type': account.account_type.type,
                    'balance': balance
                })

        trial_balance_data = {
            'accounts': account_balances,
            'total_debits': total_debits,
            'total_credits': total_credits,
            'is_balanced': total_debits == total_credits
        }

        serializer = TrialBalanceSerializer(trial_balance_data)
        return Response(serializer.data)

    @action(detail=False)
    def profit_loss(self, request):
        """Generate Profit & Loss statement"""
        company_id = request.query_params.get('company')
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date', timezone.now().date())

        if not start_date:
            # Default to current month
            start_date = timezone.now().replace(day=1).date()

        filters = {'is_active': True}
        if company_id:
            filters['company_id'] = company_id

        # Get revenue and expense accounts
        revenue_accounts = Account.objects.filter(**filters, account_type__type='REVENUE')
        expense_accounts = Account.objects.filter(**filters, account_type__type='EXPENSE')

        revenue_data = []
        expense_data = []
        total_revenue = Decimal('0')
        total_expenses = Decimal('0')

        # Calculate revenue
        for account in revenue_accounts:
            entries = JournalEntryLine.objects.filter(
                account=account,
                entry__date__range=[start_date, end_date],
                entry__state='POSTED'
            )
            balance = entries.aggregate(total=Sum('credit_amount'))['total'] or Decimal('0')

            if balance > 0:
                revenue_data.append({
                    'account_code': account.code,
                    'account_name': account.name,
                    'account_type': account.account_type.type,
                    'balance': balance
                })
                total_revenue += balance

        # Calculate expenses
        for account in expense_accounts:
            entries = JournalEntryLine.objects.filter(
                account=account,
                entry__date__range=[start_date, end_date],
                entry__state='POSTED'
            )
            balance = entries.aggregate(total=Sum('debit_amount'))['total'] or Decimal('0')

            if balance > 0:
                expense_data.append({
                    'account_code': account.code,
                    'account_name': account.name,
                    'account_type': account.account_type.type,
                    'balance': balance
                })
                total_expenses += balance

        profit_loss_data = {
            'revenue_accounts': revenue_data,
            'expense_accounts': expense_data,
            'total_revenue': total_revenue,
            'total_expenses': total_expenses,
            'net_profit': total_revenue - total_expenses,
            'period_start': start_date,
            'period_end': end_date
        }

        serializer = ProfitLossSerializer(profit_loss_data)
        return Response(serializer.data)

    @action(detail=False)
    def balance_sheet(self, request):
        """Generate Balance Sheet"""
        company_id = request.query_params.get('company')
        as_of_date = request.query_params.get('as_of_date', timezone.now().date())

        filters = {'is_active': True}
        if company_id:
            filters['company_id'] = company_id

        # Get accounts by type
        asset_accounts = Account.objects.filter(**filters, account_type__type='ASSET')
        liability_accounts = Account.objects.filter(**filters, account_type__type='LIABILITY')
        equity_accounts = Account.objects.filter(**filters, account_type__type='EQUITY')

        assets_data = []
        liabilities_data = []
        equity_data = []
        total_assets = Decimal('0')
        total_liabilities = Decimal('0')
        total_equity = Decimal('0')

        # Calculate assets
        for account in asset_accounts:
            entries = JournalEntryLine.objects.filter(
                account=account,
                entry__date__lte=as_of_date,
                entry__state='POSTED'
            )
            debit_sum = entries.aggregate(total=Sum('debit_amount'))['total'] or Decimal('0')
            credit_sum = entries.aggregate(total=Sum('credit_amount'))['total'] or Decimal('0')
            balance = debit_sum - credit_sum

            if balance != 0:
                assets_data.append({
                    'account_code': account.code,
                    'account_name': account.name,
                    'account_type': account.account_type.type,
                    'balance': balance
                })
                total_assets += balance

        # Calculate liabilities
        for account in liability_accounts:
            entries = JournalEntryLine.objects.filter(
                account=account,
                entry__date__lte=as_of_date,
                entry__state='POSTED'
            )
            debit_sum = entries.aggregate(total=Sum('debit_amount'))['total'] or Decimal('0')
            credit_sum = entries.aggregate(total=Sum('credit_amount'))['total'] or Decimal('0')
            balance = credit_sum - debit_sum

            if balance != 0:
                liabilities_data.append({
                    'account_code': account.code,
                    'account_name': account.name,
                    'account_type': account.account_type.type,
                    'balance': balance
                })
                total_liabilities += balance

        # Calculate equity
        for account in equity_accounts:
            entries = JournalEntryLine.objects.filter(
                account=account,
                entry__date__lte=as_of_date,
                entry__state='POSTED'
            )
            debit_sum = entries.aggregate(total=Sum('debit_amount'))['total'] or Decimal('0')
            credit_sum = entries.aggregate(total=Sum('credit_amount'))['total'] or Decimal('0')
            balance = credit_sum - debit_sum

            if balance != 0:
                equity_data.append({
                    'account_code': account.code,
                    'account_name': account.name,
                    'account_type': account.account_type.type,
                    'balance': balance
                })
                total_equity += balance

        balance_sheet_data = {
            'assets': assets_data,
            'liabilities': liabilities_data,
            'equity': equity_data,
            'total_assets': total_assets,
            'total_liabilities': total_liabilities,
            'total_equity': total_equity,
            'as_of_date': as_of_date
        }

        serializer = BalanceSheetSerializer(balance_sheet_data)
        return Response(serializer.data)
