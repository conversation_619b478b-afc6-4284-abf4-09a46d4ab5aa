#!/usr/bin/env python3
import psycopg2

def complete_accounting_cleanup():
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='dataicraft',
            user='odoo',
            password='odoo123'
        )
        cur = conn.cursor()
        
        print("🧹 === COMPLETE ACCOUNTING CLEANUP === 🧹")
        
        # Step 1: Delete ALL journal entries and lines
        print("\n🗑️ Step 1: Deleting ALL journal entries...")
        
        cur.execute("DELETE FROM account_move_line;")
        deleted_lines = cur.rowcount
        print(f"✅ Deleted {deleted_lines} journal items (account_move_line)")
        
        cur.execute("DELETE FROM account_move;")
        deleted_moves = cur.rowcount
        print(f"✅ Deleted {deleted_moves} journal entries (account_move)")
        
        # Step 2: Delete duplicate journals (keep only one of each type)
        print("\n🗑️ Step 2: Cleaning duplicate journals...")
        
        # Get unique journals by code and type
        cur.execute("""
            SELECT MIN(id) as keep_id, code, type, COUNT(*) as duplicates
            FROM account_journal
            GROUP BY code, type
            HAVING COUNT(*) > 1;
        """)
        
        duplicate_journals = cur.fetchall()
        
        for keep_id, code, jtype, count in duplicate_journals:
            cur.execute("""
                DELETE FROM account_journal 
                WHERE code = %s AND type = %s AND id != %s;
            """, (code, jtype, keep_id))
            deleted = cur.rowcount
            print(f"✅ Removed {deleted} duplicate journals for {code} ({jtype})")
        
        # Step 3: Reset journal sequences
        print("\n🔄 Step 3: Resetting journal sequences...")
        
        cur.execute("UPDATE ir_sequence SET number_next = 1 WHERE code LIKE 'account.%';")
        reset_sequences = cur.rowcount
        print(f"✅ Reset {reset_sequences} accounting sequences")
        
        # Step 4: Clean up related accounting data
        print("\n🧹 Step 4: Cleaning related accounting data...")
        
        # Delete payment records
        cur.execute("DELETE FROM account_payment;")
        deleted_payments = cur.rowcount
        print(f"✅ Deleted {deleted_payments} payment records")
        
        # Delete reconciliation records
        cur.execute("DELETE FROM account_partial_reconcile;")
        deleted_reconcile = cur.rowcount
        print(f"✅ Deleted {deleted_reconcile} reconciliation records")
        
        # Delete bank statement lines
        cur.execute("DELETE FROM account_bank_statement_line;")
        deleted_statement_lines = cur.rowcount
        print(f"✅ Deleted {deleted_statement_lines} bank statement lines")
        
        cur.execute("DELETE FROM account_bank_statement;")
        deleted_statements = cur.rowcount
        print(f"✅ Deleted {deleted_statements} bank statements")
        
        # Step 5: Clean up tax-related data
        print("\n🧹 Step 5: Cleaning tax data...")
        
        cur.execute("DELETE FROM account_tax_repartition_line_account_tag_rel;")
        cur.execute("DELETE FROM account_move_line_account_tax_rel;")
        print(f"✅ Cleaned tax-related data")
        
        # Step 6: Reset company accounting settings
        print("\n⚙️ Step 6: Resetting company accounting settings...")
        
        cur.execute("""
            UPDATE res_company 
            SET account_opening_move_id = NULL,
                account_opening_journal_id = NULL,
                fiscalyear_last_day = 31,
                fiscalyear_last_month = 12;
        """)
        print(f"✅ Reset company accounting settings")
        
        # Step 7: Vacuum and analyze accounting tables
        print("\n🔧 Step 7: Optimizing accounting tables...")
        
        conn.commit()  # Commit before vacuum
        conn.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_AUTOCOMMIT)
        
        accounting_tables = [
            'account_move', 'account_move_line', 'account_journal',
            'account_payment', 'account_bank_statement', 'account_bank_statement_line',
            'account_partial_reconcile'
        ]
        
        for table in accounting_tables:
            cur.execute(f"VACUUM ANALYZE {table};")
            print(f"✅ Optimized {table}")
        
        # Step 8: Final verification
        print(f"\n✅ Final Verification:")
        
        conn.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_READ_COMMITTED)
        
        cur.execute("SELECT COUNT(*) FROM account_move;")
        moves = cur.fetchone()[0]
        print(f"Journal entries: {moves}")
        
        cur.execute("SELECT COUNT(*) FROM account_move_line;")
        lines = cur.fetchone()[0]
        print(f"Journal items: {lines}")
        
        cur.execute("SELECT COUNT(*) FROM account_journal;")
        journals = cur.fetchone()[0]
        print(f"Journals: {journals}")
        
        cur.execute("SELECT COUNT(*) FROM account_payment;")
        payments = cur.fetchone()[0]
        print(f"Payments: {payments}")
        
        # Check for any remaining balances
        cur.execute("""
            SELECT COUNT(*) 
            FROM account_account aa
            LEFT JOIN account_move_line aml ON aa.id = aml.account_id
            GROUP BY aa.id
            HAVING SUM(COALESCE(aml.debit, 0) - COALESCE(aml.credit, 0)) != 0;
        """)
        
        accounts_with_balance = cur.fetchone()
        balance_count = accounts_with_balance[0] if accounts_with_balance else 0
        print(f"Accounts with balances: {balance_count}")
        
        print(f"\n🎉 COMPLETE ACCOUNTING CLEANUP FINISHED!")
        
        print(f"\n📋 What's Now Clean:")
        print(f"✅ ALL journal entries deleted")
        print(f"✅ ALL journal items deleted") 
        print(f"✅ Duplicate journals removed")
        print(f"✅ ALL payment records deleted")
        print(f"✅ ALL bank statements deleted")
        print(f"✅ ALL reconciliation data deleted")
        print(f"✅ Company accounting settings reset")
        print(f"✅ Sequences reset to start fresh")
        print(f"✅ ALL account balances are zero")
        
        print(f"\n📋 What's Preserved:")
        print(f"✅ Chart of Accounts structure")
        print(f"✅ Journal definitions (one of each type)")
        print(f"✅ Tax configurations")
        print(f"✅ Account types and categories")
        print(f"✅ Fiscal year settings")
        
        print(f"\n🚀 Ready for Clean Accounting Start!")
        print(f"You can now:")
        print(f"1. Set opening balances if needed")
        print(f"2. Create real customer invoices")
        print(f"3. Record real vendor bills")
        print(f"4. Make real payments")
        print(f"5. All accounting will start fresh")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    complete_accounting_cleanup()
