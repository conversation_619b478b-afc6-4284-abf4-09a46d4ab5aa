#!/usr/bin/env python3
import psycopg2
import time

def monitor_companies():
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='dataicraft',
            user='odoo',
            password='odoo123'
        )
        cur = conn.cursor()
        
        print("=== MONITORING COMPANIES IN DATAICRAFT DATABASE ===")
        print("Current companies:")
        
        while True:
            cur.execute("""
                SELECT id, name, email, phone, currency_id, create_date 
                FROM res_company 
                ORDER BY id;
            """)
            companies = cur.fetchall()
            
            print(f"\n[{time.strftime('%H:%M:%S')}] Companies found: {len(companies)}")
            for company in companies:
                print(f"  ID: {company[0]} | Name: {company[1]} | Email: {company[2]}")
                print(f"  Phone: {company[3]} | Currency ID: {company[4]} | Created: {company[5]}")
                print("-" * 60)
            
            # Check total table count
            cur.execute("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';")
            table_count = cur.fetchone()[0]
            print(f"Total tables: {table_count}")
            
            time.sleep(5)  # Check every 5 seconds
            
    except KeyboardInterrupt:
        print("\nMonitoring stopped.")
    except Exception as e:
        print(f"Error: {e}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    monitor_companies()
