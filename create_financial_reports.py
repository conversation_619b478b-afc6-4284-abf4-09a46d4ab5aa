#!/usr/bin/env python3
import xmlrpc.client

def create_financial_reports():
    try:
        # Connection details
        url = 'http://localhost:8069'
        db = 'dataicraft'
        username = '<EMAIL>'
        password = 'admin123'
        
        # Connect to Odoo
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            print("❌ Authentication failed!")
            return
        
        print(f"✅ Connected to Odoo as user ID: {uid}")
        
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        print("\n=== Creating Advanced Financial Reports ===")
        
        # Find the Reporting menu
        reporting_menu_ids = models.execute_kw(
            db, uid, password,
            'ir.ui.menu', 'search',
            [[['name', 'ilike', 'Reporting'], ['parent_id', '!=', False]]]
        )
        
        if not reporting_menu_ids:
            print("❌ Reporting menu not found")
            return
        
        reporting_menu_id = reporting_menu_ids[0]
        print(f"📊 Using Reporting menu: {reporting_menu_id}")
        
        # Advanced financial reports
        advanced_reports = [
            {
                'name': 'Aged Receivables',
                'model': 'account.move.line',
                'view_mode': 'tree,form',
                'domain': "[('account_id.account_type', '=', 'asset_receivable'), ('reconciled', '=', False), ('move_id.state', '=', 'posted')]",
                'context': "{'search_default_partner_id': 1, 'search_default_overdue': 1}",
                'sequence': 20
            },
            {
                'name': 'Aged Payables', 
                'model': 'account.move.line',
                'view_mode': 'tree,form',
                'domain': "[('account_id.account_type', '=', 'liability_payable'), ('reconciled', '=', False), ('move_id.state', '=', 'posted')]",
                'context': "{'search_default_partner_id': 1, 'search_default_overdue': 1}",
                'sequence': 21
            },
            {
                'name': 'Cash Flow Statement',
                'model': 'account.move.line',
                'view_mode': 'tree,form',
                'domain': "[('account_id.account_type', 'in', ['asset_cash', 'liability_current']), ('move_id.state', '=', 'posted')]",
                'context': "{'group_by': ['account_id', 'date']}",
                'sequence': 22
            },
            {
                'name': 'Tax Report',
                'model': 'account.move.line',
                'view_mode': 'tree,form',
                'domain': "[('tax_line_id', '!=', False), ('move_id.state', '=', 'posted')]",
                'context': "{'group_by': ['tax_line_id', 'date']}",
                'sequence': 23
            },
            {
                'name': 'Journal Analysis',
                'model': 'account.move',
                'view_mode': 'tree,form',
                'domain': "[('state', '=', 'posted')]",
                'context': "{'group_by': ['journal_id', 'date']}",
                'sequence': 24
            },
            {
                'name': 'Partner Ledger',
                'model': 'account.move.line',
                'view_mode': 'tree,form',
                'domain': "[('partner_id', '!=', False), ('move_id.state', '=', 'posted')]",
                'context': "{'group_by': ['partner_id', 'account_id']}",
                'sequence': 25
            }
        ]
        
        for report in advanced_reports:
            try:
                # Check if action already exists
                existing_actions = models.execute_kw(
                    db, uid, password,
                    'ir.actions.act_window', 'search',
                    [[['name', '=', report['name']]]]
                )
                
                if existing_actions:
                    print(f"   ⚠️ {report['name']}: Already exists")
                    continue
                
                # Create action
                action_data = {
                    'name': report['name'],
                    'type': 'ir.actions.act_window',
                    'res_model': report['model'],
                    'view_mode': report['view_mode'],
                    'domain': report['domain'],
                    'context': report['context'],
                    'target': 'current',
                }
                
                action_id = models.execute_kw(
                    db, uid, password,
                    'ir.actions.act_window', 'create',
                    [action_data]
                )
                
                # Create menu item
                menu_data = {
                    'name': report['name'],
                    'parent_id': reporting_menu_id,
                    'action': f'ir.actions.act_window,{action_id}',
                    'sequence': report['sequence'],
                }
                
                menu_id = models.execute_kw(
                    db, uid, password,
                    'ir.ui.menu', 'create',
                    [menu_data]
                )
                
                print(f"   ✅ Created {report['name']} (Menu ID: {menu_id})")
                
            except Exception as e:
                print(f"   ❌ {report['name']}: {e}")
        
        print(f"\n=== Creating Pakistani Specific Reports ===")
        
        # Pakistani specific reports
        pakistani_reports = [
            {
                'name': 'Sales Tax Report (17%)',
                'model': 'account.move.line',
                'view_mode': 'tree,form',
                'domain': "[('tax_ids.amount', '=', 17.0), ('move_id.state', '=', 'posted')]",
                'context': "{'group_by': ['date', 'partner_id']}",
                'sequence': 30
            },
            {
                'name': 'Withholding Tax Report',
                'model': 'account.move.line', 
                'view_mode': 'tree,form',
                'domain': "[('tax_ids.name', 'ilike', 'withholding'), ('move_id.state', '=', 'posted')]",
                'context': "{'group_by': ['date', 'partner_id']}",
                'sequence': 31
            },
            {
                'name': 'PKR Currency Report',
                'model': 'account.move.line',
                'view_mode': 'tree,form',
                'domain': "[('currency_id.name', '=', 'PKR'), ('move_id.state', '=', 'posted')]",
                'context': "{'group_by': ['account_id', 'date']}",
                'sequence': 32
            }
        ]
        
        for report in pakistani_reports:
            try:
                # Check if action already exists
                existing_actions = models.execute_kw(
                    db, uid, password,
                    'ir.actions.act_window', 'search',
                    [[['name', '=', report['name']]]]
                )
                
                if existing_actions:
                    print(f"   ⚠️ {report['name']}: Already exists")
                    continue
                
                # Create action
                action_data = {
                    'name': report['name'],
                    'type': 'ir.actions.act_window',
                    'res_model': report['model'],
                    'view_mode': report['view_mode'],
                    'domain': report['domain'],
                    'context': report['context'],
                    'target': 'current',
                }
                
                action_id = models.execute_kw(
                    db, uid, password,
                    'ir.actions.act_window', 'create',
                    [action_data]
                )
                
                # Create menu item
                menu_data = {
                    'name': report['name'],
                    'parent_id': reporting_menu_id,
                    'action': f'ir.actions.act_window,{action_id}',
                    'sequence': report['sequence'],
                }
                
                menu_id = models.execute_kw(
                    db, uid, password,
                    'ir.ui.menu', 'create',
                    [menu_data]
                )
                
                print(f"   ✅ Created {report['name']} (Menu ID: {menu_id})")
                
            except Exception as e:
                print(f"   ❌ {report['name']}: {e}")
        
        print(f"\n=== Final Summary ===")
        print("🎉 Complete financial reporting system created!")
        print("\n📊 Available Financial Reports:")
        print("   ✅ Balance Sheet")
        print("   ✅ Profit & Loss Statement") 
        print("   ✅ General Ledger")
        print("   ✅ Trial Balance")
        print("   ✅ Aged Receivables")
        print("   ✅ Aged Payables")
        print("   ✅ Cash Flow Statement")
        print("   ✅ Tax Report")
        print("   ✅ Journal Analysis")
        print("   ✅ Partner Ledger")
        print("\n🇵🇰 Pakistani Specific Reports:")
        print("   ✅ Sales Tax Report (17%)")
        print("   ✅ Withholding Tax Report")
        print("   ✅ PKR Currency Report")
        
        print(f"\n🔄 Next Steps:")
        print("1. Refresh your browser (F5)")
        print("2. Go to Invoicing app")
        print("3. Click on 'Reporting' menu")
        print("4. You'll see all financial statements!")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    create_financial_reports()
