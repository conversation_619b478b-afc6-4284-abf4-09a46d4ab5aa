"""
Setup URLs - URL configuration for setup app
Following DRF router patterns with comprehensive setup endpoints
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# Create router and register viewsets
router = DefaultRouter()
router.register(r'countries', views.CountryViewSet, basename='country')
router.register(r'currencies', views.CurrencyViewSet, basename='currency')
router.register(r'timezones', views.TimezoneViewSet, basename='timezone')
router.register(r'companies', views.CompanySetupViewSet, basename='company-setup')
router.register(r'bank-accounts', views.BankAccountViewSet, basename='bank-account')
router.register(r'tax-configurations', views.TaxConfigurationViewSet, basename='tax-configuration')
router.register(r'payment-terms', views.PaymentTermViewSet, basename='payment-term')
router.register(r'templates', views.SetupTemplateViewSet, basename='setup-template')
router.register(r'system-config', views.SystemConfigurationViewSet, basename='system-config')

# URL patterns
urlpatterns = [
    path('api/setup/', include(router.urls)),
]
