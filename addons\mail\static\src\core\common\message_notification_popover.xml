<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.MessageNotificationPopover">
        <div class="o-mail-MessageNotificationPopover m-2">
            <div t-foreach="props.message.notifications" t-as="notification" t-key="notification.id">
                <i class="me-2" t-att-class="notification.statusIcon" t-att-title="notification.statusTitle" role="img"/>
                <span t-if="notification.persona" t-esc="notification.persona.nameOrDisplayName"/>
            </div>
        </div>
    </t>

</templates>
