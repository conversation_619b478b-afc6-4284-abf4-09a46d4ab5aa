<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="mail.ActivityListPopoverItem">
        <div class="o-mail-ActivityListPopoverItem d-flex flex-column border-bottom py-2">
            <div class="overflow-auto d-flex align-items-baseline ms-3 me-1">
                <i t-if="props.activity.icon" class="fa small me-2" t-attf-class="{{ props.activity.icon }}" role="img"/>
                <t t-if="props.activity.summary">
                    <b class="text-900 me-2 text-truncate flex-grow-1 flex-basis-0" t-esc="props.activity.summary"/>
                </t>
                <t t-if="!props.activity.summary and props.activity.activity_type_id">
                    <b class="text-900 me-2 text-truncate flex-grow-1" t-esc="props.activity.activity_type_id[1]"/>
                </t>
                <button t-if="hasEditButton" class="o-mail-ActivityListPopoverItem-editbtn btn btn-sm btn-success btn-link" t-on-click="onClickEditActivityButton">
                    <i class="fa fa-pencil"/>
                </button>
                <t t-if="props.activity.can_write">
                    <FileUploader t-if="hasFileUploader" onUploaded.bind="onFileUploaded">
                        <t t-set-slot="toggler">
                            <button class="btn btn-sm btn-success btn-link" title="Upload file" aria-label="Upload File">
                                <i class="fa fa-upload"/>
                            </button>
                        </t>
                    </FileUploader>
                    <button t-if="hasMarkDoneButton" class="o-mail-ActivityListPopoverItem-markAsDone btn btn-sm btn-success btn-link" title="Mark as done" aria-label="Mark as done" t-on-click="onClickMarkAsDone">
                        <i class="fa fa-check-circle"/>
                    </button>
                </t>
            </div>
            <div class="d-flex align-items-center flex-wrap mx-3">
                <img t-if="props.activity.user_id[0]" class="me-2 rounded" t-att-src="activityAssigneeAvatar" style="max-width: 1.5rem; max-height: 1.5rem;"/>
                <div class="mt-1">
                    <t t-if="props.activity.user_id[0]">
                        <small class="text-truncate" t-esc="props.activity.user_id[1]"/>
                        <small t-if="props.activity.state !== 'today'" class="mx-1">-</small>
                    </t>
                    <small t-if="['overdue', 'planned'].includes(props.activity.state)" class="fw-bold" t-att-title="dateDeadlineFormatted" t-esc="delayLabel"/>
                    <small t-if="props.activity.state === 'done'" class="fw-bold" t-att-title="dateDoneFormatted" t-esc="dateDoneFormatted"/>
                </div>
            </div>
            <div t-if="props.activity.attachment_ids?.length" class="mx-5">
                <t t-foreach="props.activity.attachment_ids" t-as="attachment" t-key="attachment.id">
                    <a t-attf-href="/web/content/#{attachment.id}?download=true"
                       t-on-click.stop="" t-out="attachment.name"
                       class="d-inline-block text-truncate" style="max-width: 120px;"/>
                    <br t-if="!attachment_last"/>
                </t>
            </div>
            <ActivityMarkAsDone t-if="state.hasMarkDoneView" activity="props.activity" close="closeMarkAsDone" onClickDoneAndScheduleNext="props.onClickDoneAndScheduleNext" reload="props.onActivityChanged"/>
            <div t-if="props.activity.mail_template_ids.length > 0" class="mx-3 mt-2">
                <ActivityMailTemplate activity="props.activity" onClickButtons="props.onClickEditActivityButton" onUpdate="props.onActivityChanged"/>
            </div>
        </div>
    </t>
</templates>
