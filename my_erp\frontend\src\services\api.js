import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: 'http://localhost:8000/api/accounting',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API endpoints
export const accountingAPI = {
  // Companies
  getCompanies: () => api.get('/api/companies/'),
  getCompany: (id) => api.get(`/api/companies/${id}/`),
  createCompany: (data) => api.post('/api/companies/', data),
  updateCompany: (id, data) => api.put(`/api/companies/${id}/`, data),
  deleteCompany: (id) => api.delete(`/api/companies/${id}/`),

  // Partners (Customers/Vendors)
  getPartners: (params) => api.get('/api/partners/', { params }),
  getPartner: (id) => api.get(`/api/partners/${id}/`),
  createPartner: (data) => api.post('/api/partners/', data),
  updatePartner: (id, data) => api.put(`/api/partners/${id}/`, data),
  deletePartner: (id) => api.delete(`/api/partners/${id}/`),
  getCustomers: () => api.get('/api/partners/customers/'),
  getVendors: () => api.get('/api/partners/vendors/'),

  // Chart of Accounts
  getAccounts: (params) => api.get('/api/accounts/', { params }),
  getAccount: (id) => api.get(`/api/accounts/${id}/`),
  createAccount: (data) => api.post('/api/accounts/', data),
  updateAccount: (id, data) => api.put(`/api/accounts/${id}/`, data),
  deleteAccount: (id) => api.delete(`/api/accounts/${id}/`),
  getReceivableAccounts: () => api.get('/api/accounts/receivable/'),
  getPayableAccounts: () => api.get('/api/accounts/payable/'),

  // Journals
  getJournals: (params) => api.get('/api/journals/', { params }),
  getJournal: (id) => api.get(`/api/journals/${id}/`),
  createJournal: (data) => api.post('/api/journals/', data),
  updateJournal: (id, data) => api.put(`/api/journals/${id}/`, data),
  deleteJournal: (id) => api.delete(`/api/journals/${id}/`),

  // Journal Entries/Moves
  getMoves: (params) => api.get('/api/moves/', { params }),
  getMove: (id) => api.get(`/api/moves/${id}/`),
  createMove: (data) => api.post('/api/moves/', data),
  updateMove: (id, data) => api.put(`/api/moves/${id}/`, data),
  deleteMove: (id) => api.delete(`/api/moves/${id}/`),
  postMove: (id) => api.post(`/api/moves/${id}/action_post/`),
  getInvoices: () => api.get('/api/moves/invoices/'),
  getBills: () => api.get('/api/moves/bills/'),

  // Move Lines
  getMoveLines: (params) => api.get('/api/move-lines/', { params }),
  getMoveLine: (id) => api.get(`/api/move-lines/${id}/`),
  createMoveLine: (data) => api.post('/api/move-lines/', data),
  updateMoveLine: (id, data) => api.put(`/api/move-lines/${id}/`, data),
  deleteMoveLine: (id) => api.delete(`/api/move-lines/${id}/`),
  getUnreconciledLines: () => api.get('/api/move-lines/unreconciled/'),

  // Payments
  getPayments: (params) => api.get('/api/payments/', { params }),
  getPayment: (id) => api.get(`/api/payments/${id}/`),
  createPayment: (data) => api.post('/api/payments/', data),
  updatePayment: (id, data) => api.put(`/api/payments/${id}/`, data),
  deletePayment: (id) => api.delete(`/api/payments/${id}/`),
  postPayment: (id) => api.post(`/api/payments/${id}/action_post/`),

  // Taxes
  getTaxes: (params) => api.get('/api/taxes/', { params }),
  getTax: (id) => api.get(`/api/taxes/${id}/`),
  createTax: (data) => api.post('/api/taxes/', data),
  updateTax: (id, data) => api.put(`/api/taxes/${id}/`, data),
  deleteTax: (id) => api.delete(`/api/taxes/${id}/`),

  // Payment Terms
  getPaymentTerms: (params) => api.get('/api/payment-terms/', { params }),
  getPaymentTerm: (id) => api.get(`/api/payment-terms/${id}/`),
  createPaymentTerm: (data) => api.post('/api/payment-terms/', data),
  updatePaymentTerm: (id, data) => api.put(`/api/payment-terms/${id}/`, data),
  deletePaymentTerm: (id) => api.delete(`/api/payment-terms/${id}/`),

  // Dashboard & Reports
  getDashboardStats: (params) => api.get('/api/dashboard/stats/', { params }),
  getTrialBalance: (params) => api.get('/api/reports/trial-balance/', { params }),
  getProfitLoss: (params) => api.get('/api/reports/profit-loss/', { params }),
  getBalanceSheet: (params) => api.get('/api/reports/balance-sheet/', { params }),

  // Reconciliation
  getBankReconciliation: (params) => api.get('/api/reconciliation/bank/', { params }),
  performManualReconciliation: (data) => api.post('/api/reconciliation/manual/', data),

  // Utilities
  getNextSequence: (params) => api.get('/api/sequence/next/', { params }),
  setupChartOfAccounts: (data) => api.post('/api/chart-of-accounts/setup/', data),
};

export default api;
