#!/usr/bin/env python3
import xmlrpc.client
import time

def install_construction_industry():
    try:
        # Connection details
        url = 'http://localhost:8069'
        db = 'dataicraft'
        username = '<EMAIL>'
        password = 'admin123'
        
        # Connect to Odoo
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            print("❌ Authentication failed!")
            return
        
        print(f"✅ Connected to Odoo as user ID: {uid}")
        
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        print("\n🏗️ === Installing Complete Construction Industry System === 🏗️")
        
        # Construction industry modules in installation order
        construction_modules = [
            # Core Project Management
            'project',                      # Project Management
            'hr_timesheet',                # Timesheet Management
            
            # Project Integration
            'project_account',             # Project Accounting
            'sale_project',                # Sales Project Integration
            'sale_timesheet',              # Sales Timesheet
            'project_purchase',            # Project Purchase Management
            'project_hr_expense',          # Project Expense Management
            
            # Advanced Features
            'project_mrp',                 # Manufacturing Project Integration
            'sale_project_stock',          # Project Stock Integration
            'sale_timesheet_margin',       # Service Margins
            'project_timesheet_holidays',  # Timesheet Holidays
            
            # Additional Tools
            'project_todo',                # To-Do Lists
            'project_sms',                 # SMS Integration
            'hr_timesheet_attendance',     # Attendance Integration
        ]
        
        installed_modules = []
        
        for module_name in construction_modules:
            try:
                print(f"\n🏗️ Processing {module_name}...")
                
                # Search for the module
                module_ids = models.execute_kw(
                    db, uid, password,
                    'ir.module.module', 'search',
                    [[['name', '=', module_name]]]
                )
                
                if not module_ids:
                    print(f"   ❌ Module not found: {module_name}")
                    continue
                
                # Get module info
                module_info = models.execute_kw(
                    db, uid, password,
                    'ir.module.module', 'read',
                    [module_ids], {'fields': ['name', 'state', 'shortdesc']}
                )
                
                module = module_info[0]
                state = module['state']
                desc = module['shortdesc']
                
                print(f"   📋 {desc}")
                print(f"   📊 Current state: {state}")
                
                if state == 'installed':
                    print(f"   ✅ Already installed")
                    installed_modules.append(module_name)
                elif state == 'uninstalled':
                    print(f"   🔄 Installing...")
                    
                    # Install the module
                    models.execute_kw(
                        db, uid, password,
                        'ir.module.module', 'button_immediate_install',
                        [module_ids]
                    )
                    
                    print(f"   ✅ Installation triggered")
                    installed_modules.append(module_name)
                    
                    # Wait a bit for installation to process
                    time.sleep(2)
                    
                elif state == 'uninstallable':
                    print(f"   ⚠️ Not available in Community Edition")
                else:
                    print(f"   ⚠️ State: {state}")
                    
            except Exception as e:
                print(f"   ❌ Error installing {module_name}: {e}")
        
        print(f"\n=== Installation Summary ===")
        print(f"✅ Successfully processed {len(installed_modules)} modules:")
        for module in installed_modules:
            print(f"   ✅ {module}")
        
        print(f"\n=== Configuring Construction Industry Settings ===")
        
        # Configure company settings for construction
        try:
            company_ids = models.execute_kw(
                db, uid, password,
                'res.company', 'search', [[]]
            )
            
            if company_ids:
                company_settings = {
                    'project_time_mode_id': 1,  # Enable project time tracking
                }
                
                models.execute_kw(
                    db, uid, password,
                    'res.company', 'write',
                    [company_ids, company_settings]
                )
                print("✅ Company construction settings updated")
        
        except Exception as e:
            print(f"⚠️ Company settings error: {e}")
        
        print(f"\n=== Creating Construction Project Templates ===")
        
        # Create sample construction project templates
        try:
            project_templates = [
                {
                    'name': 'Residential Construction Project',
                    'description': 'Template for residential building projects',
                    'is_template': True,
                },
                {
                    'name': 'Commercial Construction Project', 
                    'description': 'Template for commercial building projects',
                    'is_template': True,
                },
                {
                    'name': 'Infrastructure Project',
                    'description': 'Template for infrastructure development',
                    'is_template': True,
                },
                {
                    'name': 'Renovation Project',
                    'description': 'Template for renovation and remodeling',
                    'is_template': True,
                }
            ]
            
            for template in project_templates:
                try:
                    # Check if template already exists
                    existing = models.execute_kw(
                        db, uid, password,
                        'project.project', 'search',
                        [[['name', '=', template['name']]]]
                    )
                    
                    if not existing:
                        project_id = models.execute_kw(
                            db, uid, password,
                            'project.project', 'create',
                            [template]
                        )
                        print(f"   ✅ Created template: {template['name']} (ID: {project_id})")
                    else:
                        print(f"   ⚠️ Template already exists: {template['name']}")
                        
                except Exception as e:
                    print(f"   ❌ Template creation error: {e}")
        
        except Exception as e:
            print(f"❌ Template creation error: {e}")
        
        print(f"\n🎉 CONSTRUCTION INDUSTRY SYSTEM INSTALLATION COMPLETE! 🎉")
        print(f"\n🏗️ What You Now Have:")
        print("   ✅ Complete Project Management")
        print("   ✅ Timesheet & Labor Tracking")
        print("   ✅ Project Accounting & Costing")
        print("   ✅ Sales Project Integration")
        print("   ✅ Purchase Project Management")
        print("   ✅ Expense Management")
        print("   ✅ Manufacturing Integration")
        print("   ✅ Stock/Inventory Integration")
        print("   ✅ Service Margin Analysis")
        print("   ✅ Task & To-Do Management")
        print("   ✅ SMS & Communication Tools")
        print("   ✅ Attendance Integration")
        
        print(f"\n🔄 Next Steps:")
        print("1. 🔄 Restart Odoo server (recommended)")
        print("2. 🌐 Refresh your browser (F5)")
        print("3. 🔍 Look for 'Project' app on dashboard")
        print("4. 📊 Check 'Timesheets' app")
        print("5. 🏗️ Start creating construction projects!")
        
        print(f"\n📋 Construction Industry App Structure:")
        print("   🏗️ PROJECT APP")
        print("   ├── 📊 Dashboard (Project overview)")
        print("   ├── 📋 Projects")
        print("   │   ├── All Projects")
        print("   │   ├── Project Templates")
        print("   │   └── Project Analysis")
        print("   ├── 📝 Tasks")
        print("   │   ├── All Tasks")
        print("   │   ├── My Tasks")
        print("   │   └── Task Analysis")
        print("   ├── 📊 Reporting")
        print("   │   ├── Project Profitability")
        print("   │   ├── Task Analysis")
        print("   │   └── Timesheet Analysis")
        print("   └── ⚙️ Configuration")
        print("       ├── Project Stages")
        print("       ├── Task Stages")
        print("       └── Project Settings")
        
        print(f"\n   ⏰ TIMESHEETS APP")
        print("   ├── 📊 Dashboard (Time overview)")
        print("   ├── ⏰ My Timesheets")
        print("   ├── 📋 All Timesheets")
        print("   ├── 📊 Reporting")
        print("   │   ├── Timesheet Analysis")
        print("   │   ├── Employee Analysis")
        print("   │   └── Project Time Analysis")
        print("   └── ⚙️ Configuration")
        print("       ├── Activities")
        print("       └── Timesheet Settings")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    install_construction_industry()
