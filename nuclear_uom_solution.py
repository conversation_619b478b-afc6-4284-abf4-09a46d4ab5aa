#!/usr/bin/env python3
import psycopg2

def nuclear_uom_solution():
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='dataicraft',
            user='odoo',
            password='odoo123'
        )
        cur = conn.cursor()
        
        print("💥 === NUCLEAR UoM SOLUTION === 💥")
        print("Removing ALL possible sources of Hours UoM")
        
        # Step 1: Find and delete ALL Hours UoMs
        print("\n🗑️ Step 1: Finding and deleting ALL Hours UoMs...")
        
        cur.execute("SELECT id, name FROM uom_uom WHERE name::text LIKE '%Hour%' OR name::text LIKE '%hour%';")
        hours_uoms = cur.fetchall()
        
        if hours_uoms:
            print(f"Found {len(hours_uoms)} Hours UoMs:")
            for uom in hours_uoms:
                print(f"  🕐 ID:{uom[0]} | {uom[1]}")
            
            # Delete them all
            for uom in hours_uoms:
                try:
                    cur.execute("DELETE FROM uom_uom WHERE id = %s;", (uom[0],))
                    print(f"  ✅ Deleted UoM ID:{uom[0]} ({uom[1]})")
                except Exception as e:
                    print(f"  ⚠️ Could not delete UoM ID:{uom[0]}: {e}")
        else:
            print("✅ No Hours UoMs found")
        
        # Step 2: Force ALL products to use Units UoM
        print("\n🔧 Step 2: Forcing ALL products to use Units UoM...")
        
        cur.execute("UPDATE product_template SET uom_id = 1, uom_po_id = 1;")
        updated_products = cur.rowcount
        print(f"✅ Updated {updated_products} products to use Units UoM")
        
        # Step 3: Delete ALL existing orders and order lines
        print("\n🗑️ Step 3: Deleting ALL existing orders...")
        
        cur.execute("DELETE FROM sale_order_line;")
        deleted_so_lines = cur.rowcount
        print(f"✅ Deleted {deleted_so_lines} sales order lines")
        
        cur.execute("DELETE FROM purchase_order_line;")
        deleted_po_lines = cur.rowcount
        print(f"✅ Deleted {deleted_po_lines} purchase order lines")
        
        cur.execute("DELETE FROM sale_order;")
        deleted_so = cur.rowcount
        print(f"✅ Deleted {deleted_so} sales orders")
        
        cur.execute("DELETE FROM purchase_order;")
        deleted_po = cur.rowcount
        print(f"✅ Deleted {deleted_po} purchase orders")
        
        # Step 4: Set company timesheet UoM to Units
        print("\n⚙️ Step 4: Setting company timesheet UoM to Units...")
        
        cur.execute("UPDATE res_company SET timesheet_encode_uom_id = 1;")
        updated_companies = cur.rowcount
        print(f"✅ Updated {updated_companies} companies to use Units for timesheets")
        
        # Step 5: Clear any cached UoM data
        print("\n🧹 Step 5: Clearing cached UoM data...")
        
        cur.execute("DELETE FROM ir_model_data WHERE model = 'uom.uom' AND res_id NOT IN (SELECT id FROM uom_uom);")
        cleared_cache = cur.rowcount
        print(f"✅ Cleared {cleared_cache} orphaned cache entries")
        
        # Step 6: Reset sequences
        print("\n🔄 Step 6: Resetting order sequences...")
        
        cur.execute("UPDATE ir_sequence SET number_next = 1 WHERE code LIKE '%sale%' OR code LIKE '%purchase%';")
        reset_sequences = cur.rowcount
        print(f"✅ Reset {reset_sequences} order sequences")
        
        # Step 7: Verification
        print("\n✅ Final Verification:")
        
        cur.execute("SELECT COUNT(*) FROM uom_uom WHERE name::text LIKE '%Hour%' OR name::text LIKE '%hour%';")
        remaining_hours = cur.fetchone()[0]
        print(f"Remaining Hours UoMs: {remaining_hours}")
        
        cur.execute("SELECT COUNT(*) FROM product_template WHERE uom_id != 1;")
        non_units_products = cur.fetchone()[0]
        print(f"Products not using Units: {non_units_products}")
        
        cur.execute("SELECT COUNT(*) FROM sale_order;")
        remaining_orders = cur.fetchone()[0]
        print(f"Remaining orders: {remaining_orders}")
        
        # Commit all changes
        conn.commit()
        
        print(f"\n🎉 NUCLEAR UoM SOLUTION COMPLETE!")
        print(f"✅ ALL Hours UoMs deleted")
        print(f"✅ ALL products use Units UoM")
        print(f"✅ ALL orders deleted")
        print(f"✅ Company settings updated")
        print(f"✅ Cache cleared")
        print(f"✅ Sequences reset")
        
        print(f"\n📋 Final State:")
        print(f"  📦 ALL products → Units UoM (ID: 1)")
        print(f"  🚫 NO Hours UoMs exist")
        print(f"  🚫 NO existing orders")
        print(f"  🚫 UoM conflicts IMPOSSIBLE")
        
        print(f"\n🔄 Next Steps:")
        print("1. Restart Odoo server")
        print("2. Clear browser cache")
        print("3. Try creating orders - UoM error CANNOT occur")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    nuclear_uom_solution()
