<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="mail.MessageCardList">
        <div class="o-mail-MessageCardList d-flex flex-column" t-att-class="{ 'justify-content-center flex-grow-1': props.messages.length === 0 }" t-ref="message-list">
            <div class="card mb-2" t-foreach="props.messages" t-as="message" t-key="message.id">
                <div class="card-body py-2">
                    <div class="d-flex justify-content-end position-absolute top-0 end-0 o-z-index-1 m-2">
                        <button class="o-mail-MessageCard-jump btn rounded bg-400 badge opacity-0 flex-shrink-0" t-att-class="{ 'opacity-100 py-1 px-2': ui.isSmall }" t-on-click="() => this.onClickJump(message)">Jump</button>
                        <button t-if="props.mode === 'pin'" class="btn ms-2 p-0 text-700 shadow-none" t-att-class="{ 'fs-5 align-self-end': ui.isSmall }" title="Unpin" t-on-click="() => this.props.onClickUnpin(message)">
                            <i class="d-block fa fa-times"/>
                        </button>
                    </div>
                    <Message hasActions="false" message="message" thread="props.thread" messageSearch="props.messageSearch"/>
                </div>
            </div>
            <span t-if="props.loadMore" t-ref="load-more"/>
            <p t-if="props.showEmpty !== undefined ? props.showEmpty : props.messages.length === 0" t-esc="emptyText" class="text-center fst-italic text-500 fs-6"/>
        </div>
    </t>

</templates>
