# Generated by Django 4.2.21 on 2025-07-15 17:00

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('accounting', '0004_alter_accountagedtrialbalance_options_and_more'),
    ]

    operations = [
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS account_invoice_report (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date DATE,
                invoice_date DATE,
                invoice_date_due DATE,
                partner_id_id INTEGER,
                commercial_partner_id_id INTEGER,
                country_id VARCHAR(2),
                invoice_user_id_id INTEGER,
                move_type VARCHAR(16),
                state VARCHAR(8),
                journal_id_id INTEGER,
                company_id_id INTEGER,
                currency_id VARCHAR(3),
                account_id_id INTEGER,
                price_subtotal DECIMAL(16,2) DEFAULT 0,
                price_average DECIMAL(16,2) DEFAULT 0,
                residual DECIMAL(16,2) DEFAULT 0,
                quantity DECIMAL(16,4) DEFAULT 0
            );
            """,
            reverse_sql="DROP TABLE IF EXISTS account_invoice_report;"
        ),
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS account_move_line_report (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date DATE,
                move_id_id INTEGER,
                journal_id_id INTEGER,
                account_id_id INTEGER,
                partner_id_id INTEGER,
                company_id_id INTEGER,
                account_type VARCHAR(64),
                account_code VARCHAR(64),
                account_name VARCHAR(128),
                debit DECIMAL(16,2) DEFAULT 0,
                credit DECIMAL(16,2) DEFAULT 0,
                balance DECIMAL(16,2) DEFAULT 0,
                reconciled BOOLEAN DEFAULT 0
            );
            """,
            reverse_sql="DROP TABLE IF EXISTS account_move_line_report;"
        ),
        migrations.RunSQL(
            """
            CREATE TABLE IF NOT EXISTS account_aged_trial_balance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                report_date DATE,
                account_id_id INTEGER,
                partner_id_id INTEGER,
                company_id_id INTEGER,
                not_due DECIMAL(16,2) DEFAULT 0,
                period_1 DECIMAL(16,2) DEFAULT 0,
                period_2 DECIMAL(16,2) DEFAULT 0,
                period_3 DECIMAL(16,2) DEFAULT 0,
                period_4 DECIMAL(16,2) DEFAULT 0,
                period_5 DECIMAL(16,2) DEFAULT 0,
                total DECIMAL(16,2) DEFAULT 0
            );
            """,
            reverse_sql="DROP TABLE IF EXISTS account_aged_trial_balance;"
        ),
    ]
