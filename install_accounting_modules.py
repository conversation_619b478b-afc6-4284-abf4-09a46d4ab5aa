#!/usr/bin/env python3
import xmlrpc.client

def install_accounting_modules():
    try:
        # Connection details
        url = 'http://localhost:8069'
        db = 'dataicraft'
        username = '<EMAIL>'
        password = 'admin123'
        
        # Connect to Odoo
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            print("❌ Authentication failed!")
            return
        
        print(f"✅ Connected to Odoo as user ID: {uid}")
        
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        # List of modules to install for better accounting features
        modules_to_install = [
            'analytic',           # Analytic Accounting
            'account_budget',     # Budget Management
            'account_asset',      # Asset Management
            'account_reports',    # Financial Reports
            'account_lock',       # Account Lock
            'account_debit_note', # Debit Notes
        ]
        
        print("\n=== Installing Accounting Modules ===")
        
        for module_name in modules_to_install:
            try:
                # Search for the module
                module_ids = models.execute_kw(
                    db, uid, password,
                    'ir.module.module', 'search',
                    [[['name', '=', module_name]]]
                )
                
                if not module_ids:
                    print(f"❌ {module_name}: Module not found")
                    continue
                
                # Get module info
                module_info = models.execute_kw(
                    db, uid, password,
                    'ir.module.module', 'read',
                    [module_ids], {'fields': ['name', 'state', 'shortdesc']}
                )
                
                module = module_info[0]
                state = module['state']
                desc = module['shortdesc']
                
                print(f"\n📦 {module_name}: {desc}")
                print(f"   Current state: {state}")
                
                if state == 'installed':
                    print(f"   ✅ Already installed")
                elif state == 'uninstalled':
                    print(f"   🔄 Installing...")
                    # Install the module
                    models.execute_kw(
                        db, uid, password,
                        'ir.module.module', 'button_immediate_install',
                        [module_ids]
                    )
                    print(f"   ✅ Installation triggered")
                elif state == 'uninstallable':
                    print(f"   ⚠️ Not available in Community Edition")
                else:
                    print(f"   ⚠️ State: {state}")
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
        
        print(f"\n=== Installation Summary ===")
        print("✅ Module installation process completed")
        print("🔄 Please refresh your browser to see new features")
        
    except Exception as e:
        print(f"❌ Connection error: {e}")

if __name__ == "__main__":
    install_accounting_modules()
