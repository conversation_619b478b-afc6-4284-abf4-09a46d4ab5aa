{"version": 12, "sheets": [{"id": "sheet1", "name": "Dashboard", "colNumber": 7, "rowNumber": 53, "rows": {"6": {"size": 40}, "23": {"size": 40}, "40": {"size": 40}, "41": {"size": 40}}, "cols": {"0": {"size": 275}, "1": {"size": 100}, "2": {"size": 100}, "3": {"size": 50}, "4": {"size": 275}, "5": {"size": 100}, "6": {"size": 100}}, "merges": [], "cells": {"A7": {"style": 1, "content": "[Best Sellers by Revenue](odoo://view/{\"viewType\":\"graph\",\"action\":{\"domain\":[[\"state\", \"not in\", [\"draft\", \"sent\", \"cancel\"]]],\"context\":{\"group_by\":[\"product_id\"],\"graph_measure\":\"price_subtotal\",\"graph_mode\":\"bar\",\"graph_groupbys\":[\"product_id\"]},\"modelName\":\"sale.report\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Best Sellers by Revenue\"})", "border": 1}, "A24": {"style": 1, "content": "[Best Sellers by Units Sold](odoo://view/{\"viewType\":\"graph\",\"action\":{\"domain\":[[\"state\", \"not in\", [\"draft\", \"sent\", \"cancel\"]]],\"context\":{\"group_by\":[\"product_id\"],\"graph_measure\":\"__count\",\"graph_mode\":\"bar\",\"graph_groupbys\":[\"product_id\"]},\"modelName\":\"sale.report\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Sales Analysis\"})", "border": 1}, "A41": {"style": 1, "content": "[Best Selling Products](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[[\"state\", \"not in\", [\"draft\", \"sent\", \"cancel\"]]],\"context\":{\"group_by\":[\"product_id\"],\"pivot_measures\":[\"__count\",\"price_subtotal\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"product_id\"]},\"modelName\":\"sale.report\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Sales Analysis\"})", "border": 1}, "A42": {"style": 2, "content": "=_t(\"Product\")", "border": 2}, "A43": {"style": 3, "content": "=ODOO.PIVOT.HEADER(1,\"#product_id\",1)"}, "A44": {"style": 4, "content": "=ODOO.PIVOT.HEADER(1,\"#product_id\",2)"}, "A45": {"style": 3, "content": "=ODOO.PIVOT.HEADER(1,\"#product_id\",3)"}, "A46": {"style": 4, "content": "=ODOO.PIVOT.HEADER(1,\"#product_id\",4)"}, "A47": {"style": 3, "content": "=ODOO.PIVOT.HEADER(1,\"#product_id\",5)"}, "A48": {"style": 4, "content": "=ODOO.PIVOT.HEADER(1,\"#product_id\",6)"}, "A49": {"style": 3, "content": "=ODOO.PIVOT.HEADER(1,\"#product_id\",7)"}, "A50": {"style": 4, "content": "=ODOO.PIVOT.HEADER(1,\"#product_id\",8)"}, "A51": {"style": 3, "content": "=ODOO.PIVOT.HEADER(1,\"#product_id\",9)"}, "A52": {"style": 4, "content": "=ODOO.PIVOT.HEADER(1,\"#product_id\",10)"}, "B42": {"style": 5, "content": "=_t(\"Units\")", "border": 2}, "B43": {"style": 6, "format": 2, "content": "=ODOO.PIVOT(1,\"product_uom_qty\",\"#product_id\",1)"}, "B44": {"format": 2, "content": "=ODOO.PIVOT(1,\"product_uom_qty\",\"#product_id\",2)"}, "B45": {"style": 6, "format": 2, "content": "=ODOO.PIVOT(1,\"product_uom_qty\",\"#product_id\",3)"}, "B46": {"format": 2, "content": "=ODOO.PIVOT(1,\"product_uom_qty\",\"#product_id\",4)"}, "B47": {"style": 6, "format": 2, "content": "=ODOO.PIVOT(1,\"product_uom_qty\",\"#product_id\",5)"}, "B48": {"format": 2, "content": "=ODOO.PIVOT(1,\"product_uom_qty\",\"#product_id\",6)"}, "B49": {"style": 6, "format": 2, "content": "=ODOO.PIVOT(1,\"product_uom_qty\",\"#product_id\",7)"}, "B50": {"format": 2, "content": "=ODOO.PIVOT(1,\"product_uom_qty\",\"#product_id\",8)"}, "B51": {"style": 6, "format": 2, "content": "=ODOO.PIVOT(1,\"product_uom_qty\",\"#product_id\",9)"}, "B52": {"format": 2, "content": "=ODOO.PIVOT(1,\"product_uom_qty\",\"#product_id\",10)"}, "C42": {"style": 5, "content": "=_t(\"Revenue\")", "border": 2}, "C43": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(1,\"price_subtotal\",\"#product_id\",1)"}, "C44": {"format": 1, "content": "=ODOO.PIVOT(1,\"price_subtotal\",\"#product_id\",2)"}, "C45": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(1,\"price_subtotal\",\"#product_id\",3)"}, "C46": {"format": 1, "content": "=ODOO.PIVOT(1,\"price_subtotal\",\"#product_id\",4)"}, "C47": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(1,\"price_subtotal\",\"#product_id\",5)"}, "C48": {"format": 1, "content": "=ODOO.PIVOT(1,\"price_subtotal\",\"#product_id\",6)"}, "C49": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(1,\"price_subtotal\",\"#product_id\",7)"}, "C50": {"format": 1, "content": "=ODOO.PIVOT(1,\"price_subtotal\",\"#product_id\",8)"}, "C51": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(1,\"price_subtotal\",\"#product_id\",9)"}, "C52": {"format": 1, "content": "=ODOO.PIVOT(1,\"price_subtotal\",\"#product_id\",10)"}, "E7": {"style": 7, "border": 1}, "E41": {"style": 1, "content": "[Best Selling Categories](odoo://view/{\"viewType\":\"pivot\",\"action\":{\"domain\":[[\"state\", \"not in\", [\"draft\", \"sent\", \"cancel\"]]],\"context\":{\"group_by\":[\"categ_id\"],\"pivot_measures\":[\"__count\",\"price_subtotal\"],\"pivot_column_groupby\":[],\"pivot_row_groupby\":[\"categ_id\"]},\"modelName\":\"sale.report\",\"views\":[[false,\"graph\"],[false,\"pivot\"],[false,\"search\"]]},\"threshold\":0,\"name\":\"Sales Analysis\"})", "border": 1}, "E42": {"style": 2, "content": "=_t(\"Category\")", "border": 2}, "E43": {"style": 3, "content": "=ODOO.PIVOT.HEADER(2,\"#categ_id\",1)"}, "E44": {"style": 4, "content": "=ODOO.PIVOT.HEADER(2,\"#categ_id\",2)"}, "E45": {"style": 3, "content": "=ODOO.PIVOT.HEADER(2,\"#categ_id\",3)"}, "E46": {"style": 4, "content": "=ODOO.PIVOT.HEADER(2,\"#categ_id\",4)"}, "E47": {"style": 3, "content": "=ODOO.PIVOT.HEADER(2,\"#categ_id\",5)"}, "E48": {"style": 4, "content": "=ODOO.PIVOT.HEADER(2,\"#categ_id\",6)"}, "E49": {"style": 3, "content": "=ODOO.PIVOT.HEADER(2,\"#categ_id\",7)"}, "E50": {"style": 4, "content": "=ODOO.PIVOT.HEADER(2,\"#categ_id\",8)"}, "E51": {"style": 3, "content": "=ODOO.PIVOT.HEADER(2,\"#categ_id\",9)"}, "E52": {"style": 4, "content": "=ODOO.PIVOT.HEADER(2,\"#categ_id\",10)"}, "F42": {"style": 5, "content": "=_t(\"Units\")", "border": 2}, "F43": {"style": 6, "format": 2, "content": "=ODOO.PIVOT(2,\"product_uom_qty\",\"#categ_id\",1)"}, "F44": {"format": 2, "content": "=ODOO.PIVOT(2,\"product_uom_qty\",\"#categ_id\",2)"}, "F45": {"style": 6, "format": 2, "content": "=ODOO.PIVOT(2,\"product_uom_qty\",\"#categ_id\",3)"}, "F46": {"format": 2, "content": "=ODOO.PIVOT(2,\"product_uom_qty\",\"#categ_id\",4)"}, "F47": {"style": 6, "format": 2, "content": "=ODOO.PIVOT(2,\"product_uom_qty\",\"#categ_id\",5)"}, "F48": {"format": 2, "content": "=ODOO.PIVOT(2,\"product_uom_qty\",\"#categ_id\",6)"}, "F49": {"style": 6, "format": 2, "content": "=ODOO.PIVOT(2,\"product_uom_qty\",\"#categ_id\",7)"}, "F50": {"format": 2, "content": "=ODOO.PIVOT(2,\"product_uom_qty\",\"#categ_id\",8)"}, "F51": {"style": 6, "format": 2, "content": "=ODOO.PIVOT(2,\"product_uom_qty\",\"#categ_id\",9)"}, "F52": {"format": 2, "content": "=ODOO.PIVOT(2,\"product_uom_qty\",\"#categ_id\",10)"}, "G42": {"style": 5, "content": "=_t(\"Revenue\")", "border": 2}, "G43": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(2,\"price_subtotal\",\"#categ_id\",1)"}, "G44": {"format": 1, "content": "=ODOO.PIVOT(2,\"price_subtotal\",\"#categ_id\",2)"}, "G45": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(2,\"price_subtotal\",\"#categ_id\",3)"}, "G46": {"format": 1, "content": "=ODOO.PIVOT(2,\"price_subtotal\",\"#categ_id\",4)"}, "G47": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(2,\"price_subtotal\",\"#categ_id\",5)"}, "G48": {"format": 1, "content": "=ODOO.PIVOT(2,\"price_subtotal\",\"#categ_id\",6)"}, "G49": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(2,\"price_subtotal\",\"#categ_id\",7)"}, "G50": {"format": 1, "content": "=ODOO.PIVOT(2,\"price_subtotal\",\"#categ_id\",8)"}, "G51": {"style": 6, "format": 1, "content": "=ODOO.PIVOT(2,\"price_subtotal\",\"#categ_id\",9)"}, "G52": {"format": 1, "content": "=ODOO.PIVOT(2,\"price_subtotal\",\"#categ_id\",10)"}, "A8": {"border": 2}, "A25": {"border": 2}, "B7": {"border": 1}, "B8": {"border": 2}, "B24": {"border": 1}, "B25": {"border": 2}, "B41": {"border": 1}, "C7": {"border": 1}, "C8": {"border": 2}, "C24": {"border": 1}, "C25": {"border": 2}, "C41": {"border": 1}, "D7": {"border": 1}, "D8": {"border": 2}, "D24": {"border": 1}, "D25": {"border": 2}, "E8": {"border": 2}, "E24": {"border": 1}, "E25": {"border": 2}, "F7": {"border": 1}, "F8": {"border": 2}, "F24": {"border": 1}, "F25": {"border": 2}, "F41": {"border": 1}, "G7": {"border": 1}, "G8": {"border": 2}, "G24": {"border": 1}, "G25": {"border": 2}, "G41": {"border": 1}}, "conditionalFormats": [], "figures": [{"id": "e35856cf-9090-489b-b055-2d441380d954", "x": 0, "y": 178, "width": 1000, "height": 345, "tag": "chart", "data": {"title": "", "background": "#FFFFFF", "legendPosition": "none", "metaData": {"groupBy": ["product_id"], "measure": "price_subtotal", "order": "DESC", "resModel": "sale.report"}, "searchParams": {"comparison": null, "context": {"group_by_no_leaf": 1, "group_by": []}, "domain": [["state", "not in", ["draft", "sent", "cancel"]]], "groupBy": ["product_id"], "orderBy": []}, "type": "odoo_bar", "verticalAxisPosition": "left", "stacked": true}}, {"id": "d0171069-d2cd-4c2c-a686-cd7515e93bb5", "x": 0, "y": 586, "width": 1000, "height": 345, "tag": "chart", "data": {"title": "", "background": "#FFFFFF", "legendPosition": "none", "metaData": {"groupBy": ["product_id"], "measure": "product_uom_qty", "order": "DESC", "resModel": "sale.report"}, "searchParams": {"comparison": null, "context": {"group_by_no_leaf": 1, "group_by": []}, "domain": [["state", "not in", ["draft", "sent", "cancel"]]], "groupBy": ["product_id"], "orderBy": []}, "type": "odoo_bar", "verticalAxisPosition": "left", "stacked": true}}, {"id": "5f383918-4073-4f19-9cc9-603216c953ad", "x": 0, "y": 0, "width": 450, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "text", "title": "Best Seller", "type": "scorecard", "background": "", "baseline": "Data!C2", "baselineDescr": "sold", "keyValue": "Data!B2"}}, {"id": "ec57f69b-f2b1-4dfc-990c-91ab61b526bf", "x": 500, "y": 0, "width": 450, "height": 120, "tag": "chart", "data": {"baselineColorDown": "#DC6965", "baselineColorUp": "#00A04A", "baselineMode": "text", "title": "Best Category", "type": "scorecard", "background": "", "baseline": "Data!C3", "baselineDescr": "sold", "keyValue": "Data!B3"}}], "areGridLinesVisible": true, "isVisible": true}, {"id": "a83a78f2-b124-4d6f-9726-125e62a32b8d", "name": "Data", "colNumber": 23, "rowNumber": 88, "rows": {}, "cols": {}, "merges": [], "cells": {"A1": {"style": 6, "content": "=_t(\"KPI\")"}, "A2": {"style": 6, "content": "=_t(\"Best selling product\")"}, "A3": {"style": 6, "content": "=_t(\"Best selling category\")"}, "B1": {"style": 6, "content": "=_t(\"Name\")"}, "B2": {"style": 8, "content": "=ODOO.PIVOT.HEADER(1,\"#product_id\",1)"}, "B3": {"style": 8, "content": "=ODOO.PIVOT.HEADER(2,\"#categ_id\",1)"}, "C1": {"style": 6, "content": "=_t(\"Units\")"}, "C2": {"style": 6, "format": 2, "content": "=ODOO.PIVOT(1,\"product_uom_qty\",\"#product_id\",1)"}, "C3": {"style": 6, "format": 2, "content": "=ODOO.PIVOT(2,\"product_uom_qty\",\"#categ_id\",1)"}, "D1": {"style": 6, "content": "=_t(\"Revenue\")"}, "D2": {"style": 6, "format": 2, "content": "=FORMAT.LARGE.NUMBER(ODOO.PIVOT(1,\"price_subtotal\",\"#product_id\",1))"}, "D3": {"style": 6, "format": 2, "content": "=FORMAT.LARGE.NUMBER(ODOO.PIVOT(2,\"price_subtotal\",\"#categ_id\",1))"}}, "conditionalFormats": [], "figures": [], "areGridLinesVisible": true, "isVisible": true}], "entities": {}, "styles": {"1": {"textColor": "#01666b", "bold": true, "fontSize": 16}, "2": {"bold": true}, "3": {"fillColor": "#f2f2f2", "textColor": "#741b47"}, "4": {"textColor": "#741b47"}, "5": {"align": "right", "bold": true}, "6": {"fillColor": "#f2f2f2"}, "7": {"textColor": "#01666b"}, "8": {"fillColor": "#f2f2f2", "textColor": ""}}, "formats": {"1": "#,##0", "2": "0"}, "borders": {"1": {"bottom": ["thin", "#000"]}, "2": {"top": ["thin", "#000"]}}, "revisionId": "START_REVISION", "settings": {"locale": {"name": "English (US)", "code": "en_US", "thousandsSeparator": ",", "decimalSeparator": ".", "dateFormat": "mm/dd/yyyy", "timeFormat": "hh:mm:ss", "formulaArgSeparator": ","}}, "chartOdooMenusReferences": {"c30175e7-604c-4fc7-8adb-03be67f0dc8f": "sale.sale_menu_root", "30212d4a-1f77-4cb8-8590-4eb34876e260": "sale.sale_menu_root", "e35856cf-9090-489b-b055-2d441380d954": "sale.sale_menu_root", "d0171069-d2cd-4c2c-a686-cd7515e93bb5": "sale.sale_menu_root", "5f383918-4073-4f19-9cc9-603216c953ad": "sale.menu_reporting_sales", "ec57f69b-f2b1-4dfc-990c-91ab61b526bf": "sale.menu_reporting_sales"}, "odooVersion": 4, "lists": {}, "listNextId": 1, "pivots": {"1": {"colGroupBys": [], "context": {"group_by_no_leaf": 1, "group_by": []}, "domain": [["state", "not in", ["draft", "sent", "cancel"]]], "id": "1", "measures": [{"field": "product_uom_qty"}, {"field": "__count"}, {"field": "price_subtotal"}], "model": "sale.report", "rowGroupBys": ["product_id"], "name": "Sales Analysis by Product Variant", "sortedColumn": {"groupId": [[], []], "measure": "price_subtotal", "order": "desc"}}, "2": {"colGroupBys": [], "context": {"group_by_no_leaf": 1, "group_by": []}, "domain": [["state", "not in", ["draft", "sent", "cancel"]]], "id": "2", "measures": [{"field": "product_uom_qty"}, {"field": "__count"}, {"field": "price_subtotal"}], "model": "sale.report", "rowGroupBys": ["categ_id"], "name": "Sales Analysis by Product Category", "sortedColumn": {"groupId": [[], []], "measure": "price_subtotal", "order": "desc"}}}, "pivotNextId": 3, "globalFilters": [{"id": "a6c3274b-e53c-4c6b-90a8-bc3e3d109f52", "type": "date", "label": "Period", "defaultValue": "last_month", "rangeType": "relative", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "date", "type": "datetime", "offset": 0}, "2": {"field": "date", "type": "datetime", "offset": 0}}, "listFields": {}, "graphFields": {"d0171069-d2cd-4c2c-a686-cd7515e93bb5": {"field": "date", "type": "datetime", "offset": 0}, "e35856cf-9090-489b-b055-2d441380d954": {"field": "date", "type": "datetime", "offset": 0}}}, {"id": "36814ad9-adac-4aba-a5ad-df595a306ef7", "type": "relation", "label": "Product", "modelName": "product.product", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "product_id", "type": "many2one"}, "2": {"field": "product_id", "type": "many2one"}}, "listFields": {}, "graphFields": {"e35856cf-9090-489b-b055-2d441380d954": {"field": "product_id", "type": "many2one"}, "d0171069-d2cd-4c2c-a686-cd7515e93bb5": {"field": "product_id", "type": "many2one"}}}, {"id": "edaf4d0c-df4b-48bc-b61a-48a07e8afd91", "type": "relation", "label": "Category", "modelName": "product.category", "defaultValue": [], "defaultValueDisplayNames": [], "rangeType": "year", "defaultsToCurrentPeriod": false, "pivotFields": {"1": {"field": "categ_id", "type": "many2one"}, "2": {"field": "categ_id", "type": "many2one"}}, "listFields": {}, "graphFields": {"e35856cf-9090-489b-b055-2d441380d954": {"field": "categ_id", "type": "many2one"}, "d0171069-d2cd-4c2c-a686-cd7515e93bb5": {"field": "categ_id", "type": "many2one"}}}]}