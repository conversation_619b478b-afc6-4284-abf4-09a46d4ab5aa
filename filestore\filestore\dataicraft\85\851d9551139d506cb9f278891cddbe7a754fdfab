)]}'
{"version": 3, "sources": ["/web/static/src/polyfills/object.js", "/web/static/src/polyfills/array.js", "/web/static/src/module_loader.js", "/web/static/src/session.js", "/web/static/src/core/browser/cookie.js", "/web/static/src/legacy/js/core/minimal_dom.js", "/web/static/src/legacy/js/public/lazyloader.js", "/web_editor/static/src/js/frontend/loader_loading.js"], "mappings": "AAAA;;;;;AAAA;AACA;AACA;AACA;ACHA;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACXA;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC1MA;;;;;;;;AAAA;AACA;AACA;AACA;AACA;;;;ACJA;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;ACvCA;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AC3IA;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AC3LA;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sourcesContent": ["if (!Object.hasOwn) {\r\n    Object.hasOwn = (obj, key) => Object.prototype.hasOwnProperty.call(obj, key);\r\n}\r\n", "if (!Array.prototype.at) {\r\n    Object.defineProperty(Array.prototype, \"at\", {\r\n        enumerable: false,\r\n        value: function (index) {\r\n            if (index >= 0) {\r\n                return this[index];\r\n            }\r\n            return this[this.length + index];\r\n        }\r\n    });\r\n}\r\n", "/**\r\n *------------------------------------------------------------------------------\r\n * Odoo Web Boostrap Code\r\n *------------------------------------------------------------------------------\r\n */\r\n(function () {\r\n    \"use strict\";\r\n\r\n    class ModuleLoader {\r\n        /** @type {Map<string,{fn: Function, deps: string[]}>} mapping name => deps/fn */\r\n        factories = new Map();\r\n        /** @type {Set<string>} names of modules waiting to be started */\r\n        jobs = new Set();\r\n        /** @type {Set<string>} names of failed modules */\r\n        failed = new Set();\r\n\r\n        /** @type {Map<string,any>} mapping name => value */\r\n        modules = new Map();\r\n\r\n        bus = new EventTarget();\r\n\r\n        checkErrorProm = null;\r\n\r\n        /**\r\n         * @param {string} name\r\n         * @param {string[]} deps\r\n         * @param {Function} factory\r\n         */\r\n        define(name, deps, factory) {\r\n            if (typeof name !== \"string\") {\r\n                throw new Error(`Invalid name definition: ${name} (should be a string)\"`);\r\n            }\r\n            if (!(deps instanceof Array)) {\r\n                throw new Error(`Dependencies should be defined by an array: ${deps}`);\r\n            }\r\n            if (typeof factory !== \"function\") {\r\n                throw new Error(`Factory should be defined by a function ${factory}`);\r\n            }\r\n            if (!this.factories.has(name)) {\r\n                this.factories.set(name, {\r\n                    deps,\r\n                    fn: factory,\r\n                    ignoreMissingDeps: globalThis.__odooIgnoreMissingDependencies,\r\n                });\r\n                this.addJob(name);\r\n                this.checkErrorProm ||= Promise.resolve().then(() => {\r\n                    this.checkAndReportErrors();\r\n                    this.checkErrorProm = null;\r\n                });\r\n            }\r\n        }\r\n\r\n        addJob(name) {\r\n            this.jobs.add(name);\r\n            this.startModules();\r\n        }\r\n\r\n        findJob() {\r\n            for (const job of this.jobs) {\r\n                if (this.factories.get(job).deps.every((dep) => this.modules.has(dep))) {\r\n                    return job;\r\n                }\r\n            }\r\n            return null;\r\n        }\r\n\r\n        startModules() {\r\n            let job;\r\n            while ((job = this.findJob())) {\r\n                this.startModule(job);\r\n            }\r\n        }\r\n\r\n        startModule(name) {\r\n            const require = (name) => this.modules.get(name);\r\n            this.jobs.delete(name);\r\n            const factory = this.factories.get(name);\r\n            let value = null;\r\n            try {\r\n                value = factory.fn(require);\r\n            } catch (error) {\r\n                this.failed.add(name);\r\n                throw new Error(`Error while loading \"${name}\":\\n${error}`);\r\n            }\r\n            this.modules.set(name, value);\r\n            this.bus.dispatchEvent(\r\n                new CustomEvent(\"module-started\", { detail: { moduleName: name, module: value } })\r\n            );\r\n        }\r\n\r\n        findErrors() {\r\n            // cycle detection\r\n            const dependencyGraph = new Map();\r\n            for (const job of this.jobs) {\r\n                dependencyGraph.set(job, this.factories.get(job).deps);\r\n            }\r\n            function visitJobs(jobs, visited = new Set()) {\r\n                for (const job of jobs) {\r\n                    const result = visitJob(job, visited);\r\n                    if (result) {\r\n                        return result;\r\n                    }\r\n                }\r\n                return null;\r\n            }\r\n\r\n            function visitJob(job, visited) {\r\n                if (visited.has(job)) {\r\n                    const jobs = Array.from(visited).concat([job]);\r\n                    const index = jobs.indexOf(job);\r\n                    return jobs\r\n                        .slice(index)\r\n                        .map((j) => `\"${j}\"`)\r\n                        .join(\" => \");\r\n                }\r\n                const deps = dependencyGraph.get(job);\r\n                return deps ? visitJobs(deps, new Set(visited).add(job)) : null;\r\n            }\r\n\r\n            // missing dependencies\r\n            const missing = new Set();\r\n            for (const job of this.jobs) {\r\n                const factory = this.factories.get(job);\r\n                if (factory.ignoreMissingDeps) {\r\n                    continue;\r\n                }\r\n                for (const dep of factory.deps) {\r\n                    if (!this.factories.has(dep)) {\r\n                        missing.add(dep);\r\n                    }\r\n                }\r\n            }\r\n\r\n            return {\r\n                failed: [...this.failed],\r\n                cycle: visitJobs(this.jobs),\r\n                missing: [...missing],\r\n                unloaded: [...this.jobs].filter((j) => !this.factories.get(j).ignoreMissingDeps),\r\n            };\r\n        }\r\n\r\n        async checkAndReportErrors() {\r\n            const { failed, cycle, missing, unloaded } = this.findErrors();\r\n            if (!failed.length && !unloaded.length) {\r\n                return;\r\n            }\r\n            const debug = new URLSearchParams(location.search).get(\"debug\");\r\n            if (debug && debug !== \"0\") {\r\n                const style = document.createElement(\"style\");\r\n                style.textContent = `\r\n                    body::before {\r\n                        font-weight: bold;\r\n                        content: \"An error occurred while loading javascript modules, you may find more information in the devtools console\";\r\n                        position: fixed;\r\n                        left: 0;\r\n                        bottom: 0;\r\n                        z-index: 100000000000;\r\n                        background-color: #C00;\r\n                        color: #DDD;\r\n                    }\r\n                `;\r\n                document.head.appendChild(style);\r\n            }\r\n\r\n            if (failed.length) {\r\n                console.error(\"The following modules failed to load because of an error:\", failed);\r\n            }\r\n            if (missing) {\r\n                console.error(\r\n                    \"The following modules are needed by other modules but have not been defined, they may not be present in the correct asset bundle:\",\r\n                    missing\r\n                );\r\n            }\r\n            if (cycle) {\r\n                console.error(\r\n                    \"The following modules could not be loaded because they form a dependency cycle:\",\r\n                    cycle\r\n                );\r\n            }\r\n            if (unloaded) {\r\n                console.error(\r\n                    \"The following modules could not be loaded because they have unmet dependencies, this is a secondary error which is likely caused by one of the above problems:\",\r\n                    unloaded\r\n                );\r\n            }\r\n        }\r\n    }\r\n\r\n    if (!globalThis.odoo) {\r\n        globalThis.odoo = {};\r\n    }\r\n    const odoo = globalThis.odoo;\r\n    if (odoo.debug && !new URLSearchParams(location.search).has(\"debug\")) {\r\n        // remove debug mode if not explicitely set in url\r\n        odoo.debug = \"\";\r\n    }\r\n\r\n    const loader = new ModuleLoader();\r\n    odoo.define = loader.define.bind(loader);\r\n\r\n    odoo.loader = loader;\r\n})();\r\n", "/** @odoo-module **/\r\n\r\nexport const session = odoo.__session_info__ || {};\r\ndelete odoo.__session_info__;\r\n", "/** @odoo-module **/\r\n\r\n/**\r\n * Utils to make use of document.cookie\r\n * https://developer.mozilla.org/en-US/docs/Web/HTTP/Cookies\r\n * As recommended, storage should not be done by the cookie\r\n * but with localStorage/sessionStorage\r\n */\r\n\r\nconst COOKIE_TTL = 24 * 60 * 60 * 365;\r\n\r\nexport const cookie = {\r\n    get _cookieMonster() {\r\n        return document.cookie;\r\n    },\r\n    set _cookieMonster(value) {\r\n        document.cookie = value;\r\n    },\r\n    get(str) {\r\n        const parts = this._cookieMonster.split(\"; \");\r\n        for (const part of parts) {\r\n            const [key, value] = part.split(/=(.*)/);\r\n            if (key === str) {\r\n                return value || \"\";\r\n            }\r\n        }\r\n    },\r\n    set(key, value, ttl = COOKIE_TTL) {\r\n        let fullCookie = [];\r\n        if (value !== undefined) {\r\n            fullCookie.push(`${key}=${value}`);\r\n        }\r\n        fullCookie = fullCookie.concat([\"path=/\", `max-age=${Math.floor(ttl)}`]);\r\n        this._cookieMonster = fullCookie.join(\"; \");\r\n    },\r\n    delete(key) {\r\n        this.set(key, \"kill\", 0);\r\n    },\r\n};\r\n", "/** @odoo-module **/\r\n\r\nexport const DEBOUNCE = 400;\r\nexport const BUTTON_HANDLER_SELECTOR = 'a, button, input[type=\"submit\"], input[type=\"button\"], .btn';\r\n\r\n/**\r\n * Protects a function which is to be used as a handler by preventing its\r\n * execution for the duration of a previous call to it (including async\r\n * parts of that call).\r\n *\r\n * Limitation: as the handler is ignored during async actions,\r\n * the 'preventDefault' or 'stopPropagation' calls it may want to do\r\n * will be ignored too. Using the 'preventDefault' and 'stopPropagation'\r\n * arguments solves that problem.\r\n *\r\n * @param {function} fct\r\n *      The function which is to be used as a handler. If a promise\r\n *      is returned, it is used to determine when the handler's action is\r\n *      finished. Otherwise, the return is used as jQuery uses it.\r\n * @param {function|boolean} preventDefault\r\n * @param {function|boolean} stopPropagation\r\n */\r\nexport function makeAsyncHandler(fct, preventDefault, stopPropagation) {\r\n    // TODO in master, add those as arguments.\r\n    const stopImmediatePropagation = this && this.__makeAsyncHandler_stopImmediatePropagation;\r\n\r\n    let pending = false;\r\n    function _isLocked() {\r\n        return pending;\r\n    }\r\n    function _lock() {\r\n        pending = true;\r\n    }\r\n    function _unlock() {\r\n        pending = false;\r\n    }\r\n    return function (ev) {\r\n        if (preventDefault === true || preventDefault && preventDefault()) {\r\n            ev.preventDefault();\r\n        }\r\n        if (stopPropagation === true || stopPropagation && stopPropagation()) {\r\n            ev.stopPropagation();\r\n        }\r\n        if (stopImmediatePropagation === true || stopImmediatePropagation && stopImmediatePropagation()) {\r\n            ev.stopImmediatePropagation();\r\n        }\r\n\r\n        if (_isLocked()) {\r\n            // If a previous call to this handler is still pending, ignore\r\n            // the new call.\r\n            return;\r\n        }\r\n\r\n        _lock();\r\n        const result = fct.apply(this, arguments);\r\n        Promise.resolve(result).finally(_unlock);\r\n        return result;\r\n    };\r\n}\r\n\r\n/**\r\n * Creates a debounced version of a function to be used as a button click\r\n * handler. Also improves the handler to disable the button for the time of\r\n * the debounce and/or the time of the async actions it performs.\r\n *\r\n * Limitation: if two handlers are put on the same button, the button will\r\n * become enabled again once any handler's action finishes (multiple click\r\n * handlers should however not be bound to the same button).\r\n *\r\n * @param {function} fct\r\n *      The function which is to be used as a button click handler. If a\r\n *      promise is returned, it is used to determine when the button can be\r\n *      re-enabled. Otherwise, the return is used as jQuery uses it.\r\n */\r\nexport function makeButtonHandler(fct) {\r\n    // TODO in master, add those as arguments. Even though buttons are probably\r\n    // blocked by the o_website_btn_loading and related classes, it is not\r\n    // necessarily true for all event types.\r\n    const preventDefault = this && this.__makeButtonHandler_preventDefault;\r\n    const stopPropagation = this && this.__makeButtonHandler_stopPropagation;\r\n    const stopImmediatePropagation = this && this.__makeButtonHandler_stopImmediatePropagation;\r\n\r\n    // Fallback: if the final handler is not bound to a button, at least\r\n    // make it an async handler (also handles the case where some events\r\n    // might ignore the disabled state of the button).\r\n    fct = makeAsyncHandler.call({\r\n        '__makeAsyncHandler_stopImmediatePropagation': stopImmediatePropagation,\r\n    }, fct, preventDefault, stopPropagation);\r\n\r\n    return function (ev) {\r\n        const result = fct.apply(this, arguments);\r\n\r\n        const buttonEl = ev.target && ev.target.closest && ev.target.closest(BUTTON_HANDLER_SELECTOR);\r\n        if (!(buttonEl instanceof HTMLElement)) {\r\n            return result;\r\n        }\r\n\r\n        // Disable the button for the duration of the handler's action\r\n        // or at least for the duration of the click debounce. This makes\r\n        // a 'real' debounce creation useless. Also, during the debouncing\r\n        // part, the button is disabled without any visual effect.\r\n        buttonEl.classList.add('pe-none');\r\n        Promise.resolve(DEBOUNCE && new Promise(r => setTimeout(r, DEBOUNCE)))\r\n            .then(function () {\r\n                buttonEl.classList.remove('pe-none');\r\n                const restore = addButtonLoadingEffect(buttonEl);\r\n                return Promise.resolve(result).then(restore, restore);\r\n            });\r\n\r\n        return result;\r\n    };\r\n}\r\n\r\n/**\r\n * Gives the button a loading effect by disabling it and adding a `fa`\r\n * spinner icon.\r\n * The existing button `fa` icons will be hidden through css.\r\n *\r\n * @param {HTMLElement} btnEl - the button to disable/load\r\n * @return {function} a callback function that will restore the button\r\n *         initial state\r\n */\r\nexport function addButtonLoadingEffect(btnEl) {\r\n    if (!(btnEl instanceof HTMLElement)) {\r\n        return () => {};\r\n    }\r\n    // Note that pe-none is used alongside \"disabled\" so that the behavior is\r\n    // the same on links not using the \"btn\" class -> pointer-events disabled.\r\n    btnEl.classList.add('o_website_btn_loading', 'disabled', 'pe-none');\r\n    btnEl.disabled = true;\r\n    const loaderEl = document.createElement('span');\r\n    loaderEl.classList.add('fa', 'fa-refresh', 'fa-spin', 'me-2');\r\n    btnEl.prepend(loaderEl);\r\n    return () => {\r\n        btnEl.classList.remove('o_website_btn_loading', 'disabled', 'pe-none');\r\n        btnEl.disabled = false;\r\n        loaderEl.remove();\r\n    };\r\n}\r\n", "/** @odoo-module **/\r\n\r\nimport {\r\n    BUTTON_HANDLER_SELECTOR,\r\n    makeAsyncHandler,\r\n    makeButtonHandler,\r\n} from '@web/legacy/js/core/minimal_dom';\r\n\r\n// Track when all JS files have been lazy loaded. Will allow to unblock the\r\n// related DOM sections when the whole JS have been loaded and executed.\r\nlet allScriptsLoadedResolve = null;\r\nconst _allScriptsLoaded = new Promise(resolve => {\r\n    allScriptsLoadedResolve = resolve;\r\n}).then(stopWaitingLazy);\r\n\r\nconst retriggeringWaitingProms = [];\r\n/**\r\n * Function to use as an event handler to replay the incoming event after the\r\n * whole lazy JS has been loaded. Note that blocking the incoming event is left\r\n * up to the caller (i.e. a potential wrapper, @see waitLazy).\r\n *\r\n * @param {Event} ev\r\n * @returns {Promise}\r\n */\r\nasync function waitForLazyAndRetrigger(ev) {\r\n    // Wait for the lazy JS to be loaded before re-triggering the event.\r\n    const targetEl = ev.target;\r\n    await _allScriptsLoaded;\r\n    // Loaded scripts were able to add a delay to wait for before re-triggering\r\n    // events: we wait for it here.\r\n    await Promise.all(retriggeringWaitingProms);\r\n\r\n    // At the end of the current execution queue, retrigger the event. Note that\r\n    // the event is reconstructed: this is necessary in some cases, e.g. submit\r\n    // buttons. Probably because the event was originally defaultPrevented.\r\n    setTimeout(() => {\r\n        // Extra safety check: the element might have been removed from the DOM\r\n        if (targetEl.isConnected) {\r\n            targetEl.dispatchEvent(new ev.constructor(ev.type, ev));\r\n        }\r\n    }, 0);\r\n}\r\n\r\nconst loadingEffectHandlers = [];\r\n/**\r\n * Adds the given event listener and saves it for later removal.\r\n *\r\n * @param {HTMLElement} el\r\n * @param {string} type\r\n * @param {Function} handler\r\n */\r\nfunction registerLoadingEffectHandler(el, type, handler) {\r\n    el.addEventListener(type, handler, {capture: true});\r\n    loadingEffectHandlers.push({el, type, handler});\r\n}\r\n\r\nlet waitingLazy = false;\r\n\r\n/**\r\n * Automatically adds a loading effect on clicked buttons (that were not marked\r\n * with a specific class). Once the whole JS has been loaded, the events will be\r\n * triggered again.\r\n *\r\n * For forms, we automatically prevent submit events (since can be triggered\r\n * without click on a button) but we do not retrigger them (could be duplicate\r\n * with re-trigger of a click on a submit button otherwise). However, submitting\r\n * a form in any way should most of the time simulate a click on the submit\r\n * button if any anyway.\r\n *\r\n * @todo This function used to consider the o_wait_lazy_js class. In master, the\r\n * uses of this classes should be removed in XML templates.\r\n * @see stopWaitingLazy\r\n */\r\nfunction waitLazy() {\r\n    if (waitingLazy) {\r\n        return;\r\n    }\r\n    waitingLazy = true;\r\n\r\n    document.body.classList.add('o_lazy_js_waiting');\r\n\r\n    // TODO should probably find the wrapwrap another way but in future versions\r\n    // the element will be gone anyway.\r\n    const mainEl = document.getElementById('wrapwrap') || document.body;\r\n    const loadingEffectButtonEls = [...mainEl.querySelectorAll(BUTTON_HANDLER_SELECTOR)]\r\n        // We target all buttons but...\r\n        .filter(el => {\r\n            // ... we allow to disable the effect by adding a specific class if\r\n            // needed. Note that if some non-lazy loaded code is adding an event\r\n            // handler on some buttons, it means that if they do not have that\r\n            // class, they will show a loading effect and not do anything until\r\n            // lazy JS is loaded anyway. This is not ideal, especially since\r\n            // this was added as a stable fix/imp, but this is a compromise: on\r\n            // next page visits, the cache should limit to effect of the lazy\r\n            // loading anyway.\r\n            return !el.classList.contains('o_no_wait_lazy_js')\r\n                // ... we also allow do not consider links with a href which is\r\n                // not \"#\". They could be linked to handlers that prevent their\r\n                // default behavior but we consider that following the link\r\n                // should still be relevant in that case.\r\n                && !(el.nodeName === 'A' && el.href && el.getAttribute('href') !== '#');\r\n        });\r\n    // Note: this is a limitation/a \"risk\" to only block and retrigger those\r\n    // specific event types.\r\n    const loadingEffectEventTypes = ['mouseover', 'mouseenter', 'mousedown', 'mouseup', 'click', 'mouseout', 'mouseleave'];\r\n    for (const buttonEl of loadingEffectButtonEls) {\r\n        for (const eventType of loadingEffectEventTypes) {\r\n            const loadingEffectHandler = eventType === 'click'\r\n                ? makeButtonHandler.call({\r\n                    '__makeButtonHandler_preventDefault': true,\r\n                    '__makeButtonHandler_stopImmediatePropagation': true,\r\n                }, waitForLazyAndRetrigger)\r\n                : makeAsyncHandler.call({\r\n                    '__makeAsyncHandler_stopImmediatePropagation': true,\r\n                }, waitForLazyAndRetrigger, true);\r\n            registerLoadingEffectHandler(buttonEl, eventType, loadingEffectHandler);\r\n        }\r\n    }\r\n\r\n    for (const formEl of document.querySelectorAll('form:not(.o_no_wait_lazy_js)')) {\r\n        registerLoadingEffectHandler(formEl, 'submit', ev => {\r\n            ev.preventDefault();\r\n            ev.stopImmediatePropagation();\r\n        });\r\n    }\r\n}\r\n/**\r\n * Undo what @see waitLazy did.\r\n */\r\nfunction stopWaitingLazy() {\r\n    if (!waitingLazy) {\r\n        return;\r\n    }\r\n    waitingLazy = false;\r\n\r\n    document.body.classList.remove('o_lazy_js_waiting');\r\n\r\n    for (const { el, type, handler } of loadingEffectHandlers) {\r\n        el.removeEventListener(type, handler, {capture: true});\r\n    }\r\n}\r\n\r\n// Start waiting for lazy loading as soon as the DOM is available\r\nif (document.readyState !== 'loading') {\r\n    waitLazy();\r\n} else {\r\n    document.addEventListener('DOMContentLoaded', function () {\r\n        waitLazy();\r\n    });\r\n}\r\n\r\n// As soon as the document is fully loaded, start loading the whole remaining JS\r\nif (document.readyState === 'complete') {\r\n    setTimeout(_loadScripts, 0);\r\n} else {\r\n    window.addEventListener('load', function () {\r\n        setTimeout(_loadScripts, 0);\r\n    });\r\n}\r\n\r\n/**\r\n * @param {DOMElement[]} scripts\r\n * @param {integer} index\r\n */\r\nfunction _loadScripts(scripts, index) {\r\n    if (scripts === undefined) {\r\n        scripts = document.querySelectorAll('script[data-src]');\r\n    }\r\n    if (index === undefined) {\r\n        index = 0;\r\n    }\r\n    if (index >= scripts.length) {\r\n        allScriptsLoadedResolve();\r\n        return;\r\n    }\r\n    const script = scripts[index];\r\n    script.addEventListener('load', _loadScripts.bind(this, scripts, index + 1));\r\n    script.setAttribute('defer', 'defer');\r\n    script.src = script.dataset.src;\r\n    script.removeAttribute('data-src');\r\n}\r\n\r\nexport default {\r\n    loadScripts: _loadScripts,\r\n    allScriptsLoaded: _allScriptsLoaded,\r\n    registerPageReadinessDelay: retriggeringWaitingProms.push.bind(retriggeringWaitingProms),\r\n};\r\n", "(function () {\r\n'use strict';\r\n\r\n/**\r\n * This file makes sure textarea elements with a specific editor class are\r\n * tweaked as soon as the DOM is ready so that they appear to be loading.\r\n *\r\n * They must then be loaded using standard Odoo modules system. In particular,\r\n * @see @web_editor/js/frontend/loadWysiwygFromTextarea\r\n */\r\n\r\ndocument.addEventListener('DOMContentLoaded', () => {\r\n    // Standard loop for better browser support\r\n    var textareaEls = document.querySelectorAll('textarea.o_wysiwyg_loader');\r\n    for (var i = 0; i < textareaEls.length; i++) {\r\n        var textarea = textareaEls[i];\r\n        var wrapper = document.createElement('div');\r\n        wrapper.classList.add('position-relative', 'o_wysiwyg_textarea_wrapper');\r\n\r\n        var loadingElement = document.createElement('div');\r\n        loadingElement.classList.add('o_wysiwyg_loading');\r\n        var loadingIcon = document.createElement('i');\r\n        loadingIcon.classList.add('text-600', 'text-center',\r\n            'fa', 'fa-circle-o-notch', 'fa-spin', 'fa-2x');\r\n        loadingElement.appendChild(loadingIcon);\r\n        wrapper.appendChild(loadingElement);\r\n\r\n        textarea.parentNode.insertBefore(wrapper, textarea);\r\n        wrapper.insertBefore(textarea, loadingElement);\r\n    }\r\n});\r\n\r\n})();\r\n"], "file": "/web/assets/a5bde4f/web.assets_frontend_minimal.js", "sourceRoot": "../../../"}