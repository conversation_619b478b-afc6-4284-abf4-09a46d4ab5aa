#!/usr/bin/env python3
import xmlrpc.client

def fix_with_days_reference():
    try:
        # Connection details
        url = 'http://localhost:8069'
        db = 'dataicraft'
        username = '<EMAIL>'
        password = 'admin123'
        
        # Connect to Odoo
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            print("❌ Authentication failed!")
            return
        
        print(f"✅ Connected to Odoo as user ID: {uid}")
        
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        print("\n🔧 === Working with Days as Reference UoM === 🔧")
        
        # Step 1: Find Working Time category and UoMs
        working_time_cat = models.execute_kw(
            db, uid, password,
            'uom.category', 'search_read',
            [[['name', '=', 'Working Time']]],
            {'fields': ['id', 'name']}
        )
        
        working_time_cat_id = working_time_cat[0]['id']
        print(f"📊 Working Time category ID: {working_time_cat_id}")
        
        # Find Days (reference) and Hours UoMs
        days_uom = models.execute_kw(
            db, uid, password,
            'uom.uom', 'search_read',
            [[['name', '=', 'Days'], ['category_id', '=', working_time_cat_id]]],
            {'fields': ['id', 'name', 'uom_type', 'factor']}
        )
        
        hours_uom = models.execute_kw(
            db, uid, password,
            'uom.uom', 'search_read',
            [[['name', '=', 'Hours'], ['category_id', '=', working_time_cat_id]]],
            {'fields': ['id', 'name', 'uom_type', 'factor']}
        )
        
        if not days_uom or not hours_uom:
            print("❌ Required UoMs not found")
            return
        
        days_id = days_uom[0]['id']
        hours_id = hours_uom[0]['id']
        
        print(f"📏 Days UoM: ID {days_id} (Reference)")
        print(f"⏰ Hours UoM: ID {hours_id} (Factor: {hours_uom[0]['factor']})")
        
        # Step 2: Fix Hours UoM factor (1 day = 8 hours, so 1 hour = 1/8 day = 0.125)
        print(f"\n🔧 Fixing Hours UoM factor...")
        
        models.execute_kw(
            db, uid, password,
            'uom.uom', 'write',
            [[hours_id], {
                'uom_type': 'smaller',
                'factor': 8.0,  # 8 hours = 1 day
                'rounding': 0.01
            }]
        )
        print("✅ Set Hours factor to 8.0 (8 hours = 1 day)")
        
        # Step 3: For services that need hourly billing, use Days but with fractional quantities
        print(f"\n🔧 Updating service products to use Days (with hourly conversion)...")
        
        # Update all service products to use Days
        service_products = models.execute_kw(
            db, uid, password,
            'product.template', 'search',
            [[['type', '=', 'service']]]
        )
        
        if service_products:
            models.execute_kw(
                db, uid, password,
                'product.template', 'write',
                [service_products, {
                    'uom_id': days_id,  # Use Days as the UoM
                    'uom_po_id': days_id
                }]
            )
            print(f"✅ Updated {len(service_products)} service products to use Days")
        
        # Step 4: Update company settings
        print(f"\n🔧 Updating company timesheet settings...")
        
        try:
            company_ids = models.execute_kw(
                db, uid, password,
                'res.company', 'search', [[]]
            )
            
            if company_ids:
                models.execute_kw(
                    db, uid, password,
                    'res.company', 'write',
                    [company_ids, {
                        'timesheet_encode_uom_id': hours_id,  # Still encode in hours
                    }]
                )
                print("✅ Company timesheet encoding set to Hours")
        
        except Exception as e:
            print(f"⚠️ Company settings: {e}")
        
        # Step 5: Alternative - Create a new "Hour" UoM in Unit category for problematic cases
        print(f"\n🔧 Creating alternative Hour UoM in Unit category...")
        
        # Find Unit category
        unit_cat = models.execute_kw(
            db, uid, password,
            'uom.category', 'search_read',
            [[['name', '=', 'Unit']]],
            {'fields': ['id', 'name']}
        )
        
        if unit_cat:
            unit_cat_id = unit_cat[0]['id']
            
            # Check if "Hour" (singular) exists in Unit category
            hour_unit = models.execute_kw(
                db, uid, password,
                'uom.uom', 'search_read',
                [[['name', '=', 'Hour'], ['category_id', '=', unit_cat_id]]],
                {'fields': ['id', 'name']}
            )
            
            if not hour_unit:
                # Create Hour UoM in Unit category
                hour_unit_id = models.execute_kw(
                    db, uid, password,
                    'uom.uom', 'create',
                    [{
                        'name': 'Hour',
                        'category_id': unit_cat_id,
                        'uom_type': 'smaller',
                        'factor': 1.0,
                        'rounding': 0.01
                    }]
                )
                print(f"✅ Created Hour UoM in Unit category (ID: {hour_unit_id})")
                
                # Update timesheet services to use this Hour UoM
                timesheet_services = models.execute_kw(
                    db, uid, password,
                    'product.template', 'search',
                    [[['type', '=', 'service'], ['service_type', '=', 'timesheet']]]
                )
                
                if timesheet_services:
                    models.execute_kw(
                        db, uid, password,
                        'product.template', 'write',
                        [timesheet_services, {
                            'uom_id': hour_unit_id,
                            'uom_po_id': hour_unit_id
                        }]
                    )
                    print(f"✅ Updated {len(timesheet_services)} timesheet services to use Hour (Unit category)")
            else:
                print("✅ Hour UoM already exists in Unit category")
        
        # Step 6: Clear problematic order lines
        print(f"\n🔧 Clearing problematic order lines...")
        
        try:
            # Clear draft order lines to avoid conflicts
            draft_orders = models.execute_kw(
                db, uid, password,
                'sale.order', 'search',
                [[['state', 'in', ['draft', 'sent']]]]
            )
            
            for order_id in draft_orders:
                try:
                    models.execute_kw(
                        db, uid, password,
                        'sale.order', 'action_cancel',
                        [[order_id]]
                    )
                except:
                    pass
            
            print(f"✅ Cleared {len(draft_orders)} draft sales orders")
            
        except Exception as e:
            print(f"⚠️ Error clearing orders: {e}")
        
        print(f"\n🎉 UoM Fix Complete!")
        print(f"✅ Working Time category uses Days as reference")
        print(f"✅ Hours UoM properly configured (8 hours = 1 day)")
        print(f"✅ Created Hour UoM in Unit category for timesheet services")
        print(f"✅ Cleared problematic orders")
        
        print(f"\n📋 Usage Guide:")
        print(f"  📅 Regular services → Use Days UoM")
        print(f"  ⏰ Timesheet services → Use Hour UoM (Unit category)")
        print(f"  📦 Physical products → Use Units UoM")
        
        print(f"\n🔄 Next Steps:")
        print("1. Refresh your browser (F5)")
        print("2. Try creating a new order")
        print("3. For timesheet services, select products with 'Hour' UoM")
        print("4. The UoM error should be resolved")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    fix_with_days_reference()
