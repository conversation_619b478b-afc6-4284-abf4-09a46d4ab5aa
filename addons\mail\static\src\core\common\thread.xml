<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

<t t-name="mail.Thread">
    <t t-if="env.inChatter" t-call="mail.Thread.jumpPresent"/> <!-- chatter has its own scrollable, this ensures proper sticky showing -->
    <div class="o-mail-Thread position-relative flex-grow-1 d-flex flex-column overflow-auto" t-att-class="{ 'pb-4':  props.showJumpPresent and !state.showJumpPresent }" t-ref="messages" tabindex="-1">
        <t t-if="!props.thread.isEmpty or props.thread.loadOlder or props.thread.hasLoadingFailed" name="content">
            <div class="d-flex flex-column position-relative flex-grow-1" t-att-class="{'justify-content-end': !env.inChatter and props.thread.type !== 'mailbox'}">
                <span class="position-absolute w-100 invisible" t-att-class="props.order === 'asc' ? 'bottom-0' : 'top-0'" t-ref="present-treshold" t-att-style="`height: Min(${PRESENT_THRESHOLD}px, 100%)`"/>
                <t t-set="currentDay" t-value="0"/>
                <t t-set="prevMsg" t-value="0"/>
                <t t-set="separatorDisplayed" t-value="false"/>
                <t t-if="props.order === 'asc'">
                    <t t-if="props.thread.loadOlder and !props.thread.isTransient and !props.thread.hasLoadingFailed" t-call="mail.Thread.loadOlder"/>
                    <t t-if="props.thread.hasLoadingFailed" t-call="mail.Thread.loadingError"/>
                </t>
                <t t-else="">
                    <span t-ref="load-newer"/>
                    <t t-if="!env.inChatter" t-call="mail.Thread.jumpPresent"/>
                </t>
                <t t-if="state.mountedAndLoaded" t-foreach="props.order === 'asc' ? props.thread.nonEmptyMessages : [...props.thread.nonEmptyMessages].reverse()" t-as="msg" t-key="msg.id">
                    <t t-if="msg.dateDay !== currentDay and props.showDates">
                        <DateSection date="msg.dateDay" className="'pt-4'"/>
                        <t t-set="currentDay" t-value="msg.dateDay"/>
                    </t>
                    <t t-if="props.thread.model === 'discuss.channel'">
                        <t t-set="showSeparator" t-value="(msg.id > props.thread.seen_message_id and !separatorDisplayed)" />
                        <t t-if="showSeparator">
                            <t t-set="separatorDisplayed" t-value="true"/>
                        </t>
                        <Transition visible="showSeparator" name="'o-fade'" t-slot-scope="transition">
                            <div class="o-mail-Thread-newMessage d-flex align-items-center fw-bolder" t-att-class="{ 'opacity-0': transition.className.includes('o-fade-leave') }">
                                <hr class="ms-2 flex-grow-1 border border-danger opacity-50"/><span class="px-2 text-danger">New messages</span><hr class="me-2 flex-grow-1 border border-danger opacity-50"/>
                            </div>
                        </Transition>
                    </t>
                    <t t-if="msg.isNotification">
                        <t t-call="mail.NotificationMessage"/>
                    </t>
                    <Message t-else=""
                        isInChatWindow="props.isInChatWindow"
                        message="msg"
                        messageToReplyTo="props.messageToReplyTo"
                        squashed="isSquashed(msg, prevMsg)"
                        highlighted="messageHighlight?.highlightedMessageId === msg.id"
                        onParentMessageClick="() => msg.parentMessage and env.messageHighlight?.highlightMessage(msg.parentMessage, props.thread)"
                        thread="props.thread"
                        messageEdition="props.messageEdition"
                        isFirstMessage="msg_first"
                        hasActions="props.messageActions"
                        showDates="props.showDates"
                    />
                    <t t-set="prevMsg" t-value="msg"/>
                </t>
                <t t-if="props.order === 'asc'">
                    <span t-ref="load-newer"/>
                    <t t-call="mail.Thread.jumpPresent"/>
                </t>
                <t t-else="">
                    <t t-if="props.thread.loadOlder and !props.thread.isTransient and !props.thread.hasLoadingFailed" t-call="mail.Thread.loadOlder"/>
                    <t t-if="props.thread.hasLoadingFailed" t-call="mail.Thread.loadingError"/>
                </t>
            </div>
        </t>
        <t t-else="">
            <div class="o-mail-Thread-empty d-flex flex-column align-items-center justify-content-center text-muted fst-italic h-100" t-att-class="{'p-4': props.showEmptyMessage}">
                <t t-if="props.thread.isLoaded">
                    <t t-if="props.thread.isEmpty and props.thread.type === 'mailbox'">
                    <t t-if="props.thread.id === 'inbox'">
                        <h4 class="mb-3 fw-bolder">
                            Congratulations, your inbox is empty
                        </h4>
                        New messages appear here.
                    </t>
                    <t t-if="props.thread.id === 'starred'">
                        <h4 class="mb-3 fw-bolder">
                            No starred messages
                        </h4>
                        You can mark any message as 'starred', and it shows up in this mailbox.
                    </t>
                    <t t-if="props.thread.id === 'history'">
                        <img src="/web/static/img/neutral_face.svg" alt="History"/>
                        <h4 class="mb-3 fw-bolder">
                            No history messages
                        </h4>
                        Messages marked as read will appear in the history.
                    </t>
                    </t>
                    <t t-elif="props.showEmptyMessage">
                        There are no messages in this conversation.
                    </t>
                </t>
            </div>
        </t>
    </div>
</t>

<t t-name="mail.Thread.jumpPresent">
    <span t-if="props.showJumpPresent and state.showJumpPresent" class="o-mail-Thread-jumpPresent position-sticky btn btn-link alert alert-info d-flex cursor-pointer align-items-center py-2 m-0" t-att-class="{ 'px-4': !env.inChatWindow, 'px-2': env.inChatWindow, 'top-0': props.order !== 'asc', 'bottom-0': props.order === 'asc' }" role="button" t-on-click="() => this.jumpToPresent()">
        <span class="small">You're viewing older messages</span>
        <span class="flex-grow-1"/>
        <span class="fw-bolder small pe-2">Jump to Present</span>
        <i class="fa" t-att-class="{ 'fa-caret-up': props.order !== 'asc', 'fa-caret-down': props.order === 'asc' }"/>
    </span>
</t>

<t t-name="mail.Thread.loadOlder">
    <button class="btn btn-link" t-on-click="onClickLoadOlder" t-ref="load-older">Load More</button>
</t>

<t t-name="mail.Thread.loadingError">
    <div class="d-flex flex-grow-1 align-items-center justify-content-center flex-column">
        <div class="o-mail-Thread-error">
            An error occurred while fetching messages.
        </div>
        <button class="btn btn-link" t-on-click="onClickLoadOlder">
            Click here to retry
        </button>
    </div>
</t>

<t t-name="mail.NotificationMessage">
    <div class="o-mail-NotificationMessage text-break mx-auto text-500 small px-3 text-center" t-on-click="onClickNotification" t-att-class="{
        'mt-2': prevMsg and !prevMsg.isNotification,
    }" t-ref="root">
        <i t-if="msg.notificationIcon" t-attf-class="{{ msg.notificationIcon }} me-1"/>
        <span class="o-mail-NotificationMessage-author d-inline" t-if="msg.author and !msg.body.includes(escape(msg.author.name))" t-esc="msg.author.name"/> <t t-out="msg.body"/>
    </div>
</t>

</templates>
