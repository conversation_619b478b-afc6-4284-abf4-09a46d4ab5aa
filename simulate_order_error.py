#!/usr/bin/env python3
import xmlrpc.client
import traceback

def simulate_order_error():
    try:
        # Connection details
        url = 'http://localhost:8069'
        db = 'dataicraft'
        username = '<EMAIL>'
        password = 'admin123'
        
        print("🎯 === Simulating Order Creation Error === 🎯")
        
        # Connect
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        print(f"✅ Connected as user {uid}")
        
        # Get all products and their UoMs
        products = models.execute_kw(
            db, uid, password,
            'product.template', 'search_read',
            [[]],
            {'fields': ['id', 'name', 'uom_id', 'type', 'service_type']}
        )
        
        print(f"\n📦 Found {len(products)} products")
        
        # Get a customer
        customers = models.execute_kw(
            db, uid, password,
            'res.partner', 'search_read',
            [[['is_company', '=', True]]],
            {'fields': ['id', 'name'], 'limit': 1}
        )
        
        if not customers:
            customer_id = models.execute_kw(
                db, uid, password,
                'res.partner', 'create',
                [{'name': 'Test Customer', 'is_company': True}]
            )
        else:
            customer_id = customers[0]['id']
        
        print(f"✅ Using customer ID: {customer_id}")
        
        # Try creating orders with each product to find the problematic one
        print(f"\n🔍 Testing each product...")
        
        for i, product in enumerate(products[:10]):  # Test first 10 products
            try:
                product_id = product['id']
                product_name = product['name']
                product_uom_id = product['uom_id'][0]
                
                print(f"\n📦 Testing Product {i+1}: {product_name}")
                print(f"   Product ID: {product_id}")
                print(f"   Product UoM ID: {product_uom_id}")
                print(f"   Type: {product.get('type', 'N/A')}")
                print(f"   Service Type: {product.get('service_type', 'N/A')}")
                
                # Try to create a sales order with this product
                so_data = {
                    'partner_id': customer_id,
                    'order_line': [(0, 0, {
                        'product_id': product_id,
                        'product_uom_qty': 1.0,
                        'product_uom': product_uom_id,  # Use same UoM as product
                        'price_unit': 100.0
                    })]
                }
                
                so_id = models.execute_kw(
                    db, uid, password,
                    'sale.order', 'create',
                    [so_data]
                )
                
                print(f"   ✅ SUCCESS: Order created (ID: {so_id})")
                
                # Delete the order to clean up
                models.execute_kw(
                    db, uid, password,
                    'sale.order', 'unlink',
                    [[so_id]]
                )
                
            except Exception as e:
                print(f"   🔴 ERROR with product {product_name}:")
                print(f"   Error: {str(e)}")
                
                if "unit of measure" in str(e).lower():
                    print(f"   🎯 FOUND THE PROBLEMATIC PRODUCT!")
                    print(f"   Product ID: {product_id}")
                    print(f"   Product Name: {product_name}")
                    print(f"   Product UoM ID: {product_uom_id}")
                    
                    # Get detailed UoM info for this product
                    uom_info = models.execute_kw(
                        db, uid, password,
                        'uom.uom', 'read',
                        [product_uom_id],
                        {'fields': ['name', 'category_id']}
                    )
                    
                    print(f"   UoM Name: {uom_info['name']}")
                    print(f"   UoM Category: {uom_info['category_id']}")
                    
                    # Fix this specific product
                    print(f"   🔧 FIXING THIS PRODUCT...")
                    models.execute_kw(
                        db, uid, password,
                        'product.template', 'write',
                        [[product_id], {'uom_id': 1, 'uom_po_id': 1}]
                    )
                    print(f"   ✅ Product fixed to use Units UoM")
                    
                    return product_id, product_name
        
        print(f"\n✅ All tested products work fine")
        return None, None
        
    except Exception as e:
        print(f"❌ Error in simulation: {e}")
        traceback.print_exc()
        return None, None

if __name__ == "__main__":
    problematic_product_id, problematic_product_name = simulate_order_error()
    
    if problematic_product_id:
        print(f"\n🎯 SOLUTION APPLIED!")
        print(f"Fixed product: {problematic_product_name} (ID: {problematic_product_id})")
        print(f"Now try creating your order again - it should work!")
    else:
        print(f"\n🤔 No problematic product found in simulation")
        print(f"The error might be occurring with a specific product you're trying to use")
        print(f"Please tell me which product you're trying to add to the order")
