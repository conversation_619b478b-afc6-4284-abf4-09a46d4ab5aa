#!/usr/bin/env python3
import psycopg2

def clean_database_for_production():
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='dataicraft',
            user='odoo',
            password='odoo123'
        )
        cur = conn.cursor()
        
        print("🧹 === CLEANING DATABASE FOR PRODUCTION === 🧹")
        
        # Step 1: Delete all test/demo data
        print("\n🗑️ Step 1: Deleting all test/demo data...")
        
        # Delete all orders and order lines
        cur.execute("DELETE FROM sale_order_line;")
        deleted_sol = cur.rowcount
        print(f"✅ Deleted {deleted_sol} sales order lines")
        
        cur.execute("DELETE FROM purchase_order_line;")
        deleted_pol = cur.rowcount
        print(f"✅ Deleted {deleted_pol} purchase order lines")
        
        cur.execute("DELETE FROM sale_order;")
        deleted_so = cur.rowcount
        print(f"✅ Deleted {deleted_so} sales orders")
        
        cur.execute("DELETE FROM purchase_order;")
        deleted_po = cur.rowcount
        print(f"✅ Deleted {deleted_po} purchase orders")
        
        # Step 2: Delete all accounting entries (except opening balances)
        print("\n🗑️ Step 2: Deleting test accounting entries...")
        
        cur.execute("DELETE FROM account_move_line WHERE move_id IN (SELECT id FROM account_move WHERE move_type != 'entry' OR name LIKE '%test%' OR name LIKE '%demo%');")
        deleted_aml = cur.rowcount
        print(f"✅ Deleted {deleted_aml} test account move lines")
        
        cur.execute("DELETE FROM account_move WHERE move_type != 'entry' OR name LIKE '%test%' OR name LIKE '%demo%';")
        deleted_am = cur.rowcount
        print(f"✅ Deleted {deleted_am} test account moves")
        
        # Step 3: Delete all inventory/stock data
        print("\n🗑️ Step 3: Deleting inventory data...")
        
        cur.execute("DELETE FROM stock_move;")
        deleted_sm = cur.rowcount
        print(f"✅ Deleted {deleted_sm} stock moves")
        
        cur.execute("DELETE FROM stock_quant;")
        deleted_sq = cur.rowcount
        print(f"✅ Deleted {deleted_sq} stock quants")
        
        cur.execute("DELETE FROM stock_picking;")
        deleted_sp = cur.rowcount
        print(f"✅ Deleted {deleted_sp} stock pickings")
        
        # Step 4: Delete all project/timesheet data
        print("\n🗑️ Step 4: Deleting project/timesheet data...")
        
        cur.execute("DELETE FROM account_analytic_line;")
        deleted_aal = cur.rowcount
        print(f"✅ Deleted {deleted_aal} analytic lines")
        
        cur.execute("DELETE FROM project_task;")
        deleted_pt = cur.rowcount
        print(f"✅ Deleted {deleted_pt} project tasks")
        
        cur.execute("DELETE FROM project_project WHERE name LIKE '%test%' OR name LIKE '%demo%' OR name LIKE '%sample%';")
        deleted_pp = cur.rowcount
        print(f"✅ Deleted {deleted_pp} test projects")
        
        # Step 5: Delete test products (keep product categories and UoMs)
        print("\n🗑️ Step 5: Deleting test products...")
        
        cur.execute("DELETE FROM product_product;")
        deleted_variants = cur.rowcount
        print(f"✅ Deleted {deleted_variants} product variants")
        
        cur.execute("DELETE FROM product_template;")
        deleted_templates = cur.rowcount
        print(f"✅ Deleted {deleted_templates} product templates")
        
        # Step 6: Delete test partners (keep company partner)
        print("\n🗑️ Step 6: Deleting test partners...")
        
        cur.execute("DELETE FROM res_partner WHERE id > 1 AND (name LIKE '%test%' OR name LIKE '%demo%' OR name LIKE '%sample%' OR name LIKE '%Test%');")
        deleted_partners = cur.rowcount
        print(f"✅ Deleted {deleted_partners} test partners")
        
        # Step 7: Reset sequences to start fresh
        print("\n🔄 Step 7: Resetting sequences...")
        
        sequences_to_reset = [
            ('sale.order', 'SO', 1),
            ('purchase.order', 'PO', 1),
            ('account.move', 'INV', 1),
            ('product.template', 'PROD', 1),
            ('res.partner', 'CUST', 1),
            ('project.project', 'PROJ', 1),
            ('project.task', 'TASK', 1)
        ]
        
        for seq_code, prefix, start_num in sequences_to_reset:
            cur.execute("UPDATE ir_sequence SET number_next = %s WHERE code = %s;", (start_num, seq_code))
            print(f"✅ Reset {seq_code} sequence to start at {start_num}")
        
        # Step 8: Clean up system data
        print("\n🧹 Step 8: Cleaning system data...")
        
        cur.execute("DELETE FROM ir_attachment WHERE res_model IN ('sale.order', 'purchase.order', 'product.template', 'project.project');")
        deleted_attachments = cur.rowcount
        print(f"✅ Deleted {deleted_attachments} test attachments")
        
        cur.execute("DELETE FROM mail_message WHERE model IN ('sale.order', 'purchase.order', 'product.template', 'project.project');")
        deleted_messages = cur.rowcount
        print(f"✅ Deleted {deleted_messages} test messages")
        
        # Step 9: Vacuum and analyze database
        print("\n🔧 Step 9: Optimizing database...")
        
        conn.commit()  # Commit before vacuum
        conn.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_AUTOCOMMIT)
        
        tables_to_vacuum = [
            'product_template', 'product_product', 'res_partner',
            'sale_order', 'sale_order_line', 'purchase_order', 'purchase_order_line',
            'account_move', 'account_move_line', 'stock_move', 'stock_quant',
            'project_project', 'project_task', 'account_analytic_line'
        ]
        
        for table in tables_to_vacuum:
            cur.execute(f"VACUUM ANALYZE {table};")
            print(f"✅ Optimized {table}")
        
        print(f"\n🎉 DATABASE CLEANED FOR PRODUCTION!")
        
        # Step 10: Final verification
        print(f"\n✅ Final Status:")
        
        conn.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_READ_COMMITTED)
        
        cur.execute("SELECT COUNT(*) FROM product_template;")
        products = cur.fetchone()[0]
        print(f"Products: {products}")
        
        cur.execute("SELECT COUNT(*) FROM res_partner WHERE id > 1;")
        partners = cur.fetchone()[0]
        print(f"Partners (excluding company): {partners}")
        
        cur.execute("SELECT COUNT(*) FROM sale_order;")
        sales_orders = cur.fetchone()[0]
        print(f"Sales orders: {sales_orders}")
        
        cur.execute("SELECT COUNT(*) FROM purchase_order;")
        purchase_orders = cur.fetchone()[0]
        print(f"Purchase orders: {purchase_orders}")
        
        cur.execute("SELECT COUNT(*) FROM account_move;")
        account_moves = cur.fetchone()[0]
        print(f"Account moves: {account_moves}")
        
        cur.execute("SELECT COUNT(*) FROM project_project;")
        projects = cur.fetchone()[0]
        print(f"Projects: {projects}")
        
        print(f"\n📋 What's Preserved:")
        print(f"✅ Chart of Accounts (47 accounts)")
        print(f"✅ Journals (7 journals)")
        print(f"✅ UoM structure (working correctly)")
        print(f"✅ Company settings (Dataicraft Pvt Ltd)")
        print(f"✅ User accounts and permissions")
        print(f"✅ All installed modules and configurations")
        print(f"✅ Currency settings (PKR)")
        print(f"✅ Tax configurations")
        
        print(f"\n📋 What's Cleaned:")
        print(f"🗑️ All test/demo products")
        print(f"🗑️ All test orders and transactions")
        print(f"🗑️ All test partners/customers")
        print(f"🗑️ All test inventory data")
        print(f"🗑️ All test project data")
        print(f"🗑️ All test accounting entries")
        print(f"🔄 All sequences reset to start fresh")
        
        print(f"\n🚀 Ready for Production Data Entry!")
        print(f"You can now start entering:")
        print(f"1. Real customers and vendors")
        print(f"2. Real products and services")
        print(f"3. Real sales and purchase orders")
        print(f"4. Real construction projects")
        print(f"5. Real accounting transactions")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    clean_database_for_production()
