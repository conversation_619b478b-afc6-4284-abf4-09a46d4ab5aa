<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.LinkPreview">
        <t t-if="props.linkPreview.isCard">
            <t t-call="mail.LinkPreviewCard"/>
        </t>
        <t t-if="props.linkPreview.isVideo">
            <t t-call="mail.LinkPreviewVideo"/>
        </t>
        <t t-if="props.linkPreview.isImage">
            <t t-call="mail.LinkPreviewImage"/>
        </t>
    </t>

    <t t-name="mail.LinkPreviewCard">
        <div class="o-mail-LinkPreviewCard card position-relative w-100 mb-2 rounded bg-view shadow-sm overflow-hidden" t-att-class="{ 'me-2': env.inChatter }" t-attf-class="{{ className }}">
            <div class="row g-0 flex-row-reverse h-100">
                <div class="col min-w-0" t-att-class="{ 'd-flex align-items-center': !props.linkPreview.og_description }">
                    <div class="p-3">
                        <h6 class="card-title mb-0 me-2" t-attf-class="{{ props.linkPreview.og_description ? 'text-truncate' : 'overflow-hidden' }}">
                            <a t-att-href="props.linkPreview.source_url" target="_blank" t-out="props.linkPreview.og_title"/>
                        </h6>
                        <span t-if="props.linkPreview.og_site_name" t-out="props.linkPreview.og_site_name"/>
                        <p t-if="props.linkPreview.og_description" class="o-mail-LinkPreviewCard-description card-text mb-0 mt-2 text-muted small overflow-hidden" t-out="props.linkPreview.og_description"/>
                    </div>
                </div>
                <div class="o-mail-LinkPreviewCard-imageLinkWrap col-4 d-block">
                    <a t-att-href="props.linkPreview.source_url" target="_blank">
                        <img t-if="props.linkPreview.og_image" class="w-100 h-100 o_object_fit_cover" t-att-src="props.linkPreview.og_image" t-att-alt="props.linkPreview.og_title" t-on-load="onImageLoaded"/>
                        <span t-else="" class="d-flex align-items-center justify-content-center w-100 h-100 bg-100 text-300">
                            <i class="fa fa-camera fa-2x"/>
                        </span>
                    </a>
                </div>
            </div>
            <t t-if="props.deletable" t-call="mail.LinkPreview.aside">
                <t t-set="className" t-value="'fa fa-stack p-0 opacity-75 opacity-100-hover'"/>
            </t>
        </div>
    </t>

    <t t-name="mail.LinkPreviewImage">
        <div class="o-mail-LinkPreviewImage position-relative mb-2" t-att-class="{ 'me-2': env.inChatter }" t-attf-class="{{ className }}">
            <a t-if="props.linkPreview.imageUrl" t-att-href="props.linkPreview.imageUrl" target="_blank">
                <img class="h-auto w-auto rounded" t-att-src="props.linkPreview.imageUrl" t-on-load="onImageLoaded"/>
            </a>
            <t t-if="props.deletable" t-call="mail.LinkPreview.aside">
                <t t-set="className" t-value="'btn btn-sm btn-dark mt-2 me-2 rounded opacity-75 opacity-100-hover'"/>
            </t>
        </div>
    </t>

    <t t-name="mail.LinkPreviewVideo">
        <div class="o-mail-LinkPreviewVideo card position-relative w-100 mb-2 rounded bg-view shadow-sm overflow-hidden" t-att-class="{ 'me-2': env.inChatter }" t-attf-class="{{ className }}">
            <div class="row g-0 flex-row-reverse h-100">
                <div class="col min-w-0" t-att-class="{ 'd-flex align-items-center': !props.linkPreview.og_description }">
                    <div class="p-3 bg-view">
                        <h6 class="o-mail-LinkPreviewVideo-title card-title mb-0 me-2" t-attf-class="{{ props.linkPreview.og_description ? 'text-truncate' : 'o-mail-LinkPreviewVideo-hasDescription overflow-hidden' }}">
                            <a t-att-href="props.linkPreview.source_url" target="_blank" t-esc="props.linkPreview.og_title"/>
                        </h6>
                        <p t-if="props.linkPreview.og_description" class="o-mail-LinkPreviewVideo-description o-mail-LinkPreviewVideo-hasDescription card-text mb-0 mt-2 text-muted small overflow-hidden" t-out="props.linkPreview.og_description"></p>
                    </div>
                </div>
                <div class="o-mail-LinkPreviewVideo-videoWrap col-4 d-block">
                    <a t-att-href="props.linkPreview.source_url" target="_blank">
                        <div t-if="props.linkPreview.og_image" class="o-mail-LinkPreviewVideo-overlay position-relative h-100 opacity-trigger-hover">
                            <img class="img-fluid h-100 rounded-bottom o_object_fit_cover" t-att-src="props.linkPreview.og_image" t-att-alt="props.linkPreview.og_title" t-on-load="onImageLoaded"/>
                            <div class="position-absolute top-50 start-50 translate-middle">
                                <div class="o-mail-LinkPreviewVideo-play btn btn-lg rounded opacity-75 opacity-100-hover transition-base">
                                    <i class="fa fa-play"/>
                                </div>
                            </div>
                        </div>
                        <span t-else="" class="d-flex align-items-center justify-content-center w-100 h-100 bg-100 text-300">
                            <i class="fa fa-camera fa-2x"/>
                        </span>
                    </a>
                </div>
            </div>
            <t t-if="props.deletable" t-call="mail.LinkPreview.aside">
                <t t-set="className" t-value="'fa fa-stack p-0 opacity-75 opacity-100-hover'"/>
            </t>
        </div>
    </t>
    
    <t t-name="mail.LinkPreview.aside">
        <div class="position-absolute top-0 end-0 small">
            <button t-attf-class="{{ className }}" class="o-mail-LinkPreview-aside btn" aria-label="Remove" t-on-click="onClick">
                <i class="fa fa-times"/>
            </button>
        </div>
    </t>
</templates>
