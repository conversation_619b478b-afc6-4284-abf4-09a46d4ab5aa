)]}'
{"version": 3, "sources": ["/web_editor/static/lib/cropperjs/cropper.css", "/web/static/lib/bootstrap/scss/_functions.scss", "/web/static/lib/bootstrap/scss/_mixins.scss", "/web/static/src/scss/functions.scss", "/web/static/src/scss/mixins_forwardport.scss", "/web/static/src/scss/bs_mixins_overrides.scss", "/web/static/src/scss/utils.scss", "/web/static/src/scss/primary_variables.scss", "/web/static/src/core/avatar/avatar.variables.scss", "/web/static/src/core/notifications/notification.variables.scss", "/web/static/src/search/control_panel/control_panel.variables.scss", "/web/static/src/search/search_panel/search_panel.variables.scss", "/web/static/src/views/fields/statusbar/statusbar_field.variables.scss", "/web/static/src/views/form/form.variables.scss", "/web/static/src/views/kanban/kanban.variables.scss", "/web/static/src/webclient/burger_menu/burger_menu.variables.scss", "/web/static/src/webclient/navbar/navbar.variables.scss", "/mail/static/src/core/common/primary_variables.scss", "/mail/static/src/discuss/typing/common/primary_variables.scss", "/mail/static/src/scss/variables/primary_variables.scss", "/onboarding/static/src/scss/onboarding.variables.scss", "/web_editor/static/src/scss/web_editor.variables.scss", "/web_editor/static/src/scss/wysiwyg.variables.scss", "/portal/static/src/scss/primary_variables.scss", "/account/static/src/scss/variables.scss", "/web/static/src/scss/secondary_variables.scss", "/web_editor/static/src/scss/secondary_variables.scss", "/web_editor/static/src/scss/bootstrap_overridden.scss", "/web/static/src/scss/pre_variables.scss", "/web/static/lib/bootstrap/scss/_variables.scss", "/web_editor/static/src/js/editor/odoo-editor/src/style.scss", "/web_editor/static/src/scss/wysiwyg.scss", "/web_editor/static/src/scss/wysiwyg_iframe.scss", "/web_editor/static/src/scss/wysiwyg_snippets.scss"], "mappings": ";;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AClTA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACjCA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AChBA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC7lBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AC5tBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;ACnGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "sourcesContent": ["\n/* /web_editor/static/lib/cropperjs/cropper.css */\n/*!\r\n * Cropper.js v1.5.5\r\n * https://fengyuanchen.github.io/cropperjs\r\n *\r\n * Copyright 2015-present Chen Fengyuan\r\n * Released under the MIT license\r\n *\r\n * Date: 2019-08-04T02:26:27.232Z\r\n */\r\n\r\n .cropper-container {\r\n  direction: ltr;\r\n  font-size: 0;\r\n  line-height: 0;\r\n  position: relative;\r\n  -ms-touch-action: none;\r\n  touch-action: none;\r\n  -webkit-user-select: none;\r\n  -moz-user-select: none;\r\n  -ms-user-select: none;\r\n  user-select: none;\r\n}\r\n\r\n.cropper-container img {\r\n  display: block;\r\n  height: 100%;\r\n  image-orientation: 0deg;\r\n  max-height: none !important;\r\n  max-width: none !important;\r\n  min-height: 0 !important;\r\n  min-width: 0 !important;\r\n  width: 100%;\r\n}\r\n\r\n.cropper-wrap-box,\r\n.cropper-canvas,\r\n.cropper-drag-box,\r\n.cropper-crop-box,\r\n.cropper-modal {\r\n  bottom: 0;\r\n  left: 0;\r\n  position: absolute;\r\n  right: 0;\r\n  top: 0;\r\n}\r\n\r\n.cropper-wrap-box,\r\n.cropper-canvas {\r\n  overflow: hidden;\r\n}\r\n\r\n.cropper-drag-box {\r\n  background-color: #fff;\r\n  opacity: 0;\r\n}\r\n\r\n.cropper-modal {\r\n  background-color: #000;\r\n  opacity: 0.5;\r\n}\r\n\r\n.cropper-view-box {\r\n  display: block;\r\n  height: 100%;\r\n  outline: 1px solid #39f;\r\n  outline-color: rgba(51, 153, 255, 0.75);\r\n  overflow: hidden;\r\n  width: 100%;\r\n}\r\n\r\n.cropper-dashed {\r\n  border: 0 dashed #eee;\r\n  display: block;\r\n  opacity: 0.5;\r\n  position: absolute;\r\n}\r\n\r\n.cropper-dashed.dashed-h {\r\n  border-bottom-width: 1px;\r\n  border-top-width: 1px;\r\n  height: calc(100% / 3);\r\n  left: 0;\r\n  top: calc(100% / 3);\r\n  width: 100%;\r\n}\r\n\r\n.cropper-dashed.dashed-v {\r\n  border-left-width: 1px;\r\n  border-right-width: 1px;\r\n  height: 100%;\r\n  left: calc(100% / 3);\r\n  top: 0;\r\n  width: calc(100% / 3);\r\n}\r\n\r\n.cropper-center {\r\n  display: block;\r\n  height: 0;\r\n  left: 50%;\r\n  opacity: 0.75;\r\n  position: absolute;\r\n  top: 50%;\r\n  width: 0;\r\n}\r\n\r\n.cropper-center::before,\r\n.cropper-center::after {\r\n  background-color: #eee;\r\n  content: ' ';\r\n  display: block;\r\n  position: absolute;\r\n}\r\n\r\n.cropper-center::before {\r\n  height: 1px;\r\n  left: -3px;\r\n  top: 0;\r\n  width: 7px;\r\n}\r\n\r\n.cropper-center::after {\r\n  height: 7px;\r\n  left: 0;\r\n  top: -3px;\r\n  width: 1px;\r\n}\r\n\r\n.cropper-face,\r\n.cropper-line,\r\n.cropper-point {\r\n  display: block;\r\n  height: 100%;\r\n  opacity: 0.1;\r\n  position: absolute;\r\n  width: 100%;\r\n}\r\n\r\n.cropper-face {\r\n  background-color: #fff;\r\n  left: 0;\r\n  top: 0;\r\n}\r\n\r\n.cropper-line {\r\n  background-color: #39f;\r\n}\r\n\r\n.cropper-line.line-e {\r\n  cursor: ew-resize;\r\n  right: -3px;\r\n  top: 0;\r\n  width: 5px;\r\n}\r\n\r\n.cropper-line.line-n {\r\n  cursor: ns-resize;\r\n  height: 5px;\r\n  left: 0;\r\n  top: -3px;\r\n}\r\n\r\n.cropper-line.line-w {\r\n  cursor: ew-resize;\r\n  left: -3px;\r\n  top: 0;\r\n  width: 5px;\r\n}\r\n\r\n.cropper-line.line-s {\r\n  bottom: -3px;\r\n  cursor: ns-resize;\r\n  height: 5px;\r\n  left: 0;\r\n}\r\n\r\n.cropper-point {\r\n  background-color: #39f;\r\n  height: 5px;\r\n  opacity: 0.75;\r\n  width: 5px;\r\n}\r\n\r\n.cropper-point.point-e {\r\n  cursor: ew-resize;\r\n  margin-top: -3px;\r\n  right: -3px;\r\n  top: 50%;\r\n}\r\n\r\n.cropper-point.point-n {\r\n  cursor: ns-resize;\r\n  left: 50%;\r\n  margin-left: -3px;\r\n  top: -3px;\r\n}\r\n\r\n.cropper-point.point-w {\r\n  cursor: ew-resize;\r\n  left: -3px;\r\n  margin-top: -3px;\r\n  top: 50%;\r\n}\r\n\r\n.cropper-point.point-s {\r\n  bottom: -3px;\r\n  cursor: s-resize;\r\n  left: 50%;\r\n  margin-left: -3px;\r\n}\r\n\r\n.cropper-point.point-ne {\r\n  cursor: nesw-resize;\r\n  right: -3px;\r\n  top: -3px;\r\n}\r\n\r\n.cropper-point.point-nw {\r\n  cursor: nwse-resize;\r\n  left: -3px;\r\n  top: -3px;\r\n}\r\n\r\n.cropper-point.point-sw {\r\n  bottom: -3px;\r\n  cursor: nesw-resize;\r\n  left: -3px;\r\n}\r\n\r\n.cropper-point.point-se {\r\n  bottom: -3px;\r\n  cursor: nwse-resize;\r\n  height: 20px;\r\n  opacity: 1;\r\n  right: -3px;\r\n  width: 20px;\r\n}\r\n\r\n@media (min-width: 768px) {\r\n  .cropper-point.point-se {\r\n    height: 15px;\r\n    width: 15px;\r\n  }\r\n}\r\n\r\n@media (min-width: 992px) {\r\n  .cropper-point.point-se {\r\n    height: 10px;\r\n    width: 10px;\r\n  }\r\n}\r\n\r\n@media (min-width: 1200px) {\r\n  .cropper-point.point-se {\r\n    height: 5px;\r\n    opacity: 0.75;\r\n    width: 5px;\r\n  }\r\n}\r\n\r\n.cropper-point.point-se::before {\r\n  background-color: #39f;\r\n  bottom: -50%;\r\n  content: ' ';\r\n  display: block;\r\n  height: 200%;\r\n  opacity: 0;\r\n  position: absolute;\r\n  right: -50%;\r\n  width: 200%;\r\n}\r\n\r\n.cropper-invisible {\r\n  opacity: 0;\r\n}\r\n\r\n.cropper-bg {\r\n  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABAAAAAQAQMAAAAlPW0iAAAAA3NCSVQICAjb4U/gAAAABlBMVEXMzMz////TjRV2AAAACXBIWXMAAArrAAAK6wGCiw1aAAAAHHRFWHRTb2Z0d2FyZQBBZG9iZSBGaXJld29ya3MgQ1M26LyyjAAAABFJREFUCJlj+M/AgBVhF/0PAH6/D/HkDxOGAAAAAElFTkSuQmCC');\r\n}\r\n\r\n.cropper-hide {\r\n  display: block;\r\n  height: 0;\r\n  position: absolute;\r\n  width: 0;\r\n}\r\n\r\n.cropper-hidden {\r\n  display: none !important;\r\n}\r\n\r\n.cropper-move {\r\n  cursor: move;\r\n}\r\n\r\n.cropper-crop {\r\n  cursor: crosshair;\r\n}\r\n\r\n.cropper-disabled .cropper-drag-box,\r\n.cropper-disabled .cropper-face,\r\n.cropper-disabled .cropper-line,\r\n.cropper-disabled .cropper-point {\r\n  cursor: not-allowed;\r\n}\r\n", "\n/* /web/static/lib/bootstrap/scss/_functions.scss */\n\n", "\n/* /web/static/lib/bootstrap/scss/_mixins.scss */\n\n", "\n/* /web/static/src/scss/functions.scss */\n\n", "\n/* /web/static/src/scss/mixins_forwardport.scss */\n\n", "\n/* /web/static/src/scss/bs_mixins_overrides.scss */\n\n", "\n/* /web/static/src/scss/utils.scss */\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_scale, .o_we_cc_preview_wrapper, #oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page we-button, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"transparent_grayscale\"] .o_we_color_btn, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"theme\"] .o_we_color_btn, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"reset\"] .o_we_color_btn, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"custom\"] .o_we_color_btn {\n  position: relative;\n  z-index: 0;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_scale::before, .o_we_cc_preview_wrapper::before, #oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview::before, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page we-button::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"transparent_grayscale\"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"theme\"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"reset\"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"custom\"] .o_we_color_btn::before {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  z-index: -1;\n  background-image: url(\"/web/static/img/transparent.png\");\n  background-size: 10px auto;\n  border-radius: inherit;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_scale::after, .o_we_cc_preview_wrapper::after, #oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview::after, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page we-button::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"transparent_grayscale\"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"theme\"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"reset\"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"custom\"] .o_we_color_btn::after {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  z-index: -1;\n  background: inherit;\n  border-radius: inherit;\n}\n\n", "\n/* /web/static/src/scss/primary_variables.scss */\n\n", "\n/* /web/static/src/core/avatar/avatar.variables.scss */\n\n", "\n/* /web/static/src/core/notifications/notification.variables.scss */\n\n", "\n/* /web/static/src/search/control_panel/control_panel.variables.scss */\n\n", "\n/* /web/static/src/search/search_panel/search_panel.variables.scss */\n\n", "\n/* /web/static/src/views/fields/statusbar/statusbar_field.variables.scss */\n\n", "\n/* /web/static/src/views/form/form.variables.scss */\n\n", "\n/* /web/static/src/views/kanban/kanban.variables.scss */\n\n", "\n/* /web/static/src/webclient/burger_menu/burger_menu.variables.scss */\n\n", "\n/* /web/static/src/webclient/navbar/navbar.variables.scss */\n\n", "\n/* /mail/static/src/core/common/primary_variables.scss */\n\n", "\n/* /mail/static/src/discuss/typing/common/primary_variables.scss */\n\n", "\n/* /mail/static/src/scss/variables/primary_variables.scss */\n\n", "\n/* /onboarding/static/src/scss/onboarding.variables.scss */\n\n", "\n/* /web_editor/static/src/scss/web_editor.variables.scss */\n\n", "\n/* /web_editor/static/src/scss/wysiwyg.variables.scss */\n\n", "\n/* /portal/static/src/scss/primary_variables.scss */\n\n", "\n/* /account/static/src/scss/variables.scss */\n\n@keyframes animate-red {\n  0% {\n    color: red;\n  }\n  100% {\n    color: inherit;\n  }\n}\n\n.animate {\n  animation: animate-red 1s ease;\n}\n\n", "\n/* /web/static/src/scss/secondary_variables.scss */\n\n", "\n/* /web_editor/static/src/scss/secondary_variables.scss */\n\n", "\n/* /web_editor/static/src/scss/bootstrap_overridden.scss */\n\n", "\n/* /web/static/src/scss/pre_variables.scss */\n\n", "\n/* /web/static/lib/bootstrap/scss/_variables.scss */\n\n", "\n/* /web_editor/static/src/js/editor/odoo-editor/src/style.scss */\n\n.odoo-editor-editable .btn {\n  user-select: auto;\n  cursor: text !important;\n}\n\n.odoo-editor-editable ::selection {\n  /* For color conversion over white background, use X = (Y-(1-P)*255)/P where\n            X = converted color component (R, G, B) (0 <= X <= 255)\n            Y = desired apparent color component (R, G, B) (0 <= Y <= 255)\n            P = opacity (0 <= P <=1)\n            (limitation: Y + 255P >= 255)\n        */\n  background-color: rgba(117, 167, 249, 0.5) !important;\n  /* #bad3fc equivalent when over white*/\n}\n\n.odoo-editor-editable.o_col_resize {\n  cursor: col-resize;\n}\n\n.odoo-editor-editable.o_col_resize ::selection {\n  background-color: transparent;\n}\n\n.odoo-editor-editable.o_row_resize {\n  cursor: row-resize;\n}\n\n.odoo-editor-editable.o_row_resize ::selection {\n  background-color: transparent;\n}\n\n.o_selected_table {\n  caret-color: transparent;\n}\n\n.o_selected_table ::selection {\n  background-color: transparent !important;\n}\n\n.o_selected_table .o_selected_td {\n  box-shadow: 0 0 0 100vmax rgba(117, 167, 249, 0.5) inset;\n  /* #bad3fc equivalent when over white, overlaying on the bg color*/\n  border-collapse: separate;\n}\n\n.o_table_ui_container {\n  position: absolute;\n  visibility: hidden;\n  top: 0;\n  left: 0;\n}\n\n.o_table_ui {\n  background-color: transparent;\n  position: absolute;\n  z-index: 10;\n  padding: 0;\n}\n\n.o_table_ui:hover {\n  visibility: visible !important;\n}\n\n.o_table_ui > div {\n  position: absolute;\n  left: 0;\n  top: 0;\n}\n\n.o_table_ui .o_table_ui_menu_toggler {\n  cursor: pointer;\n  background-color: var(--o-table-ui-bg, #FFFFFF);\n  color: var(--o-table-ui-color, #495057);\n  border: 1px solid #71639e;\n  width: 100%;\n  height: 100%;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  justify-content: center;\n  align-items: center;\n  color: #fff;\n  background-color: rgba(113, 99, 158, 0.7);\n}\n\n.o_table_ui .o_table_ui_menu {\n  display: none;\n  cursor: pointer;\n  background-color: var(--o-table-ui-bg, #FFFFFF);\n  width: fit-content;\n  border: 1px solid var(--o-table-ui-border, #DEE2E6);\n  padding: 5px 0;\n  white-space: nowrap;\n  margin-left: 50%;\n}\n\n.o_table_ui .o_table_ui_menu > div:hover {\n  background-color: var(--o-table-ui-hover, #e9ecef);\n}\n\n.o_table_ui .o_table_ui_menu span {\n  margin-right: 8px;\n  color: var(--o-table-ui-color, #495057);\n}\n\n.o_table_ui .o_table_ui_menu div {\n  padding: 2px 8px;\n}\n\n.o_table_ui.o_open {\n  visibility: visible !important;\n}\n\n.o_table_ui.o_open .o_table_ui_menu {\n  display: block;\n}\n\n.o_table_ui.o_open .o_table_ui_menu > div.o_hide {\n  display: none;\n}\n\n.o_table_ui.o_row_ui {\n  border-right: none !important;\n  min-width: 1rem;\n}\n\n.o_table_ui.o_row_ui .o_table_ui_menu_toggler {\n  min-width: 1rem;\n}\n\n.o_table_ui.o_row_ui .o_table_ui_menu {\n  position: absolute;\n  margin-left: 100%;\n  top: 50%;\n}\n\n.o_table_ui.o_column_ui {\n  border-bottom: none !important;\n}\n\n.odoo-editor-editable a.o_link_in_selection:not(.btn) {\n  background-color: #a6e3e2;\n  color: black !important;\n  border: 1px dashed #008f8c;\n  margin: -1px;\n}\n\n.oe-floating {\n  box-shadow: 0px 3px 18px rgba(0, 0, 0, 0.23);\n  border-radius: 4px;\n  position: absolute;\n}\n\n/* toolbar styling */\n.oe-toolbar {\n  box-sizing: border-box;\n  position: absolute;\n  visibility: hidden;\n  height: fit-content;\n  width: fit-content;\n  padding-left: 5px;\n  padding-right: 5px;\n  background: #222222;\n  color: white;\n}\n\n.oe-toolbar.toolbar-bottom::before {\n  content: '';\n  position: absolute;\n  width: 0;\n  height: 0;\n  left: var(--arrow-left-pos);\n  top: var(--arrow-top-pos);\n  border: transparent 10px solid;\n  border-bottom: #222222 10px solid;\n  z-index: 0;\n}\n\n.oe-toolbar:not(.toolbar-bottom)::before {\n  content: '';\n  position: absolute;\n  width: 0;\n  height: 0;\n  left: var(--arrow-left-pos);\n  top: var(--arrow-top-pos);\n  border: transparent 10px solid;\n  border-top: #222222 10px solid;\n  z-index: 0;\n  pointer-events: none;\n}\n\n.oe-toolbar .button-group {\n  display: inline-block;\n  margin-right: 13px;\n}\n\n.oe-toolbar .button-group:last-of-type {\n  margin-right: 0;\n}\n\n.oe-toolbar .btn {\n  position: relative;\n  box-sizing: content-box;\n  display: inline-block;\n  padding: 7px;\n  color: white;\n}\n\n.oe-toolbar .btn:not(.disabled):hover {\n  background: #868686;\n}\n\n.oe-toolbar .oe-toolbar .dropdown-menu .btn {\n  background: #222222;\n}\n\n.oe-toolbar .btn.active {\n  background: #555555;\n}\n\n.oe-toolbar .dropdown-toggle {\n  background: transparent;\n  border: none;\n  padding: 7px;\n}\n\n.oe-toolbar .dropdown-toggle[aria-expanded=\"true\"] {\n  background: #555555;\n}\n\n.oe-toolbar .dropdown-menu {\n  background: #222222;\n  min-width: max-content;\n  min-width: -webkit-max-content;\n  text-align: center;\n}\n\n.oe-toolbar .dropdown-item {\n  background: transparent;\n  color: white;\n}\n\n.oe-toolbar .dropdown-item pre, .oe-toolbar .dropdown-item h1, .oe-toolbar .dropdown-item h2, .oe-toolbar .dropdown-item h3, .oe-toolbar .dropdown-item h4, .oe-toolbar .dropdown-item h5, .oe-toolbar .dropdown-item h6, .oe-toolbar .dropdown-item blockquote {\n  margin: 0;\n  color: white;\n}\n\n.oe-toolbar .dropdown-item:hover, .oe-toolbar .dropdown-item:focus {\n  color: white;\n  background: #868686;\n}\n\n.oe-toolbar .dropdown-item.active, .oe-toolbar .dropdown-item:active {\n  color: white;\n  background: #555555;\n}\n\n.oe-toolbar li > a.dropdown-item {\n  color: white;\n}\n\n.oe-toolbar label, .oe-toolbar label span {\n  display: inline-block;\n}\n\n.oe-toolbar input[type=\"color\"] {\n  width: 0;\n  height: 0;\n  padding: 0;\n  border: none;\n  box-sizing: border-box;\n  position: absolute;\n  opacity: 0;\n  top: 100%;\n  margin: 2px 0 0;\n}\n\n.oe-toolbar #colorInputButtonGroup label {\n  margin-bottom: 0;\n}\n\n.oe-toolbar .color-indicator {\n  background-color: transparent;\n  padding-bottom: 4px;\n}\n\n.oe-toolbar .color-indicator.fore-color {\n  border-bottom: 2px solid var(--fore-color);\n  padding: 5px;\n}\n\n.oe-toolbar .color-indicator.hilite-color {\n  border-bottom: 2px solid var(--hilite-color);\n  padding: 5px;\n}\n\n.oe-toolbar #style .dropdown-menu {\n  text-align: left;\n}\n\n.oe-tablepicker {\n  margin: -3px 2px -6px 2px;\n}\n\n.oe-tablepicker-wrapper.oe-floating {\n  padding: 3px;\n  z-index: 1056;\n  background-color: var(--oeTablepicker__wrapper-bg, #FFFFFF);\n}\n\n.oe-tablepicker-row {\n  line-height: 0;\n}\n\n.oe-tablepicker {\n  width: max-content;\n  width: -webkit-max-content;\n}\n\n.oe-tablepicker .oe-tablepicker-row .oe-tablepicker-cell {\n  display: inline-block;\n  background-color: var(--oeTablepicker__cell-bg, #e9ecef);\n  width: 19px;\n  height: 19px;\n  padding: 0;\n  margin-inline-end: 3px;\n  margin-bottom: 3px;\n}\n\n.oe-tablepicker .oe-tablepicker-row .oe-tablepicker-cell:last-of-type {\n  margin-inline-end: 0;\n}\n\n.oe-tablepicker .oe-tablepicker-row .oe-tablepicker-cell.active {\n  background-color: var(--oeTablepicker-color-accent, #71639e);\n}\n\n.oe-tablepicker-size {\n  text-align: center;\n  margin-top: 7px;\n}\n\n@media (max-width: 767.98px) {\n  .oe-toolbar {\n    position: relative;\n    overflow-x: auto;\n    visibility: visible;\n    width: auto;\n    height: auto;\n    border-bottom: 1px solid #DEE2E6;\n    border-radius: 0;\n    background-color: white;\n    box-shadow: none;\n  }\n  .oe-toolbar .btn {\n    color: black;\n    padding: 3px 4px !important;\n  }\n  .oe-toolbar .dropdown-menu {\n    position: fixed !important;\n  }\n}\n\n@media (min-width: 768px) {\n  .oe-toolbar.oe-floating {\n    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\n  }\n}\n\n/* Content styling */\n.oe-powerbox-wrapper {\n  z-index: 1055;\n  background: var(--oePowerbox__wrapper-bg, #FFFFFF);\n  color: #495057;\n  max-height: 40vh;\n  box-sizing: border-box;\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\n  min-width: max-content;\n}\n\n.oe-powerbox-wrapper ::-webkit-scrollbar {\n  background: transparent;\n}\n\n.oe-powerbox-wrapper ::-webkit-scrollbar {\n  width: 10px;\n  height: 10px;\n}\n\n.oe-powerbox-wrapper ::-webkit-scrollbar-thumb {\n  background: var(--oePowerbox__ScrollbarThumb-background-color, #D3D1CB);\n}\n\n.oe-powerbox-wrapper ::-webkit-scrollbar-track {\n  background: var(--oePowerbox__ScrollbarTrack-background-color, #EDECE9);\n}\n\n.oe-powerbox-category, .oe-powerbox-noResult {\n  color: var(--oePowerbox__category-color, #6c757d);\n  font-size: 11px;\n}\n\n.oe-powerbox-noResult {\n  display: none;\n}\n\n.oe-powerbox-commandWrapper.active {\n  background: var(--oePowerbox__commandName-bg, #f8f9fa);\n}\n\ni.oe-powerbox-commandImg {\n  height: 35px;\n  width: 35px;\n  background: var(--oePowerbox__commandImg-bg, #f8f9fa);\n  color: var(--oePowerbox__commandImg-color, #343a40);\n}\n\n.oe-powerbox-commandName {\n  font-size: 13px;\n  color: var(--oePowerbox__commandName-color, #495057);\n}\n\n.oe-powerbox-commandDescription {\n  color: var(--oePowerbox__commandDescription-color, rgba(73, 80, 87, 0.76));\n  font-size: 12px;\n}\n\n/* Command hints */\n.oe-hint {\n  position: relative;\n}\n\n.oe-hint:before {\n  content: attr(placeholder);\n  position: absolute;\n  top: 0;\n  left: 0;\n  display: block;\n  color: inherit;\n  opacity: 0.4;\n  pointer-events: none;\n  text-align: inherit;\n  width: 100%;\n}\n\n/* Element widget */\n.oe-sidewidget-move {\n  position: absolute;\n  opacity: 0.6;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  justify-content: center;\n  align-items: center;\n  background-color: white;\n  color: #6e727a;\n  border-radius: 3px;\n  padding: 2px 3px;\n  cursor: move;\n  /* fallback if grab cursor is unsupported */\n  cursor: grab;\n  right: 5px;\n  top: 0px;\n}\n\n.oe-sidewidget-move:hover {\n  opacity: 1;\n}\n\n/* Element widget drag & drop zone */\n.oe-dropzone-box {\n  position: absolute;\n}\n\n.oe-dropzone-box-side {\n  position: absolute;\n}\n\n.oe-dropzone-box-side.oe-dropzone-box-side-north {\n  width: 100%;\n  height: 50%;\n  top: -1px;\n}\n\n.oe-dropzone-box-side.oe-dropzone-box-side-south {\n  width: 100%;\n  height: 50%;\n  bottom: -1px;\n}\n\n.oe-dropzone-box-side.oe-dropzone-box-side-east {\n  height: 100%;\n  width: 3px;\n  right: -1px;\n}\n\n.oe-dropzone-box-side.oe-dropzone-box-side-west {\n  height: 100%;\n  width: 3px;\n  left: -1px;\n}\n\n.debug .oe-dropzone-box {\n  background: rgba(255, 0, 0, 0.3);\n}\n\n.debug .oe-dropzone-box-side {\n  background: #ffa600;\n}\n\n.debug .oe-dropzone-hook {\n  background: rgba(255, 0, 132, 0.2);\n}\n\n.oe-dropzone-hook {\n  position: absolute;\n}\n\n[data-oe-absolute-container-id=oe-dropzones-container] {\n  opacity: 0.3;\n}\n\n[data-oe-absolute-container-id=oe-widget-hooks-container] {\n  opacity: 0.3;\n}\n\n[data-oe-absolute-container-id=oe-dropzone-hint-container] {\n  pointer-events: none;\n}\n\n.oe-current-drop-hint {\n  position: absolute;\n  background: rgba(0, 136, 255, 0.508);\n}\n\n.oe-editor-dragging {\n  pointer-events: none;\n}\n\n/* Collaboration cursor */\n.oe-absolute-container {\n  position: absolute;\n  isolation: isolate;\n  height: 0;\n  width: 0;\n  z-index: 1;\n}\n\n.oe-collaboration-caret-top-square {\n  min-height: 5px;\n  min-width: 5px;\n  color: #fff;\n  text-shadow: 0 0 5px #000;\n  position: absolute;\n  bottom: 100%;\n  left: -4px;\n  white-space: nowrap;\n}\n\n.oe-collaboration-caret-top-square:hover {\n  border-radius: 2px;\n  padding: 0.3em 0.6em;\n}\n\n.oe-collaboration-caret-top-square:hover::before {\n  content: attr(data-client-name);\n}\n\n.oe-collaboration-caret-avatar {\n  position: absolute;\n  height: 1.5rem;\n  width: 1.5rem;\n  border-radius: 50%;\n  transition: top 0.5s, left 0.5s, opacity 0.2s;\n}\n\n.oe-collaboration-caret-avatar > img {\n  position: absolute;\n  opacity: 1;\n  height: 100%;\n  width: 100%;\n  border-radius: 50%;\n}\n\n.oe-avatars-counters-container {\n  pointer-events: none;\n}\n\n.oe-overlapping-counter {\n  position: absolute;\n  background-color: green;\n  color: white;\n  border-radius: 50%;\n  font-size: 9px;\n  padding: 0 4px;\n}\n\ncode.o_inline_code {\n  background-color: #c5c5c5;\n  padding: 2px;\n  margin: 2px;\n  color: black;\n  font-size: inherit;\n}\n\n", "\n/* /web_editor/static/src/scss/wysiwyg.scss */\n\n:root {\n  --o-we-toolbar-height: 40px;\n}\n\n.o_we_command_protector {\n  font-weight: 400 !important;\n}\n\n.o_we_command_protector b, .o_we_command_protector strong {\n  font-weight: 700 !important;\n}\n\n.o_we_command_protector * {\n  font-weight: inherit !important;\n}\n\n.o_we_command_protector .btn {\n  text-align: unset !important;\n}\n\n.wysiwyg_iframe,\n.note-editor {\n  border: 1px solid #D9D9D9;\n  margin: 0;\n  padding: 0;\n}\n\n.colorpicker {\n  --bg: #FFF;\n  --text-rgb: 43, 43, 51;\n  --border-rgb: var(--text-rgb);\n  --tab-border-top: transparent;\n  --tab-border-bottom: #D9D9D9;\n  --btn-color-active: inset 0 0 0 2px #01bad2,\r\n                        inset 0 0 0 3px var(--bg),\r\n                        inset 0 0 0 4px rgba(var(--border-rgb), .5);\n}\n\n.colorpicker, .colorpicker input {\n  color: rgba(var(--text-rgb), 1);\n}\n\n.colorpicker label {\n  color: rgba(var(--text-rgb), 0.5);\n}\n\n.colorpicker button {\n  outline: none;\n}\n\n.colorpicker .o_we_colorpicker_switch_panel {\n  font-size: 13px;\n  border-bottom: 1px solid var(--tab-border-bottom);\n  box-shadow: inset 0 1px 0 var(--tab-border-top);\n}\n\n.colorpicker .o_we_colorpicker_switch_pane_btn, .colorpicker .o_colorpicker_reset {\n  -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;\n}\n\n.colorpicker .o_colorpicker_reset {\n  margin-left: auto !important;\n}\n\n.colorpicker .o_colorpicker_sections {\n  background: var(--bg);\n}\n\n.colorpicker .o_colorpicker_sections > * {\n  padding-top: 8px;\n}\n\n.colorpicker .o_colorpicker_sections > *:first-child {\n  padding-top: 0;\n}\n\n.colorpicker .o_colorpicker_sections .o_colorpicker_widget .o_hex_div:focus-within, .colorpicker .o_colorpicker_sections .o_colorpicker_widget .o_rgba_div:focus-within {\n  border-color: #01bad2;\n}\n\n.colorpicker .o_colorpicker_sections .o_colorpicker_widget .o_color_picker_inputs input, .colorpicker .o_colorpicker_sections .o_colorpicker_widget .o_color_picker_inputs input:focus {\n  border: none;\n  outline: none;\n}\n\n.colorpicker .o_colorpicker_sections .o_we_color_btn, .colorpicker .o_colorpicker_sections .o_color_pick_area, .colorpicker .o_colorpicker_sections .o_color_slider, .colorpicker .o_colorpicker_sections .o_opacity_slider:before, .colorpicker .o_colorpicker_sections .o_hex_div, .colorpicker .o_colorpicker_sections .o_rgba_div {\n  box-shadow: inset 0 0 0 1px rgba(var(--border-rgb), 0.5);\n}\n\n.colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_btn {\n  float: none;\n  box-sizing: border-box;\n}\n\n.colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_color_picker_inputs we-button {\n  border: 1px solid black;\n  padding: 0 6px;\n  color: white;\n}\n\n.colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_color_picker_inputs we-button.active {\n  background-color: #2b2b33;\n}\n\n.colorpicker .o_colorpicker_sections .o_we_color_btn {\n  position: relative;\n  float: left;\n  width: 12.5%;\n  padding-top: 10%;\n  margin: 0;\n  border: 1px solid var(--bg);\n}\n\n.colorpicker .o_colorpicker_sections .o_we_color_btn.o_colorpicker_reset {\n  background-color: transparent;\n}\n\n.colorpicker .o_colorpicker_sections .o_we_color_btn.o_colorpicker_reset::before {\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  font-family: FontAwesome !important;\n  content: \"\\f00d\" !important;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  justify-content: center;\n  align-items: center;\n  color: #e6586c;\n}\n\n.colorpicker .o_colorpicker_sections .o_we_color_btn.selected {\n  box-shadow: var(--btn-color-active);\n}\n\n.colorpicker .o_colorpicker_sections .o_we_color_btn.o_btn_transparent::before {\n  background-color: transparent;\n}\n\n.colorpicker .o_colorpicker_sections .o_colorpicker_section.o_custom_gradient_editor .o_custom_gradient_btn {\n  border: 1px solid var(--o-we-toolbar-border);\n}\n\n.colorpicker .o_colorpicker_sections .o_colorpicker_section::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"transparent_grayscale\"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"transparent_grayscale\"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"theme\"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"theme\"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"reset\"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"reset\"] .o_we_color_btn::after, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"custom\"] .o_we_color_btn::before, .colorpicker .o_colorpicker_sections .o_colorpicker_section[data-name=\"custom\"] .o_we_color_btn::after {\n  box-shadow: inherit;\n}\n\n.oe-toolbar {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n}\n\n.oe-toolbar .btn {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.oe-toolbar .colorpicker-menu {\n  height: auto !important;\n  box-sizing: content-box;\n  min-height: fit-content;\n}\n\n.oe-toolbar .dropdown-item.active:not(.dropdown-item_active_noarrow):before, .oe-toolbar .dropdown-item.selected:not(.dropdown-item_active_noarrow):before {\n  transform: translate(-1.5em, 0);\n  height: 100%;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n}\n\n.oe-toolbar.oe-floating {\n  gap: 0.35em;\n  align-items: stretch;\n  min-height: 40px;\n  padding: 0 0.5em;\n  background-color: var(--o-we-toolbar-bg, #FFF);\n  color: var(--o-we-toolbar-color-text, #2b2b33);\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Ubuntu, \"Noto Sans\", Arial, \"Odoo Unicode Support Noto\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n}\n\n.oe-toolbar.oe-floating.toolbar-bottom:before {\n  border-bottom-color: var(--o-we-toolbar-bg, #FFF);\n}\n\n.oe-toolbar.oe-floating:not(.toolbar-bottom):before {\n  border-top-color: var(--o-we-toolbar-bg, #FFF);\n}\n\n.oe-toolbar.oe-floating.noarrow::before {\n  display: none;\n}\n\n.oe-toolbar.oe-floating > .btn-group:not(.d-none) ~ .btn-group:not(.d-none):before, .oe-toolbar.oe-floating .oe-toolbar-separator:before {\n  content: \"\";\n  width: 1px;\n  margin-right: calc(0.35em - 1px);\n  background: var(--o-we-toolbar-border, #D9D9D9);\n  transform: scaleY(0.6);\n}\n\n.oe-toolbar.oe-floating .btn, .oe-toolbar.oe-floating .dropdown-item {\n  padding: 3.5px 7px;\n  color: var(--o-we-toolbar-color-clickable, #595964);\n}\n\n.oe-toolbar.oe-floating .btn:hover:not(.active), .oe-toolbar.oe-floating .dropdown-item:hover:not(.active) {\n  color: var(--o-we-toolbar-color-clickable-active, #000000);\n  background-color: transparent;\n}\n\n.oe-toolbar.oe-floating .btn.active, .oe-toolbar.oe-floating .dropdown-item.active {\n  background: var(--o-we-toolbar-bg-active, rgba(217, 217, 217, 0.2));\n  box-shadow: inset 0 0 3px RGBA(var(--o-we-toolbar-bg-active, rgba(217, 217, 217, 0.2)), 0.5);\n}\n\n.oe-toolbar.oe-floating .btn {\n  border: none;\n  border-radius: 0;\n  background: transparent;\n  font-weight: 400;\n}\n\n.oe-toolbar.oe-floating .btn.active {\n  color: var(--o-we-toolbar-color-accent, #018597);\n}\n\n.oe-toolbar.oe-floating > .btn-group > .btn, .oe-toolbar.oe-floating > .btn-group > .colorpicker-group {\n  margin: 4px auto;\n  padding-top: 0;\n  padding-bottom: 0;\n}\n\n.oe-toolbar.oe-floating .show > .btn, .oe-toolbar.oe-floating .show > .btn:hover, .oe-toolbar.oe-floating .show > .btn:focus {\n  color: var(--o-we-toolbar-color-clickable-active, #000000);\n}\n\n.oe-toolbar.oe-floating .dropdown-toggle::after {\n  content: \"\";\n  display: inline-block;\n  width: 0;\n  height: 0;\n  vertical-align: middle;\n  -moz-transform: scale(0.9999);\n  border-bottom: 0;\n  border-left: 0.3em solid transparent;\n  border-right: 0.3em solid transparent;\n  border-top: 0.3em solid var(--o-caret-color, currentColor);\n  margin-left: .3em;\n}\n\n.oe-toolbar.oe-floating .dropdown-menu {\n  margin: 0;\n  border: 0;\n  padding: 0;\n  max-height: none;\n  overflow: visible;\n  border-top: 1px solid var(--o-we-toolbar-border, #D9D9D9);\n  background-color: var(--o-we-toolbar-bg, #FFF);\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\n  border-top-left-radius: 0;\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n}\n\n.oe-toolbar.oe-floating .dropdown-menu.show {\n  min-width: 0;\n}\n\n.oe-toolbar.oe-floating .dropdown-menu:not(.colorpicker-menu) > li:last-child {\n  margin-bottom: 1em;\n}\n\n.oe-toolbar.oe-floating .dropdown-menu.colorpicker-menu {\n  margin-top: 0;\n  min-width: 222px !important;\n}\n\n.oe-toolbar.oe-floating .dropdown-item {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n  -webkit-box-pack: start; justify-content: flex-start;\n  padding: 0 27.2px;\n  min-height: 34px;\n}\n\n.oe-toolbar.oe-floating .dropdown-item > * {\n  color: inherit;\n}\n\n.oe-toolbar.oe-floating .dropdown-item.active > *, .oe-toolbar.oe-floating .dropdown-item.active:hover, .oe-toolbar.oe-floating .dropdown-item.active:focus {\n  color: var(--o-we-toolbar-color-clickable-active, #000000);\n}\n\n.oe-toolbar.oe-floating .dropdown-item.active > *:before, .oe-toolbar.oe-floating .dropdown-item.active:hover:before, .oe-toolbar.oe-floating .dropdown-item.active:focus:before {\n  top: 0;\n  transform: translate(-17px, 0);\n  line-height: 34px;\n}\n\n.oe-toolbar.oe-floating #decoration #removeFormat {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n}\n\n.oe-toolbar.oe-floating #colorInputButtonGroup label:last-of-type .btn {\n  margin: 0 1px 0 -1px;\n}\n\n.oe-toolbar.oe-floating #colorInputButtonGroup .note-back-color-preview.dropup .dropdown-menu {\n  left: -52px;\n}\n\n.oe-toolbar.oe-floating .colorpicker-group .dropdown-toggle::after {\n  display: none;\n}\n\n.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button {\n  margin-bottom: -1px;\n  border: 0;\n  padding: 0.25rem 0.5rem;\n  background: transparent;\n  color: var(--o-we-toolbar-color-clickable, #595964);\n}\n\n.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button:hover {\n  color: var(--o-we-toolbar-color-clickable-active, #000000);\n}\n\n.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.active {\n  box-shadow: inset 0 -1px 0 var(--o-we-toolbar-color-accent, #018597);\n  color: var(--o-we-toolbar-color-clickable-active, #000000);\n}\n\n.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.o_colorpicker_reset {\n  background: #71639e;\n}\n\n.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.o_colorpicker_reset, .oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.o_colorpicker_reset:hover {\n  color: #FFFFFF;\n}\n\n.oe-toolbar.oe-floating .colorpicker-group .o_we_colorpicker_switch_panel button.o_colorpicker_reset:hover {\n  background: #66598f;\n}\n\n.oe-toolbar.oe-floating .colorpicker {\n  background: var(--o-we-toolbar-bg, #FFF);\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\n}\n\n.oe-toolbar.oe-floating .o_image_alt {\n  display: inline-block;\n  max-width: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  vertical-align: top;\n  max-width: 150px;\n}\n\n.oe-toolbar.oe-floating input#fontSizeCurrentValue {\n  width: 20px;\n  border: none;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar {\n  display: grid;\n  align-items: stretch;\n}\n\n.oe-tablepicker-wrapper .oe-tablepicker .oe-tablepicker-cell {\n  border-radius: 0;\n}\n\n.oe-tablepicker-wrapper .oe-tablepicker .oe-tablepicker-cell.active {\n  background: var(--o-we-toolbar-color-accent, #018597);\n}\n\nbody:not(.editor_has_snippets) > .oe-toolbar {\n  z-index: 1056;\n}\n\n@media (max-width: 767.98px) {\n  .oe-toolbar {\n    background-color: white;\n  }\n  .oe-toolbar .btn {\n    color: black;\n  }\n  .oe-toolbar::before {\n    display: none;\n  }\n  .oe-toolbar::after {\n    display: none;\n  }\n}\n\n.oe_edited_link {\n  position: relative;\n}\n\n.oe_edited_link:not(.nav-link) {\n  display: inline-block;\n}\n\n.oe_edited_link::before {\n  content: '';\n  border: dashed 3px #01bad2;\n  position: absolute;\n  inset: -5px;\n  pointer-events: none;\n}\n\n.oe_edited_link:empty::after {\n  content: \"\\00a0\\00a0\";\n}\n\n@keyframes fadeInDownSmall {\n  0% {\n    opacity: 0;\n    transform: translate(0, -5px);\n  }\n  100% {\n    opacity: 1;\n    transform: translate(0, 0);\n  }\n}\n\n@keyframes inputHighlighter {\n  from {\n    background: #71639e;\n  }\n  to {\n    width: 0;\n    background: transparent;\n  }\n}\n\n.o_we_horizontal_collapse {\n  width: 0 !important;\n  padding: 0 !important;\n  border: none !important;\n}\n\n.o_we_transition_ease {\n  transition: all ease 0.35s;\n}\n\nbody .modal .o_link_dialog input.link-style:checked + span::after {\n  content: \"\\f00c\";\n  display: inline-block;\n  font-family: FontAwesome;\n  margin-left: 2px;\n}\n\nbody .modal .o_link_dialog .o_link_dialog_preview {\n  border-left: var(--o-link-dialog-preview-border, 1px solid #DEE2E6);\n}\n\n.o_we_progressbar:last-child hr {\n  display: none;\n}\n\n.fa.o_we_selected_image::before, img.o_we_selected_image {\n  outline: 3px solid rgba(150, 150, 220, 0.3);\n}\n\n.o_we_media_author {\n  font-size: 11px;\n  position: absolute;\n  top: auto;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  text-align: center;\n  background-color: rgba(255, 255, 255, 0.7);\n}\n\n@media (max-width: 991.98px) {\n  #web_editor-top-edit {\n    position: initial !important;\n    height: initial !important;\n    top: initial !important;\n    left: initial !important;\n  }\n  .oe-toolbar.oe-floating {\n    display: -webkit-box; display: -webkit-flex; display: flex;\n    -webkit-flex-wrap: wrap; flex-wrap: wrap;\n    margin-bottom: 1rem;\n    overflow-y: visible;\n  }\n  .oe-toolbar.oe-floating .dropdown-menu {\n    max-height: 200px;\n    overflow: auto;\n  }\n  .oe-toolbar.oe-floating .dropdown-menu.colorpicker-menu {\n    bottom: auto;\n  }\n}\n\n.note-editable .modal:not(.o_technical_modal) {\n  top: 40px;\n  right: 0;\n  bottom: 0;\n  right: 288px;\n  width: auto;\n  height: auto;\n}\n\n.note-editable .modal:not(.o_technical_modal) .modal-dialog {\n  padding: 0.5rem 0;\n}\n\n.o_wysiwyg_wrapper {\n  position: relative;\n  margin-bottom: 11px;\n}\n\n.o_wysiwyg_resizer {\n  background: #f5f5f5;\n  height: 10px;\n  width: 100%;\n  border-left: 1px solid #D9D9D9;\n  border-bottom: 1px solid #D9D9D9;\n  border-right: 1px solid #D9D9D9;\n  cursor: row-resize;\n  padding-top: 1px;\n}\n\n.o_wysiwyg_resizer_hook {\n  width: 20px;\n  margin: 1px auto;\n  border-top: 1px solid #a9a9a9;\n}\n\n.note-editable {\n  border: 1px solid #D9D9D9;\n  overflow: auto;\n  height: 100%;\n  padding: 4px 40px 4px 4px;\n  min-height: 10px;\n  border-radius: 3px;\n}\n\n.oe-bordered-editor .note-editable {\n  border-width: 1px;\n  padding: 4px 40px 4px 4px;\n  min-height: 180px;\n}\n\n.o_we_no_pointer_events {\n  pointer-events: none;\n}\n\n.o_we_crop_widget {\n  background-color: rgba(128, 128, 128, 0.5);\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  /* This value must be higher than dialog z-index in bootstrap */\n  z-index: 1056;\n  overflow: auto;\n}\n\n.o_we_crop_widget .o_we_cropper_wrapper {\n  position: absolute;\n}\n\n.o_we_crop_widget .o_we_crop_buttons {\n  margin-top: 0.5rem;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  -webkit-flex-wrap: wrap; flex-wrap: wrap;\n  bottom: 1rem;\n}\n\n.o_we_crop_widget .o_we_crop_buttons input[type=radio] {\n  display: none;\n}\n\n.o_we_crop_widget .o_we_crop_buttons .btn-group {\n  border-radius: 0.25rem;\n  margin: 0.1rem;\n}\n\n.o_we_crop_widget .o_we_crop_buttons button, .o_we_crop_widget .o_we_crop_buttons label {\n  cursor: pointer !important;\n  padding: 0.2rem 0.3rem;\n}\n\n.o_we_crop_widget .o_we_crop_buttons label {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n}\n\n.o_we_crop_widget .o_we_crop_buttons label.active {\n  background-color: #000000;\n}\n\n.o_we_crop_widget .o_we_crop_buttons button:not(.btn), .o_we_crop_widget .o_we_crop_buttons label {\n  margin: 0;\n  border: none;\n  border-right: 1px solid #2b2b33;\n  background-color: #2b2b33;\n  color: #D9D9D9;\n}\n\n.o_we_crop_widget .o_we_crop_buttons button:not(.btn):first-child, .o_we_crop_widget .o_we_crop_buttons label:first-child {\n  border-top-left-radius: 0.25rem;\n  border-bottom-left-radius: 0.25rem;\n}\n\n.o_we_crop_widget .o_we_crop_buttons button:not(.btn):last-child, .o_we_crop_widget .o_we_crop_buttons label:last-child {\n  border-top-right-radius: 0.25rem;\n  border-bottom-right-radius: 0.25rem;\n  border-right: none;\n}\n\n[data-oe-xpath], [data-oe-xpath] [contenteditable=true] {\n  outline: none;\n}\n\n.o_transform_removal {\n  transform: none !important;\n}\n\n.o_edit_menu_popover {\n  max-width: 331.2px;\n  width: 331.2px;\n  user-select: none;\n  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);\n  border-color: rgba(0, 0, 0, 0.025);\n  font-size: 12px;\n  font-weight: 400 !important;\n}\n\n.o_edit_menu_popover .popover-arrow::before {\n  border-right-color: rgba(0, 0, 0, 0.05);\n}\n\n.o_edit_menu_popover .fw-bold {\n  font-weight: 500 !important;\n}\n\n.o_edit_menu_popover .o_we_preview_favicon > img {\n  max-height: 16px;\n  max-width: 16px;\n}\n\n.o_edit_menu_popover .o_we_url_link {\n  width: 100px;\n}\n\n.o_edit_menu_popover .o_we_full_url {\n  word-break: break-all;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n}\n\n.o_edit_menu_popover .o_we_full_url.o_we_webkit_box {\n  display: -webkit-box;\n}\n\n.o_edit_menu_popover .o_we_full_url:hover {\n  -webkit-line-clamp: unset;\n}\n\ntextarea.o_codeview {\n  min-height: 400px;\n}\n\n@keyframes fade {\n  0%, 100% {\n    opacity: 0;\n  }\n  30%, 70% {\n    opacity: 1;\n  }\n}\n\n.o-chatgpt-content {\n  position: absolute;\n  background: rgba(1, 186, 210, 0.5);\n  opacity: 0;\n  animation: fade 1.5s ease-in-out;\n  z-index: 1;\n  outline: 2px dashed #01bad2;\n  outline-offset: -2px;\n}\n\n.o-prompt-input {\n  position: relative;\n}\n\n.o-prompt-input > textarea {\n  padding-top: 10px;\n  padding-bottom: 10px;\n  min-height: 40px;\n  max-height: 110px;\n  resize: none;\n}\n\nbutton.o-message-insert {\n  line-height: 1;\n}\n\n.o-chatgpt-message > div > *:last-child, .o-chatgpt-alternative > *:last-child {\n  margin-bottom: 0;\n}\n\n.o-message-error {\n  color: #d44c59;\n  font-weight: bold;\n  --bg-opacity: 0.25;\n}\n\n", "\n/* /web_editor/static/src/scss/wysiwyg_iframe.scss */\n\niframe.wysiwyg_iframe.o_fullscreen {\n  left: 0 !important;\n  right: 0 !important;\n  top: 0 !important;\n  bottom: 0 !important;\n  width: 100% !important;\n  min-height: 100% !important;\n  z-index: 1001 !important;\n  border: 0;\n}\n\n.o_wysiwyg_no_transform {\n  transform: none !important;\n}\n\nbody.o_in_iframe {\n  background-color: white;\n}\n\nbody.o_in_iframe .o_editable {\n  position: relative;\n}\n\nbody.o_in_iframe .note-editable {\n  border: none;\n  padding: 0;\n  border-radius: 0;\n}\n\nbody.o_in_iframe #oe_snippets {\n  top: 0;\n}\n\nbody.o_in_iframe .iframe-editor-wrapper {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n  overflow: auto;\n}\n\nbody.o_in_iframe.oe_dropzone_active .note-editable {\n  overflow: hidden;\n}\n\nbody.o_in_iframe .iframe-utils-zone {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n}\n\nbody.o_in_iframe .note-statusbar {\n  display: none;\n}\n\nbody.o_in_iframe #oe_snippets .email_designer_top_actions {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  margin: auto 9px auto auto;\n}\n\nbody.o_in_iframe #oe_snippets .email_designer_top_actions .btn {\n  align-items: center;\n  width: 24px;\n  height: 24px;\n  background-color: #337ab7;\n  border: 1px solid #2e6da4;\n  border-radius: 4px;\n  padding: 0;\n  margin-left: 5px;\n}\n\nbody.o_in_iframe #oe_snippets .email_designer_top_actions .o_fullscreen_btn img {\n  margin: auto;\n}\n\nbody.o_in_iframe textarea.o_codeview {\n  position: absolute;\n  font-family: 'Courier New', Courier, monospace;\n  outline: none;\n  resize: none;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 288px;\n  width: calc(100% - 288px);\n  height: 100%;\n  border: none;\n}\n\nbody.o_in_iframe .o_height_400, body.o_in_iframe .o_height_400 div.container, body.o_in_iframe .o_height_400 div.row {\n  min-height: 400px;\n}\n\nbody.o_in_iframe .o_height_800, body.o_in_iframe .o_height_800 div.container, body.o_in_iframe .o_height_800 div.row {\n  min-height: 800px;\n}\n\nbody.o_in_iframe .btn {\n  user-select: auto;\n}\n\n", "\n/* /web_editor/static/src/scss/wysiwyg_snippets.scss */\n\n@media (max-width: 991.98px) {\n  body.editor_enable.editor_has_snippets #web_editor-top-edit {\n    position: initial !important;\n    height: initial !important;\n    top: initial !important;\n    left: initial !important;\n  }\n  body.editor_enable.editor_has_snippets #web_editor-top-edit .note-popover .popover {\n    right: 0 !important;\n  }\n}\n\n.oe_snippet {\n  position: relative;\n  z-index: 1041;\n  width: 77px;\n  background-color: #3e3e46;\n}\n\n.oe_snippet.o_draggable_dragging {\n  transform: rotate(-3deg) scale(1.2);\n  box-shadow: 0 5px 25px -10px black;\n  transition: transform 0.3s, box-shadow 0.3s;\n}\n\n.oe_snippet > .oe_snippet_body {\n  display: none !important;\n}\n\n.oe_snippet .oe_snippet_thumbnail {\n  width: 100%;\n}\n\n.oe_snippet .oe_snippet_thumbnail .oe_snippet_thumbnail_img {\n  width: 100%;\n  padding-top: 75%;\n  background-repeat: no-repeat;\n  background-size: contain;\n  background-position: top center;\n  overflow: hidden;\n}\n\n.oe_snippet .oe_snippet_thumbnail_title {\n  display: none;\n}\n\n.oe_snippet:not(:hover):not(.o_disabled):not(.o_snippet_install) {\n  background-color: rgba(62, 62, 70, 0.9);\n}\n\n.oe_snippet:not(:hover):not(.o_disabled):not(.o_snippet_install) .oe_snippet_thumbnail {\n  filter: saturate(0.7);\n  opacity: .9;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button, #oe_snippets .colorpicker .o_colorpicker_reset, #oe_snippets > .o_we_customize_panel #oe-table-options .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn, #oe_snippets > .o_we_customize_panel we-button, #oe_snippets > .o_we_customize_panel we-toggler, #oe_snippets > .o_we_customize_panel .o_we_fold_icon, #oe_snippets > .o_we_customize_panel we-button.o_we_link, #oe_snippets > .o_we_customize_panel #removeFormat, #oe_snippets > .o_we_customize_panel #oe-table-delete-table, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn, #oe_snippets #snippets_menu > button {\n  outline: none;\n  text-decoration: none;\n  line-height: 20px;\n  cursor: pointer;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager [disabled].o_pager_nav_angle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button[disabled], #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > [disabled], #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > [disabled]:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > [disabled], #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button[disabled], #oe_snippets .colorpicker [disabled].o_colorpicker_reset, #oe_snippets > .o_we_customize_panel #oe-table-options [disabled].btn, #oe_snippets > .o_we_customize_panel .oe-toolbar [disabled].btn, #oe_snippets > .o_we_customize_panel we-button[disabled], #oe_snippets > .o_we_customize_panel we-toggler[disabled], #oe_snippets > .o_we_customize_panel [disabled].o_we_fold_icon, #oe_snippets > .o_we_customize_panel [disabled]#removeFormat, #oe_snippets > .o_we_customize_panel [disabled]#oe-table-delete-table, #oe_snippets > #o_scroll #snippet_custom .oe_snippet [disabled].btn, #oe_snippets .colorpicker [disabled].o_we_colorpicker_switch_pane_btn, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager [disabled].o_pager_nav_btn, #oe_snippets #snippets_menu > button[disabled] {\n  opacity: .5;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper):not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel we-button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets #snippets_menu > button:not([disabled]).active:not(.o_we_no_toggle):not(.o_we_checkbox_wrapper), #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]):hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]):hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]):hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]):hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]):hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]):hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]):hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]):hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]):hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]):hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]):hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]):hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]):hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]):hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]):hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]):hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]):hover, #oe_snippets #snippets_menu > button:not([disabled]):hover {\n  color: #FFFFFF;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_text_success, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_text_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_text_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_text_success:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_text_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_text_success, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_text_success, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_text_success, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_success, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_text_success, #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_success {\n  color: #40ad67;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_hover_success:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_hover_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_hover_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_hover_success:hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_hover_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_hover_success:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_hover_success:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_hover_success:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_success:hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_hover_success:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_success:hover {\n  color: #40ad67;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_bg_success, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_success:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_bg_success, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_success, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_success, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_success, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_success, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_bg_success, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_success {\n  color: white;\n  background-color: #40ad67;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_bg_success:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_success:hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_bg_success:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_success:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_success:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_success:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_success:hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_bg_success:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_success:hover {\n  background-color: #369156;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_text_info, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_text_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_text_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_text_info:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_text_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_text_info, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_text_info, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_text_info, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_info, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_text_info, #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_info {\n  color: #6999a8;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_hover_info:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_hover_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_hover_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_hover_info:hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_hover_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_hover_info:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_hover_info:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_hover_info:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_info:hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_hover_info:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_info:hover {\n  color: #6999a8;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_bg_info, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_info:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_bg_info, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_info, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_info, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_info, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_info, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_bg_info, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_info {\n  color: white;\n  background-color: #6999a8;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_bg_info:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_info:hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_bg_info:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_info:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_info:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_info:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_info:hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_bg_info:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_info:hover {\n  background-color: #568695;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_text_warning, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_text_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_text_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_text_warning:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_text_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_text_warning, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_text_warning, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_text_warning, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_warning, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_text_warning, #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_warning {\n  color: #f0ad4e;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_hover_warning:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_hover_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_hover_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_hover_warning:hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_hover_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_hover_warning:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_hover_warning:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_hover_warning:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_warning:hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_hover_warning:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_warning:hover {\n  color: #f0ad4e;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_bg_warning, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_warning:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_bg_warning, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_warning, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_warning, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_warning, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_warning, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_bg_warning, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_warning {\n  color: white;\n  background-color: #f0ad4e;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_bg_warning:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_warning:hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_bg_warning:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_warning:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_warning:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_warning:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_warning:hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_bg_warning:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_warning:hover {\n  background-color: #ed9d2b;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_text_danger, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_text_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_text_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_text_danger:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_text_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_text_danger, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_text_danger, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_text_danger, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_danger, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_text_danger, #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_danger {\n  color: #e6586c;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_hover_danger:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_hover_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_hover_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_hover_danger:hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_hover_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_hover_danger:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_hover_danger:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_hover_danger:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_danger:hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_hover_danger:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_danger:hover {\n  color: #e6586c;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_bg_danger, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_danger:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_bg_danger, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_danger, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_danger, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_danger, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_danger, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_bg_danger, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_danger {\n  color: white;\n  background-color: #e6586c;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_bg_danger:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_danger:hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_bg_danger:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_danger:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_danger:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_danger:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_danger:hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_bg_danger:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_danger:hover {\n  background-color: #e1374f;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_text_brand_primary, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_text_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_text_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_text_brand_primary:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_text_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_text_brand_primary, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_text_brand_primary, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_text_brand_primary, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_text_brand_primary, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_text_brand_primary, #oe_snippets #snippets_menu > button:not([disabled]).o_we_text_brand_primary {\n  color: #71639e;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_hover_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_hover_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_hover_brand_primary:hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_hover_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_hover_brand_primary:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_hover_brand_primary:hover {\n  color: #71639e;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_bg_brand_primary, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_brand_primary:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_bg_brand_primary, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_brand_primary, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_brand_primary, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_brand_primary, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_brand_primary, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_bg_brand_primary, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_brand_primary {\n  color: white;\n  background-color: #71639e;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:not([disabled]).o_we_bg_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]).o_we_bg_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]).o_we_bg_brand_primary:hover:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]).o_we_bg_brand_primary:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-button:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).o_we_bg_brand_primary:hover, #oe_snippets #snippets_menu > button:not([disabled]).o_we_bg_brand_primary:hover {\n  background-color: #605487;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_fold_icon, #oe_snippets > .o_we_customize_panel we-button.o_we_link, #oe_snippets > .o_we_customize_panel #removeFormat, #oe_snippets > .o_we_customize_panel #oe-table-delete-table, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn, #oe_snippets #snippets_menu > button {\n  color: #9d9d9d;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_fold_icon svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button.o_we_link svg .o_graphic, #oe_snippets > .o_we_customize_panel #removeFormat svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-delete-table svg .o_graphic, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn svg .o_graphic, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn svg .o_graphic, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn svg .o_graphic, #oe_snippets #snippets_menu > button svg .o_graphic {\n  fill: #9d9d9d;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_fold_icon svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button.o_we_link svg .o_subdle, #oe_snippets > .o_we_customize_panel #removeFormat svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-delete-table svg .o_subdle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn svg .o_subdle, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn svg .o_subdle, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn svg .o_subdle, #oe_snippets #snippets_menu > button svg .o_subdle {\n  fill: rgba(157, 157, 157, 0.5);\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).active svg .o_graphic, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).active svg .o_graphic, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).active svg .o_graphic, #oe_snippets #snippets_menu > button:not([disabled]).active svg .o_graphic, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]):hover svg .o_graphic, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]):hover svg .o_graphic, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]):hover svg .o_graphic, #oe_snippets #snippets_menu > button:not([disabled]):hover svg .o_graphic {\n  fill: #FFFFFF;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]).active svg .o_subdle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]).active svg .o_subdle, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]).active svg .o_subdle, #oe_snippets #snippets_menu > button:not([disabled]).active svg .o_subdle, #oe_snippets > .o_we_customize_panel .o_we_fold_icon:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button.o_we_link:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel #removeFormat:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-delete-table:not([disabled]):hover svg .o_subdle, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn:not([disabled]):hover svg .o_subdle, #oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:not([disabled]):hover svg .o_subdle, #oe_snippets #snippets_menu > button:not([disabled]):hover svg .o_subdle {\n  fill: rgba(157, 157, 157, 0.75);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button, #oe_snippets .colorpicker .o_colorpicker_reset, #oe_snippets > .o_we_customize_panel #oe-table-options .btn, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn, #oe_snippets > .o_we_customize_panel we-button, #oe_snippets > .o_we_customize_panel we-toggler {\n  display: block;\n  max-width: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  vertical-align: top;\n  padding: 0 6px;\n  border: 1px solid transparent;\n  border-radius: 4px;\n  background-color: #595964;\n  color: #D9D9D9;\n  text-align: center;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > * svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *:not(span) svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button svg .o_graphic, #oe_snippets .colorpicker .o_colorpicker_reset svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-options .btn svg .o_graphic, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button svg .o_graphic, #oe_snippets > .o_we_customize_panel we-toggler svg .o_graphic {\n  fill: #D9D9D9;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > * svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *:not(span) svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button svg .o_subdle, #oe_snippets .colorpicker .o_colorpicker_reset svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-options .btn svg .o_subdle, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button svg .o_subdle, #oe_snippets > .o_we_customize_panel we-toggler svg .o_subdle {\n  fill: rgba(217, 217, 217, 0.5);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]):hover svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]):hover:not(span) svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]):hover svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]):hover svg .o_graphic, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button:not([disabled]):hover svg .o_graphic, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]):hover svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > .active:not(.o_we_no_toggle) svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > .active:not(.o_we_no_toggle):not(span) svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > .active:not(.o_we_no_toggle) svg .o_graphic, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button.active:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets .colorpicker .active.o_colorpicker_reset:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel #oe-table-options .active.btn:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel .oe-toolbar .active.btn:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel we-button.active:not(.o_we_no_toggle) svg .o_graphic, #oe_snippets > .o_we_customize_panel we-toggler.active:not(.o_we_no_toggle) svg .o_graphic {\n  fill: #FFFFFF;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > :not([disabled]):hover svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > :not([disabled]):hover:not(span) svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > :not([disabled]):hover svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button:not([disabled]):hover svg .o_subdle, #oe_snippets .colorpicker .o_colorpicker_reset:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-options .btn:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel .oe-toolbar .btn:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button:not([disabled]):hover svg .o_subdle, #oe_snippets > .o_we_customize_panel we-toggler:not([disabled]):hover svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > .active:not(.o_we_no_toggle) svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > .active:not(.o_we_no_toggle):not(span) svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > .active:not(.o_we_no_toggle) svg .o_subdle, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button.active:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets .colorpicker .active.o_colorpicker_reset:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel #oe-table-options .active.btn:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel .oe-toolbar .active.btn:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel we-button.active:not(.o_we_no_toggle) svg .o_subdle, #oe_snippets > .o_we_customize_panel we-toggler.active:not(.o_we_no_toggle) svg .o_subdle {\n  fill: rgba(157, 157, 157, 0.75);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > .active:not(.o_we_no_toggle, .o_we_collapse_toggler), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > .active:not(.o_we_no_toggle, .o_we_collapse_toggler):not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > .active:not(.o_we_no_toggle, .o_we_collapse_toggler), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button.active:not(.o_we_no_toggle, .o_we_collapse_toggler), #oe_snippets .colorpicker .active.o_colorpicker_reset:not(.o_we_no_toggle, .o_we_collapse_toggler), #oe_snippets > .o_we_customize_panel #oe-table-options .active.btn:not(.o_we_no_toggle, .o_we_collapse_toggler), #oe_snippets > .o_we_customize_panel .oe-toolbar .active.btn:not(.o_we_no_toggle, .o_we_collapse_toggler), #oe_snippets > .o_we_customize_panel we-button.active:not(.o_we_no_toggle, .o_we_collapse_toggler), #oe_snippets > .o_we_customize_panel we-toggler.active:not(.o_we_no_toggle, .o_we_collapse_toggler) {\n  background-color: #2b2b33;\n}\n\n#oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn, #oe_snippets #snippets_menu > button {\n  display: -webkit-inline-box; display: -webkit-inline-flex; display: inline-flex;\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n  justify-content: center;\n  min-width: 0;\n  border: none;\n  background-color: transparent;\n  color: inherit;\n  font-weight: normal;\n}\n\n#oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn > span, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn > span, #oe_snippets #snippets_menu > button > span {\n  display: inline-block;\n  max-width: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  vertical-align: top;\n  padding: 0.6em 0.4em 0.5em;\n}\n\n#oe_snippets .colorpicker .active.o_we_colorpicker_switch_pane_btn > span, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .active.o_pager_nav_btn > span, #oe_snippets #snippets_menu > button.active > span {\n  color: #FFFFFF;\n  box-shadow: inset 0 -2px 0 #01bad2;\n}\n\n#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div, #oe_snippets > .o_we_customize_panel .oe-toolbar #font-size > button:focus-within > div, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div {\n  border: 1px solid transparent;\n  border-radius: 4px;\n  background-color: #2b2b33;\n}\n\n#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div:focus-within, #oe_snippets > .o_we_customize_panel .oe-toolbar #font-size > button:focus-within > div:focus-within, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div:focus-within {\n  border-color: #01bad2;\n}\n\n#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div input, #oe_snippets > .o_we_customize_panel .oe-toolbar #font-size > button:focus-within > div input, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div input {\n  box-sizing: content-box;\n  padding: 0 6px;\n  border: none;\n  border-radius: 0;\n  background-color: transparent;\n  color: inherit;\n}\n\n#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div input:focus, #oe_snippets > .o_we_customize_panel .oe-toolbar #font-size > button:focus-within > div input:focus, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div input:focus {\n  outline: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div > we-button, #oe_snippets > .o_we_customize_panel .oe-toolbar #font-size > button:focus-within > div > we-button, #oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div > we-button {\n  border: none;\n}\n\n#oe_snippets {\n  position: absolute;\n  top: var(--o-we-toolbar-height);\n  left: auto;\n  bottom: 0;\n  right: 0;\n  position: fixed;\n  z-index: 1041;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  -webkit-flex-flow: column nowrap; flex-flow: column nowrap;\n  width: 288px;\n  border-left: 1px solid #2b2b33;\n  background-color: #2b2b33;\n  color: #D9D9D9;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Ubuntu, \"Noto Sans\", Arial, \"Odoo Unicode Support Noto\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  font-size: 12px;\n  font-weight: 400;\n  transition: transform 400ms ease 0s;\n  transform: translateX(100%);\n}\n\n#oe_snippets input::-webkit-outer-spin-button,\n#oe_snippets input::-webkit-inner-spin-button {\n  -webkit--webkit-appearance: none; -moz-appearance: none; appearance: none;\n  margin: 0;\n}\n\n#oe_snippets input[type=number] {\n  -moz--webkit-appearance: textfield; -moz-appearance: textfield; appearance: textfield;\n}\n\n#oe_snippets *::selection {\n  background: #03e1fe;\n  color: #000000;\n}\n\n#oe_snippets .o_we_website_top_actions {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  -webkit-box-pack: start; justify-content: flex-start;\n  width: 288px;\n  height: 46px;\n  min-height: 46px;\n  background-color: #141217;\n}\n\n#oe_snippets .o_we_website_top_actions .btn-group, #oe_snippets .o_we_website_top_actions .btn {\n  height: 100%;\n}\n\n#oe_snippets .o_we_website_top_actions .btn {\n  border: none;\n  border-radius: 0;\n  padding: 0.375rem 0.75rem;\n  font-size: 13px;\n  font-weight: 400;\n  line-height: 1;\n}\n\n#oe_snippets .o_we_website_top_actions .btn:not(.fa) {\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Ubuntu, \"Noto Sans\", Arial, \"Odoo Unicode Support Noto\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n}\n\n#oe_snippets .o_we_website_top_actions .btn.btn-primary {\n  color: #FFFFFF;\n  background-color: #71639e;\n  border-color: #71639e;\n}\n\n#oe_snippets .o_we_website_top_actions .btn.btn-primary:hover {\n  color: #FFFFFF;\n  background-color: #605486;\n  border-color: #5a4f7e;\n}\n\n.btn-check:focus + #oe_snippets .o_we_website_top_actions .btn.btn-primary, #oe_snippets .o_we_website_top_actions .btn.btn-primary:focus {\n  color: #FFFFFF;\n  background-color: #605486;\n  border-color: #5a4f7e;\n  box-shadow: 0 0 0 0.25rem rgba(134, 122, 173, 0.5);\n}\n\n.btn-check:checked + #oe_snippets .o_we_website_top_actions .btn.btn-primary, .btn-check:active + #oe_snippets .o_we_website_top_actions .btn.btn-primary, #oe_snippets .o_we_website_top_actions .btn.btn-primary:active, #oe_snippets .o_we_website_top_actions .btn.btn-primary.active, .show > #oe_snippets .o_we_website_top_actions .btn.btn-primary.dropdown-toggle {\n  color: #FFFFFF;\n  background-color: #5a4f7e;\n  border-color: #554a77;\n}\n\n.btn-check:checked + #oe_snippets .o_we_website_top_actions .btn.btn-primary:focus, .btn-check:active + #oe_snippets .o_we_website_top_actions .btn.btn-primary:focus, #oe_snippets .o_we_website_top_actions .btn.btn-primary:active:focus, #oe_snippets .o_we_website_top_actions .btn.btn-primary.active:focus, .show > #oe_snippets .o_we_website_top_actions .btn.btn-primary.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.25rem rgba(134, 122, 173, 0.5);\n}\n\n#oe_snippets .o_we_website_top_actions .btn.btn-primary:disabled, #oe_snippets .o_we_website_top_actions .btn.btn-primary.disabled {\n  color: #FFFFFF;\n  background-color: #71639e;\n  border-color: #71639e;\n}\n\n#oe_snippets .o_we_website_top_actions .btn.btn-secondary {\n  color: #FFFFFF;\n  background-color: #141217;\n  border-color: #141217;\n}\n\n#oe_snippets .o_we_website_top_actions .btn.btn-secondary:hover {\n  color: #FFFFFF;\n  background-color: #110f14;\n  border-color: #100e12;\n}\n\n.btn-check:focus + #oe_snippets .o_we_website_top_actions .btn.btn-secondary, #oe_snippets .o_we_website_top_actions .btn.btn-secondary:focus {\n  color: #FFFFFF;\n  background-color: #110f14;\n  border-color: #100e12;\n  box-shadow: 0 0 0 0.25rem rgba(55, 54, 58, 0.5);\n}\n\n.btn-check:checked + #oe_snippets .o_we_website_top_actions .btn.btn-secondary, .btn-check:active + #oe_snippets .o_we_website_top_actions .btn.btn-secondary, #oe_snippets .o_we_website_top_actions .btn.btn-secondary:active, #oe_snippets .o_we_website_top_actions .btn.btn-secondary.active, .show > #oe_snippets .o_we_website_top_actions .btn.btn-secondary.dropdown-toggle {\n  color: #FFFFFF;\n  background-color: #100e12;\n  border-color: #0f0e11;\n}\n\n.btn-check:checked + #oe_snippets .o_we_website_top_actions .btn.btn-secondary:focus, .btn-check:active + #oe_snippets .o_we_website_top_actions .btn.btn-secondary:focus, #oe_snippets .o_we_website_top_actions .btn.btn-secondary:active:focus, #oe_snippets .o_we_website_top_actions .btn.btn-secondary.active:focus, .show > #oe_snippets .o_we_website_top_actions .btn.btn-secondary.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.25rem rgba(55, 54, 58, 0.5);\n}\n\n#oe_snippets .o_we_website_top_actions .btn.btn-secondary:disabled, #oe_snippets .o_we_website_top_actions .btn.btn-secondary.disabled {\n  color: #FFFFFF;\n  background-color: #141217;\n  border-color: #141217;\n}\n\n#oe_snippets .o_we_website_top_actions .btn:focus, #oe_snippets .o_we_website_top_actions .btn:active, #oe_snippets .o_we_website_top_actions .btn:focus:active {\n  outline: none;\n  box-shadow: none !important;\n}\n\n#oe_snippets .o_we_website_top_actions .dropdown-menu {\n  left: auto;\n  right: 0;\n}\n\n#oe_snippets .o_we_sublevel_3 > we-title::before, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_3 > .oe-table-label::before, #oe_snippets .o_we_sublevel_2 > we-title::before, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_2 > .oe-table-label::before, #oe_snippets .o_we_sublevel_1 > we-title::before, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_1 > .oe-table-label::before {\n  content: \"\u2514\";\n  display: inline-block;\n  margin-right: 0.4em;\n}\n\n.o_rtl #oe_snippets .o_we_sublevel_3 > we-title::before, .o_rtl #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_3 > .oe-table-label::before, .o_rtl #oe_snippets .o_we_sublevel_2 > we-title::before, .o_rtl #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_2 > .oe-table-label::before, .o_rtl #oe_snippets .o_we_sublevel_1 > we-title::before, .o_rtl #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_1 > .oe-table-label::before {\n  transform: scaleX(-1);\n}\n\n#oe_snippets .o_we_sublevel_2 > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_2 > .oe-table-label {\n  padding-left: 0.6em;\n}\n\n#oe_snippets .o_we_sublevel_3 > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_sublevel_3 > .oe-table-label {\n  padding-left: 1.2em;\n}\n\n#oe_snippets #snippets_menu {\n  -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: end;\n  background-color: #141217;\n  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);\n  color: #D9D9D9;\n  height: 3rem;\n}\n\n#oe_snippets .tooltip {\n  pointer-events: none !important;\n}\n\n#oe_snippets .o_snippet_search_filter {\n  position: relative;\n  box-shadow: inset 0 -1px 0 #000000, 0 10px 10px rgba(0, 0, 0, 0.2);\n  z-index: 2;\n}\n\n#oe_snippets .o_snippet_search_filter, #oe_snippets .o_snippet_search_filter .o_snippet_search_filter_input {\n  width: 100%;\n}\n\n#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_input {\n  background-color: #2b2b33;\n  padding: 10px 2em 10px 10px;\n  border: 0;\n  border-bottom: 1px solid transparent;\n  color: #FFFFFF;\n}\n\n#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_input::placeholder {\n  font-style: italic;\n  color: #9d9d9d;\n}\n\n#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_input:focus {\n  background-color: #3e3e46;\n  outline: none;\n}\n\n#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_reset {\n  position: absolute;\n  top: 10px;\n  left: auto;\n  bottom: 10px;\n  right: 10px;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  justify-content: center;\n  align-items: center;\n  padding: 0 6px;\n  color: #9d9d9d;\n  cursor: pointer;\n}\n\n#oe_snippets .o_snippet_search_filter .o_snippet_search_filter_reset:hover, #oe_snippets .o_snippet_search_filter .o_snippet_search_filter_reset:focus, #oe_snippets .o_snippet_search_filter .o_snippet_search_filter_reset.focus {\n  color: #FFFFFF;\n}\n\n#oe_snippets > #o_scroll, #oe_snippets > .o_we_customize_panel {\n  min-height: 0;\n  overflow: auto;\n}\n\n#oe_snippets > #o_scroll {\n  background-color: #191922;\n  padding: 0 10px;\n  height: 100%;\n  z-index: 1;\n}\n\n#oe_snippets > #o_scroll .o_panel, #oe_snippets > #o_scroll .o_panel_header {\n  padding: 10px 0;\n}\n\n#oe_snippets > #o_scroll .o_panel_body {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  -webkit-flex-wrap: wrap; flex-wrap: wrap;\n  margin-left: -2px;\n}\n\n#oe_snippets > #o_scroll .o_panel_body > .oe_snippet {\n  -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;\n  width: 33.33333333%;\n  background-clip: padding-box;\n  border-left: 2px solid transparent;\n  margin-bottom: 2px;\n  user-select: none;\n  cursor: url(/web/static/img/openhand.cur), grab;\n}\n\n#oe_snippets > #o_scroll .o_panel_body > .oe_snippet .oe_snippet_thumbnail_title {\n  display: block;\n  max-width: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  vertical-align: top;\n  white-space: normal;\n  padding: 5px;\n  text-align: center;\n}\n\n#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_disabled .o_snippet_undroppable {\n  position: absolute;\n  top: 8px;\n  left: auto;\n  bottom: auto;\n  right: 6px;\n}\n\n#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install .btn.o_install_btn {\n  position: absolute;\n  top: 10px;\n  left: auto;\n  bottom: auto;\n  right: auto;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n\n#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install:not(:hover) .btn.o_install_btn {\n  display: none;\n}\n\n#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_disabled, #oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install {\n  background-color: rgba(62, 62, 70, 0.2);\n}\n\n#oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_disabled .oe_snippet_thumbnail_img, #oe_snippets > #o_scroll .o_panel_body > .oe_snippet.o_snippet_install .oe_snippet_thumbnail_img {\n  opacity: .4;\n  filter: saturate(0) blur(1px);\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet {\n  width: 100%;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail, #oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn {\n  align-items: center;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail {\n  min-width: 0;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail_title {\n  white-space: nowrap;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet .oe_snippet_thumbnail_img {\n  flex-shrink: 0;\n  width: 41px;\n  height: 30px;\n  padding: 0;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet .btn {\n  padding-top: 0;\n  padding-bottom: 0;\n  padding-left: 0;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet:not(:hover) .btn {\n  display: none;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget {\n  cursor: pointer;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget > div {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget input {\n  cursor: text;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button {\n  cursor: pointer;\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n  padding: 0 6px;\n  line-height: 17px;\n  text-align: center;\n}\n\n#oe_snippets > #o_scroll #snippet_custom .oe_snippet we-input.o_we_user_value_widget we-button:hover {\n  background-color: gray;\n}\n\n#oe_snippets > .o_we_customize_panel {\n  position: relative;\n  flex: 1;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar {\n  position: relative;\n  background: transparent;\n  margin-top: 8px;\n  padding: 0 10px 0 15px;\n  grid-template-areas: \"typo typo style style colors\" \"size align list list link\" \"ai animate animate hilight hilight\" \"options options options options options\" \"options2 options2 options2 options2 options2\" \"options3 options3 options3 options3 options3\";\n  grid-template-columns: 1fr 1fr 1fr 1fr 1fr;\n  grid-template-rows: minmax(22px, auto) minmax(22px, auto) minmax(22px, auto) auto auto auto;\n  row-gap: 8px;\n  column-gap: 3px;\n  width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar::before {\n  display: none;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar .fa {\n  font-size: 12px;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar .btn {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  padding: 2.64px 3.5px;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-menu {\n  border-color: #000000;\n  padding: 0;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-menu > li ~ li {\n  border-top: 1px solid transparent;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-item {\n  padding-left: 2em;\n  background-color: #595964;\n  color: #C6C6C6;\n  border-radius: 0;\n  padding-top: 5px;\n  padding-bottom: 5px;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-item::after {\n  content: \"\";\n  color: #9d9d9d;\n  right: auto;\n  left: 0.5em;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-item:hover, #oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-item:focus {\n  background: #2b2b33;\n  color: #D9D9D9;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-item.active {\n  background: #42424c;\n  color: #FFFFFF;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar .o_we_font_size_badge {\n  opacity: 0.6;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #font-size > button:focus-within {\n  margin-top: -1px;\n  margin-bottom: -1px;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #font-size > button:focus-within input {\n  padding: 0 !important;\n  width: calc(2ch + 12px - 2px);\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #font-size > button.dropdown-toggle::after {\n  display: none;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #font-size > button input {\n  background-color: unset;\n  border: none;\n  color: unset;\n  padding: 0;\n  text-align: center;\n  width: calc(2ch + 12px);\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options {\n  grid-area: options;\n  padding-left: 0;\n  padding-right: 0;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-title.o_short_title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar we-customizeblock-option .o_short_title.oe-table-label, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-title.o_short_title, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .o_short_title.oe-table-label {\n  width: unset !important;\n  padding-right: 0 !important;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-title.o_long_title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-toolbar we-customizeblock-option .o_long_title.oe-table-label, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-title.o_long_title, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .o_long_title.oe-table-label {\n  width: fit-content !important;\n  padding-right: 10px !important;\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-button .o_switch, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-button .o_switch {\n  min-width: fit-content;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .highlighted-text, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .highlighted-text {\n  color: white;\n  font-weight: bold;\n  padding: 1px;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown {\n  position: unset;\n  width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-toggle, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-toggle {\n  padding: 0;\n  width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-toggle::after, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-toggle::after {\n  display: none;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-menu.show, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-menu.show {\n  position: absolute !important;\n  padding: 0;\n  width: 100%;\n  border-color: #000000;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-menu.show[data-popper-placement$=\"start\"], #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-menu.show[data-popper-placement$=\"start\"] {\n  left: -10px !important;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-menu.show we-button:not(.fa), #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-menu.show we-button:not(.fa) {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n  height: 34px;\n  text-align: left;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Ubuntu, \"Noto Sans\", Arial, \"Odoo Unicode Support Noto\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n  font-size: 12px;\n  font-weight: 400;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-menu.show we-button:not(.fa) div, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-menu.show we-button:not(.fa) div {\n  width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option .dropdown .dropdown-menu.show .dropdown-item::before, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options .dropdown .dropdown-menu.show .dropdown-item::before {\n  display: none;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option input::placeholder, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options input::placeholder {\n  font-style: italic;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-input:not(.o_we_small_input), #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-input:not(.o_we_small_input), #oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-input:not(.o_we_small_input) div, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-input:not(.o_we_small_input) div, #oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option we-input:not(.o_we_small_input) input, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options we-input:not(.o_we_small_input) input {\n  width: 100% !important;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option ~ we-customizeblock-option, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options ~ we-customizeblock-option, #oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option ~ #oe-table-options, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options ~ #oe-table-options {\n  grid-area: options2;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option ~ we-customizeblock-option ~ we-customizeblock-option, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options ~ we-customizeblock-option ~ we-customizeblock-option, #oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option ~ #oe-table-options ~ we-customizeblock-option, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options ~ #oe-table-options ~ we-customizeblock-option, #oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option ~ we-customizeblock-option ~ #oe-table-options, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options ~ we-customizeblock-option ~ #oe-table-options, #oe_snippets > .o_we_customize_panel .oe-toolbar we-customizeblock-option ~ #oe-table-options ~ #oe-table-options, #oe_snippets > .o_we_customize_panel .oe-toolbar #oe-table-options ~ #oe-table-options ~ #oe-table-options {\n  grid-area: options3;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #colorInputButtonGroup {\n  position: static;\n  grid-area: colors;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #colorInputButtonGroup .dropdown-toggle:after {\n  display: none;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #colorInputButtonGroup .colorpicker-group {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: stretch;\n  position: static;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #colorInputButtonGroup #oe-text-color {\n  border-right: 0;\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #colorInputButtonGroup #oe-fore-color {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar .btn + .btn {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar .btn-group > .btn:not(:last-of-type) {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #list {\n  grid-area: list;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #link {\n  grid-area: link;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #link #unlink {\n  display: none;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #font-size {\n  grid-area: size;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #decoration {\n  grid-area: style;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #style {\n  grid-area: typo;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #style .dropdown-toggle {\n  justify-content: space-between;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #style .dropdown-toggle span {\n  overflow: hidden;\n  text-overflow: ellipsis;\n  color: white;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #style .dropdown-toggle span pre, #oe_snippets > .o_we_customize_panel .oe-toolbar #style .dropdown-toggle span blockquote {\n  padding: 0;\n  border: 0;\n  color: inherit;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #justify {\n  grid-area: align;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #justify .dropdown-menu {\n  padding: 0;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #justify .dropdown-menu .btn {\n  padding: 6.6px 11px;\n  border-width: 0;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #justify .dropdown-menu .btn:hover {\n  z-index: 0;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #justify .dropdown-menu .btn + .btn {\n  border-left-width: 1px;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #chatgpt {\n  grid-area: ai;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #animate {\n  grid-area: animate;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #highlight {\n  grid-area: hilight;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar #chatgpt .fa, #oe_snippets > .o_we_customize_panel .oe-toolbar #animate .fa, #oe_snippets > .o_we_customize_panel .oe-toolbar #highlight .fa {\n  margin-right: 2px;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-menu.colorpicker-menu {\n  min-width: 0;\n  max-height: none;\n  left: 15px;\n  right: 10px;\n  border: 1px solid #000000;\n  border-radius: 4px;\n  padding: 0;\n}\n\n#oe_snippets > .o_we_customize_panel .oe-toolbar :not(.dropup) > .dropdown-menu.colorpicker-menu {\n  top: 2em;\n}\n\n#oe_snippets > .o_we_customize_panel .link-custom-color-border we-input, #oe_snippets > .o_we_customize_panel .link-custom-color-border we-select {\n  max-width: max-content;\n}\n\n#oe_snippets > .o_we_customize_panel .link-custom-color-border we-toggler {\n  width: 85px !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_link {\n  margin-top: 0;\n  border: 0;\n  padding: 0;\n  background: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-toggler {\n  padding-right: 2em;\n  text-align: left;\n}\n\n#oe_snippets > .o_we_customize_panel we-toggler::after {\n  content: \"\uf0d7\";\n  color: #9d9d9d;\n}\n\n#oe_snippets > .o_we_customize_panel we-toggler.o_we_toggler_pager {\n  padding-right: 2em;\n}\n\n#oe_snippets > .o_we_customize_panel we-toggler.o_we_toggler_pager::after {\n  content: \"\uf105\";\n  color: #9d9d9d;\n}\n\n#oe_snippets > .o_we_customize_panel we-toggler > img, #oe_snippets > .o_we_customize_panel we-toggler > svg {\n  max-width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel we-toggler + * {\n  display: none;\n  border: 1px solid #000000;\n  border-radius: 4px;\n  background-color: #141217;\n  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.5);\n}\n\n#oe_snippets > .o_we_customize_panel we-toggler.active {\n  padding-right: 2em;\n}\n\n#oe_snippets > .o_we_customize_panel we-toggler.active::after {\n  content: \"\uf0d8\";\n  color: #9d9d9d;\n}\n\n#oe_snippets > .o_we_customize_panel we-toggler.active + * {\n  display: block;\n}\n\n#oe_snippets > .o_we_customize_panel we-toggler.active + .o_we_has_pager {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.active, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button, #oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-item, #oe_snippets > .o_we_customize_panel we-toggler, #oe_snippets > .o_we_customize_panel we-toggler.o_we_toggler_pager, #oe_snippets > .o_we_customize_panel we-toggler.active {\n  position: relative;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.active::after, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button::after, #oe_snippets > .o_we_customize_panel .oe-toolbar .dropdown-item::after, #oe_snippets > .o_we_customize_panel we-toggler::after, #oe_snippets > .o_we_customize_panel we-toggler.o_we_toggler_pager::after, #oe_snippets > .o_we_customize_panel we-toggler.active::after {\n  position: absolute;\n  top: 50%;\n  left: auto;\n  bottom: auto;\n  right: 0.5em;\n  transform: translateY(-50%);\n  width: 1em;\n  text-align: center;\n  font-family: FontAwesome;\n}\n\n#oe_snippets > .o_we_customize_panel we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-label {\n  display: block;\n  text-align: left;\n}\n\n#oe_snippets > .o_we_customize_panel we-title:where(:lang(en)), #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-label:where(:lang(en)) {\n  text-transform: capitalize;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-options {\n  position: relative;\n  display: block;\n  padding: 0 0 15px 0;\n  background-color: #3e3e46;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n  padding: 3px 10px 0 15px;\n  background-color: #2b2b33;\n  font-size: 13px;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > span, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > span {\n  display: inline-block;\n  max-width: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  vertical-align: top;\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n  cursor: pointer;\n  color: #FFFFFF !important;\n  line-height: 32px;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group {\n  -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  margin-left: auto;\n  font-size: .9em;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group .oe_snippet_remove, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group .oe_snippet_remove {\n  font-size: 1.2em;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group we-customizeblock-option, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group we-customizeblock-option, #oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group #oe-table-options, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group #oe-table-options {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  padding: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group we-button, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group we-button {\n  margin-top: 0 !important;\n  margin-left: 3px;\n  padding: 0 3px !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group we-button.fa, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group we-button.fa, #oe_snippets > .o_we_customize_panel we-customizeblock-options > we-title > we-top-button-group we-button.o_we_icon_button, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-options > .oe-table-label > we-top-button-group we-button.o_we_icon_button {\n  box-sizing: content-box;\n  width: 1.29em;\n  padding: 0 0.15em !important;\n  margin-left: 6px;\n  text-align: center;\n  justify-content: center;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-options.o_we_outdated_block_options {\n  padding: 0 !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-options.o_we_outdated_block_options > we-customizeblock-option:not(.snippet-option-VersionControl), #oe_snippets > .o_we_customize_panel we-customizeblock-options.o_we_outdated_block_options > #oe-table-options:not(.snippet-option-VersionControl) {\n  display: none !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-options.o_we_outdated_block_options > we-customizeblock-option.snippet-option-VersionControl, #oe_snippets > .o_we_customize_panel we-customizeblock-options.o_we_outdated_block_options > .snippet-option-VersionControl#oe-table-options {\n  padding: 0 !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-option, #oe_snippets > .o_we_customize_panel #oe-table-options {\n  position: relative;\n  display: block;\n  padding: 0 10px 0 15px;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-option .dropdown-menu, #oe_snippets > .o_we_customize_panel #oe-table-options .dropdown-menu {\n  position: static !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-alert, #oe_snippets > .o_we_customize_panel #oe-table-options > we-alert {\n  background-color: #6999a8;\n  display: block;\n  padding: 6px;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-alert we-title, #oe_snippets > .o_we_customize_panel #oe-table-options > we-alert we-title, #oe_snippets > .o_we_customize_panel we-customizeblock-option > we-alert #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-option > we-alert .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options > we-alert .oe-table-label {\n  margin-bottom: 6px;\n  text-transform: uppercase;\n  font-weight: bold;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-option > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options > .oe-table-label {\n  margin-bottom: -4px;\n  font-size: 13px;\n  color: #FFFFFF;\n  font-weight: 500;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-title:not(:first-child), #oe_snippets > .o_we_customize_panel #oe-table-options > we-title:not(:first-child), #oe_snippets > .o_we_customize_panel #oe-table-options we-customizeblock-option > .oe-table-label:not(:first-child), #oe_snippets > .o_we_customize_panel #oe-table-options > .oe-table-label:not(:first-child) {\n  margin-top: 16px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_fold_icon {\n  position: absolute;\n  top: 0;\n  left: -15px;\n  bottom: 0;\n  right: 100%;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 15px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget {\n  margin-top: 8px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget > div {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n  min-height: 22px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_large > div {\n  flex: 1 1 auto !important;\n  width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div {\n  display: block;\n  max-width: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  vertical-align: top;\n  min-height: 20px;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div, #oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div > .fa {\n  line-height: 20px;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div > img {\n  margin-bottom: 1px;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget > div > svg {\n  margin-bottom: 2px;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget.fa > div, #oe_snippets > .o_we_customize_panel we-button.o_we_user_value_widget.oi > div {\n  display: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_icon_button, #oe_snippets > .o_we_customize_panel we-button.fa {\n  -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.fa-fw, #oe_snippets > .o_we_customize_panel we-button.oi-fw {\n  padding: 0 .5em;\n  width: 2.29em;\n  justify-content: center;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget {\n  min-width: 20px;\n  padding: 0;\n  border: none;\n  background: none;\n  cursor: default;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options we-button.o_we_checkbox_wrapper.o_we_user_value_widget > .oe-table-label {\n  cursor: pointer;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget > div {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  min-height: 22px;\n  line-height: 22px;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget we-checkbox {\n  -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n  width: 20px;\n  height: 12px;\n  background-color: #9d9d9d;\n  border-radius: 10rem;\n  cursor: pointer;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget we-checkbox::after {\n  content: \"\";\n  display: block;\n  width: 11px;\n  height: 10px;\n  border-radius: 10rem;\n  background-color: #FFFFFF;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget.active we-checkbox {\n  background-color: #01bad2;\n  -webkit-box-pack: end; justify-content: flex-end;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget.active, #oe_snippets > .o_we_customize_panel we-button.o_we_checkbox_wrapper.o_we_user_value_widget:hover {\n  color: #D9D9D9;\n}\n\n#oe_snippets > .o_we_customize_panel we-selection-items .o_we_user_value_widget {\n  margin-top: 0;\n  flex-grow: 1;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget {\n  position: relative;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget:not(.o_we_icon_select) we-toggler {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n  width: 157.8px;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget:not(.o_we_select_pager).o_we_widget_opened .o_we_dropdown_caret {\n  position: relative;\n  display: block;\n  align-self: flex-end;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget:not(.o_we_select_pager).o_we_widget_opened .o_we_dropdown_caret::before, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget:not(.o_we_select_pager).o_we_widget_opened .o_we_dropdown_caret::after {\n  content: '';\n  position: absolute;\n  top: 100%;\n  left: auto;\n  bottom: auto;\n  right: 2em;\n  z-index: 1001;\n  transform: translateX(50%);\n  margin-top: 2px;\n  border-bottom: 7px solid #000000;\n  border-left: 8px solid transparent;\n  border-right: 8px solid transparent;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget:not(.o_we_select_pager).o_we_widget_opened .o_we_dropdown_caret::after {\n  border-bottom-color: #595964;\n  border-left-width: 7px;\n  border-right-width: 7px;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget:not(.o_we_so_color_palette) ~ we-button:not(:hover):not(.o_we_image_shape_remove):last-child {\n  background: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-toggler:empty::before {\n  content: attr(data-placeholder-text);\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items:not(.o_we_has_pager) {\n  position: absolute;\n  top: 100%;\n  left: 0;\n  bottom: auto;\n  right: 0;\n  z-index: 1000;\n  max-height: 600px;\n  overflow-y: auto;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager {\n  width: 288px;\n  max-height: calc(100% - calc(46px + 3rem));\n  margin-top: calc(46px + 3rem);\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager we-title, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .oe-table-label {\n  padding: 3px 10px 0 15px;\n  background-color: #2b2b33;\n  line-height: 32px;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav {\n  background-color: #2b2b33;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav > div:first-child {\n  padding-left: 12px;\n  padding-top: 8px;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_title {\n  padding-left: 12px;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle {\n  color: #FFFFFF;\n  background-color: #2b2b33;\n  border-color: #2b2b33;\n  padding: 4px;\n  font-size: 16.8px;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:hover {\n  color: #FFFFFF;\n  background-color: #25252b;\n  border-color: #222229;\n}\n\n.btn-check:focus + #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:focus {\n  color: #FFFFFF;\n  background-color: #25252b;\n  border-color: #222229;\n  box-shadow: 0 0 0 0.25rem rgba(75, 75, 82, 0.5);\n}\n\n.btn-check:checked + #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle, .btn-check:active + #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:active, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle.active, .show > #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle.dropdown-toggle {\n  color: #FFFFFF;\n  background-color: #222229;\n  border-color: #202026;\n}\n\n.btn-check:checked + #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:focus, .btn-check:active + #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:focus, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:active:focus, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle.active:focus, .show > #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.25rem rgba(75, 75, 82, 0.5);\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle:disabled, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_angle.disabled {\n  color: #FFFFFF;\n  background-color: #2b2b33;\n  border-color: #2b2b33;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_nav_btn:focus {\n  box-shadow: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items.o_we_has_pager .o_pager_container {\n  overflow-y: scroll;\n  scroll-behavior: smooth;\n  background-color: #3e3e46;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items:not(.dropdown-menu):not(.o_we_has_pager) {\n  margin-top: 8px;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items:empty {\n  line-height: 34px;\n  background-color: #595964;\n  color: #C6C6C6;\n  padding-left: 2em;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items:empty::before {\n  content: attr(data-placeholder-text);\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-title, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget #oe-table-options we-selection-items > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items > .oe-table-label {\n  line-height: 34px;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button {\n  padding-left: 2em;\n  border: none;\n  background: none;\n  background-clip: padding-box;\n  background-color: #595964;\n  color: #C6C6C6;\n  border-radius: 0;\n  text-align: left;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button::after {\n  content: \"\";\n  color: #9d9d9d;\n  right: auto;\n  left: 0.5em;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > we-title, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget #oe-table-options we-selection-items > we-button > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items > we-button > .oe-table-label {\n  flex-grow: 1;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > div, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > we-title, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget #oe-table-options we-selection-items > we-button > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items > we-button > .oe-table-label {\n  line-height: 34px;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > div img, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > div svg, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > we-title img, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget #oe-table-options we-selection-items > we-button > .oe-table-label img, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items > we-button > .oe-table-label img, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button > we-title svg, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget #oe-table-options we-selection-items > we-button > .oe-table-label svg, #oe_snippets > .o_we_customize_panel #oe-table-options we-select.o_we_user_value_widget we-selection-items > we-button > .oe-table-label svg {\n  max-width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.o_we_badge_at_end > div {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  width: 100%;\n  justify-content: space-between;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button:not(.d-none) ~ we-button {\n  border-top: 1px solid transparent;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button:hover {\n  background-color: #2b2b33;\n  color: #D9D9D9;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.active {\n  padding-left: 2em;\n  background-color: #42424c;\n  color: #FFFFFF;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.active::after {\n  content: \"\uf00c\";\n  color: #9d9d9d;\n  right: auto;\n  left: 0.5em;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-selection-items > we-button.active:after {\n  color: #01bad2;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_dropdown_up we-selection-items {\n  top: auto !important;\n  bottom: 100% !important;\n  margin-bottom: 8px !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_dropdown_up .o_we_dropdown_caret {\n  align-self: flex-start !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_dropdown_up .o_we_dropdown_caret::before, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_dropdown_up .o_we_dropdown_caret::after {\n  top: auto !important;\n  bottom: 100% !important;\n  margin-bottom: 2px;\n  transform: rotate(180deg) translateX(-50%) !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_dropdown_up.o_we_so_color_palette .o_we_dropdown_caret::before, #oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_dropdown_up.o_we_so_color_palette .o_we_dropdown_caret::after {\n  margin-bottom: 1px;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page {\n  display: grid;\n  grid-template-columns: repeat(4, 1fr);\n  gap: 4px;\n  padding: 8px;\n  background-color: #3e3e46;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page we-button {\n  padding: 8px;\n  background-color: transparent;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page we-button[data-shape] {\n  grid-column: span 4;\n  padding: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page we-button[data-shape] div {\n  width: 100%;\n  height: 75px;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page we-button.active {\n  outline: 4px solid #40ad67;\n  outline-offset: -4px;\n  background-color: rgba(64, 173, 103, 0.2);\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget we-select-page img {\n  width: 100%;\n  aspect-ratio: 1;\n  object-fit: contain;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid we-selection-items we-button {\n  padding: 20px 5px;\n  border: 1px solid #000000;\n  border-radius: 2px;\n  justify-content: center;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid we-selection-items we-button.active {\n  border: 2px solid #40ad67 !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid we-selection-items we-button[data-set-text-highlight] {\n  --text-highlight-width: .15em;\n  --text-highlight-color: #D9D9D9;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid we-selection-items we-button[data-set-text-highlight] > div {\n  flex: none;\n  position: relative;\n  width: 60%;\n  font-size: 15.6px;\n  font-weight: bold;\n  overflow: visible;\n  isolation: isolate;\n}\n\n#oe_snippets > .o_we_customize_panel we-select.o_we_user_value_widget.o_we_select_grid we-selection-items we-button[data-set-text-highlight] > div svg {\n  z-index: -1;\n}\n\n#oe_snippets > .o_we_customize_panel we-button.o_we_image_shape_remove div {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n}\n\n#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  flex-grow: 1;\n  max-width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button {\n  padding: 0 6px;\n  border-radius: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button + we-button {\n  border-left: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button:first-child, #oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button .active:first-child {\n  border-top-left-radius: 4px;\n  border-bottom-left-radius: 4px;\n}\n\n#oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button:last-child, #oe_snippets > .o_we_customize_panel we-button-group.o_we_user_value_widget we-selection-items we-button .active:last-child {\n  border-top-right-radius: 4px;\n  border-bottom-right-radius: 4px;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-button-group.o_we_user_value_widget we-selection-items, #oe_snippets > .o_we_customize_panel #oe-table-options > we-button-group.o_we_user_value_widget we-selection-items {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n  width: 157.8px;\n}\n\n#oe_snippets > .o_we_customize_panel we-customizeblock-option > we-button-group.o_we_user_value_widget we-selection-items we-button, #oe_snippets > .o_we_customize_panel #oe-table-options > we-button-group.o_we_user_value_widget we-selection-items we-button {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  justify-content: center;\n  flex: 1 1 25%;\n  padding: 1.5px 2px;\n}\n\n#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget > div {\n  -webkit-box-flex: 0; -webkit-flex: 0 1 auto; flex: 0 1 auto;\n  width: 60px;\n}\n\n#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget input {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n  width: 0;\n  min-width: 2ch;\n  height: 20px;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Ubuntu, \"Noto Sans\", Arial, \"Odoo Unicode Support Noto\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n}\n\n#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget input::placeholder {\n  color: #9d9d9d;\n}\n\n#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget input.datetimepicker-input.text-primary {\n  color: inherit !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-input.o_we_user_value_widget span {\n  -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;\n  padding-right: 6px;\n  font-size: 11px;\n  color: #9d9d9d;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview {\n  -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;\n  display: block;\n  width: 20px;\n  height: 20px;\n  border: 1px solid #000000;\n  border-radius: 10rem;\n  cursor: pointer;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget .o_we_color_preview::after {\n  box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.5);\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget.o_we_widget_opened .o_we_color_preview {\n  border: 2px solid #01bad2;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget.o_we_widget_opened span.o_we_dropdown_caret::before, #oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget.o_we_widget_opened span.o_we_dropdown_caret::after {\n  right: 10px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget.o_we_widget_opened span.o_we_dropdown_caret::after {\n  border-bottom-width: 8px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_so_color_palette.o_we_user_value_widget we-toggler {\n  display: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-matrix {\n  overflow-y: auto;\n}\n\n#oe_snippets > .o_we_customize_panel we-matrix table {\n  table-layout: fixed;\n  width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel we-matrix table td, #oe_snippets > .o_we_customize_panel we-matrix table th {\n  text-align: center;\n}\n\n#oe_snippets > .o_we_customize_panel we-matrix table td we-button, #oe_snippets > .o_we_customize_panel we-matrix table th we-button {\n  display: inline-block;\n  color: inherit;\n  height: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel we-matrix table td we-button.o_we_matrix_remove_col, #oe_snippets > .o_we_customize_panel we-matrix table td we-button.o_we_matrix_remove_row, #oe_snippets > .o_we_customize_panel we-matrix table th we-button.o_we_matrix_remove_col, #oe_snippets > .o_we_customize_panel we-matrix table th we-button.o_we_matrix_remove_row {\n  display: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-matrix table td input, #oe_snippets > .o_we_customize_panel we-matrix table th input {\n  border: 1px solid transparent;\n  background-color: #2b2b33;\n  color: inherit;\n  font-size: 12px;\n  width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel we-matrix table td:last-child, #oe_snippets > .o_we_customize_panel we-matrix table th:last-child {\n  width: 28px;\n}\n\n#oe_snippets > .o_we_customize_panel we-matrix table tr:last-child we-button {\n  overflow: visible;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget[data-display-range-value] input[type=\"range\"] {\n  min-width: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"] {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n  width: 157.8px;\n  height: 22px;\n  padding: 0 1px 0 0;\n  background-color: transparent;\n  -webkit-appearance: none; -moz-appearance: none; appearance: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]:focus {\n  outline: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]:focus::-webkit-slider-thumb {\n  box-shadow: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]:focus::-moz-range-thumb {\n  box-shadow: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]:focus::-ms-thumb {\n  box-shadow: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-moz-focus-outer {\n  border: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-webkit-slider-thumb {\n  width: 10px;\n  height: 10px;\n  margin-top: -3px;\n  border: none;\n  border-radius: 10rem;\n  background-color: #01bad2;\n  box-shadow: none;\n  -webkit-appearance: none; -moz-appearance: none; appearance: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-webkit-slider-thumb:active {\n  background-color: #01bad2;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-webkit-slider-runnable-track {\n  width: 100%;\n  height: 4px;\n  cursor: pointer;\n  background-color: #9d9d9d;\n  border-color: transparent;\n  border-radius: 10rem;\n  box-shadow: none;\n  position: relative;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-moz-range-thumb {\n  width: 10px;\n  height: 10px;\n  border: none;\n  border-radius: 10rem;\n  background-color: #01bad2;\n  box-shadow: none;\n  -webkit-appearance: none; -moz-appearance: none; appearance: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-moz-range-thumb:active {\n  background-color: #01bad2;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-moz-range-track {\n  width: 100%;\n  height: 4px;\n  cursor: pointer;\n  background-color: #9d9d9d;\n  border-color: transparent;\n  border-radius: 10rem;\n  box-shadow: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-moz-range-progress {\n  background-color: #01bad2;\n  height: 4px;\n  border-color: transparent;\n  border-radius: 10rem;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-ms-thumb {\n  width: 10px;\n  height: 10px;\n  margin-top: 0;\n  margin-right: 0;\n  margin-left: 0;\n  border: none;\n  border-radius: 10rem;\n  background-color: #01bad2;\n  box-shadow: none;\n  -webkit-appearance: none; -moz-appearance: none; appearance: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-ms-thumb:active {\n  background-color: #01bad2;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-ms-track {\n  width: 100%;\n  height: 4px;\n  cursor: pointer;\n  background-color: transparent;\n  border-color: transparent;\n  border-width: 5px;\n  box-shadow: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-ms-fill-lower {\n  background-color: #01bad2;\n  border-radius: 10rem;\n  border-radius: 1rem;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"]::-ms-fill-upper {\n  background-color: #9d9d9d;\n  border-radius: 10rem;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"].o_we_inverted_range {\n  transform: rotate(180deg);\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"].o_we_inverted_range::-moz-range-track {\n  background-color: #01bad2;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"].o_we_inverted_range::-moz-range-progress {\n  background-color: #9d9d9d;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"].o_we_inverted_range::-ms-fill-lower {\n  background-color: #9d9d9d;\n}\n\n#oe_snippets > .o_we_customize_panel we-range.o_we_user_value_widget input[type=\"range\"].o_we_inverted_range::-ms-fill-upper {\n  background-color: #01bad2;\n}\n\n#oe_snippets > .o_we_customize_panel we-list > div {\n  -webkit-flex-flow: row wrap; flex-flow: row wrap;\n}\n\n#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper {\n  width: 100%;\n  max-height: 200px;\n  overflow-y: auto;\n}\n\n#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table {\n  table-layout: auto;\n  width: 100%;\n  margin-bottom: 4px;\n}\n\n#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table:empty {\n  margin-bottom: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table input {\n  width: 100%;\n  border: 1px solid transparent;\n  border-radius: 4px;\n  padding: 0 6px;\n  background-color: #2b2b33;\n  color: inherit;\n  font-family: -apple-system, BlinkMacSystemFont, \"Segoe UI\", Roboto, \"Helvetica Neue\", Ubuntu, \"Noto Sans\", Arial, \"Odoo Unicode Support Noto\", sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\";\n}\n\n#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table tr {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n  border: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table td {\n  flex-grow: 1;\n  padding-bottom: 4px;\n}\n\n#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table td:not(.o_we_list_record_name) {\n  flex-grow: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-list .o_we_table_wrapper table td we-button.o_we_checkbox_wrapper {\n  margin: 0 0 0 0.3em;\n}\n\n#oe_snippets > .o_we_customize_panel we-list .o_we_user_value_widget {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n}\n\n#oe_snippets > .o_we_customize_panel we-multi.o_we_user_value_widget {\n  margin-top: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-multi.o_we_user_value_widget > div {\n  -webkit-flex-flow: row wrap; flex-flow: row wrap;\n}\n\n#oe_snippets > .o_we_customize_panel we-multi.o_we_user_value_widget > div > * {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_m2o_search {\n  background-color: #595964;\n  flex-grow: 1 !important;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n  margin-bottom: 1px;\n  border-radius: 4px;\n  padding: .25em .5em;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_m2o_search::before {\n  content: \"\\f002\";\n  font-size: 1.2em;\n  padding-right: .5em;\n  font-family: FontAwesome;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_m2o_search input {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n  color: inherit;\n  border: 1px solid transparent;\n  border-radius: 4px;\n  background-color: #2b2b33;\n  padding: 1px 6px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_m2o_search input:focus {\n  outline: none;\n  border-color: #01bad2;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_m2o_search input::placeholder {\n  color: #9d9d9d;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_m2o_search_more {\n  color: var(--o-cc1-btn-primary);\n  margin-top: 1px;\n  width: 100%;\n  cursor: pointer;\n  padding-left: 2em;\n  line-height: 20px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_m2o_create {\n  margin-top: 1px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_m2m we-list, #oe_snippets > .o_we_customize_panel .o_we_m2m we-list > div, #oe_snippets > .o_we_customize_panel .o_we_m2m we-list we-select {\n  margin-top: 0;\n  max-width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_m2m we-title, #oe_snippets > .o_we_customize_panel .o_we_m2m #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_m2m .oe-table-label {\n  align-self: flex-start;\n}\n\n#oe_snippets > .o_we_customize_panel we-row, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row {\n  position: relative;\n  margin-top: 8px;\n}\n\n#oe_snippets > .o_we_customize_panel we-row .o_we_user_value_widget, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_we_user_value_widget {\n  margin-top: 0;\n  min-width: 4em;\n}\n\n#oe_snippets > .o_we_customize_panel we-row we-button.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row we-button.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel we-row .o_we_so_color_palette.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_we_so_color_palette.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel we-row we-button-group.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row we-button-group.o_we_user_value_widget {\n  min-width: auto;\n}\n\n#oe_snippets > .o_we_customize_panel we-row.o_we_header_font_row > div, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_header_font_row.oe-table-row > div {\n  justify-content: space-between;\n}\n\n#oe_snippets > .o_we_customize_panel we-row.o_we_header_font_row > div we-select, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_header_font_row.oe-table-row > div we-select {\n  max-width: fit-content;\n  min-width: fit-content;\n  margin-right: 0px !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-row > div, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > div {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n}\n\n#oe_snippets > .o_we_customize_panel we-row > div > :not(.d-none) ~ *, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > div > :not(.d-none) ~ * {\n  margin-left: 3px;\n}\n\n#oe_snippets > .o_we_customize_panel we-row we-select.o_we_user_value_widget, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row we-select.o_we_user_value_widget {\n  position: static;\n}\n\n#oe_snippets > .o_we_customize_panel we-row.o_we_full_row > div, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_full_row.oe-table-row > div {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n}\n\n#oe_snippets > .o_we_customize_panel we-row.o_short_title we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_short_title.oe-table-row we-title, #oe_snippets > .o_we_customize_panel we-row.o_short_title #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row.o_short_title .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .o_short_title.oe-table-row .oe-table-label, #oe_snippets > .o_we_customize_panel we-row .o_short_title we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_short_title we-title, #oe_snippets > .o_we_customize_panel we-row .o_short_title #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row .o_short_title .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_short_title .oe-table-label {\n  width: unset !important;\n  padding-right: 0 !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-row.o_long_title we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_long_title.oe-table-row we-title, #oe_snippets > .o_we_customize_panel we-row.o_long_title #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row.o_long_title .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .o_long_title.oe-table-row .oe-table-label, #oe_snippets > .o_we_customize_panel we-row .o_long_title we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_long_title we-title, #oe_snippets > .o_we_customize_panel we-row .o_long_title #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row .o_long_title .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_long_title .oe-table-label {\n  width: fit-content !important;\n  padding-right: 10px !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-row.o_design_tab_title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_design_tab_title.oe-table-row {\n  margin-top: 15px;\n}\n\n#oe_snippets > .o_we_customize_panel we-row.o_design_tab_title we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_design_tab_title.oe-table-row we-title, #oe_snippets > .o_we_customize_panel we-row.o_design_tab_title #oe-table-options .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row.o_design_tab_title .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .o_design_tab_title.oe-table-row .oe-table-label {\n  font-weight: 600;\n}\n\n#oe_snippets > .o_we_customize_panel we-row .o_we_so_color_palette.o_we_user_value_widget + .o_we_user_value_widget:not(.o_we_so_color_palette), #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row .o_we_so_color_palette.o_we_user_value_widget + .o_we_user_value_widget:not(.o_we_so_color_palette) {\n  margin-left: 12px !important;\n}\n\n#oe_snippets > .o_we_customize_panel we-row:has(div > we-button-group + we-select.o_grid) we-button-group we-selection-items, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row:has(div > we-button-group + we-select.o_grid) we-button-group we-selection-items {\n  display: grid !important;\n  grid-template-columns: auto auto;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget, #oe_snippets > .o_we_customize_panel we-row, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  -webkit-flex-flow: row wrap; flex-flow: row wrap;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget > we-title, #oe_snippets > .o_we_customize_panel we-row > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_user_value_widget > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > .oe-table-label {\n  width: 100%;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget > div, #oe_snippets > .o_we_customize_panel we-row > div, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > div {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n  min-width: 0;\n  margin-top: 8px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget > div svg, #oe_snippets > .o_we_customize_panel we-row > div svg, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row > div svg {\n  margin: 0 auto;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget:not(.o_we_fw), #oe_snippets > .o_we_customize_panel we-row:not(.o_we_fw), #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row:not(.o_we_fw) {\n  -webkit-flex-flow: row nowrap; flex-flow: row nowrap;\n  align-items: center;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget:not(.o_we_fw):not(we-input):not(.o_we_so_color_palette), #oe_snippets > .o_we_customize_panel we-row:not(.o_we_fw):not(we-input):not(.o_we_so_color_palette), #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row:not(.o_we_fw):not(we-input):not(.o_we_so_color_palette) {\n  flex-grow: 1;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget:not(.o_we_fw) > we-title, #oe_snippets > .o_we_customize_panel we-row:not(.o_we_fw) > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row:not(.o_we_fw) > we-title, #oe_snippets > .o_we_customize_panel #oe-table-options .o_we_user_value_widget:not(.o_we_fw) > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options we-row:not(.o_we_fw) > .oe-table-label, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row:not(.o_we_fw) > .oe-table-label {\n  -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;\n  display: inline-block;\n  max-width: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  vertical-align: top;\n  width: 105.2px;\n  padding-right: 6px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_user_value_widget:not(.o_we_fw) > div, #oe_snippets > .o_we_customize_panel we-row:not(.o_we_fw) > div, #oe_snippets > .o_we_customize_panel #oe-table-options .oe-table-row:not(.o_we_fw) > div {\n  margin-top: 0;\n}\n\n#oe_snippets > .o_we_customize_panel we-collapse {\n  position: relative;\n  display: block;\n  padding-left: 15px;\n  padding-right: 10px;\n  margin-right: -10px;\n  margin-left: -15px;\n  border-top: 4px solid transparent;\n  padding-bottom: 4px;\n  margin-bottom: -4px;\n  background-clip: padding-box;\n}\n\n#oe_snippets > .o_we_customize_panel we-collapse > :first-child, #oe_snippets > .o_we_customize_panel we-collapse .o_we_collapse_toggler {\n  margin-top: 4px;\n}\n\n#oe_snippets > .o_we_customize_panel we-collapse we-toggler.o_we_collapse_toggler {\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: auto;\n  right: auto;\n  width: 15px;\n  height: 22px;\n  display: -webkit-box; display: -webkit-flex; display: flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0;\n  background: none;\n  border: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-collapse we-toggler.o_we_collapse_toggler::after {\n  content: '\\f0da';\n  position: static;\n  transform: none;\n}\n\n.o_rtl #oe_snippets > .o_we_customize_panel we-collapse we-toggler.o_we_collapse_toggler::after {\n  transform: scaleX(-1);\n}\n\n#oe_snippets > .o_we_customize_panel we-collapse we-toggler.o_we_collapse_toggler.active::after {\n  content: '\\f0d7';\n}\n\n#oe_snippets > .o_we_customize_panel we-collapse we-toggler.o_we_collapse_toggler.active + * {\n  background: none;\n  border: none;\n  box-shadow: none;\n}\n\n#oe_snippets > .o_we_customize_panel we-collapse.active we-collapse.active, #oe_snippets > .o_we_customize_panel we-collapse.active we-collapse.active we-toggler.o_we_collapse_toggler {\n  background-color: #3e3e46;\n}\n\n#oe_snippets > .o_we_customize_panel we-collapse.active, #oe_snippets > .o_we_customize_panel we-collapse.active we-collapse.active we-collapse.active, #oe_snippets > .o_we_customize_panel we-collapse.active we-collapse.active we-collapse.active .o_we_collapse_toggler {\n  background-color: #3e3e46;\n}\n\n#oe_snippets > .o_we_customize_panel we-collapse .o_we_collapse_toggler {\n  cursor: pointer;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_image_weight {\n  margin-left: 12px;\n}\n\n#oe_snippets > .o_we_customize_panel we-button + .o_we_image_weight {\n  margin-left: 6px;\n}\n\n#oe_snippets > .o_we_customize_panel .o_we_tag {\n  background-color: #000000;\n  white-space: nowrap;\n  padding: 1.5px 3px;\n  border-radius: 3px;\n  font-size: 0.85em;\n}\n\n#oe_snippets > .o_we_invisible_el_panel {\n  -webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;\n  max-height: 220px;\n  overflow-y: auto;\n  margin-top: auto;\n  padding: 10px;\n  background-color: #191922;\n  box-shadow: inset 0 1px 0 rgba(255, 255, 255, 0.2);\n}\n\n#oe_snippets > .o_we_invisible_el_panel .o_panel_header {\n  padding: 8px 0;\n}\n\n#oe_snippets > .o_we_invisible_el_panel .o_we_invisible_entry {\n  padding: 8px 6px;\n  cursor: pointer;\n}\n\n#oe_snippets > .o_we_invisible_el_panel .o_we_invisible_entry:hover {\n  background-color: #2b2b33;\n}\n\n#oe_snippets > .o_we_invisible_el_panel div.o_we_invisible_root_parent {\n  padding-bottom: 3px;\n}\n\n#oe_snippets > .o_we_invisible_el_panel ul {\n  list-style: none;\n  padding-inline-start: 15px;\n  margin-bottom: 5px;\n}\n\n#oe_snippets > .o_we_invisible_el_panel ul div.o_we_invisible_entry {\n  padding-top: 3px;\n  padding-bottom: 3px;\n}\n\n#oe_snippets.o_we_backdrop > .o_we_customize_panel {\n  -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;\n}\n\n#oe_snippets.o_we_backdrop > .o_we_customize_panel:not(:has(.o_we_select_pager.o_we_widget_opened))::after {\n  content: \"\";\n  position: -webkit-sticky;\n  position: sticky;\n  top: auto;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  display: block;\n  height: 100vh;\n  margin-top: -100vh;\n  pointer-events: none;\n  background: rgba(0, 0, 0, 0.2);\n}\n\n#oe_snippets.o_we_backdrop .o_we_widget_opened {\n  z-index: 1000;\n}\n\n.o_we_cc_preview_wrapper {\n  font-family: sans-serif !important;\n  font-size: 15px !important;\n  padding: 8px 8px 6.4px;\n}\n\n.o_we_cc_preview_wrapper > * {\n  margin-bottom: 0 !important;\n  line-height: 1 !important;\n}\n\n.o_we_color_combination_btn_text {\n  color: inherit !important;\n  font-family: inherit !important;\n  font-size: 0.8em !important;\n  margin-top: 0.5em !important;\n}\n\n.o_we_color_combination_btn_title {\n  margin-top: 0 !important;\n  font-size: 1.3em !important;\n}\n\n.o_we_color_combination_btn_btn {\n  padding: 0.2em 3px 0.3em !important;\n  border-radius: 2px !important;\n  font-size: 0.8em !important;\n}\n\n.o_we_border_preview {\n  display: inline-block;\n  width: 999px;\n  max-width: 100%;\n  margin-bottom: 2px;\n  border-width: 4px;\n  border-bottom: none !important;\n}\n\nwe-select.o_we_border_preview_aligned_select {\n  width: 60px;\n}\n\n#oe_snippets .colorpicker {\n  --bg: #3e3e46;\n  --text-rgb: 217, 217, 217;\n  --border-rgb: var(--text-rgb);\n  --tab-border-top: rgba(255, 255, 255, .2);\n  --tab-border-bottom: #191922;\n  --btn-color-active: inset 0 0 0 1px #3e3e46,\r\n                        inset 0 0 0 3px #01bad2,\r\n                        inset 0 0 0 4px white;\n}\n\n#oe_snippets .colorpicker .o_we_colorpicker_switch_pane_btn {\n  -webkit-box-flex: 0; -webkit-flex: 0 1 auto; flex: 0 1 auto;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_reset {\n  border: 0;\n  background-color: transparent;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_we_color_combination_btn {\n  float: none;\n  width: 100%;\n  padding: 0;\n  margin: 0;\n  border: 0;\n  background-color: transparent;\n  background-clip: padding-box;\n  border-top: 8px solid transparent;\n  border-bottom: 8px solid transparent;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_we_color_combination_btn + .o_we_color_combination_btn {\n  margin-top: -4px;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_we_color_combination_btn.selected > .o_we_cc_preview_wrapper {\n  box-shadow: 0 0 0 1px #40ad67 !important;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_we_color_combination_btn.selected .o_we_color_combination_btn_title::before {\n  content: \"\\f00c\";\n  margin-right: 8px;\n  font-size: 0.8em;\n  font-family: FontAwesome;\n  color: #40ad67;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_we_color_combination_btn .o_we_cc_preview_wrapper:after {\n  bottom: -1px;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor {\n  font-size: 12px;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_btn {\n  color: #ffffff;\n  background-color: #3e3e46;\n  float: none;\n  box-sizing: border-box;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor span.o_custom_gradient_input {\n  border: 1px solid black;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor span.o_custom_gradient_input input {\n  outline: none;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor span.o_custom_gradient_input:focus-within {\n  border-color: #01bad2;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_scale {\n  cursor: copy;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_custom_gradient_scale div {\n  height: 20px;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi {\n  display: grid;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range] {\n  pointer-events: none;\n  grid-column: 1/span 2;\n  grid-row: 3;\n  background: none;\n  -webkit-appearance: none; -moz-appearance: none; appearance: none;\n  cursor: ew-resize;\n}\n\n@supports (-moz-appearance: none) {\n  #oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range] {\n    margin-top: 2px;\n  }\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range]::-webkit-slider-thumb {\n  pointer-events: auto;\n  border: 1.5px solid rgba(255, 255, 255, 0.8);\n  background: currentColor;\n  -webkit-appearance: none; -moz-appearance: none; appearance: none;\n  box-shadow: 0px 0px 0px #000000;\n  height: 20px;\n  width: 12px;\n  border-radius: 5px;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range].active::-webkit-slider-thumb {\n  border-color: #01bad2;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range]::-moz-range-thumb {\n  pointer-events: auto;\n  border: 1.5px solid rgba(255, 255, 255, 0.8);\n  background: currentColor;\n  box-shadow: 0px 0px 0px #000000;\n  height: 18px;\n  width: 10px;\n  border-radius: 5px;\n  margin-top: 3px;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range].active::-moz-range-thumb {\n  border-color: #01bad2;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range]::-ms-thumb {\n  pointer-events: auto;\n  border: 1.5px solid rgba(255, 255, 255, 0.8);\n  background: currentColor;\n  box-shadow: 0px 0px 0px #000000;\n  height: 20px;\n  width: 12px;\n  border-radius: 5px;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_slider_multi input[type=range].active::-ms-thumb {\n  border-color: #01bad2;\n}\n\n#oe_snippets .colorpicker .o_colorpicker_sections .o_custom_gradient_editor .o_remove_color {\n  font-size: 14px !important;\n  text-align: center !important;\n  padding: 0;\n}\n\n@keyframes dropZoneInsert {\n  to {\n    box-shadow: inset 0 0 30px 0 rgba(1, 186, 210, 0.5);\n  }\n}\n\n.oe_drop_zone {\n  background: rgba(1, 186, 210, 0.5);\n  animation: dropZoneInsert 1s linear 0s infinite alternate;\n}\n\n.oe_drop_zone.oe_insert {\n  position: relative;\n  width: 100%;\n  border-radius: 0.3rem;\n  outline: 2px dashed #01bad2;\n  outline-offset: -2px;\n  z-index: 1040;\n}\n\n.oe_drop_zone:not(.oe_grid_zone).oe_insert {\n  min-width: 30px;\n  height: 30px;\n  min-height: 30px;\n  margin: -15px 0;\n  padding: 0;\n}\n\n.oe_drop_zone:not(.oe_grid_zone).oe_insert.oe_vertical {\n  width: 30px;\n  float: left;\n  margin: 0 -15px;\n}\n\n.oe_drop_zone:not(.oe_grid_zone).oe_drop_zone_danger {\n  background-color: rgba(230, 88, 108, 0.15);\n  color: #e6586c;\n  border-color: #e6586c;\n}\n\n#oe_manipulators {\n  position: relative;\n  z-index: 1040;\n  pointer-events: none;\n}\n\n#oe_manipulators .oe_overlay {\n  position: absolute;\n  top: auto;\n  left: auto;\n  bottom: auto;\n  right: auto;\n  display: none;\n  border-color: #01bad2;\n  background: transparent;\n  text-align: center;\n  font-size: 16px;\n  transition: opacity 400ms linear 0s;\n}\n\n#oe_manipulators .oe_overlay.o_overlay_hidden {\n  opacity: 0 !important;\n  transition: none;\n}\n\n#oe_manipulators .oe_overlay.oe_active {\n  display: block;\n  z-index: 1;\n}\n\n#oe_manipulators .oe_overlay > .o_handles {\n  position: absolute;\n  top: -100000px;\n  left: 0;\n  bottom: auto;\n  right: 0;\n  border-color: inherit;\n  pointer-events: auto;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle {\n  position: absolute;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_side_y {\n  height: 14px;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_side_x {\n  width: 14px;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.w {\n  inset: 100000px auto -100000px 1px;\n  transform: translateX(-50%);\n  cursor: ew-resize;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.e {\n  inset: 100000px 1px -100000px auto;\n  transform: translateX(50%);\n  cursor: ew-resize;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.n {\n  inset: 100000px 0 auto 0;\n  cursor: ns-resize;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.n.o_grid_handle {\n  transform: translateY(-50%);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.n.o_grid_handle:before {\n  transform: translateY(1px);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.s {\n  inset: auto 0 -100000px 0;\n  cursor: ns-resize;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.s.o_grid_handle {\n  transform: translateY(50%);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.s.o_grid_handle:before {\n  transform: translateY(-1px);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.ne {\n  inset: 100001px 1px auto auto;\n  transform: translate(50%, -50%);\n  cursor: nesw-resize;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.se {\n  inset: auto 1px -99999px auto;\n  transform: translate(50%, 50%);\n  cursor: nwse-resize;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.sw {\n  inset: auto auto -99999px 1px;\n  transform: translate(-50%, 50%);\n  cursor: nesw-resize;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.nw {\n  inset: 100001px auto auto 1px;\n  transform: translate(-50%, -50%);\n  cursor: nwse-resize;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle .o_handle_indicator {\n  position: absolute;\n  inset: -7px;\n  display: block;\n  width: 14px;\n  height: 14px;\n  margin: auto;\n  border: solid 2px #01bad2;\n  border-radius: 14px;\n  background: #FFFFFF;\n  outline: 3px solid #FFFFFF;\n  outline-offset: -7px;\n  transition: all 0.2s ease-in-out;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle .o_handle_indicator::before {\n  content: '';\n  position: absolute;\n  inset: -14px;\n  display: block;\n  border-radius: inherit;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_column_handle.o_side_y {\n  background-color: rgba(1, 186, 210, 0.1);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_column_handle.o_side_y::after {\n  content: '';\n  position: absolute;\n  height: 14px;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_column_handle.o_side_y.n {\n  border-bottom: dashed 1px rgba(1, 186, 210, 0.5);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_column_handle.o_side_y.n::after {\n  inset: 0 0 auto 0;\n  transform: translateY(-50%);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_column_handle.o_side_y.s {\n  border-top: dashed 1px rgba(1, 186, 210, 0.5);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_column_handle.o_side_y.s::after {\n  inset: auto 0 0 0;\n  transform: translateY(50%);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_side::before {\n  content: '';\n  position: absolute;\n  inset: 0;\n  background: #01bad2;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_side.o_side_x::before {\n  width: 2px;\n  margin: 0 auto;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_side.o_side_y::before {\n  height: 2px;\n  margin: auto 0;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_side.o_column_handle.n::before {\n  margin: 0 auto auto;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.o_side.o_column_handle.s::before {\n  margin: auto auto 0;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.readonly {\n  cursor: default;\n  pointer-events: none;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.readonly.o_column_handle.o_side_y {\n  border: none;\n  background: none;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_handle.readonly::after, #oe_manipulators .oe_overlay > .o_handles > .o_handle.readonly .o_handle_indicator {\n  display: none;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap {\n  position: absolute;\n  top: 100000px;\n  left: 50%;\n  bottom: auto;\n  right: auto;\n  transform: translate(-50%, -150%);\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap.o_we_hidden_overlay_options {\n  display: none;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options {\n  display: -webkit-box; display: -webkit-flex; display: flex;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button {\n  margin: 0 1px 0;\n  min-width: 22px;\n  padding: 0 3px;\n  color: #FFFFFF;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_move_options > *.oe_snippet_remove, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > *:not(span).oe_snippet_remove, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap .o_overlay_edit_options > span > *.oe_snippet_remove, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > we-button.oe_snippet_remove {\n  background-color: #a05968;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > .o_move_handle {\n  cursor: move;\n  width: 30px;\n  height: 22px;\n  background-position: center;\n  background-repeat: no-repeat;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > .o_send_back {\n  width: 30px;\n  height: 22px;\n  background-image: url(\"/web_editor/static/src/img/snippets_options/bring-backward.svg\");\n  background-position: center;\n  background-repeat: no-repeat;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap > .o_overlay_move_options > .o_bring_front {\n  width: 30px;\n  height: 22px;\n  background-image: url(\"/web_editor/static/src/img/snippets_options/bring-forward.svg\");\n  background-position: center;\n  background-repeat: no-repeat;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *:not(span), #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button {\n  opacity: 0.6;\n}\n\n#oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *:focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_move_options > *.focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *:not(span):hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *:not(span):focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover .o_overlay_edit_options > *:not(span).focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button:hover, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button:focus, #oe_manipulators .oe_overlay > .o_handles > .o_overlay_options_wrap:hover > we-button.focus {\n  opacity: 1;\n}\n\n#oe_manipulators .oe_overlay.o_top_cover > .o_handles > .o_overlay_options_wrap {\n  top: auto;\n  bottom: -100000px;\n  transform: translate(-50%, 110%);\n}\n\n#oe_manipulators .oe_overlay.o_we_overlay_preview {\n  pointer-events: none;\n}\n\n#oe_manipulators .oe_overlay.o_we_overlay_preview > .o_handles > .o_handle::after, #oe_manipulators .oe_overlay.o_we_overlay_preview > .o_handles .o_overlay_options_wrap {\n  display: none;\n}\n\n#oe_manipulators .oe_overlay.o_we_background_position_overlay {\n  background-color: rgba(0, 0, 0, 0.7);\n  pointer-events: auto;\n}\n\n#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_content {\n  cursor: url(/web/static/img/openhand.cur), grab;\n}\n\n#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_content .o_we_grabbing {\n  cursor: grabbing;\n}\n\n#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary {\n  color: #FFFFFF;\n  background-color: #71639e;\n  border-color: #71639e;\n}\n\n#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:hover {\n  color: #FFFFFF;\n  background-color: #605486;\n  border-color: #5a4f7e;\n}\n\n.btn-check:focus + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:focus {\n  color: #FFFFFF;\n  background-color: #605486;\n  border-color: #5a4f7e;\n  box-shadow: 0 0 0 0.25rem rgba(134, 122, 173, 0.5);\n}\n\n.btn-check:checked + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary, .btn-check:active + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:active, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary.active, .show > #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary.dropdown-toggle {\n  color: #FFFFFF;\n  background-color: #5a4f7e;\n  border-color: #554a77;\n}\n\n.btn-check:checked + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:focus, .btn-check:active + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:focus, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:active:focus, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary.active:focus, .show > #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.25rem rgba(134, 122, 173, 0.5);\n}\n\n#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary:disabled, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-primary.disabled {\n  color: #FFFFFF;\n  background-color: #71639e;\n  border-color: #71639e;\n}\n\n#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary {\n  color: #000000;\n  background-color: #e6586c;\n  border-color: #e6586c;\n}\n\n#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:hover {\n  color: #000000;\n  background-color: #ea7182;\n  border-color: #e9697b;\n}\n\n.btn-check:focus + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:focus {\n  color: #000000;\n  background-color: #ea7182;\n  border-color: #e9697b;\n  box-shadow: 0 0 0 0.25rem rgba(196, 75, 92, 0.5);\n}\n\n.btn-check:checked + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary, .btn-check:active + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:active, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary.active, .show > #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary.dropdown-toggle {\n  color: #000000;\n  background-color: #eb7989;\n  border-color: #e9697b;\n}\n\n.btn-check:checked + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:focus, .btn-check:active + #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:focus, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:active:focus, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary.active:focus, .show > #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary.dropdown-toggle:focus {\n  box-shadow: 0 0 0 0.25rem rgba(196, 75, 92, 0.5);\n}\n\n#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary:disabled, #oe_manipulators .oe_overlay.o_we_background_position_overlay .o_we_overlay_buttons .btn-secondary.disabled {\n  color: #000000;\n  background-color: #e6586c;\n  border-color: #e6586c;\n}\n\n#oe_manipulators .oe_overlay.o_we_background_position_overlay .o_overlay_background > * {\n  display: block !important;\n  top: 0 !important;\n  right: 0 !important;\n  bottom: 0 !important;\n  left: 0 !important;\n  transform: none !important;\n  max-width: unset !important;\n  max-height: unset !important;\n  z-index: 0 !important;\n}\n\n#oe_manipulators .oe_overlay.o_handlers_idle .o_handle:hover .o_handle_indicator, #oe_manipulators .oe_overlay .o_handle:active .o_handle_indicator {\n  outline-color: #01bad2;\n}\n\n#oe_manipulators .oe_overlay.o_handlers_idle .o_corner_handle:hover .o_handle_indicator, #oe_manipulators .oe_overlay .o_corner_handle:active .o_handle_indicator {\n  transform: scale(1.25);\n}\n\n#oe_manipulators .oe_overlay.o_handlers_idle .o_column_handle.o_side_y:hover, #oe_manipulators .oe_overlay .o_column_handle.o_side_y:active {\n  background: repeating-linear-gradient(45deg, rgba(1, 186, 210, 0.1), rgba(1, 186, 210, 0.1) 5px, rgba(1, 164, 185, 0.25) 5px, rgba(1, 164, 185, 0.25) 10px);\n}\n\n#oe_manipulators .oe_overlay.o_handlers_idle .o_side_x:hover::before, #oe_manipulators .oe_overlay .o_side_x:active::before {\n  width: 4px;\n}\n\n#oe_manipulators .oe_overlay.o_handlers_idle .o_side_x:hover .o_handle_indicator, #oe_manipulators .oe_overlay .o_side_x:active .o_handle_indicator {\n  height: 28px;\n}\n\n#oe_manipulators .oe_overlay.o_handlers_idle .o_side_y:hover::before, #oe_manipulators .oe_overlay .o_side_y:active::before {\n  height: 4px;\n}\n\n#oe_manipulators .oe_overlay.o_handlers_idle .o_side_y:hover .o_handle_indicator, #oe_manipulators .oe_overlay .o_side_y:active .o_handle_indicator {\n  width: 28px;\n}\n\n#oe_manipulators .o_edit_menu_popover {\n  pointer-events: auto;\n}\n\n.oe_overlay.o_draggable_dragging .o_handles {\n  display: none;\n}\n\n.nesw-resize-important * {\n  cursor: nesw-resize !important;\n}\n\n.nwse-resize-important * {\n  cursor: nwse-resize !important;\n}\n\n.ns-resize-important * {\n  cursor: ns-resize !important;\n}\n\n.ew-resize-important * {\n  cursor: ew-resize !important;\n}\n\n.move-important * {\n  cursor: move !important;\n}\n\n.dropdown-menu label .o_switch {\n  margin: 0;\n  padding: 2px 0;\n}\n\n.text-input-group {\n  position: relative;\n  margin-bottom: 45px;\n  /* LABEL ======================================= */\n  /* active state */\n  /* BOTTOM BARS ================================= */\n  /* active state */\n  /* HIGHLIGHTER ================================== */\n  /* active state */\n}\n\n.text-input-group input {\n  font-size: 18px;\n  padding: 10px 10px 10px 5px;\n  display: block;\n  width: 300px;\n  border: none;\n  border-bottom: 1px solid #757575;\n}\n\n.text-input-group input:focus {\n  outline: none;\n}\n\n.text-input-group label {\n  color: #999;\n  font-size: 18px;\n  font-weight: normal;\n  position: absolute;\n  top: 10px;\n  left: 5px;\n  bottom: auto;\n  right: auto;\n  pointer-events: none;\n  transition: 0.2s ease all;\n}\n\n.text-input-group input:focus ~ label,\n.text-input-group input:valid ~ label {\n  top: -20px;\n  font-size: 14px;\n  color: #5264AE;\n}\n\n.text-input-group .bar {\n  position: relative;\n  display: block;\n  width: 300px;\n}\n\n.text-input-group .bar:before,\n.text-input-group .bar:after {\n  content: '';\n  height: 2px;\n  width: 0;\n  bottom: 1px;\n  position: absolute;\n  top: auto;\n  left: auto;\n  bottom: auto;\n  right: auto;\n  background: #5264AE;\n  transition: 0.2s ease all;\n}\n\n.text-input-group .bar:before {\n  left: 50%;\n}\n\n.text-input-group .bar:after {\n  right: 50%;\n}\n\n.text-input-group input:focus ~ .bar:before,\n.text-input-group input:focus ~ .bar:after {\n  width: 50%;\n}\n\n.text-input-group .highlight {\n  position: absolute;\n  top: 25%;\n  left: 0;\n  bottom: auto;\n  right: auto;\n  height: 60%;\n  width: 100px;\n  pointer-events: none;\n  opacity: 0.5;\n}\n\n.text-input-group input:focus ~ .highlight {\n  animation: inputHighlighter 0.3s ease;\n}\n\n.oe_snippet_body {\n  opacity: 0;\n  animation: fadeInDownSmall 700ms forwards;\n}\n\n.o_container_preview {\n  outline: 2px dashed #01bad2;\n}\n\n.o_we_shape_animated_label {\n  position: absolute;\n  top: 0;\n  left: auto;\n  bottom: auto;\n  right: 0;\n  padding: 0 4px;\n  background: #40ad67;\n  color: white;\n}\n\n.o_we_shape_animated_label > span {\n  display: inline-block;\n  max-width: 100%;\n  white-space: nowrap;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  vertical-align: top;\n  max-width: 0;\n}\n\nwe-button:hover .o_we_shape_animated_label i {\n  padding-right: 4px;\n}\n\nwe-button:hover .o_we_shape_animated_label > span {\n  max-width: 144px;\n  transition: max-width 0.5s ease 0s;\n}\n\n.o_we_ui_loading {\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  z-index: 1041;\n  background-color: rgba(0, 0, 0, 0.2);\n  color: #FFFFFF;\n}\n\n#oe_manipulators > .o_we_ui_loading {\n  position: fixed;\n}\n\n.o_we_force_no_transition {\n  transition: none !important;\n}\n\nwe-button.o_grid {\n  min-width: fit-content;\n  padding-left: 4.5px !important;\n  padding-right: 4.5px !important;\n}\n\nwe-select.o_grid we-toggler {\n  width: fit-content !important;\n}\n\n.o_we_background_grid {\n  padding: 0 !important;\n}\n\n.o_we_background_grid .o_we_cell {\n  fill: #FFFFFF;\n  fill-opacity: .1;\n  stroke: #000000;\n  stroke-opacity: .2;\n  stroke-width: 1px;\n  filter: drop-shadow(-1px -1px 0px rgba(255, 255, 255, 0.3));\n}\n\n.o_we_background_grid.o_we_grid_preview {\n  pointer-events: none;\n}\n\n@media (max-width: 991.98px) {\n  .o_we_background_grid.o_we_grid_preview {\n    height: 0;\n  }\n}\n\n.o_we_background_grid.o_we_grid_preview .o_we_cell {\n  animation: gridPreview 2s 0.5s;\n}\n\n@keyframes gridPreview {\n  to {\n    fill-opacity: 0;\n    stroke-opacity: 0;\n  }\n}\n\n.o_we_drag_helper {\n  padding: 0;\n  border: 4px solid #01bad2;\n  border-radius: 4px;\n}\n\n@keyframes highlightPadding {\n  from {\n    border: solid rgba(1, 186, 210, 0.2);\n    border-width: var(--grid-item-padding-y) var(--grid-item-padding-x);\n  }\n  to {\n    border: solid rgba(1, 186, 210, 0);\n    border-width: var(--grid-item-padding-y) var(--grid-item-padding-x);\n  }\n}\n\n.o_we_padding_highlight.o_grid_item {\n  position: relative;\n}\n\n.o_we_padding_highlight.o_grid_item::after {\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: 0;\n  bottom: 0;\n  right: 0;\n  animation: highlightPadding 2s;\n  pointer-events: none;\n}"], "file": "/web/assets/03fc1b6/web_editor.backend_assets_wysiwyg.css", "sourceRoot": "../../../"}