#!/usr/bin/env python
"""
Create sample data for the ERP system
"""
import os
import sys
import django
from datetime import date, datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from accounting.models import (
    ResCompany, ResPartner, AccountAccount, AccountJournal,
    AccountTax, AccountMove, AccountMoveLine, AccountPayment,
    AccountPaymentTerm, AccountFiscalPosition, AccountAnalyticAccount,
    ProductCategory, ProductTemplate, ProductProduct,
    IrSequence, ResCountry, ResCurrency, CrossoveredBudget,
    AccountBankStatement, AccountAsset, AccountAssetCategory
)
from sales.models import SaleOrder, SaleOrderLine
from purchase.models import PurchaseOrder, PurchaseOrderLine
from inventory.models import StockLocation, StockWarehouse, StockPickingType, StockQuant
from hr.models import <PERSON>r<PERSON><PERSON><PERSON><PERSON>, <PERSON>r<PERSON><PERSON>, Hr<PERSON><PERSON>loyee, <PERSON>r<PERSON>ontractType, HrContract, HrLeaveType
from crm.models import CrmTeam, CrmStage, CrmLead, CrmLostReason
from project.models import ProjectProject, ProjectTaskType, ProjectTask, ProjectMilestone
from analytics.models import AnalyticsDashboard, AnalyticsKpi, AnalyticsReport
from manufacturing.models import (
    MrpWorkcenter, MrpRouting, MrpRoutingWorkcenter, MrpBom, MrpBomLine,
    MrpProduction, QualityPoint
)
from financial_reports.models import (
    DateRange, AccountAgeReportConfiguration, AccountAgeReportConfigurationLine,
    TrialBalanceReport, GeneralLedgerReport, AgedPartnerBalanceReport,
    OpenItemsReport, JournalLedgerReport, VatReport
)

def create_sample_data():
    print("Creating sample data...")
    
    # Create company
    company, created = ResCompany.objects.get_or_create(
        name="DataiCraft Solutions",
        defaults={
            'legal_name': 'DataiCraft Solutions Pvt Ltd',
            'street': 'Main Street 123',
            'city': 'Karachi',
            'country_id': 'PK',
            'phone': '+92-21-1234567',
            'email': '<EMAIL>',
            'vat': 'PK-*********',
            'currency_id': 'PKR',
        }
    )
    print(f"Company: {company.name} {'created' if created else 'already exists'}")
    
    # Create sample accounts
    accounts_data = [
        {'code': '1001', 'name': 'Cash in Hand', 'account_type': 'asset_cash'},
        {'code': '1002', 'name': 'Bank Account', 'account_type': 'asset_cash'},
        {'code': '1100', 'name': 'Accounts Receivable', 'account_type': 'asset_receivable'},
        {'code': '1200', 'name': 'Inventory', 'account_type': 'asset_current'},
        {'code': '1500', 'name': 'Office Equipment', 'account_type': 'asset_fixed'},
        {'code': '2001', 'name': 'Accounts Payable', 'account_type': 'liability_payable'},
        {'code': '2100', 'name': 'Short Term Loans', 'account_type': 'liability_current'},
        {'code': '3001', 'name': 'Owner Equity', 'account_type': 'equity'},
        {'code': '4001', 'name': 'Sales Revenue', 'account_type': 'income'},
        {'code': '4002', 'name': 'Service Revenue', 'account_type': 'income'},
        {'code': '5001', 'name': 'Cost of Goods Sold', 'account_type': 'expense_direct_cost'},
        {'code': '6001', 'name': 'Office Rent', 'account_type': 'expense'},
        {'code': '6002', 'name': 'Utilities', 'account_type': 'expense'},
        {'code': '6003', 'name': 'Marketing Expenses', 'account_type': 'expense'},
    ]
    
    for acc_data in accounts_data:
        account, created = AccountAccount.objects.get_or_create(
            code=acc_data['code'],
            company_id=company,
            defaults={
                'name': acc_data['name'],
                'account_type': acc_data['account_type'],
                'reconcile': acc_data['account_type'] in ['asset_receivable', 'liability_payable'],
            }
        )
        print(f"Account: {account.code} - {account.name} {'created' if created else 'already exists'}")
    
    # Create journals
    journals_data = [
        {'code': 'SALE', 'name': 'Sales Journal', 'type': 'sale', 'default_account': '4001'},
        {'code': 'PURCH', 'name': 'Purchase Journal', 'type': 'purchase', 'default_account': '5001'},
        {'code': 'CASH', 'name': 'Cash Journal', 'type': 'cash', 'default_account': '1001'},
        {'code': 'BANK', 'name': 'Bank Journal', 'type': 'bank', 'default_account': '1002'},
        {'code': 'MISC', 'name': 'Miscellaneous Journal', 'type': 'general', 'default_account': '1001'},
    ]
    
    for journal_data in journals_data:
        default_account = AccountAccount.objects.get(code=journal_data['default_account'], company_id=company)
        journal, created = AccountJournal.objects.get_or_create(
            code=journal_data['code'],
            company_id=company,
            defaults={
                'name': journal_data['name'],
                'type': journal_data['type'],
                'default_account_id': default_account,
            }
        )
        print(f"Journal: {journal.code} - {journal.name} {'created' if created else 'already exists'}")
    
    # Create sample partners (customers and vendors)
    customers_data = [
        {'name': 'ABC Company Ltd.', 'email': '<EMAIL>', 'phone': '+92-300-1234567', 'city': 'Karachi', 'customer_rank': 1},
        {'name': 'XYZ Corporation', 'email': '<EMAIL>', 'phone': '+92-321-7654321', 'city': 'Lahore', 'customer_rank': 1},
        {'name': 'Tech Solutions Inc.', 'email': '<EMAIL>', 'phone': '+92-42-9876543', 'city': 'Islamabad', 'customer_rank': 1},
    ]
    
    for customer_data in customers_data:
        partner, created = ResPartner.objects.get_or_create(
            name=customer_data['name'],
            company_id=company,
            defaults={
                'email': customer_data['email'],
                'phone': customer_data['phone'],
                'city': customer_data['city'],
                'country_id': 'PK',
                'customer_rank': customer_data['customer_rank'],
                'supplier_rank': 0,
            }
        )
        print(f"Customer: {partner.name} {'created' if created else 'already exists'}")
    
    vendors_data = [
        {'name': 'Office Supplies Co.', 'email': '<EMAIL>', 'phone': '+92-42-1234567', 'city': 'Lahore', 'supplier_rank': 1},
        {'name': 'Tech Hardware Ltd.', 'email': '<EMAIL>', 'phone': '+92-21-7654321', 'city': 'Karachi', 'supplier_rank': 1},
        {'name': 'Utility Services', 'email': '<EMAIL>', 'phone': '+92-51-9876543', 'city': 'Islamabad', 'supplier_rank': 1},
    ]
    
    for vendor_data in vendors_data:
        partner, created = ResPartner.objects.get_or_create(
            name=vendor_data['name'],
            company_id=company,
            defaults={
                'email': vendor_data['email'],
                'phone': vendor_data['phone'],
                'city': vendor_data['city'],
                'country_id': 'PK',
                'customer_rank': 0,
                'supplier_rank': vendor_data['supplier_rank'],
            }
        )
        print(f"Vendor: {partner.name} {'created' if created else 'already exists'}")

    # Create sample taxes
    taxes_data = [
        {'name': 'GST 17%', 'type_tax_use': 'sale', 'amount': 17.0, 'description': 'GST17'},
        {'name': 'GST 5%', 'type_tax_use': 'sale', 'amount': 5.0, 'description': 'GST5'},
        {'name': 'Purchase GST 17%', 'type_tax_use': 'purchase', 'amount': 17.0, 'description': 'PGST17'},
        {'name': 'Withholding Tax 1%', 'type_tax_use': 'purchase', 'amount': 1.0, 'description': 'WHT1'},
    ]

    for tax_data in taxes_data:
        tax, created = AccountTax.objects.get_or_create(
            name=tax_data['name'],
            company_id=company,
            defaults={
                'type_tax_use': tax_data['type_tax_use'],
                'amount': tax_data['amount'],
                'description': tax_data['description'],
            }
        )
        print(f"Tax: {tax.name} {'created' if created else 'already exists'}")

    # Create payment terms
    payment_terms_data = [
        {'name': 'Immediate Payment', 'note': 'Payment due immediately'},
        {'name': '30 Days', 'note': 'Payment due in 30 days'},
        {'name': '60 Days', 'note': 'Payment due in 60 days'},
    ]

    for term_data in payment_terms_data:
        term, created = AccountPaymentTerm.objects.get_or_create(
            name=term_data['name'],
            company_id=company,
            defaults={
                'note': term_data['note'],
            }
        )
        print(f"Payment Term: {term.name} {'created' if created else 'already exists'}")

    # Create analytic accounts
    analytic_data = [
        {'name': 'Marketing Campaign', 'code': 'MKT001'},
        {'name': 'R&D Project', 'code': 'RND001'},
        {'name': 'Office Operations', 'code': 'OPS001'},
    ]

    for analytic_data_item in analytic_data:
        analytic, created = AccountAnalyticAccount.objects.get_or_create(
            name=analytic_data_item['name'],
            company_id=company,
            defaults={
                'code': analytic_data_item['code'],
            }
        )
        print(f"Analytic Account: {analytic.name} {'created' if created else 'already exists'}")

    # Create sales teams
    from django.contrib.auth.models import User
    admin_user = User.objects.get(username='admin')

    sales_teams_data = [
        {'name': 'Direct Sales', 'user_id': admin_user, 'invoiced_target': 50000},
        {'name': 'Online Sales', 'user_id': admin_user, 'invoiced_target': 30000},
        {'name': 'Channel Partners', 'user_id': admin_user, 'invoiced_target': 20000},
    ]

    for team_data in sales_teams_data:
        team, created = CrmTeam.objects.get_or_create(
            name=team_data['name'],
            company_id=company,
            defaults={
                'user_id': team_data['user_id'],
                'invoiced_target': team_data['invoiced_target'],
            }
        )
        print(f"CRM Team: {team.name} {'created' if created else 'already exists'}")

    # Create sample sales orders
    customers = ResPartner.objects.filter(customer_rank__gt=0)
    products = ProductProduct.objects.all()[:3]  # Get first 3 products
    direct_sales_team = CrmTeam.objects.get(name='Direct Sales')

    if customers.exists() and products.exists():
        for i, customer in enumerate(customers[:2]):  # Create 2 sample orders
            order, created = SaleOrder.objects.get_or_create(
                name=f'SO{2024}{i+1:03d}',
                defaults={
                    'partner_id': customer,
                    'partner_invoice_id': customer,
                    'partner_shipping_id': customer,
                    'user_id': admin_user,
                    'team_id': direct_sales_team.id,
                    'state': 'draft',
                    'company_id': company,
                }
            )

            if created:
                # Add order lines
                for j, product in enumerate(products):
                    SaleOrderLine.objects.create(
                        order_id=order,
                        product_id=product,
                        product_template_id=product.product_tmpl_id,
                        name=product.name,
                        product_uom_qty=j+1,
                        price_unit=product.list_price,
                        company_id=company,
                    )

                print(f"Sales Order: {order.name} created with {order.order_line.count()} lines")

    # Create sample purchase orders
    vendors = ResPartner.objects.filter(supplier_rank__gt=0)

    if vendors.exists() and products.exists():
        for i, vendor in enumerate(vendors[:2]):  # Create 2 sample purchase orders
            po, created = PurchaseOrder.objects.get_or_create(
                name=f'PO{2024}{i+1:03d}',
                defaults={
                    'partner_id': vendor,
                    'user_id': admin_user,
                    'state': 'draft',
                    'company_id': company,
                    'date_planned': datetime.now(),
                }
            )

            if created:
                # Add order lines
                for j, product in enumerate(products):
                    PurchaseOrderLine.objects.create(
                        order_id=po,
                        product_id=product,
                        product_template_id=product.product_tmpl_id,
                        name=product.name,
                        product_qty=j+2,  # Different quantities than sales
                        price_unit=product.standard_price,
                        date_planned=datetime.now(),
                        company_id=company,
                    )

                # Compute amounts
                po._compute_amounts()
                print(f"Purchase Order: {po.name} created with {po.order_line.count()} lines")

    # Create inventory structure
    print("\n📦 Creating inventory structure...")

    # First create basic locations without warehouse reference
    locations_data = [
        {'name': 'Physical Locations', 'usage': 'view', 'parent': None},
        {'name': 'Stock', 'usage': 'internal', 'parent': 'Physical Locations'},
        {'name': 'Input', 'usage': 'internal', 'parent': 'Physical Locations'},
        {'name': 'Output', 'usage': 'internal', 'parent': 'Physical Locations'},
        {'name': 'Vendors', 'usage': 'supplier', 'parent': None},
        {'name': 'Customers', 'usage': 'customer', 'parent': None},
        {'name': 'Inventory Loss', 'usage': 'inventory', 'parent': None},
    ]

    location_objects = {}
    for loc_data in locations_data:
        parent_location = location_objects.get(loc_data['parent']) if loc_data['parent'] else None

        location, created = StockLocation.objects.get_or_create(
            name=loc_data['name'],
            company_id=company,
            defaults={
                'usage': loc_data['usage'],
                'location_id': parent_location,
            }
        )
        location_objects[loc_data['name']] = location
        print(f"Location: {location.name} ({'created' if created else 'already exists'})")

    # Now create warehouse with proper location references
    main_warehouse, created = StockWarehouse.objects.get_or_create(
        code='WH01',
        defaults={
            'name': 'Main Warehouse',
            'view_location_id': location_objects['Physical Locations'],
            'lot_stock_id': location_objects['Stock'],
            'wh_input_stock_loc_id': location_objects['Input'],
            'wh_output_stock_loc_id': location_objects['Output'],
            'wh_qc_stock_loc_id': location_objects['Stock'],  # Same as stock for simplicity
            'wh_pack_stock_loc_id': location_objects['Output'],  # Same as output
            'company_id': company,
        }
    )
    print(f"Warehouse: {main_warehouse.name} {'created' if created else 'already exists'}")

    # Update internal locations to reference the warehouse
    for loc_name in ['Stock', 'Input', 'Output']:
        location = location_objects[loc_name]
        if not location.warehouse_id:
            location.warehouse_id = main_warehouse
            location.save()

    # Create picking types
    picking_types_data = [
        {'name': 'Receipts', 'code': 'incoming', 'sequence_code': 'IN'},
        {'name': 'Delivery Orders', 'code': 'outgoing', 'sequence_code': 'OUT'},
        {'name': 'Internal Transfers', 'code': 'internal', 'sequence_code': 'INT'},
    ]

    for pt_data in picking_types_data:
        picking_type, created = StockPickingType.objects.get_or_create(
            name=pt_data['name'],
            warehouse_id=main_warehouse,
            defaults={
                'code': pt_data['code'],
                'sequence_code': pt_data['sequence_code'],
                'default_location_src_id': location_objects['Vendors'] if pt_data['code'] == 'incoming' else location_objects['Stock'],
                'default_location_dest_id': location_objects['Stock'] if pt_data['code'] == 'incoming' else location_objects['Customers'],
                'company_id': company,
            }
        )
        print(f"Picking Type: {picking_type.name} {'created' if created else 'already exists'}")

    # Create initial stock for products (if any exist)
    if products.exists():
        stock_location = location_objects['Stock']
        for product in products:
            quant, created = StockQuant.objects.get_or_create(
                product_id=product,
                location_id=stock_location,
                defaults={
                    'quantity': 100,  # Initial stock
                    'company_id': company,
                }
            )
            if created:
                print(f"Stock: {product.name} - {quant.quantity} units in {stock_location.name}")

    # Create HR structure
    print("\n👥 Creating HR structure...")

    # Create departments
    departments_data = [
        {'name': 'Administration', 'parent': None},
        {'name': 'Sales & Marketing', 'parent': None},
        {'name': 'Sales', 'parent': 'Sales & Marketing'},
        {'name': 'Marketing', 'parent': 'Sales & Marketing'},
        {'name': 'Research & Development', 'parent': None},
        {'name': 'Operations', 'parent': None},
        {'name': 'Human Resources', 'parent': None},
        {'name': 'Finance', 'parent': None},
    ]

    department_objects = {}
    for dept_data in departments_data:
        parent_dept = department_objects.get(dept_data['parent']) if dept_data['parent'] else None

        department, created = HrDepartment.objects.get_or_create(
            name=dept_data['name'],
            company_id=company,
            defaults={
                'parent_id': parent_dept,
            }
        )
        department_objects[dept_data['name']] = department
        print(f"Department: {department.name} {'created' if created else 'already exists'}")

    # Create contract types
    contract_types_data = [
        'Full-time Employee',
        'Part-time Employee',
        'Contractor',
        'Intern',
        'Consultant',
    ]

    for ct_name in contract_types_data:
        contract_type, created = HrContractType.objects.get_or_create(
            name=ct_name,
            defaults={'sequence': contract_types_data.index(ct_name) + 1}
        )
        print(f"Contract Type: {contract_type.name} {'created' if created else 'already exists'}")

    # Create job positions
    jobs_data = [
        {'name': 'Chief Executive Officer', 'department': 'Administration'},
        {'name': 'Sales Manager', 'department': 'Sales'},
        {'name': 'Sales Representative', 'department': 'Sales'},
        {'name': 'Marketing Manager', 'department': 'Marketing'},
        {'name': 'Software Developer', 'department': 'Research & Development'},
        {'name': 'HR Manager', 'department': 'Human Resources'},
        {'name': 'Accountant', 'department': 'Finance'},
        {'name': 'Operations Manager', 'department': 'Operations'},
    ]

    job_objects = {}
    for job_data in jobs_data:
        job, created = HrJob.objects.get_or_create(
            name=job_data['name'],
            company_id=company,
            defaults={
                'department_id': department_objects[job_data['department']],
                'state': 'open',
                'no_of_recruitment': 1,
            }
        )
        job_objects[job_data['name']] = job
        print(f"Job: {job.name} {'created' if created else 'already exists'}")

    # Create sample employees
    employees_data = [
        {
            'name': 'John Smith',
            'job': 'Chief Executive Officer',
            'email': '<EMAIL>',
            'gender': 'male',
            'wage': 150000,
        },
        {
            'name': 'Sarah Johnson',
            'job': 'Sales Manager',
            'email': '<EMAIL>',
            'gender': 'female',
            'wage': 80000,
        },
        {
            'name': 'Mike Wilson',
            'job': 'Software Developer',
            'email': '<EMAIL>',
            'gender': 'male',
            'wage': 75000,
        },
        {
            'name': 'Lisa Brown',
            'job': 'HR Manager',
            'email': '<EMAIL>',
            'gender': 'female',
            'wage': 70000,
        },
        {
            'name': 'David Davis',
            'job': 'Accountant',
            'email': '<EMAIL>',
            'gender': 'male',
            'wage': 60000,
        },
    ]

    for emp_data in employees_data:
        job = job_objects[emp_data['job']]

        employee, created = HrEmployee.objects.get_or_create(
            name=emp_data['name'],
            company_id=company,
            defaults={
                'work_email': emp_data['email'],
                'gender': emp_data['gender'],
                'department_id': job.department_id,
                'job_id': job,
                'employee_type': 'employee',
            }
        )

        if created:
            # Create contract for the employee
            contract_type = HrContractType.objects.get(name='Full-time Employee')
            contract = HrContract.objects.create(
                name=f"{employee.name} - Contract",
                employee_id=employee,
                department_id=employee.department_id,
                job_id=employee.job_id,
                type_id=contract_type,
                date_start=date.today() - timedelta(days=30),  # Started 30 days ago
                wage=emp_data['wage'],
                state='open',
                company_id=company,
            )
            print(f"Employee: {employee.name} created with contract (${contract.wage:,.2f})")
        else:
            print(f"Employee: {employee.name} already exists")

    # Create leave types
    leave_types_data = [
        {'name': 'Annual Leave', 'max_leaves': 21, 'allocation_type': 'fixed'},
        {'name': 'Sick Leave', 'max_leaves': 10, 'allocation_type': 'fixed'},
        {'name': 'Maternity Leave', 'max_leaves': 90, 'allocation_type': 'fixed'},
        {'name': 'Paternity Leave', 'max_leaves': 15, 'allocation_type': 'fixed'},
        {'name': 'Emergency Leave', 'max_leaves': 5, 'allocation_type': 'fixed'},
    ]

    for leave_data in leave_types_data:
        leave_type, created = HrLeaveType.objects.get_or_create(
            name=leave_data['name'],
            company_id=company,
            defaults={
                'max_leaves': leave_data['max_leaves'],
                'allocation_type': leave_data['allocation_type'],
            }
        )
        print(f"Leave Type: {leave_type.name} {'created' if created else 'already exists'}")

    # Create CRM structure
    print("\n📞 Creating CRM structure...")

    # Create CRM stages
    stages_data = [
        {'name': 'New', 'sequence': 1, 'is_won': False},
        {'name': 'Qualified', 'sequence': 2, 'is_won': False},
        {'name': 'Proposition', 'sequence': 3, 'is_won': False},
        {'name': 'Negotiation', 'sequence': 4, 'is_won': False},
        {'name': 'Won', 'sequence': 5, 'is_won': True},
    ]

    for stage_data in stages_data:
        stage, created = CrmStage.objects.get_or_create(
            name=stage_data['name'],
            defaults={
                'sequence': stage_data['sequence'],
                'is_won': stage_data['is_won'],
            }
        )
        print(f"CRM Stage: {stage.name} {'created' if created else 'already exists'}")

    # Create lost reasons
    lost_reasons_data = [
        'Too expensive',
        'Competitor chosen',
        'Budget constraints',
        'Not interested',
        'Wrong timing',
    ]

    for reason_name in lost_reasons_data:
        reason, created = CrmLostReason.objects.get_or_create(
            name=reason_name
        )
        print(f"Lost Reason: {reason.name} {'created' if created else 'already exists'}")

    # Create sample leads and opportunities
    crm_teams = CrmTeam.objects.all()
    if crm_teams.exists():
        leads_data = [
            {
                'name': 'Website Inquiry - ABC Corp',
                'type': 'lead',
                'partner_name': 'ABC Corporation',
                'contact_name': 'John Doe',
                'email_from': '<EMAIL>',
                'phone': '******-0101',
                'expected_revenue': 25000,
                'probability': 20,
            },
            {
                'name': 'ERP Implementation - XYZ Ltd',
                'type': 'opportunity',
                'partner_name': 'XYZ Limited',
                'contact_name': 'Jane Smith',
                'email_from': '<EMAIL>',
                'phone': '******-0102',
                'expected_revenue': 50000,
                'probability': 60,
            },
            {
                'name': 'Software Consultation - Tech Solutions',
                'type': 'opportunity',
                'partner_name': 'Tech Solutions Inc',
                'contact_name': 'Mike Johnson',
                'email_from': '<EMAIL>',
                'phone': '******-0103',
                'expected_revenue': 15000,
                'probability': 80,
            },
        ]

        new_stage = CrmStage.objects.get(name='New')
        qualified_stage = CrmStage.objects.get(name='Qualified')

        for lead_data in leads_data:
            lead, created = CrmLead.objects.get_or_create(
                name=lead_data['name'],
                company_id=company,
                defaults={
                    'type': lead_data['type'],
                    'partner_name': lead_data['partner_name'],
                    'contact_name': lead_data['contact_name'],
                    'email_from': lead_data['email_from'],
                    'phone': lead_data['phone'],
                    'expected_revenue': lead_data['expected_revenue'],
                    'probability': lead_data['probability'],
                    'user_id': admin_user,
                    'team_id': crm_teams.first(),
                    'stage_id': new_stage if lead_data['type'] == 'lead' else qualified_stage,
                }
            )
            if created:
                lead._compute_prorated_revenue()
                print(f"CRM {lead.type.title()}: {lead.name} created (${lead.expected_revenue:,.2f})")
            else:
                print(f"CRM {lead.type.title()}: {lead.name} already exists")

    # Create project management structure
    print("\n📋 Creating project management structure...")

    # Create task types/stages
    task_types_data = [
        {'name': 'To Do', 'sequence': 1, 'fold': False},
        {'name': 'In Progress', 'sequence': 2, 'fold': False},
        {'name': 'Testing', 'sequence': 3, 'fold': False},
        {'name': 'Done', 'sequence': 4, 'fold': True},
    ]

    task_type_objects = {}
    for task_type_data in task_types_data:
        task_type, created = ProjectTaskType.objects.get_or_create(
            name=task_type_data['name'],
            defaults={
                'sequence': task_type_data['sequence'],
                'fold': task_type_data['fold'],
            }
        )
        task_type_objects[task_type_data['name']] = task_type
        print(f"Task Type: {task_type.name} {'created' if created else 'already exists'}")

    # Create sample projects
    projects_data = [
        {
            'name': 'ERP System Development',
            'description': 'Complete ERP system development project',
            'date_start': date.today() - timedelta(days=30),
            'date': date.today() + timedelta(days=60),
            'partner_id': customers.first() if customers.exists() else None,
        },
        {
            'name': 'Website Redesign',
            'description': 'Company website redesign and optimization',
            'date_start': date.today() - timedelta(days=15),
            'date': date.today() + timedelta(days=45),
            'partner_id': customers.last() if customers.count() > 1 else None,
        },
        {
            'name': 'Internal Process Automation',
            'description': 'Automate internal business processes',
            'date_start': date.today(),
            'date': date.today() + timedelta(days=90),
            'partner_id': None,  # Internal project
        },
    ]

    project_objects = {}
    for project_data in projects_data:
        project, created = ProjectProject.objects.get_or_create(
            name=project_data['name'],
            company_id=company,
            defaults={
                'description': project_data['description'],
                'date_start': project_data['date_start'],
                'date': project_data['date'],
                'partner_id': project_data['partner_id'],
                'user_id': admin_user,
                'allow_timesheets': True,
                'allow_subtasks': True,
            }
        )
        project_objects[project_data['name']] = project

        # Add task types to project
        if created:
            for task_type in task_type_objects.values():
                task_type.project_ids.add(project)

        print(f"Project: {project.name} {'created' if created else 'already exists'}")

    # Create sample tasks
    if project_objects:
        erp_project = project_objects.get('ERP System Development')
        if erp_project:
            tasks_data = [
                {
                    'name': 'Database Design',
                    'description': 'Design database schema for ERP system',
                    'stage': 'Done',
                    'planned_hours': 40,
                    'priority': '1',
                },
                {
                    'name': 'Backend Development',
                    'description': 'Develop backend APIs and business logic',
                    'stage': 'In Progress',
                    'planned_hours': 80,
                    'priority': '1',
                },
                {
                    'name': 'Frontend Development',
                    'description': 'Develop user interface components',
                    'stage': 'To Do',
                    'planned_hours': 60,
                    'priority': '0',
                },
                {
                    'name': 'Testing & QA',
                    'description': 'Comprehensive testing and quality assurance',
                    'stage': 'To Do',
                    'planned_hours': 30,
                    'priority': '0',
                },
            ]

            for task_data in tasks_data:
                task, created = ProjectTask.objects.get_or_create(
                    name=task_data['name'],
                    project_id=erp_project,
                    defaults={
                        'description': task_data['description'],
                        'stage_id': task_type_objects[task_data['stage']],
                        'planned_hours': task_data['planned_hours'],
                        'priority': task_data['priority'],
                        'company_id': company,
                    }
                )
                if created:
                    task.user_ids.add(admin_user)
                    print(f"Task: {task.name} created ({task.planned_hours}h)")
                else:
                    print(f"Task: {task.name} already exists")

    # Create analytics structure
    print("\n📊 Creating analytics structure...")

    # Create executive dashboard
    dashboard, created = AnalyticsDashboard.objects.get_or_create(
        name='Executive Dashboard',
        company_id=company,
        defaults={
            'type': 'executive',
            'public': True,
            'config': '{"refresh_interval": 300, "auto_refresh": true}',
        }
    )
    print(f"Dashboard: {dashboard.name} {'created' if created else 'already exists'}")

    # Create sample KPIs
    kpis_data = [
        {
            'name': 'Monthly Revenue',
            'type': 'revenue',
            'current_value': 125000,
            'target_value': 150000,
            'previous_value': 110000,
            'suffix': '$',
            'period': 'monthly',
            'color': 'yellow',
        },
        {
            'name': 'Active Customers',
            'type': 'customer_count',
            'current_value': 45,
            'target_value': 50,
            'previous_value': 42,
            'suffix': '',
            'period': 'monthly',
            'color': 'green',
        },
        {
            'name': 'Project Completion Rate',
            'type': 'task_completion',
            'current_value': 78.5,
            'target_value': 85.0,
            'previous_value': 75.0,
            'suffix': '%',
            'period': 'monthly',
            'color': 'yellow',
        },
        {
            'name': 'Employee Count',
            'type': 'employee_count',
            'current_value': HrEmployee.objects.filter(active=True).count(),
            'target_value': 10,
            'previous_value': 4,
            'suffix': '',
            'period': 'monthly',
            'color': 'green',
        },
    ]

    for kpi_data in kpis_data:
        kpi, created = AnalyticsKpi.objects.get_or_create(
            name=kpi_data['name'],
            company_id=company,
            defaults={
                'type': kpi_data['type'],
                'current_value': kpi_data['current_value'],
                'target_value': kpi_data['target_value'],
                'previous_value': kpi_data['previous_value'],
                'suffix': kpi_data['suffix'],
                'period': kpi_data['period'],
                'color': kpi_data['color'],
                'dashboard_id': dashboard,
                'last_update': datetime.now(),
            }
        )
        print(f"KPI: {kpi.name} {'created' if created else 'already exists'} ({kpi.current_value}{kpi.suffix})")

    # Create sample reports
    reports_data = [
        {
            'name': 'Monthly Sales Report',
            'type': 'sales_report',
            'model_id': 'sales.saleorder',
            'output_format': 'pdf',
        },
        {
            'name': 'Financial Summary',
            'type': 'financial_report',
            'model_id': 'accounting.accountmove',
            'output_format': 'xlsx',
        },
        {
            'name': 'Project Status Report',
            'type': 'project_report',
            'model_id': 'project.project',
            'output_format': 'html',
        },
    ]

    for report_data in reports_data:
        report, created = AnalyticsReport.objects.get_or_create(
            name=report_data['name'],
            company_id=company,
            defaults={
                'type': report_data['type'],
                'model_id': report_data['model_id'],
                'output_format': report_data['output_format'],
                'public': True,
            }
        )
        print(f"Report: {report.name} {'created' if created else 'already exists'}")

    # Create manufacturing structure
    print("\n🏭 Creating manufacturing structure...")

    # Create work centers
    workcenters_data = [
        {
            'name': 'Assembly Line 1',
            'capacity': 1,
            'time_efficiency': 100,
            'costs_hour': 25.00,
            'time_start': 10,
            'time_stop': 5,
        },
        {
            'name': 'Quality Control Station',
            'capacity': 1,
            'time_efficiency': 95,
            'costs_hour': 30.00,
            'time_start': 5,
            'time_stop': 5,
        },
        {
            'name': 'Packaging Center',
            'capacity': 2,
            'time_efficiency': 100,
            'costs_hour': 20.00,
            'time_start': 5,
            'time_stop': 10,
        },
    ]

    workcenter_objects = {}
    for wc_data in workcenters_data:
        workcenter, created = MrpWorkcenter.objects.get_or_create(
            name=wc_data['name'],
            company_id=company,
            defaults={
                'capacity': wc_data['capacity'],
                'time_efficiency': wc_data['time_efficiency'],
                'costs_hour': wc_data['costs_hour'],
                'time_start': wc_data['time_start'],
                'time_stop': wc_data['time_stop'],
            }
        )
        workcenter_objects[wc_data['name']] = workcenter
        print(f"Work Center: {workcenter.name} {'created' if created else 'already exists'}")

    # Create routing
    stock_location = StockLocation.objects.filter(name='Stock').first()
    if stock_location:
        routing, created = MrpRouting.objects.get_or_create(
            name='Standard Product Routing',
            company_id=company,
            defaults={
                'location_id': stock_location,
                'note': 'Standard routing for manufactured products',
            }
        )
        print(f"Routing: {routing.name} {'created' if created else 'already exists'}")

        # Create routing operations
        if created:
            operations_data = [
                {
                    'name': 'Assembly',
                    'sequence': 10,
                    'workcenter': 'Assembly Line 1',
                    'time_cycle': 60,
                    'batch_size': 1,
                },
                {
                    'name': 'Quality Check',
                    'sequence': 20,
                    'workcenter': 'Quality Control Station',
                    'time_cycle': 15,
                    'batch_size': 1,
                },
                {
                    'name': 'Packaging',
                    'sequence': 30,
                    'workcenter': 'Packaging Center',
                    'time_cycle': 30,
                    'batch_size': 5,
                },
            ]

            for op_data in operations_data:
                operation, created = MrpRoutingWorkcenter.objects.get_or_create(
                    name=op_data['name'],
                    routing_id=routing,
                    workcenter_id=workcenter_objects[op_data['workcenter']],
                    defaults={
                        'sequence': op_data['sequence'],
                        'time_cycle': op_data['time_cycle'],
                        'batch_size': op_data['batch_size'],
                    }
                )
                print(f"Operation: {operation.name} {'created' if created else 'already exists'}")

    # Create sample products for manufacturing
    # Get or create a default category
    default_category, created = ProductCategory.objects.get_or_create(
        name='Manufactured Products',
        defaults={
            'complete_name': 'Manufactured Products',
            'parent_id': None,
        }
    )
    if created:
        print(f"Product Category: {default_category.name} created")

    manufactured_products_data = [
        {
            'name': 'Custom ERP Software',
            'type': 'product',
            'categ_id': default_category,
            'list_price': 5000.00,
            'standard_price': 3000.00,
        },
        {
            'name': 'Business Analytics Dashboard',
            'type': 'product',
            'categ_id': default_category,
            'list_price': 2500.00,
            'standard_price': 1500.00,
        },
    ]

    manufactured_product_objects = {}
    for prod_data in manufactured_products_data:
        # Create product template
        template, created = ProductTemplate.objects.get_or_create(
            name=prod_data['name'],
            defaults={
                'type': prod_data['type'],
                'categ_id': prod_data['categ_id'],
                'list_price': prod_data['list_price'],
                'standard_price': prod_data['standard_price'],
                'sale_ok': True,
                'purchase_ok': False,
                'company_id': company,
            }
        )

        # Create product variant
        product, created = ProductProduct.objects.get_or_create(
            product_tmpl_id=template,
            defaults={
                'active': True,
                'barcode': f'MFG{template.id:04d}',
            }
        )
        manufactured_product_objects[prod_data['name']] = product
        print(f"Manufactured Product: {product.name} {'created' if created else 'already exists'}")

    # Create component products
    # Get or create a components category
    components_category, created = ProductCategory.objects.get_or_create(
        name='Components',
        defaults={
            'complete_name': 'Components',
            'parent_id': None,
        }
    )
    if created:
        print(f"Product Category: {components_category.name} created")

    component_products_data = [
        {
            'name': 'Software License',
            'type': 'service',
            'categ_id': components_category,
            'list_price': 500.00,
            'standard_price': 300.00,
        },
        {
            'name': 'Development Hours',
            'type': 'service',
            'categ_id': components_category,
            'list_price': 100.00,
            'standard_price': 75.00,
        },
        {
            'name': 'Testing Hours',
            'type': 'service',
            'categ_id': components_category,
            'list_price': 80.00,
            'standard_price': 60.00,
        },
        {
            'name': 'Documentation',
            'type': 'service',
            'categ_id': components_category,
            'list_price': 200.00,
            'standard_price': 150.00,
        },
    ]

    component_objects = {}
    for comp_data in component_products_data:
        # Create component template
        template, created = ProductTemplate.objects.get_or_create(
            name=comp_data['name'],
            defaults={
                'type': comp_data['type'],
                'categ_id': comp_data['categ_id'],
                'list_price': comp_data['list_price'],
                'standard_price': comp_data['standard_price'],
                'sale_ok': False,
                'purchase_ok': True,
                'company_id': company,
            }
        )

        # Create component variant
        product, created = ProductProduct.objects.get_or_create(
            product_tmpl_id=template,
            defaults={
                'active': True,
                'barcode': f'COMP{template.id:04d}',
            }
        )
        component_objects[comp_data['name']] = product
        print(f"Component: {product.name} {'created' if created else 'already exists'}")

    # Create Bills of Materials (BOMs)
    if manufactured_product_objects and component_objects:
        erp_product = manufactured_product_objects.get('Custom ERP Software')
        if erp_product:
            bom, created = MrpBom.objects.get_or_create(
                product_tmpl_id=erp_product.product_tmpl_id,
                product_id=erp_product,
                company_id=company,
                defaults={
                    'product_qty': 1,
                    'type': 'normal',
                    'routing_id': routing if 'routing' in locals() else None,
                    'consumption': 'flexible',
                }
            )
            print(f"BoM: {bom.display_name} {'created' if created else 'already exists'}")

            # Create BOM lines (components)
            if created:
                bom_lines_data = [
                    {'component': 'Software License', 'qty': 1},
                    {'component': 'Development Hours', 'qty': 40},
                    {'component': 'Testing Hours', 'qty': 10},
                    {'component': 'Documentation', 'qty': 1},
                ]

                for line_data in bom_lines_data:
                    component = component_objects.get(line_data['component'])
                    if component:
                        bom_line, created = MrpBomLine.objects.get_or_create(
                            bom_id=bom,
                            product_id=component,
                            defaults={
                                'product_qty': line_data['qty'],
                            }
                        )
                        print(f"BoM Line: {bom_line.product_id.name} x{bom_line.product_qty} {'created' if created else 'already exists'}")

    # Create quality control points
    if manufactured_product_objects:
        quality_points_data = [
            {
                'title': 'Code Quality Review',
                'test_type': 'pass_fail',
                'note': 'Review code quality and standards compliance',
            },
            {
                'title': 'Performance Testing',
                'test_type': 'measure',
                'note': 'Measure system response time',
                'norm': 2.0,
                'tolerance_min': 0.5,
                'tolerance_max': 3.0,
                'norm_unit': 'seconds',
            },
            {
                'title': 'User Acceptance Testing',
                'test_type': 'pass_fail',
                'note': 'Verify user requirements are met',
            },
        ]

        for qp_data in quality_points_data:
            quality_point, created = QualityPoint.objects.get_or_create(
                title=qp_data['title'],
                company_id=company,
                defaults={
                    'test_type': qp_data['test_type'],
                    'note': qp_data['note'],
                    'norm': qp_data.get('norm', 0),
                    'tolerance_min': qp_data.get('tolerance_min', 0),
                    'tolerance_max': qp_data.get('tolerance_max', 0),
                    'norm_unit': qp_data.get('norm_unit', ''),
                }
            )

            # Add products to quality point
            if created:
                for product in manufactured_product_objects.values():
                    quality_point.product_ids.add(product)

            print(f"Quality Point: {quality_point.title} {'created' if created else 'already exists'}")

    # Create financial reports structure
    print("\n📊 Creating financial reports structure...")

    # Create date ranges
    date_ranges_data = [
        {
            'name': 'Current Month',
            'type_id': 'month',
            'date_start': date.today().replace(day=1),
            'date_end': date.today(),
        },
        {
            'name': 'Current Quarter',
            'type_id': 'quarter',
            'date_start': date.today().replace(month=((date.today().month-1)//3)*3+1, day=1),
            'date_end': date.today(),
        },
        {
            'name': 'Current Year',
            'type_id': 'year',
            'date_start': date.today().replace(month=1, day=1),
            'date_end': date.today(),
        },
        {
            'name': 'Last Month',
            'type_id': 'month',
            'date_start': (date.today().replace(day=1) - timedelta(days=1)).replace(day=1),
            'date_end': date.today().replace(day=1) - timedelta(days=1),
        },
    ]

    for dr_data in date_ranges_data:
        date_range, created = DateRange.objects.get_or_create(
            name=dr_data['name'],
            company_id=company,
            defaults={
                'type_id': dr_data['type_id'],
                'date_start': dr_data['date_start'],
                'date_end': dr_data['date_end'],
            }
        )
        print(f"Date Range: {date_range.name} {'created' if created else 'already exists'}")

    # Create age report configuration
    age_config, created = AccountAgeReportConfiguration.objects.get_or_create(
        name='Standard Age Configuration',
        company_id=company,
    )
    if created:
        # Create age intervals
        intervals_data = [
            {'name': 'Current', 'days_from': 0, 'days_to': 0, 'sequence': 1},
            {'name': '1-30 days', 'days_from': 1, 'days_to': 30, 'sequence': 2},
            {'name': '31-60 days', 'days_from': 31, 'days_to': 60, 'sequence': 3},
            {'name': '61-90 days', 'days_from': 61, 'days_to': 90, 'sequence': 4},
            {'name': '91-120 days', 'days_from': 91, 'days_to': 120, 'sequence': 5},
            {'name': '120+ days', 'days_from': 121, 'days_to': 999, 'sequence': 6},
        ]

        for interval_data in intervals_data:
            AccountAgeReportConfigurationLine.objects.create(
                configuration_id=age_config,
                **interval_data
            )
        print(f"Age Configuration: {age_config.name} created with {len(intervals_data)} intervals")
    else:
        print(f"Age Configuration: {age_config.name} already exists")

    # Create sample financial reports
    current_month_start = date.today().replace(day=1)
    current_month_end = date.today()

    # Trial Balance Report
    trial_balance, created = TrialBalanceReport.objects.get_or_create(
        name='Monthly Trial Balance',
        company_id=company,
        defaults={
            'date_from': current_month_start,
            'date_to': current_month_end,
            'only_posted_moves': True,
            'show_partner_details': False,
            'hide_account_at_0': True,
        }
    )
    print(f"Trial Balance Report: {trial_balance.name} {'created' if created else 'already exists'}")

    # General Ledger Report
    general_ledger, created = GeneralLedgerReport.objects.get_or_create(
        name='Monthly General Ledger',
        company_id=company,
        defaults={
            'date_from': current_month_start,
            'date_to': current_month_end,
            'only_posted_moves': True,
            'centralize': True,
            'show_analytic_tags': False,
        }
    )
    print(f"General Ledger Report: {general_ledger.name} {'created' if created else 'already exists'}")

    # Aged Partner Balance Report
    aged_receivable, created = AgedPartnerBalanceReport.objects.get_or_create(
        name='Aged Receivables',
        company_id=company,
        defaults={
            'date_from': current_month_start,
            'date_to': current_month_end,
            'account_type': 'receivable',
            'only_posted_moves': True,
            'show_move_line_details': True,
        }
    )
    print(f"Aged Receivables Report: {aged_receivable.name} {'created' if created else 'already exists'}")

    aged_payable, created = AgedPartnerBalanceReport.objects.get_or_create(
        name='Aged Payables',
        company_id=company,
        defaults={
            'date_from': current_month_start,
            'date_to': current_month_end,
            'account_type': 'payable',
            'only_posted_moves': True,
            'show_move_line_details': True,
        }
    )
    print(f"Aged Payables Report: {aged_payable.name} {'created' if created else 'already exists'}")

    # Open Items Report
    open_receivables, created = OpenItemsReport.objects.get_or_create(
        name='Open Receivables',
        company_id=company,
        defaults={
            'date_from': current_month_start,
            'date_to': current_month_end,
            'account_type': 'receivable',
            'target_move': 'posted',
        }
    )
    print(f"Open Receivables Report: {open_receivables.name} {'created' if created else 'already exists'}")

    # Journal Ledger Report
    journal_ledger, created = JournalLedgerReport.objects.get_or_create(
        name='Monthly Journal Ledger',
        company_id=company,
        defaults={
            'date_from': current_month_start,
            'date_to': current_month_end,
            'only_posted_moves': True,
            'sort_option': 'date',
            'group_option': 'journal',
        }
    )
    print(f"Journal Ledger Report: {journal_ledger.name} {'created' if created else 'already exists'}")

    # VAT Report
    vat_report, created = VatReport.objects.get_or_create(
        name='Monthly VAT Report',
        company_id=company,
        defaults={
            'date_from': current_month_start,
            'date_to': current_month_end,
            'only_posted_moves': True,
            'based_on': 'taxtags',
            'tax_detail': True,
        }
    )
    print(f"VAT Report: {vat_report.name} {'created' if created else 'already exists'}")

    print("\n" + "="*60)
    print("🎉 MODULAR ERP SYSTEM - SAMPLE DATA COMPLETED!")
    print("="*60)
    print("📊 ACCOUNTING MODULE:")
    print(f"   📊 Companies: {ResCompany.objects.count()}")
    print(f"   📊 Accounts: {AccountAccount.objects.count()}")
    print(f"   📖 Journals: {AccountJournal.objects.count()}")
    print(f"   👥 Partners: {ResPartner.objects.count()}")
    print(f"   👤 Customers: {ResPartner.objects.filter(customer_rank__gt=0).count()}")
    print(f"   🏪 Vendors: {ResPartner.objects.filter(supplier_rank__gt=0).count()}")
    print(f"   💰 Taxes: {AccountTax.objects.count()}")
    print(f"   📅 Payment Terms: {AccountPaymentTerm.objects.count()}")
    print(f"   📈 Analytic Accounts: {AccountAnalyticAccount.objects.count()}")
    print()
    print("🛒 SALES MODULE:")
    print(f"   📋 Sales Orders: {SaleOrder.objects.count()}")
    print(f"   📝 Sales Order Lines: {SaleOrderLine.objects.count()}")
    print(f"   🛍️ Products: {ProductProduct.objects.count()}")
    print()
    print("🛍️ PURCHASE MODULE:")
    print(f"   📋 Purchase Orders: {PurchaseOrder.objects.count()}")
    print(f"   📝 Purchase Order Lines: {PurchaseOrderLine.objects.count()}")
    print()
    print("📦 INVENTORY MODULE:")
    print(f"   🏢 Warehouses: {StockWarehouse.objects.count()}")
    print(f"   📍 Locations: {StockLocation.objects.count()}")
    print(f"   📋 Picking Types: {StockPickingType.objects.count()}")
    print(f"   📊 Stock Quants: {StockQuant.objects.count()}")
    print()
    print("👥 HR MODULE:")
    print(f"   🏢 Departments: {HrDepartment.objects.count()}")
    print(f"   💼 Job Positions: {HrJob.objects.count()}")
    print(f"   👤 Employees: {HrEmployee.objects.count()}")
    print(f"   📄 Contracts: {HrContract.objects.count()}")
    print(f"   🏖️ Leave Types: {HrLeaveType.objects.count()}")
    print()
    print("📞 CRM MODULE:")
    print(f"   👥 CRM Teams: {CrmTeam.objects.count()}")
    print(f"   📊 CRM Stages: {CrmStage.objects.count()}")
    print(f"   🎯 Leads & Opportunities: {CrmLead.objects.count()}")
    print(f"   ❌ Lost Reasons: {CrmLostReason.objects.count()}")
    print()
    print("📋 PROJECT MODULE:")
    print(f"   📁 Projects: {ProjectProject.objects.count()}")
    print(f"   📋 Task Types: {ProjectTaskType.objects.count()}")
    print(f"   ✅ Tasks: {ProjectTask.objects.count()}")
    print(f"   🎯 Milestones: {ProjectMilestone.objects.count()}")
    print()
    print("📊 ANALYTICS MODULE:")
    print(f"   📊 Dashboards: {AnalyticsDashboard.objects.count()}")
    print(f"   📈 KPIs: {AnalyticsKpi.objects.count()}")
    print(f"   📋 Reports: {AnalyticsReport.objects.count()}")
    print()
    print("🏭 MANUFACTURING MODULE:")
    print(f"   🏭 Work Centers: {MrpWorkcenter.objects.count()}")
    print(f"   🔄 Routings: {MrpRouting.objects.count()}")
    print(f"   📋 Bills of Materials: {MrpBom.objects.count()}")
    print(f"   🏗️ Manufacturing Orders: {MrpProduction.objects.count()}")
    print(f"   ✅ Quality Points: {QualityPoint.objects.count()}")
    print()
    print("📊 FINANCIAL REPORTS MODULE:")
    print(f"   📅 Date Ranges: {DateRange.objects.count()}")
    print(f"   📊 Trial Balance Reports: {TrialBalanceReport.objects.count()}")
    print(f"   📋 General Ledger Reports: {GeneralLedgerReport.objects.count()}")
    print(f"   👥 Aged Partner Reports: {AgedPartnerBalanceReport.objects.count()}")
    print(f"   📄 Open Items Reports: {OpenItemsReport.objects.count()}")
    print(f"   📚 Journal Ledger Reports: {JournalLedgerReport.objects.count()}")
    print(f"   💰 VAT Reports: {VatReport.objects.count()}")
    print("="*60)
    print("🚀 Your MODULAR ERP system is ready with complete Odoo business logic!")
    print("✅ Accounting + Sales + Purchase + Inventory + HR + CRM + Project + Analytics + Manufacturing + Financial Reports!")
    print("✅ All models follow exact Odoo structure and business rules!")
    print("✅ Complete business ecosystem: Finance, Sales, CRM, Projects, Analytics, Manufacturing, Advanced Reports!")
    print("✅ Lead-to-Cash + Project Management + Business Intelligence + Manufacturing + Financial Reporting!")
    print("✅ 10 MODULES - 105+ MODELS - ENTERPRISE READY!")
    print("✅ COMPLETE MRP SYSTEM: BOM → Production → Quality → Delivery!")
    print("✅ ADVANCED FINANCIAL REPORTING: Trial Balance, General Ledger, Aged Partners, VAT Reports!")

if __name__ == '__main__':
    create_sample_data()
