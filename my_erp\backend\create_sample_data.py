#!/usr/bin/env python
"""
Create sample data for the ERP system
"""
import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from accounting.models import (
    ResCompany, ResPartner, AccountAccount, AccountJournal,
    AccountTax, AccountMove, AccountMoveLine, AccountPayment,
    AccountPaymentTerm, AccountFiscalPosition, AccountAnalyticAccount
)

def create_sample_data():
    print("Creating sample data...")
    
    # Create company
    company, created = ResCompany.objects.get_or_create(
        name="DataiCraft Solutions",
        defaults={
            'legal_name': 'DataiCraft Solutions Pvt Ltd',
            'street': 'Main Street 123',
            'city': 'Karachi',
            'country_id': 'PK',
            'phone': '+92-21-1234567',
            'email': '<EMAIL>',
            'vat': 'PK-*********',
            'currency_id': 'PKR',
        }
    )
    print(f"Company: {company.name} {'created' if created else 'already exists'}")
    
    # Create sample accounts
    accounts_data = [
        {'code': '1001', 'name': 'Cash in Hand', 'account_type': 'asset_cash'},
        {'code': '1002', 'name': 'Bank Account', 'account_type': 'asset_cash'},
        {'code': '1100', 'name': 'Accounts Receivable', 'account_type': 'asset_receivable'},
        {'code': '1200', 'name': 'Inventory', 'account_type': 'asset_current'},
        {'code': '1500', 'name': 'Office Equipment', 'account_type': 'asset_fixed'},
        {'code': '2001', 'name': 'Accounts Payable', 'account_type': 'liability_payable'},
        {'code': '2100', 'name': 'Short Term Loans', 'account_type': 'liability_current'},
        {'code': '3001', 'name': 'Owner Equity', 'account_type': 'equity'},
        {'code': '4001', 'name': 'Sales Revenue', 'account_type': 'income'},
        {'code': '4002', 'name': 'Service Revenue', 'account_type': 'income'},
        {'code': '5001', 'name': 'Cost of Goods Sold', 'account_type': 'expense_direct_cost'},
        {'code': '6001', 'name': 'Office Rent', 'account_type': 'expense'},
        {'code': '6002', 'name': 'Utilities', 'account_type': 'expense'},
        {'code': '6003', 'name': 'Marketing Expenses', 'account_type': 'expense'},
    ]
    
    for acc_data in accounts_data:
        account, created = AccountAccount.objects.get_or_create(
            code=acc_data['code'],
            company_id=company,
            defaults={
                'name': acc_data['name'],
                'account_type': acc_data['account_type'],
                'reconcile': acc_data['account_type'] in ['asset_receivable', 'liability_payable'],
            }
        )
        print(f"Account: {account.code} - {account.name} {'created' if created else 'already exists'}")
    
    # Create journals
    journals_data = [
        {'code': 'SALE', 'name': 'Sales Journal', 'type': 'sale', 'default_account': '4001'},
        {'code': 'PURCH', 'name': 'Purchase Journal', 'type': 'purchase', 'default_account': '5001'},
        {'code': 'CASH', 'name': 'Cash Journal', 'type': 'cash', 'default_account': '1001'},
        {'code': 'BANK', 'name': 'Bank Journal', 'type': 'bank', 'default_account': '1002'},
        {'code': 'MISC', 'name': 'Miscellaneous Journal', 'type': 'general', 'default_account': '1001'},
    ]
    
    for journal_data in journals_data:
        default_account = AccountAccount.objects.get(code=journal_data['default_account'], company_id=company)
        journal, created = AccountJournal.objects.get_or_create(
            code=journal_data['code'],
            company_id=company,
            defaults={
                'name': journal_data['name'],
                'type': journal_data['type'],
                'default_account_id': default_account,
            }
        )
        print(f"Journal: {journal.code} - {journal.name} {'created' if created else 'already exists'}")
    
    # Create sample partners (customers and vendors)
    customers_data = [
        {'name': 'ABC Company Ltd.', 'email': '<EMAIL>', 'phone': '+92-300-1234567', 'city': 'Karachi', 'customer_rank': 1},
        {'name': 'XYZ Corporation', 'email': '<EMAIL>', 'phone': '+92-321-7654321', 'city': 'Lahore', 'customer_rank': 1},
        {'name': 'Tech Solutions Inc.', 'email': '<EMAIL>', 'phone': '+92-42-9876543', 'city': 'Islamabad', 'customer_rank': 1},
    ]
    
    for customer_data in customers_data:
        partner, created = ResPartner.objects.get_or_create(
            name=customer_data['name'],
            company_id=company,
            defaults={
                'email': customer_data['email'],
                'phone': customer_data['phone'],
                'city': customer_data['city'],
                'country_id': 'PK',
                'customer_rank': customer_data['customer_rank'],
                'supplier_rank': 0,
            }
        )
        print(f"Customer: {partner.name} {'created' if created else 'already exists'}")
    
    vendors_data = [
        {'name': 'Office Supplies Co.', 'email': '<EMAIL>', 'phone': '+92-42-1234567', 'city': 'Lahore', 'supplier_rank': 1},
        {'name': 'Tech Hardware Ltd.', 'email': '<EMAIL>', 'phone': '+92-21-7654321', 'city': 'Karachi', 'supplier_rank': 1},
        {'name': 'Utility Services', 'email': '<EMAIL>', 'phone': '+92-51-9876543', 'city': 'Islamabad', 'supplier_rank': 1},
    ]
    
    for vendor_data in vendors_data:
        partner, created = ResPartner.objects.get_or_create(
            name=vendor_data['name'],
            company_id=company,
            defaults={
                'email': vendor_data['email'],
                'phone': vendor_data['phone'],
                'city': vendor_data['city'],
                'country_id': 'PK',
                'customer_rank': 0,
                'supplier_rank': vendor_data['supplier_rank'],
            }
        )
        print(f"Vendor: {partner.name} {'created' if created else 'already exists'}")

    # Create sample taxes
    taxes_data = [
        {'name': 'GST 17%', 'type_tax_use': 'sale', 'amount': 17.0, 'description': 'GST17'},
        {'name': 'GST 5%', 'type_tax_use': 'sale', 'amount': 5.0, 'description': 'GST5'},
        {'name': 'Purchase GST 17%', 'type_tax_use': 'purchase', 'amount': 17.0, 'description': 'PGST17'},
        {'name': 'Withholding Tax 1%', 'type_tax_use': 'purchase', 'amount': 1.0, 'description': 'WHT1'},
    ]

    for tax_data in taxes_data:
        tax, created = AccountTax.objects.get_or_create(
            name=tax_data['name'],
            company_id=company,
            defaults={
                'type_tax_use': tax_data['type_tax_use'],
                'amount': tax_data['amount'],
                'description': tax_data['description'],
            }
        )
        print(f"Tax: {tax.name} {'created' if created else 'already exists'}")

    # Create payment terms
    payment_terms_data = [
        {'name': 'Immediate Payment', 'note': 'Payment due immediately'},
        {'name': '30 Days', 'note': 'Payment due in 30 days'},
        {'name': '60 Days', 'note': 'Payment due in 60 days'},
    ]

    for term_data in payment_terms_data:
        term, created = AccountPaymentTerm.objects.get_or_create(
            name=term_data['name'],
            company_id=company,
            defaults={
                'note': term_data['note'],
            }
        )
        print(f"Payment Term: {term.name} {'created' if created else 'already exists'}")

    # Create analytic accounts
    analytic_data = [
        {'name': 'Marketing Campaign', 'code': 'MKT001'},
        {'name': 'R&D Project', 'code': 'RND001'},
        {'name': 'Office Operations', 'code': 'OPS001'},
    ]

    for analytic_data_item in analytic_data:
        analytic, created = AccountAnalyticAccount.objects.get_or_create(
            name=analytic_data_item['name'],
            company_id=company,
            defaults={
                'code': analytic_data_item['code'],
            }
        )
        print(f"Analytic Account: {analytic.name} {'created' if created else 'already exists'}")

    print("\n" + "="*50)
    print("COMPLETE SAMPLE DATA CREATION COMPLETED!")
    print("="*50)
    print(f"📊 Companies: {ResCompany.objects.count()}")
    print(f"📊 Accounts: {AccountAccount.objects.count()}")
    print(f"📖 Journals: {AccountJournal.objects.count()}")
    print(f"👥 Partners: {ResPartner.objects.count()}")
    print(f"👤 Customers: {ResPartner.objects.filter(customer_rank__gt=0).count()}")
    print(f"🏪 Vendors: {ResPartner.objects.filter(supplier_rank__gt=0).count()}")
    print(f"💰 Taxes: {AccountTax.objects.count()}")
    print(f"📅 Payment Terms: {AccountPaymentTerm.objects.count()}")
    print(f"📈 Analytic Accounts: {AccountAnalyticAccount.objects.count()}")
    print(f"📝 Journal Entries: {AccountMove.objects.count()}")
    print(f"💳 Payments: {AccountPayment.objects.count()}")
    print("="*50)
    print("🚀 Your ERP system now has COMPLETE Odoo-style data!")

if __name__ == '__main__':
    create_sample_data()
