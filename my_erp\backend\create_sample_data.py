#!/usr/bin/env python
"""
Create sample data for the ERP system
"""
import os
import sys
import django
from datetime import date, datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'erp_backend.settings')
django.setup()

from accounting.models import (
    ResCompany, ResPartner, AccountAccount, AccountJournal,
    AccountTax, AccountMove, AccountMoveLine, AccountPayment,
    AccountPaymentTerm, AccountFiscalPosition, AccountAnalyticAccount,
    ProductCategory, ProductTemplate, ProductProduct,
    IrSequence, ResCountry, ResCurrency, CrossoveredBudget,
    AccountBankStatement, AccountAsset, AccountAssetCategory
)
from sales.models import SalesTeam, SaleOrder, SaleOrderLine
from purchase.models import PurchaseOrder, PurchaseOrderLine
from inventory.models import StockLocation, StockWarehouse, StockPickingType, StockQuant
from hr.models import <PERSON><PERSON><PERSON><PERSON>art<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Hr<PERSON>ontractType, HrContract, HrLeaveType

def create_sample_data():
    print("Creating sample data...")
    
    # Create company
    company, created = ResCompany.objects.get_or_create(
        name="DataiCraft Solutions",
        defaults={
            'legal_name': 'DataiCraft Solutions Pvt Ltd',
            'street': 'Main Street 123',
            'city': 'Karachi',
            'country_id': 'PK',
            'phone': '+92-21-1234567',
            'email': '<EMAIL>',
            'vat': 'PK-*********',
            'currency_id': 'PKR',
        }
    )
    print(f"Company: {company.name} {'created' if created else 'already exists'}")
    
    # Create sample accounts
    accounts_data = [
        {'code': '1001', 'name': 'Cash in Hand', 'account_type': 'asset_cash'},
        {'code': '1002', 'name': 'Bank Account', 'account_type': 'asset_cash'},
        {'code': '1100', 'name': 'Accounts Receivable', 'account_type': 'asset_receivable'},
        {'code': '1200', 'name': 'Inventory', 'account_type': 'asset_current'},
        {'code': '1500', 'name': 'Office Equipment', 'account_type': 'asset_fixed'},
        {'code': '2001', 'name': 'Accounts Payable', 'account_type': 'liability_payable'},
        {'code': '2100', 'name': 'Short Term Loans', 'account_type': 'liability_current'},
        {'code': '3001', 'name': 'Owner Equity', 'account_type': 'equity'},
        {'code': '4001', 'name': 'Sales Revenue', 'account_type': 'income'},
        {'code': '4002', 'name': 'Service Revenue', 'account_type': 'income'},
        {'code': '5001', 'name': 'Cost of Goods Sold', 'account_type': 'expense_direct_cost'},
        {'code': '6001', 'name': 'Office Rent', 'account_type': 'expense'},
        {'code': '6002', 'name': 'Utilities', 'account_type': 'expense'},
        {'code': '6003', 'name': 'Marketing Expenses', 'account_type': 'expense'},
    ]
    
    for acc_data in accounts_data:
        account, created = AccountAccount.objects.get_or_create(
            code=acc_data['code'],
            company_id=company,
            defaults={
                'name': acc_data['name'],
                'account_type': acc_data['account_type'],
                'reconcile': acc_data['account_type'] in ['asset_receivable', 'liability_payable'],
            }
        )
        print(f"Account: {account.code} - {account.name} {'created' if created else 'already exists'}")
    
    # Create journals
    journals_data = [
        {'code': 'SALE', 'name': 'Sales Journal', 'type': 'sale', 'default_account': '4001'},
        {'code': 'PURCH', 'name': 'Purchase Journal', 'type': 'purchase', 'default_account': '5001'},
        {'code': 'CASH', 'name': 'Cash Journal', 'type': 'cash', 'default_account': '1001'},
        {'code': 'BANK', 'name': 'Bank Journal', 'type': 'bank', 'default_account': '1002'},
        {'code': 'MISC', 'name': 'Miscellaneous Journal', 'type': 'general', 'default_account': '1001'},
    ]
    
    for journal_data in journals_data:
        default_account = AccountAccount.objects.get(code=journal_data['default_account'], company_id=company)
        journal, created = AccountJournal.objects.get_or_create(
            code=journal_data['code'],
            company_id=company,
            defaults={
                'name': journal_data['name'],
                'type': journal_data['type'],
                'default_account_id': default_account,
            }
        )
        print(f"Journal: {journal.code} - {journal.name} {'created' if created else 'already exists'}")
    
    # Create sample partners (customers and vendors)
    customers_data = [
        {'name': 'ABC Company Ltd.', 'email': '<EMAIL>', 'phone': '+92-300-1234567', 'city': 'Karachi', 'customer_rank': 1},
        {'name': 'XYZ Corporation', 'email': '<EMAIL>', 'phone': '+92-321-7654321', 'city': 'Lahore', 'customer_rank': 1},
        {'name': 'Tech Solutions Inc.', 'email': '<EMAIL>', 'phone': '+92-42-9876543', 'city': 'Islamabad', 'customer_rank': 1},
    ]
    
    for customer_data in customers_data:
        partner, created = ResPartner.objects.get_or_create(
            name=customer_data['name'],
            company_id=company,
            defaults={
                'email': customer_data['email'],
                'phone': customer_data['phone'],
                'city': customer_data['city'],
                'country_id': 'PK',
                'customer_rank': customer_data['customer_rank'],
                'supplier_rank': 0,
            }
        )
        print(f"Customer: {partner.name} {'created' if created else 'already exists'}")
    
    vendors_data = [
        {'name': 'Office Supplies Co.', 'email': '<EMAIL>', 'phone': '+92-42-1234567', 'city': 'Lahore', 'supplier_rank': 1},
        {'name': 'Tech Hardware Ltd.', 'email': '<EMAIL>', 'phone': '+92-21-7654321', 'city': 'Karachi', 'supplier_rank': 1},
        {'name': 'Utility Services', 'email': '<EMAIL>', 'phone': '+92-51-9876543', 'city': 'Islamabad', 'supplier_rank': 1},
    ]
    
    for vendor_data in vendors_data:
        partner, created = ResPartner.objects.get_or_create(
            name=vendor_data['name'],
            company_id=company,
            defaults={
                'email': vendor_data['email'],
                'phone': vendor_data['phone'],
                'city': vendor_data['city'],
                'country_id': 'PK',
                'customer_rank': 0,
                'supplier_rank': vendor_data['supplier_rank'],
            }
        )
        print(f"Vendor: {partner.name} {'created' if created else 'already exists'}")

    # Create sample taxes
    taxes_data = [
        {'name': 'GST 17%', 'type_tax_use': 'sale', 'amount': 17.0, 'description': 'GST17'},
        {'name': 'GST 5%', 'type_tax_use': 'sale', 'amount': 5.0, 'description': 'GST5'},
        {'name': 'Purchase GST 17%', 'type_tax_use': 'purchase', 'amount': 17.0, 'description': 'PGST17'},
        {'name': 'Withholding Tax 1%', 'type_tax_use': 'purchase', 'amount': 1.0, 'description': 'WHT1'},
    ]

    for tax_data in taxes_data:
        tax, created = AccountTax.objects.get_or_create(
            name=tax_data['name'],
            company_id=company,
            defaults={
                'type_tax_use': tax_data['type_tax_use'],
                'amount': tax_data['amount'],
                'description': tax_data['description'],
            }
        )
        print(f"Tax: {tax.name} {'created' if created else 'already exists'}")

    # Create payment terms
    payment_terms_data = [
        {'name': 'Immediate Payment', 'note': 'Payment due immediately'},
        {'name': '30 Days', 'note': 'Payment due in 30 days'},
        {'name': '60 Days', 'note': 'Payment due in 60 days'},
    ]

    for term_data in payment_terms_data:
        term, created = AccountPaymentTerm.objects.get_or_create(
            name=term_data['name'],
            company_id=company,
            defaults={
                'note': term_data['note'],
            }
        )
        print(f"Payment Term: {term.name} {'created' if created else 'already exists'}")

    # Create analytic accounts
    analytic_data = [
        {'name': 'Marketing Campaign', 'code': 'MKT001'},
        {'name': 'R&D Project', 'code': 'RND001'},
        {'name': 'Office Operations', 'code': 'OPS001'},
    ]

    for analytic_data_item in analytic_data:
        analytic, created = AccountAnalyticAccount.objects.get_or_create(
            name=analytic_data_item['name'],
            company_id=company,
            defaults={
                'code': analytic_data_item['code'],
            }
        )
        print(f"Analytic Account: {analytic.name} {'created' if created else 'already exists'}")

    # Create sales teams
    from django.contrib.auth.models import User
    admin_user = User.objects.get(username='admin')

    sales_teams_data = [
        {'name': 'Direct Sales', 'user_id': admin_user, 'invoiced_target': 50000},
        {'name': 'Online Sales', 'user_id': admin_user, 'invoiced_target': 30000},
        {'name': 'Channel Partners', 'user_id': admin_user, 'invoiced_target': 20000},
    ]

    for team_data in sales_teams_data:
        team, created = SalesTeam.objects.get_or_create(
            name=team_data['name'],
            company_id=company,
            defaults={
                'user_id': team_data['user_id'],
                'invoiced_target': team_data['invoiced_target'],
            }
        )
        print(f"Sales Team: {team.name} {'created' if created else 'already exists'}")

    # Create sample sales orders
    customers = ResPartner.objects.filter(customer_rank__gt=0)
    products = ProductProduct.objects.all()[:3]  # Get first 3 products
    direct_sales_team = SalesTeam.objects.get(name='Direct Sales')

    if customers.exists() and products.exists():
        for i, customer in enumerate(customers[:2]):  # Create 2 sample orders
            order, created = SaleOrder.objects.get_or_create(
                name=f'SO{2024}{i+1:03d}',
                defaults={
                    'partner_id': customer,
                    'partner_invoice_id': customer,
                    'partner_shipping_id': customer,
                    'user_id': admin_user,
                    'team_id': direct_sales_team,
                    'state': 'draft',
                    'company_id': company,
                }
            )

            if created:
                # Add order lines
                for j, product in enumerate(products):
                    SaleOrderLine.objects.create(
                        order_id=order,
                        product_id=product,
                        product_template_id=product.product_tmpl_id,
                        name=product.name,
                        product_uom_qty=j+1,
                        price_unit=product.list_price,
                        company_id=company,
                    )

                # Compute amounts
                order._compute_amounts()
                print(f"Sales Order: {order.name} created with {order.order_line.count()} lines")

    # Create sample purchase orders
    vendors = ResPartner.objects.filter(supplier_rank__gt=0)

    if vendors.exists() and products.exists():
        for i, vendor in enumerate(vendors[:2]):  # Create 2 sample purchase orders
            po, created = PurchaseOrder.objects.get_or_create(
                name=f'PO{2024}{i+1:03d}',
                defaults={
                    'partner_id': vendor,
                    'user_id': admin_user,
                    'state': 'draft',
                    'company_id': company,
                    'date_planned': datetime.now(),
                }
            )

            if created:
                # Add order lines
                for j, product in enumerate(products):
                    PurchaseOrderLine.objects.create(
                        order_id=po,
                        product_id=product,
                        product_template_id=product.product_tmpl_id,
                        name=product.name,
                        product_qty=j+2,  # Different quantities than sales
                        price_unit=product.standard_price,
                        date_planned=datetime.now(),
                        company_id=company,
                    )

                # Compute amounts
                po._compute_amounts()
                print(f"Purchase Order: {po.name} created with {po.order_line.count()} lines")

    # Create inventory structure
    print("\n📦 Creating inventory structure...")

    # First create basic locations without warehouse reference
    locations_data = [
        {'name': 'Physical Locations', 'usage': 'view', 'parent': None},
        {'name': 'Stock', 'usage': 'internal', 'parent': 'Physical Locations'},
        {'name': 'Input', 'usage': 'internal', 'parent': 'Physical Locations'},
        {'name': 'Output', 'usage': 'internal', 'parent': 'Physical Locations'},
        {'name': 'Vendors', 'usage': 'supplier', 'parent': None},
        {'name': 'Customers', 'usage': 'customer', 'parent': None},
        {'name': 'Inventory Loss', 'usage': 'inventory', 'parent': None},
    ]

    location_objects = {}
    for loc_data in locations_data:
        parent_location = location_objects.get(loc_data['parent']) if loc_data['parent'] else None

        location, created = StockLocation.objects.get_or_create(
            name=loc_data['name'],
            company_id=company,
            defaults={
                'usage': loc_data['usage'],
                'location_id': parent_location,
            }
        )
        location_objects[loc_data['name']] = location
        print(f"Location: {location.name} ({'created' if created else 'already exists'})")

    # Now create warehouse with proper location references
    main_warehouse, created = StockWarehouse.objects.get_or_create(
        code='WH01',
        defaults={
            'name': 'Main Warehouse',
            'view_location_id': location_objects['Physical Locations'],
            'lot_stock_id': location_objects['Stock'],
            'wh_input_stock_loc_id': location_objects['Input'],
            'wh_output_stock_loc_id': location_objects['Output'],
            'wh_qc_stock_loc_id': location_objects['Stock'],  # Same as stock for simplicity
            'wh_pack_stock_loc_id': location_objects['Output'],  # Same as output
            'company_id': company,
        }
    )
    print(f"Warehouse: {main_warehouse.name} {'created' if created else 'already exists'}")

    # Update internal locations to reference the warehouse
    for loc_name in ['Stock', 'Input', 'Output']:
        location = location_objects[loc_name]
        if not location.warehouse_id:
            location.warehouse_id = main_warehouse
            location.save()

    # Create picking types
    picking_types_data = [
        {'name': 'Receipts', 'code': 'incoming', 'sequence_code': 'IN'},
        {'name': 'Delivery Orders', 'code': 'outgoing', 'sequence_code': 'OUT'},
        {'name': 'Internal Transfers', 'code': 'internal', 'sequence_code': 'INT'},
    ]

    for pt_data in picking_types_data:
        picking_type, created = StockPickingType.objects.get_or_create(
            name=pt_data['name'],
            warehouse_id=main_warehouse,
            defaults={
                'code': pt_data['code'],
                'sequence_code': pt_data['sequence_code'],
                'default_location_src_id': location_objects['Vendors'] if pt_data['code'] == 'incoming' else location_objects['Stock'],
                'default_location_dest_id': location_objects['Stock'] if pt_data['code'] == 'incoming' else location_objects['Customers'],
                'company_id': company,
            }
        )
        print(f"Picking Type: {picking_type.name} {'created' if created else 'already exists'}")

    # Create initial stock for products (if any exist)
    if products.exists():
        stock_location = location_objects['Stock']
        for product in products:
            quant, created = StockQuant.objects.get_or_create(
                product_id=product,
                location_id=stock_location,
                defaults={
                    'quantity': 100,  # Initial stock
                    'company_id': company,
                }
            )
            if created:
                print(f"Stock: {product.name} - {quant.quantity} units in {stock_location.name}")

    # Create HR structure
    print("\n👥 Creating HR structure...")

    # Create departments
    departments_data = [
        {'name': 'Administration', 'parent': None},
        {'name': 'Sales & Marketing', 'parent': None},
        {'name': 'Sales', 'parent': 'Sales & Marketing'},
        {'name': 'Marketing', 'parent': 'Sales & Marketing'},
        {'name': 'Research & Development', 'parent': None},
        {'name': 'Operations', 'parent': None},
        {'name': 'Human Resources', 'parent': None},
        {'name': 'Finance', 'parent': None},
    ]

    department_objects = {}
    for dept_data in departments_data:
        parent_dept = department_objects.get(dept_data['parent']) if dept_data['parent'] else None

        department, created = HrDepartment.objects.get_or_create(
            name=dept_data['name'],
            company_id=company,
            defaults={
                'parent_id': parent_dept,
            }
        )
        department_objects[dept_data['name']] = department
        print(f"Department: {department.name} {'created' if created else 'already exists'}")

    # Create contract types
    contract_types_data = [
        'Full-time Employee',
        'Part-time Employee',
        'Contractor',
        'Intern',
        'Consultant',
    ]

    for ct_name in contract_types_data:
        contract_type, created = HrContractType.objects.get_or_create(
            name=ct_name,
            defaults={'sequence': contract_types_data.index(ct_name) + 1}
        )
        print(f"Contract Type: {contract_type.name} {'created' if created else 'already exists'}")

    # Create job positions
    jobs_data = [
        {'name': 'Chief Executive Officer', 'department': 'Administration'},
        {'name': 'Sales Manager', 'department': 'Sales'},
        {'name': 'Sales Representative', 'department': 'Sales'},
        {'name': 'Marketing Manager', 'department': 'Marketing'},
        {'name': 'Software Developer', 'department': 'Research & Development'},
        {'name': 'HR Manager', 'department': 'Human Resources'},
        {'name': 'Accountant', 'department': 'Finance'},
        {'name': 'Operations Manager', 'department': 'Operations'},
    ]

    job_objects = {}
    for job_data in jobs_data:
        job, created = HrJob.objects.get_or_create(
            name=job_data['name'],
            company_id=company,
            defaults={
                'department_id': department_objects[job_data['department']],
                'state': 'open',
                'no_of_recruitment': 1,
            }
        )
        job_objects[job_data['name']] = job
        print(f"Job: {job.name} {'created' if created else 'already exists'}")

    # Create sample employees
    employees_data = [
        {
            'name': 'John Smith',
            'job': 'Chief Executive Officer',
            'email': '<EMAIL>',
            'gender': 'male',
            'wage': 150000,
        },
        {
            'name': 'Sarah Johnson',
            'job': 'Sales Manager',
            'email': '<EMAIL>',
            'gender': 'female',
            'wage': 80000,
        },
        {
            'name': 'Mike Wilson',
            'job': 'Software Developer',
            'email': '<EMAIL>',
            'gender': 'male',
            'wage': 75000,
        },
        {
            'name': 'Lisa Brown',
            'job': 'HR Manager',
            'email': '<EMAIL>',
            'gender': 'female',
            'wage': 70000,
        },
        {
            'name': 'David Davis',
            'job': 'Accountant',
            'email': '<EMAIL>',
            'gender': 'male',
            'wage': 60000,
        },
    ]

    for emp_data in employees_data:
        job = job_objects[emp_data['job']]

        employee, created = HrEmployee.objects.get_or_create(
            name=emp_data['name'],
            company_id=company,
            defaults={
                'work_email': emp_data['email'],
                'gender': emp_data['gender'],
                'department_id': job.department_id,
                'job_id': job,
                'employee_type': 'employee',
            }
        )

        if created:
            # Create contract for the employee
            contract_type = HrContractType.objects.get(name='Full-time Employee')
            contract = HrContract.objects.create(
                name=f"{employee.name} - Contract",
                employee_id=employee,
                department_id=employee.department_id,
                job_id=employee.job_id,
                type_id=contract_type,
                date_start=date.today() - timedelta(days=30),  # Started 30 days ago
                wage=emp_data['wage'],
                state='open',
                company_id=company,
            )
            print(f"Employee: {employee.name} created with contract (${contract.wage:,.2f})")
        else:
            print(f"Employee: {employee.name} already exists")

    # Create leave types
    leave_types_data = [
        {'name': 'Annual Leave', 'max_leaves': 21, 'allocation_type': 'fixed'},
        {'name': 'Sick Leave', 'max_leaves': 10, 'allocation_type': 'fixed'},
        {'name': 'Maternity Leave', 'max_leaves': 90, 'allocation_type': 'fixed'},
        {'name': 'Paternity Leave', 'max_leaves': 15, 'allocation_type': 'fixed'},
        {'name': 'Emergency Leave', 'max_leaves': 5, 'allocation_type': 'fixed'},
    ]

    for leave_data in leave_types_data:
        leave_type, created = HrLeaveType.objects.get_or_create(
            name=leave_data['name'],
            company_id=company,
            defaults={
                'max_leaves': leave_data['max_leaves'],
                'allocation_type': leave_data['allocation_type'],
            }
        )
        print(f"Leave Type: {leave_type.name} {'created' if created else 'already exists'}")

    print("\n" + "="*60)
    print("🎉 MODULAR ERP SYSTEM - SAMPLE DATA COMPLETED!")
    print("="*60)
    print("📊 ACCOUNTING MODULE:")
    print(f"   📊 Companies: {ResCompany.objects.count()}")
    print(f"   📊 Accounts: {AccountAccount.objects.count()}")
    print(f"   📖 Journals: {AccountJournal.objects.count()}")
    print(f"   👥 Partners: {ResPartner.objects.count()}")
    print(f"   👤 Customers: {ResPartner.objects.filter(customer_rank__gt=0).count()}")
    print(f"   🏪 Vendors: {ResPartner.objects.filter(supplier_rank__gt=0).count()}")
    print(f"   💰 Taxes: {AccountTax.objects.count()}")
    print(f"   📅 Payment Terms: {AccountPaymentTerm.objects.count()}")
    print(f"   📈 Analytic Accounts: {AccountAnalyticAccount.objects.count()}")
    print()
    print("🛒 SALES MODULE:")
    print(f"   👥 Sales Teams: {SalesTeam.objects.count()}")
    print(f"   📋 Sales Orders: {SaleOrder.objects.count()}")
    print(f"   📝 Sales Order Lines: {SaleOrderLine.objects.count()}")
    print(f"   🛍️ Products: {ProductProduct.objects.count()}")
    print()
    print("🛍️ PURCHASE MODULE:")
    print(f"   📋 Purchase Orders: {PurchaseOrder.objects.count()}")
    print(f"   📝 Purchase Order Lines: {PurchaseOrderLine.objects.count()}")
    print()
    print("📦 INVENTORY MODULE:")
    print(f"   🏢 Warehouses: {StockWarehouse.objects.count()}")
    print(f"   📍 Locations: {StockLocation.objects.count()}")
    print(f"   📋 Picking Types: {StockPickingType.objects.count()}")
    print(f"   📊 Stock Quants: {StockQuant.objects.count()}")
    print()
    print("👥 HR MODULE:")
    print(f"   🏢 Departments: {HrDepartment.objects.count()}")
    print(f"   💼 Job Positions: {HrJob.objects.count()}")
    print(f"   👤 Employees: {HrEmployee.objects.count()}")
    print(f"   📄 Contracts: {HrContract.objects.count()}")
    print(f"   🏖️ Leave Types: {HrLeaveType.objects.count()}")
    print("="*60)
    print("🚀 Your MODULAR ERP system is ready with complete Odoo business logic!")
    print("✅ Accounting + Sales + Purchase + Inventory + HR modules integrated!")
    print("✅ All models follow exact Odoo structure and business rules!")
    print("✅ Complete business management: Finance, Sales, Procurement, Stock & HR!")

if __name__ == '__main__':
    create_sample_data()
