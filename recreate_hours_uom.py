#!/usr/bin/env python3
import psycopg2

def recreate_hours_uom():
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='dataicraft',
            user='odoo',
            password='odoo123'
        )
        cur = conn.cursor()
        
        print("🔧 === Recreating Required Hours UoM === 🔧")
        
        # Step 1: Find Working Time category
        cur.execute("SELECT id FROM uom_category WHERE name::text LIKE '%Working Time%';")
        working_time_cat = cur.fetchone()
        
        if not working_time_cat:
            print("❌ Working Time category not found")
            return
        
        working_time_cat_id = working_time_cat[0]
        print(f"📊 Working Time category ID: {working_time_cat_id}")
        
        # Step 2: Recreate Hours UoM with ID 4 (the system expects this)
        print("\n🔧 Recreating Hours UoM...")
        
        cur.execute("""
            INSERT INTO uom_uom (id, name, category_id, uom_type, factor, rounding, active)
            VALUES (4, '{"en_US": "Hours"}', %s, 'smaller', 8.0, 0.01, true)
            ON CONFLICT (id) DO UPDATE SET
                name = '{"en_US": "Hours"}',
                category_id = %s,
                uom_type = 'smaller',
                factor = 8.0,
                rounding = 0.01,
                active = true;
        """, (working_time_cat_id, working_time_cat_id))
        
        print("✅ Hours UoM recreated with ID 4")
        
        # Step 3: Recreate the system reference
        print("\n🔧 Recreating system reference...")
        
        cur.execute("""
            INSERT INTO ir_model_data (name, model, module, res_id, noupdate)
            VALUES ('product_uom_hour', 'uom.uom', 'uom', 4, true)
            ON CONFLICT (name, module) DO UPDATE SET
                res_id = 4,
                model = 'uom.uom';
        """)
        
        print("✅ System reference 'uom.product_uom_hour' recreated")
        
        # Step 4: BUT keep ALL products using Units UoM
        print("\n🔧 Ensuring products still use Units UoM...")
        
        cur.execute("UPDATE product_template SET uom_id = 1, uom_po_id = 1;")
        updated_products = cur.rowcount
        print(f"✅ {updated_products} products still use Units UoM")
        
        # Step 5: Set company timesheet to use Hours (for timesheet functionality)
        print("\n⚙️ Setting company timesheet to Hours...")
        
        cur.execute("UPDATE res_company SET timesheet_encode_uom_id = 4;")
        updated_companies = cur.rowcount
        print(f"✅ {updated_companies} companies set to use Hours for timesheets")
        
        # Step 6: Verification
        print("\n✅ Verification:")
        
        cur.execute("SELECT COUNT(*) FROM uom_uom WHERE id = 4;")
        hours_exists = cur.fetchone()[0]
        print(f"Hours UoM exists: {hours_exists}")
        
        cur.execute("SELECT COUNT(*) FROM product_template WHERE uom_id = 1;")
        units_products = cur.fetchone()[0]
        print(f"Products using Units: {units_products}")
        
        cur.execute("SELECT COUNT(*) FROM ir_model_data WHERE name = 'product_uom_hour';")
        system_ref_exists = cur.fetchone()[0]
        print(f"System reference exists: {system_ref_exists}")
        
        # Commit changes
        conn.commit()
        
        print(f"\n🎉 Hours UoM Recreation Complete!")
        print(f"✅ Hours UoM (ID: 4) exists in Working Time category")
        print(f"✅ System reference 'uom.product_uom_hour' exists")
        print(f"✅ ALL products still use Units UoM")
        print(f"✅ Timesheet functionality will work")
        print(f"✅ Order creation will work without conflicts")
        
        print(f"\n📋 Final Structure:")
        print(f"  📦 Products → Units UoM (Unit category)")
        print(f"  ⏰ Timesheets → Hours UoM (Working Time category)")
        print(f"  🚫 NO category mixing in orders")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    recreate_hours_uom()
