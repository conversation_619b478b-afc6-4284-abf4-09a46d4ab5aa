<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">

<t t-name="mail.Activity">
    <t t-set="activity" t-value="props.data"/>
    <div class="o-mail-Activity d-flex px-3 py-1 mb-2" t-on-click="onClick">
        <div class="o-mail-Activity-sidebar flex-shrink-0 position-relative">
            <img class="w-100 h-100 rounded cursor-pointer" t-attf-src="/web/image/res.users/{{activity.user_id[0]}}/avatar_128" t-on-click.stop.prevent="onClickAvatar"/>
                <div
                    class="o-mail-Activity-iconContainer position-absolute top-100 start-100 translate-middle d-flex align-items-center justify-content-center mt-n1 ms-n1 rounded-circle w-50 h-50"
                    t-att-class="{
                            'text-bg-success': activity.state === 'planned',
                            'text-bg-warning': activity.state === 'today',
                            'text-bg-danger': activity.state === 'overdue',
                        }"
                >
                    <i class="fa small" t-attf-class="{{ activity.icon }}"/>
                </div>
        </div>
        <div class="flex-grow px-3">
            <div class="o-mail-Activity-info lh-1">
                <span class="fw-bolder text-success" t-if="delay === 1">Tomorrow:</span>
                <span class="fw-bolder text-success" t-elif="delay gt 0">Due in <t t-esc="delay"/> days:</span>
                <span class="fw-bolder text-danger" t-elif="delay === -1">Yesterday:</span>
                <span class="fw-bolder text-danger" t-elif="delay lt 0"><t t-esc="-delay"/> days overdue:</span>
                <span class="fw-bolder text-warning" t-else="">Today:</span>
                <span class="fw-bolder px-2 text-break"><t t-esc="displayName"/></span>
                <span class="o-mail-Activity-user px-1">for <t t-esc="activity.user_id[1]"/></span>
                <i class="fa fa-info-circle btn-link btn-primary cursor-pointer ms-1" role="img" title="Info" aria-label="Info" t-on-click="toggleDetails"></i>
            </div>
            <div t-if="state.showDetails">
                <table class="o-mail-Activity-details table table-sm mt-2">
                    <tbody>
                        <tr><td class="text-end fw-bolder">Activity type</td><td><t t-esc="activity.activity_type_id[1]"/></td></tr>
                        <tr><td class="text-end fw-bolder">Created</td><td><t t-esc="displayCreateDate"/> by <t t-esc="activity.create_uid[1]"/></td></tr>
                        <tr><td class="text-end fw-bolder">Assigned to</td><td><t t-esc="activity.user_id[1]"/></td></tr>
                        <tr><td class="text-end fw-bolder">Due on</td><td><t t-esc="displayDeadlineDate"/></td></tr>
                    </tbody>
                </table>
            </div>
            <div t-if="activity.note" class="o-mail-Activity-note text-break" t-out="activity.note"/>
            <div t-if="activity.mail_template_ids?.length > 0" class="o-mail-Activity-mailTemplates">
                <ActivityMailTemplate activity="activity" onUpdate="props.onUpdate"/>
            </div>
            <div t-if="activity.can_write" class="lh-lg">
                <t name="tools">
                    <span class="o-mail-Activity-markDone btn btn-link btn-success p-0 me-3" t-on-click="onClickMarkAsDone"><i class="fa fa-check"/> Mark Done</span>
                    <FileUploader t-if="activity.activity_category === 'upload_file'" onUploaded.bind="onFileUploaded">
                        <t t-set-slot="toggler">
                            <span class="btn btn-link text-action p-0 me-3"><i t-attf-class="fa {{ activity.icon }}"/><t t-out="' ' + activity.display_name"/></span>
                        </t>
                    </FileUploader>
                    <span class="o-mail-Activity-edit btn btn-link text-action p-0 me-3" t-on-click="edit"><i class="fa fa-pencil"/> Edit</span>
                    <span class="btn btn-link btn-danger p-0" t-on-click="unlink"><i class="fa fa-times"/> Cancel</span>
                </t>
            </div>
        </div>
    </div>
</t>

</templates>
