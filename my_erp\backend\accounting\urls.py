"""
URL Configuration for Accounting Module
"""
from django.urls import path, include
from rest_framework.routers import Default<PERSON><PERSON>er
from . import views

# Create router and register viewsets
router = DefaultRouter()
router.register(r'companies', views.CompanyViewSet)
router.register(r'partners', views.PartnerViewSet)
router.register(r'accounts', views.AccountViewSet)
router.register(r'journals', views.JournalViewSet)
router.register(r'moves', views.MoveViewSet)
router.register(r'move-lines', views.MoveLineViewSet)
router.register(r'payments', views.PaymentViewSet)
router.register(r'taxes', views.TaxViewSet)
router.register(r'payment-terms', views.PaymentTermViewSet)

urlpatterns = [
    # API endpoints
    path('api/', include(router.urls)),
    
    # Dashboard endpoints
    path('api/dashboard/stats/', views.DashboardStatsView.as_view(), name='dashboard-stats'),
    path('api/reports/trial-balance/', views.TrialBalanceView.as_view(), name='trial-balance'),
    path('api/reports/profit-loss/', views.ProfitLossView.as_view(), name='profit-loss'),
    path('api/reports/balance-sheet/', views.BalanceSheetView.as_view(), name='balance-sheet'),
    
    # Reconciliation endpoints
    path('api/reconciliation/bank/', views.BankReconciliationView.as_view(), name='bank-reconciliation'),
    path('api/reconciliation/manual/', views.ManualReconciliationView.as_view(), name='manual-reconciliation'),
    
    # Utility endpoints
    path('api/sequence/next/', views.NextSequenceView.as_view(), name='next-sequence'),
    path('api/chart-of-accounts/setup/', views.ChartOfAccountsSetupView.as_view(), name='coa-setup'),
]
