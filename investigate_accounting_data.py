#!/usr/bin/env python3
import psycopg2

def investigate_accounting_data():
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='dataicraft',
            user='odoo',
            password='odoo123'
        )
        cur = conn.cursor()
        
        print("🔍 === INVESTIGATING REMAINING ACCOUNTING DATA === 🔍")
        
        # Step 1: Check journal entries by type
        print("\n📊 Journal Entries by Type:")
        
        cur.execute("""
            SELECT move_type, state, COUNT(*) as count
            FROM account_move 
            GROUP BY move_type, state
            ORDER BY move_type, state;
        """)
        
        move_types = cur.fetchall()
        for move_type, state, count in move_types:
            print(f"  📋 {move_type or 'None'} ({state}): {count} entries")
        
        # Step 2: Check recent journal entries
        print("\n📋 Recent Journal Entries:")
        
        cur.execute("""
            SELECT id, name, move_type, state, date, ref, amount_total
            FROM account_move 
            ORDER BY id DESC
            LIMIT 10;
        """)
        
        recent_moves = cur.fetchall()
        for move in recent_moves:
            move_id, name, move_type, state, date, ref, amount = move
            print(f"  📄 ID:{move_id} | {name} | Type:{move_type} | State:{state} | Date:{date} | Amount:{amount}")
        
        # Step 3: Check journals
        print("\n📖 Journals:")
        
        cur.execute("""
            SELECT id, name, code, type, active
            FROM account_journal
            ORDER BY type, name;
        """)
        
        journals = cur.fetchall()
        for journal in journals:
            journal_id, name, code, jtype, active = journal
            print(f"  📖 {code} | {name} | Type:{jtype} | Active:{active}")
        
        # Step 4: Check accounts with balances
        print("\n💰 Accounts with Balances:")
        
        cur.execute("""
            SELECT aa.code, aa.name, SUM(aml.debit - aml.credit) as balance
            FROM account_account aa
            LEFT JOIN account_move_line aml ON aa.id = aml.account_id
            GROUP BY aa.id, aa.code, aa.name
            HAVING SUM(aml.debit - aml.credit) != 0
            ORDER BY ABS(SUM(aml.debit - aml.credit)) DESC
            LIMIT 15;
        """)
        
        balances = cur.fetchall()
        if balances:
            print("Accounts with non-zero balances:")
            for code, name, balance in balances:
                print(f"  💰 {code} | {name} | Balance: {balance}")
        else:
            print("✅ No accounts with balances")
        
        # Step 5: Check for demo/test data patterns
        print("\n🔍 Checking for Demo/Test Patterns:")
        
        cur.execute("""
            SELECT COUNT(*) 
            FROM account_move 
            WHERE name LIKE '%DEMO%' OR name LIKE '%TEST%' OR name LIKE '%test%' 
            OR ref LIKE '%demo%' OR ref LIKE '%test%';
        """)
        
        demo_moves = cur.fetchone()[0]
        print(f"Moves with demo/test patterns: {demo_moves}")
        
        # Step 6: Check opening balance entries
        print("\n🔍 Opening Balance Entries:")
        
        cur.execute("""
            SELECT COUNT(*) 
            FROM account_move 
            WHERE move_type = 'entry' AND (name LIKE '%Opening%' OR name LIKE '%opening%');
        """)
        
        opening_entries = cur.fetchone()[0]
        print(f"Opening balance entries: {opening_entries}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    investigate_accounting_data()
