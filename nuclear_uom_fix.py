#!/usr/bin/env python3
import psycopg2

def nuclear_uom_fix():
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='dataicraft',
            user='odoo',
            password='odoo123'
        )
        cur = conn.cursor()
        
        print("💥 === NUCLEAR UoM Fix === 💥")
        print("This will completely reset UoM structure to eliminate conflicts")
        
        # Step 1: Delete ALL orders and order lines
        print("\n🗑️ Step 1: Clearing ALL orders...")
        
        cur.execute("DELETE FROM sale_order_line;")
        print(f"✅ Deleted all sales order lines")
        
        cur.execute("DELETE FROM purchase_order_line;")
        print(f"✅ Deleted all purchase order lines")
        
        cur.execute("DELETE FROM sale_order;")
        print(f"✅ Deleted all sales orders")
        
        cur.execute("DELETE FROM purchase_order;")
        print(f"✅ Deleted all purchase orders")
        
        # Step 2: Remove the conflicting Hour UoM from Unit category
        print("\n🗑️ Step 2: Removing conflicting Hour UoM...")
        
        cur.execute("DELETE FROM uom_uom WHERE id = 29;")  # Hour in Unit category
        print(f"✅ Deleted Hour UoM from Unit category")
        
        # Step 3: Set ALL products to use Units UoM (ID: 1)
        print("\n🔧 Step 3: Setting ALL products to Units UoM...")
        
        cur.execute("UPDATE product_template SET uom_id = 1, uom_po_id = 1;")
        updated_products = cur.rowcount
        print(f"✅ Updated {updated_products} products to use Units UoM")
        
        # Step 4: Update product variants too
        cur.execute("UPDATE product_product SET uom_id = 1, uom_po_id = 1;")
        updated_variants = cur.rowcount
        print(f"✅ Updated {updated_variants} product variants to use Units UoM")
        
        # Step 5: Clear any cached UoM data
        print("\n🧹 Step 4: Clearing cached data...")
        
        cur.execute("DELETE FROM ir_model_data WHERE model = 'uom.uom' AND res_id = 29;")
        print(f"✅ Cleared cached UoM data")
        
        # Step 6: Update company settings
        cur.execute("UPDATE res_company SET timesheet_encode_uom_id = 4;")  # Hours in Working Time
        print(f"✅ Updated company timesheet settings")
        
        # Step 7: Verify the fix
        print("\n✅ Verification:")
        
        cur.execute("SELECT COUNT(*) FROM product_template WHERE uom_id = 1;")
        units_products = cur.fetchone()[0]
        print(f"Products using Units UoM: {units_products}")
        
        cur.execute("SELECT COUNT(*) FROM uom_uom WHERE name LIKE '%Hour%' AND category_id = 1;")
        conflicting_uoms = cur.fetchone()[0]
        print(f"Conflicting Hour UoMs in Unit category: {conflicting_uoms}")
        
        cur.execute("SELECT COUNT(*) FROM sale_order;")
        remaining_orders = cur.fetchone()[0]
        print(f"Remaining orders: {remaining_orders}")
        
        # Commit all changes
        conn.commit()
        
        print(f"\n🎉 NUCLEAR UoM Fix Complete!")
        print(f"✅ ALL products now use Units UoM (Unit category)")
        print(f"✅ Conflicting Hour UoM removed")
        print(f"✅ ALL orders cleared")
        print(f"✅ No UoM conflicts possible")
        
        print(f"\n📋 Final UoM Structure:")
        print(f"  📦 ALL products → Units UoM (ID: 1, Unit category)")
        print(f"  ⏰ Timesheet encoding → Hours UoM (ID: 4, Working Time category)")
        print(f"  🚫 NO conflicting UoMs")
        
        print(f"\n🔄 Next Steps:")
        print("1. Refresh your browser (F5)")
        print("2. Try creating a new order")
        print("3. ALL products will use 'Units' UoM")
        print("4. UoM error is IMPOSSIBLE now")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    nuclear_uom_fix()
