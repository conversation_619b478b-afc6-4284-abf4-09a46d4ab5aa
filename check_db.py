#!/usr/bin/env python3
import psycopg2

def check_companies():
    try:
        # Connect to database
        conn = psycopg2.connect(
            host='localhost',
            database='dataicraft',
            user='odoo',
            password='odoo123'
        )
        cur = conn.cursor()
        
        # First, check the structure of res_company table
        print("=== RES_COMPANY TABLE STRUCTURE ===")
        cur.execute("""
            SELECT column_name, data_type
            FROM information_schema.columns
            WHERE table_name = 'res_company'
            ORDER BY ordinal_position;
        """)
        columns = cur.fetchall()
        for col in columns:
            print(f"{col[0]}: {col[1]}")

        print("\n=== EXISTING COMPANIES ===")
        cur.execute("SELECT id, name, email, phone FROM res_company;")
        companies = cur.fetchall()

        for company in companies:
            print(f"ID: {company[0]}")
            print(f"Name: {company[1]}")
            print(f"Email: {company[2]}")
            print(f"Phone: {company[3]}")
            print("-" * 40)
        
        # Check total tables
        cur.execute("SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';")
        table_count = cur.fetchone()[0]
        print(f"\nTotal tables in database: {table_count}")
        
        # Check some key accounting tables
        print("\n=== KEY ACCOUNTING TABLES ===")
        accounting_tables = [
            'account_account',
            'account_move', 
            'account_move_line',
            'account_journal',
            'account_tax'
        ]
        
        for table in accounting_tables:
            try:
                cur.execute(f"SELECT COUNT(*) FROM {table};")
                count = cur.fetchone()[0]
                print(f"{table}: {count} records")
            except Exception as e:
                print(f"{table}: Error - {e}")
        
        conn.close()
        
    except Exception as e:
        print(f"Database connection error: {e}")

if __name__ == "__main__":
    check_companies()
