<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.AttachmentList">
        <div class="o-mail-AttachmentList overflow-y-auto d-flex flex-column mt-1"
            t-att-class="{
                'o-inComposer': env.inComposer,
                'o-inChatWindow': env.inChatWindow,
                'me-2 pe-4': isInChatWindowAndIsAlignedLeft and !env.inComposer,
                'ms-2 ps-4': isInChatWindowAndIsAlignedRight and !env.inComposer,
            }"
        >
            <div class="d-flex flex-grow-1 flex-wrap mx-1" t-att-class="{
                'justify-content-end': isInChatWindowAndIsAlignedRight and !env.inComposer,
                        }" role="menu" >
                <div t-foreach="imagesAttachments" t-as="attachment" t-key="attachment.id"
                     t-att-aria-label="attachment.filename"
                     class="o-mail-AttachmentImage d-flex position-relative flex-shrink-0 mw-100 mb-1 me-1 rounded o-viewable"
                     t-att-title="attachment.name"
                     t-att-class="{ 'o-isUploading': attachment.uploading }"
                     tabindex="0"
                     t-att-data-mimetype="attachment.mimetype"
                     t-on-click="() => this.fileViewer.open(attachment, props.attachments)"
                     role="menuitem"
                >
                    <img
                        class="img img-fluid my-0 mx-auto"
                        t-att-class="{ 'opacity-25': attachment.uploading }"
                        t-att-src="getImageUrl(attachment)"
                        t-att-alt="attachment.name"
                        t-attf-style="max-width: min(100%, {{ imagesWidth }}px); max-height: {{ props.imagesHeight }}px;"
                        t-on-load="onImageLoaded"
                    />
                    <div t-if="attachment.uploading" class="position-absolute top-0 bottom-0 start-0 end-0 d-flex align-items-center justify-content-center" title="Uploading">
                        <i class="fa fa-spin fa-spinner"/>
                    </div>
                    <ImageActions actions="getActions(attachment)" imagesHeight="props.imagesHeight"/>
                </div>
            </div>
            <div class="d-flex flex-grow-1 flex-wrap mt-1 mx-1" t-att-class="{
                'justify-content-end': isInChatWindowAndIsAlignedRight and !env.inComposer,
            }">
                <!-- t-attf-class overridden in extensions -->
                <div t-foreach="nonImagesAttachments" t-as="attachment" t-key="attachment.id"
                    class="o-mail-AttachmentCard d-flex rounded mb-1 me-1 mw-100 overflow-auto"
                    t-att-class="{
                        'ms-1': isInChatWindowAndIsAlignedRight,
                        'me-1': !isInChatWindowAndIsAlignedRight,
                        'o-viewable': attachment.isViewable,
                        'o-isUploading': attachment.uploading,
                    }"
                    t-attf-class="bg-300"
                    t-att-title="attachment.name ? attachment.name : undefined" role="menu" t-att-aria-label="attachment.name"
                    t-on-click="() => this.fileViewer.open(attachment, props.attachments)"
                >
                    <!-- o_image from mimetype.scss -->
                    <t t-ref="nonImageMain">
                        <div class="o-mail-AttachmentCard-image o_image flex-shrink-0 m-1" role="menuitem" aria-label="Preview" t-att-tabindex="-1" t-att-aria-disabled="false" t-att-data-mimetype="attachment.mimetype"/>
                        <div class="overflow-auto d-flex justify-content-center flex-column px-1">
                            <div t-if="attachment.name" class="text-truncate" t-out="props.messageSearch?.highlight(attachment.name) ?? attachment.name"/>
                            <small t-if="attachment.extension" class="text-uppercase" t-esc="attachment.extension"/>
                        </div>
                    </t>
                    <div class="o-mail-AttachmentCard-aside position-relative rounded-end overflow-hidden" t-att-class="{ 'o-hasMultipleActions d-flex flex-column': showDelete and !env.inComposer }">
                        <div t-if="attachment.uploading" class="d-flex justify-content-center align-items-center w-100 h-100" title="Uploading">
                            <i class="fa fa-spin fa-spinner"/>
                        </div>
                        <div t-if="!attachment.uploading and env.inComposer" class="d-flex justify-content-center align-items-center w-100 h-100 text-primary" title="Uploaded">
                            <i class="fa fa-check"/>
                        </div>
                        <button t-if="showDelete"
                            class="o-mail-AttachmentCard-unlink btn top-0 justify-content-center align-items-center d-flex w-100 h-100 rounded-0 border-0"
                            t-attf-class="{{ env.inComposer ? 'o-inComposer position-absolute btn-primary transition-base' : 'bg-300' }}"
                            t-on-click.stop="() => this.onClickUnlink(attachment)" title="Remove"
                        >
                            <i class="fa fa-trash" role="img" aria-label="Remove"/>
                        </button>
                        <t t-if="attachment.type === 'url'">
                            <a class="btn d-flex align-items-center justify-content-center w-100 h-100 rounded-0" t-attf-class="bg-300" t-att-href="attachment.url" target='_blank' title="Open Link">
                                <i class="fa fa-external-link" role="img" aria-label="Open Link"/>
                            </a>
                        </t>
                        <!-- t-attf-class overridden in extensions -->
                        <button t-elif="canDownload(attachment)" class="btn d-flex align-items-center justify-content-center w-100 h-100 rounded-0"
                                t-attf-class="bg-300"
                                t-att-data-download-url="attachment.downloadUrl"
                                t-on-click.stop="() => this.onClickDownload(attachment)" title="Download"
                        >
                            <i class="fa fa-download" role="img" aria-label="Download"/>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </t>

    <t t-name="mail.ImageActions">
        <div class="o-mail-attachment-actions position-absolute top-0 bottom-0 start-0 end-0 p-1 text-white o-opacity-hoverable opacity-100-hover d-flex align-items-end flax-wrap flex-column" t-att-class="{ 'opacity-0': !actionsMenuState.isOpen }">
            <button t-if="props.actions.length === 1 and props.imagesHeight gt 75" class="btn btn-sm btn-light rounded px-1 py-0" t-att-class="{ 'opacity-75 opacity-100-hover': !isMobileOS }" tabindex="0" t-att-aria-label="props.actions[0].label" t-att-title="props.actions[0].label" role="menuitem" t-on-click.stop="props.actions[0].onSelect">
                <i t-att-class="props.actions[0].icon"/>
            </button>
            <Dropdown t-else="" onStateChanged="(state) => this.setActionsMenuState(state)" togglerClass="'btn btn-sm btn-light rounded px-1 py-0'" menuClass="'d-flex flex-column py-0' + (props.actions.length gt 1 ? ' py-0' : '')" position="'right-start'">
                <t t-set-slot="toggler">
                    <i class="oi oi-chevron-down" t-att-class="{ 'opacity-75 opacity-100-hover py-0': !isMobileOS }" tabindex="0" aria-label="Actions" title="Actions" role="menuitem"/>
                </t>
                <t t-set-slot="default">
                    <DropdownItem t-foreach="props.actions" t-as="action" t-key="action_index" class="'px-2 py-1 d-flex align-items-center rounded-0'" onSelected="action.onSelect">
                        <i class="fa-fw" t-att-class="action.icon"/>
                        <span class="mx-2" t-esc="action.label"/>
                    </DropdownItem>
                </t>
            </Dropdown>
        </div>
    </t>

</templates>
