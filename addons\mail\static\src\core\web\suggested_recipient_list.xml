<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="mail.SuggestedRecipientsList">
        <div t-att-class="props.className" t-att-style="props.styleString">
            <SuggestedRecipient
                t-foreach="suggestedRecipients"
                t-as="recipient"
                t-key="recipient.id"
                thread="props.thread"
                recipient="recipient"
            />
            <t t-if="props.thread.suggestedRecipients.length > 3">
                <button t-if="!state.showMore" class="btn btn-sm btn-link" t-on-click="() => this.state.showMore = true">
                    Show more
                </button>
                <button t-else="" class="btn btn-sm btn-link" t-on-click="() => this.state.showMore = false">
                    Show less
                </button>
            </t>
        </div>
    </t>
</templates>
