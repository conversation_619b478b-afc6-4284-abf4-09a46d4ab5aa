#!/usr/bin/env python3
import psycopg2

def fix_database_integrity():
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='dataicraft',
            user='odoo',
            password='odoo123'
        )
        cur = conn.cursor()
        
        print("🔧 === Fixing Database Integrity === 🔧")
        
        # Step 1: Check for orphaned records
        print("\n🔍 Checking for orphaned records...")
        
        # Check for products with invalid UoM references
        cur.execute("""
            SELECT pt.id, pt.name, pt.uom_id 
            FROM product_template pt 
            LEFT JOIN uom_uom u ON pt.uom_id = u.id 
            WHERE u.id IS NULL;
        """)
        
        orphaned_products = cur.fetchall()
        if orphaned_products:
            print(f"❌ Found {len(orphaned_products)} products with invalid UoM references")
            for product in orphaned_products:
                print(f"  Product ID {product[0]}: {product[1]} -> UoM ID {product[2]} (missing)")
        else:
            print("✅ No orphaned product UoM references")
        
        # Step 2: Fix any remaining UoM issues
        print("\n🔧 Ensuring all products use valid UoMs...")
        
        # Set any products with invalid UoM to Units (ID: 1)
        cur.execute("""
            UPDATE product_template 
            SET uom_id = 1, uom_po_id = 1 
            WHERE uom_id NOT IN (SELECT id FROM uom_uom);
        """)
        fixed_products = cur.rowcount
        if fixed_products > 0:
            print(f"✅ Fixed {fixed_products} products with invalid UoM references")
        
        # Step 3: Clear any problematic cache tables
        print("\n🧹 Clearing cache tables...")
        
        # Clear ir_model_data for UoM records that might be cached
        cur.execute("DELETE FROM ir_model_data WHERE model = 'uom.uom' AND res_id NOT IN (SELECT id FROM uom_uom);")
        cleared_cache = cur.rowcount
        print(f"✅ Cleared {cleared_cache} orphaned cache entries")
        
        # Step 4: Rebuild sequences if needed
        print("\n🔧 Checking sequences...")
        
        cur.execute("SELECT COUNT(*) FROM ir_sequence WHERE code LIKE '%sale%' OR code LIKE '%purchase%';")
        sequences = cur.fetchone()[0]
        print(f"✅ Found {sequences} order sequences")
        
        # Step 5: Check for any constraint violations
        print("\n🔍 Checking constraints...")
        
        # Check UoM category consistency
        cur.execute("""
            SELECT COUNT(*) 
            FROM uom_uom u1 
            JOIN uom_uom u2 ON u1.category_id = u2.category_id 
            WHERE u1.uom_type = 'reference' AND u2.uom_type = 'reference' AND u1.id != u2.id;
        """)
        
        duplicate_refs = cur.fetchone()[0]
        if duplicate_refs > 0:
            print(f"❌ Found {duplicate_refs} UoM categories with multiple reference units")
        else:
            print("✅ UoM categories have single reference units")
        
        # Step 6: Vacuum and analyze database
        print("\n🧹 Optimizing database...")
        
        conn.commit()  # Commit before vacuum
        conn.set_isolation_level(psycopg2.extensions.ISOLATION_LEVEL_AUTOCOMMIT)
        
        cur.execute("VACUUM ANALYZE product_template;")
        cur.execute("VACUUM ANALYZE uom_uom;")
        cur.execute("VACUUM ANALYZE sale_order;")
        cur.execute("VACUUM ANALYZE purchase_order;")
        
        print("✅ Database optimized")
        
        # Step 7: Final verification
        print("\n✅ Final verification...")
        
        cur.execute("SELECT COUNT(*) FROM product_template WHERE uom_id = 1;")
        units_products = cur.fetchone()[0]
        
        cur.execute("SELECT COUNT(*) FROM product_template;")
        total_products = cur.fetchone()[0]
        
        print(f"Products using Units UoM: {units_products}/{total_products}")
        
        if units_products == total_products:
            print("✅ ALL products use Units UoM - no conflicts possible")
        else:
            print(f"⚠️ {total_products - units_products} products not using Units UoM")
        
        print(f"\n🎉 Database Integrity Fix Complete!")
        print(f"✅ Fixed orphaned records")
        print(f"✅ Cleared cache entries")
        print(f"✅ Optimized database")
        print(f"✅ Verified UoM consistency")
        
        print(f"\n🔄 Next Steps:")
        print("1. The server should be running properly now")
        print("2. Try accessing http://localhost:8069 in your browser")
        print("3. Clear browser cache (Ctrl+F5)")
        print("4. Try creating an order again")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    fix_database_integrity()
