#!/usr/bin/env python3
import xmlrpc.client

def diagnose_uom_error():
    try:
        # Connection details
        url = 'http://localhost:8069'
        db = 'dataicraft'
        username = '<EMAIL>'
        password = 'admin123'
        
        # Connect to Odoo
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            print("❌ Authentication failed!")
            return
        
        print(f"✅ Connected to Odoo as user ID: {uid}")
        
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        print("\n🔍 === Diagnosing UoM Error === 🔍")
        
        # Step 1: Check all UoM categories and their reference UoMs
        print("\n📊 UoM Categories and Reference UoMs:")
        
        categories = models.execute_kw(
            db, uid, password,
            'uom.category', 'search_read',
            [[]],
            {'fields': ['id', 'name']}
        )
        
        for category in categories:
            # Find reference UoM for this category
            ref_uoms = models.execute_kw(
                db, uid, password,
                'uom.uom', 'search_read',
                [[['category_id', '=', category['id']], ['uom_type', '=', 'reference']]],
                {'fields': ['id', 'name', 'uom_type']}
            )
            
            print(f"  📂 {category['name']} (ID: {category['id']})")
            if ref_uoms:
                for ref_uom in ref_uoms:
                    print(f"    📏 Reference UoM: {ref_uom['name']} (ID: {ref_uom['id']})")
            else:
                print(f"    ❌ NO REFERENCE UoM FOUND!")
        
        # Step 2: Check for products with UoM issues
        print("\n🔍 Checking Products for UoM Issues:")
        
        products = models.execute_kw(
            db, uid, password,
            'product.template', 'search_read',
            [[]],
            {'fields': ['id', 'name', 'uom_id', 'uom_po_id', 'type', 'service_type']}
        )
        
        uom_issues = []
        
        for product in products:
            try:
                # Get UoM details
                uom_id = product['uom_id'][0] if product['uom_id'] else None
                uom_po_id = product['uom_po_id'][0] if product['uom_po_id'] else None
                
                if uom_id:
                    uom_details = models.execute_kw(
                        db, uid, password,
                        'uom.uom', 'read',
                        [uom_id],
                        {'fields': ['name', 'category_id', 'uom_type']}
                    )
                    
                    uom_name = uom_details['name']
                    uom_category = uom_details['category_id'][1] if uom_details['category_id'] else 'No Category'
                    
                    # Check for potential issues
                    issue = None
                    if product['type'] == 'service' and uom_category != 'Working Time':
                        issue = f"Service product using {uom_category} instead of Working Time"
                    elif product['type'] in ['product', 'consu'] and uom_category != 'Unit':
                        issue = f"Physical product using {uom_category} instead of Unit"
                    
                    if issue:
                        uom_issues.append({
                            'product': product['name'],
                            'type': product['type'],
                            'uom': uom_name,
                            'category': uom_category,
                            'issue': issue
                        })
                        
            except Exception as e:
                print(f"    ⚠️ Error checking product {product['name']}: {e}")
        
        if uom_issues:
            print(f"\n❌ Found {len(uom_issues)} products with UoM issues:")
            for issue in uom_issues:
                print(f"  🔴 {issue['product']}")
                print(f"     Type: {issue['type']}")
                print(f"     UoM: {issue['uom']} ({issue['category']})")
                print(f"     Issue: {issue['issue']}")
                print()
        else:
            print("✅ No obvious UoM issues found in products")
        
        # Step 3: Check recent order lines for UoM conflicts
        print("\n🔍 Checking Recent Order Lines:")
        
        # Check sales order lines
        so_lines = models.execute_kw(
            db, uid, password,
            'sale.order.line', 'search_read',
            [[['create_date', '>', '2025-07-14']]],
            {'fields': ['id', 'product_id', 'product_uom', 'order_id', 'create_date'], 'limit': 10}
        )
        
        print(f"Recent Sales Order Lines: {len(so_lines)}")
        for line in so_lines:
            if line['product_id'] and line['product_uom']:
                try:
                    # Get product UoM
                    product = models.execute_kw(
                        db, uid, password,
                        'product.product', 'read',
                        [line['product_id'][0]],
                        {'fields': ['name', 'uom_id']}
                    )
                    
                    # Get line UoM
                    line_uom = models.execute_kw(
                        db, uid, password,
                        'uom.uom', 'read',
                        [line['product_uom'][0]],
                        {'fields': ['name', 'category_id']}
                    )
                    
                    # Get product UoM
                    product_uom = models.execute_kw(
                        db, uid, password,
                        'uom.uom', 'read',
                        [product['uom_id'][0]],
                        {'fields': ['name', 'category_id']}
                    )
                    
                    # Check if categories match
                    if line_uom['category_id'][0] != product_uom['category_id'][0]:
                        print(f"  🔴 CONFLICT in SO Line {line['id']}:")
                        print(f"     Product: {product['name']}")
                        print(f"     Product UoM: {product_uom['name']} ({product_uom['category_id'][1]})")
                        print(f"     Line UoM: {line_uom['name']} ({line_uom['category_id'][1]})")
                        print(f"     Order: {line['order_id'][1]}")
                        print()
                        
                except Exception as e:
                    print(f"  ⚠️ Error checking SO line {line['id']}: {e}")
        
        # Step 4: Provide specific fix recommendations
        print("\n🔧 Fix Recommendations:")
        
        if uom_issues:
            print("1. Fix the following products:")
            for issue in uom_issues:
                if 'Service' in issue['issue']:
                    print(f"   - Change {issue['product']} UoM to 'Hours'")
                else:
                    print(f"   - Change {issue['product']} UoM to 'Units'")
        
        print("\n2. Manual steps to fix:")
        print("   a. Go to Inventory → Products → Products")
        print("   b. Find the problematic product")
        print("   c. Edit the product")
        print("   d. Change 'Unit of Measure' to correct one:")
        print("      - Services → Hours")
        print("      - Physical products → Units")
        print("   e. Save the product")
        
        print("\n3. If creating an order:")
        print("   a. Make sure the product UoM is correct first")
        print("   b. The order line will automatically use the product's UoM")
        print("   c. Don't manually change the UoM on the order line")
        
        # Step 5: Create a simple fix script
        print("\n🛠️ Quick Fix Available:")
        print("Run this command to fix all UoM issues:")
        print("python fix_specific_uom_issues.py")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    diagnose_uom_error()
