<?xml version="1.0" encoding="UTF-8" ?>
<templates xml:space="preserve">

<t t-name="mail.ChatWindow">
    <div class="o-mail-ChatWindow"
        t-att-style="style"
        t-att-class="{
                'o-mobile': ui.isSmall,
                'w-100 h-100': ui.isSmall and !isMobileFoldedForLivechatVisitor,
                'o-folded': props.chatWindow.folded or props.chatWindow.hidden,
                'position-fixed d-flex flex-column': !props.chatWindow.hidden,
                'overflow-hidden bottom-0': !props.chatWindow.hidden and !isMobileFoldedForLivechatVisitor,
                'rounded-top-3': !props.chatWindow.hidden and !ui.isSmall,
                'o-isMobileFoldedForLivechatVisitor': isMobileFoldedForLivechatVisitor,
                'o-moveUp': isMobileFoldedForLivechatVisitor and state.moveUp,
                }"
        t-on-keydown="onKeydown"
        tabindex="1"
    >
        <div class="o-mail-ChatWindow-header d-flex align-items-center flex-shrink-0 bg-100" t-on-click="onClickHeader" t-att-class="{ 'cursor-pointer': !ui.isSmall, 'border-bottom': !props.chatWindow.folded, 'position-relative rounded-circle': isMobileFoldedForLivechatVisitor }">
            <t t-if="threadActions.actions.length > 3">
                <Dropdown position="'bottom-start'" onStateChanged="state => this.onActionsMenuStateChanged(state)" togglerClass="`o-mail-ChatWindow-command d-flex btn align-items-center text-truncate p-0 p-1 ${ !ui.isSmall ? 'ms-2' : '' } ${state.actionsMenuOpened ? 'o-active' : ''}`" menuClass="'d-flex flex-column py-0'" class="'d-flex text-truncate'" disabled="state.editingName" title="actionsMenuTitleText">
                    <t t-set-slot="toggler">
                        <t t-call="mail.ChatWindow.headerContent"/>
                    </t>
                    <t t-set-slot="default">
                        <t t-if="ui.isSmall" t-set="actions" t-value="threadActions.actions.slice(1, -1)"/>
                        <t t-else="" t-set="actions" t-value="threadActions.actions.slice(1, -2)"/>
                        <DropdownItem t-foreach="actions" t-as="action" t-key="action.id" class="'o-mail-ChatWindow-command btn rounded-0 d-flex align-items-center px-2 py-2 m-0 opacity-75 opacity-100-hover'" title="action.name" onSelected="() => action.onSelect()"><i t-att-class="action.icon"/><span class="mx-2" t-out="action.name"/></DropdownItem>
                    </t>
                </Dropdown>
                <AutoresizeInput
                    t-if="state.editingName"
                    className="'text-truncate fw-bold flex-shrink-1 me-1 py-0'"
                    enabled="true"
                    autofocus="true"
                    onValidate.bind="renameThread"
                    value="props.chatWindow.displayName"
                />
            </t>
            <t t-else="">
                <t t-call="mail.ChatWindow.headerContent"/>
            </t>
            <div class="flex-grow-1"/>
            <div t-if="thread and thread.needactionCounter > 0" class="o-mail-ChatWindow-counter mx-1 my-0 badge rounded-pill fw-bold o-discuss-badge" t-ref="needactionCounter" t-att-class="{
                'position-absolute': isMobileFoldedForLivechatVisitor,
            }" t-att-style="isMobileFoldedForLivechatVisitor ? 'top: -5px; right: -10px;' : ''">
                <t t-out="thread.needactionCounter"/>
            </div>
            <t t-if="threadActions.actions.length > 1 and !isMobileFoldedForLivechatVisitor" t-call="mail.ChatWindow.dropdownAction">
                <t t-set="action" t-value="threadActions.actions[0]"/>
            </t>
            <t t-if="!ui.isSmall and threadActions.actions.length > 2 and !isMobileFoldedForLivechatVisitor">
                <t t-call="mail.ChatWindow.dropdownAction">
                    <t t-set="action" t-value="threadActions.actions.at(-2)"/>
                </t>
            </t>
            <t t-if="!isMobileFoldedForLivechatVisitor" t-call="mail.ChatWindow.dropdownAction">
                <t t-set="action" t-value="threadActions.actions.at(-1)"/>
                <t t-set="itemClass" t-value="'me-1'"/>
            </t>
        </div>
        <div t-if="!props.chatWindow.folded and !props.chatWindow.hidden or ui.isSmall" class="bg-view d-flex flex-column h-100 overflow-auto position-relative" t-ref="content">
            <t t-if="thread" name="thread content">
                <div t-if="threadActions.activeAction?.componentCondition" class="h-100" t-attf-class="{{ threadActions.activeAction.panelOuterClass }}">
                    <t t-component="threadActions.activeAction.component" t-props="{ ...threadActions.activeAction.componentProps, thread }"/>
                </div>
                <t t-else="">
                    <Thread isInChatWindow="true" thread="thread" t-key="thread.localId" jumpPresent="state.jumpThreadPresent" messageEdition="messageEdition" messageToReplyTo="messageToReplyTo"/>
                    <div t-if="thread and typingService.hasTypingMembers(thread)" class="d-flex bg-view position-relative">
                        <div class="o-mail-ChatWindow-typing d-flex px-2 position-absolute bottom-0 start-0 w-100 bg-view align-items-center">
                            <Typing channel="thread" size="'medium'"/>
                        </div>
                    </div>
                    <Composer t-if="thread" composer="thread.composer" autofocus="props.chatWindow.autofocus" mode="'compact'" messageEdition="messageEdition" messageToReplyTo="messageToReplyTo" onPostCallback.bind="() => this.state.jumpThreadPresent++" dropzoneRef="contentRef" type="composerType"/>
                </t>
            </t>
        </div>
    </div>
</t>

<t t-name="mail.ChatWindow.dropdownAction">
    <button class="o-mail-ChatWindow-command btn d-flex p-2 opacity-75 opacity-100-hover" t-att-class="itemClass" t-att-title="action.name" t-on-click.stop="() => action.onSelect()"><i t-att-class="action.icon"/></button>
</t>

<t t-name="mail.ChatWindow.headerContent">
    <div t-if="thread" class="o-mail-ChatWindow-threadAvatar my-0" t-att-class="{
        'ms-1': threadActions.actions.length > 4 and !isMobileFoldedForLivechatVisitor,
        'ms-3': threadActions.actions.length lte 4 and !isMobileFoldedForLivechatVisitor,
        'me-1': !isMobileFoldedForLivechatVisitor,
    }">
        <img t-att-class="{ 'o-isMobileFoldedForLivechatVisitor rounded-circle': isMobileFoldedForLivechatVisitor, 'rounded': !isMobileFoldedForLivechatVisitor }" t-att-src="thread.imgUrl" alt="Thread Image"/>
    </div>
    <ThreadIcon t-if="thread and thread.type === 'chat' and thread.chatPartner" thread="thread"/>
    <div t-if="!state.editingName and !isMobileFoldedForLivechatVisitor" class="text-truncate fw-bold border border-transparent me-1 my-0" t-att-title="props.chatWindow.displayName" t-esc="props.chatWindow.displayName" t-att-class="thread ? 'ms-1' : 'ms-3'"/>
</t>
</templates>
