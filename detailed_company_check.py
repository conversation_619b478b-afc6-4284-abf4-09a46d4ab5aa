#!/usr/bin/env python3
import psycopg2

def detailed_company_check():
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='dataicraft',
            user='odoo',
            password='odoo123'
        )
        cur = conn.cursor()
        
        print("=== DETAILED COMPANY ANALYSIS ===")
        
        # Check all companies with full details
        cur.execute("""
            SELECT 
                c.id, 
                c.name, 
                c.email, 
                c.phone, 
                c.create_date,
                c.write_date,
                c.currency_id,
                curr.name as currency_name,
                c.partner_id,
                p.name as partner_name,
                p.country_id,
                country.name as country_name
            FROM res_company c
            LEFT JOIN res_currency curr ON c.currency_id = curr.id
            LEFT JOIN res_partner p ON c.partner_id = p.id
            LEFT JOIN res_country country ON p.country_id = country.id
            ORDER BY c.create_date DESC;
        """)
        
        companies = cur.fetchall()
        
        print(f"Total companies found: {len(companies)}")
        print("=" * 80)
        
        for i, company in enumerate(companies, 1):
            print(f"COMPANY {i}:")
            print(f"  ID: {company[0]}")
            print(f"  Name: {company[1]}")
            print(f"  Email: {company[2]}")
            print(f"  Phone: {company[3]}")
            print(f"  Created: {company[4]}")
            print(f"  Last Modified: {company[5]}")
            print(f"  Currency ID: {company[6]} ({company[7]})")
            print(f"  Partner ID: {company[8]} ({company[9]})")
            print(f"  Country ID: {company[10]} ({company[11]})")
            print("-" * 80)
        
        # Check recent activity
        print("\n=== RECENT DATABASE ACTIVITY ===")
        
        # Check recent partners (companies create partners)
        cur.execute("""
            SELECT id, name, email, phone, create_date, is_company
            FROM res_partner 
            WHERE create_date > NOW() - INTERVAL '1 hour'
            ORDER BY create_date DESC;
        """)
        
        recent_partners = cur.fetchall()
        print(f"Recent partners (last hour): {len(recent_partners)}")
        for partner in recent_partners:
            print(f"  Partner ID: {partner[0]} | Name: {partner[1]} | Email: {partner[2]} | Is Company: {partner[5]}")
        
        # Check accounting setup for companies
        print("\n=== ACCOUNTING SETUP PER COMPANY ===")
        for company in companies:
            company_id = company[0]
            company_name = company[1]
            
            print(f"\nCompany: {company_name} (ID: {company_id})")
            
            # Check journals for this company
            cur.execute("""
                SELECT id, name, code, type 
                FROM account_journal 
                WHERE company_id = %s
                ORDER BY type, name;
            """, (company_id,))
            
            journals = cur.fetchall()
            print(f"  Journals: {len(journals)}")
            for journal in journals:
                print(f"    - {journal[1]} ({journal[2]}) - Type: {journal[3]}")
            
            # Check chart of accounts for this company
            cur.execute("""
                SELECT COUNT(*) 
                FROM account_account 
                WHERE company_id = %s;
            """, (company_id,))
            
            account_count = cur.fetchone()[0]
            print(f"  Chart of Accounts: {account_count} accounts")
            
            # Check taxes for this company
            cur.execute("""
                SELECT id, name, amount, type_tax_use
                FROM account_tax 
                WHERE company_id = %s;
            """, (company_id,))
            
            taxes = cur.fetchall()
            print(f"  Taxes: {len(taxes)}")
            for tax in taxes:
                print(f"    - {tax[1]}: {tax[2]}% ({tax[3]})")
        
        conn.close()
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    detailed_company_check()
