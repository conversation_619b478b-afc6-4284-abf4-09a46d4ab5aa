
/* <inline asset> */
@charset "UTF-8"; 

/* /web/static/lib/bootstrap/scss/_functions.scss */
 

/* /web/static/lib/bootstrap/scss/_mixins.scss */
 

/* /web/static/src/scss/functions.scss */
 

/* /web/static/src/scss/mixins_forwardport.scss */
 

/* /web/static/src/scss/bs_mixins_overrides.scss */
 .o_cc5 h1, .o_colored_level .o_cc5 h1, .o_cc4 h1, .o_colored_level .o_cc4 h1, .o_cc3 h1, .o_colored_level .o_cc3 h1, .o_cc2 h1, .o_colored_level .o_cc2 h1, .o_cc1 h1, .o_colored_level .o_cc1 h1, .bg-o-color-5 h1, .o_colored_level .bg-o-color-5 h1, .bg-o-color-4 h1, .o_colored_level .bg-o-color-4 h1, .bg-o-color-3 h1, .o_colored_level .bg-o-color-3 h1, .bg-o-color-2 h1, .o_colored_level .bg-o-color-2 h1, .bg-o-color-1 h1, .o_colored_level .bg-o-color-1 h1, .bg-white h1, .o_colored_level .bg-white h1, .bg-black h1, .o_colored_level .bg-black h1, .bg-black-50 h1, .o_colored_level .bg-black-50 h1, .bg-black-75 h1, .o_colored_level .bg-black-75 h1, .bg-white-50 h1, .o_colored_level .bg-white-50 h1, .bg-white-75 h1, .o_colored_level .bg-white-75 h1, .bg-white-85 h1, .o_colored_level .bg-white-85 h1, .bg-900 h1, .o_colored_level .bg-900 h1, .bg-800 h1, .o_colored_level .bg-800 h1, .bg-700 h1, .o_colored_level .bg-700 h1, .bg-600 h1, .o_colored_level .bg-600 h1, .bg-500 h1, .o_colored_level .bg-500 h1, .bg-400 h1, .o_colored_level .bg-400 h1, .bg-300 h1, .o_colored_level .bg-300 h1, .bg-200 h1, .o_colored_level .bg-200 h1, .bg-100 h1, .o_colored_level .bg-100 h1, .toast-body h1, .o_colored_level .toast-body h1, .card-body h1, .o_colored_level .card-body h1, .o_cc5 .h1, .o_colored_level .o_cc5 .h1, .o_cc4 .h1, .o_colored_level .o_cc4 .h1, .o_cc3 .h1, .o_colored_level .o_cc3 .h1, .o_cc2 .h1, .o_colored_level .o_cc2 .h1, .o_cc1 .h1, .o_colored_level .o_cc1 .h1, .bg-o-color-5 .h1, .o_colored_level .bg-o-color-5 .h1, .bg-o-color-4 .h1, .o_colored_level .bg-o-color-4 .h1, .bg-o-color-3 .h1, .o_colored_level .bg-o-color-3 .h1, .bg-o-color-2 .h1, .o_colored_level .bg-o-color-2 .h1, .bg-o-color-1 .h1, .o_colored_level .bg-o-color-1 .h1, .bg-white .h1, .o_colored_level .bg-white .h1, .bg-black .h1, .o_colored_level .bg-black .h1, .bg-black-50 .h1, .o_colored_level .bg-black-50 .h1, .bg-black-75 .h1, .o_colored_level .bg-black-75 .h1, .bg-white-50 .h1, .o_colored_level .bg-white-50 .h1, .bg-white-75 .h1, .o_colored_level .bg-white-75 .h1, .bg-white-85 .h1, .o_colored_level .bg-white-85 .h1, .bg-900 .h1, .o_colored_level .bg-900 .h1, .bg-800 .h1, .o_colored_level .bg-800 .h1, .bg-700 .h1, .o_colored_level .bg-700 .h1, .bg-600 .h1, .o_colored_level .bg-600 .h1, .bg-500 .h1, .o_colored_level .bg-500 .h1, .bg-400 .h1, .o_colored_level .bg-400 .h1, .bg-300 .h1, .o_colored_level .bg-300 .h1, .bg-200 .h1, .bg-100 .h1, .toast-body .h1, .card-body .h1, .o_cc5 h2, .o_colored_level .o_cc5 h2, .o_cc4 h2, .o_colored_level .o_cc4 h2, .o_cc3 h2, .o_colored_level .o_cc3 h2, .o_cc2 h2, .o_colored_level .o_cc2 h2, .o_cc1 h2, .o_colored_level .o_cc1 h2, .bg-o-color-5 h2, .o_colored_level .bg-o-color-5 h2, .bg-o-color-4 h2, .o_colored_level .bg-o-color-4 h2, .bg-o-color-3 h2, .o_colored_level .bg-o-color-3 h2, .bg-o-color-2 h2, .o_colored_level .bg-o-color-2 h2, .bg-o-color-1 h2, .o_colored_level .bg-o-color-1 h2, .bg-white h2, .o_colored_level .bg-white h2, .bg-black h2, .o_colored_level .bg-black h2, .bg-black-50 h2, .o_colored_level .bg-black-50 h2, .bg-black-75 h2, .o_colored_level .bg-black-75 h2, .bg-white-50 h2, .o_colored_level .bg-white-50 h2, .bg-white-75 h2, .o_colored_level .bg-white-75 h2, .bg-white-85 h2, .o_colored_level .bg-white-85 h2, .bg-900 h2, .o_colored_level .bg-900 h2, .bg-800 h2, .o_colored_level .bg-800 h2, .bg-700 h2, .o_colored_level .bg-700 h2, .bg-600 h2, .o_colored_level .bg-600 h2, .bg-500 h2, .o_colored_level .bg-500 h2, .bg-400 h2, .o_colored_level .bg-400 h2, .bg-300 h2, .o_colored_level .bg-300 h2, .bg-200 h2, .o_colored_level .bg-200 h2, .bg-100 h2, .o_colored_level .bg-100 h2, .toast-body h2, .o_colored_level .toast-body h2, .card-body h2, .o_colored_level .card-body h2, .o_cc5 .h2, .o_colored_level .o_cc5 .h2, .o_cc4 .h2, .o_colored_level .o_cc4 .h2, .o_cc3 .h2, .o_colored_level .o_cc3 .h2, .o_cc2 .h2, .o_colored_level .o_cc2 .h2, .o_cc1 .h2, .o_colored_level .o_cc1 .h2, .bg-o-color-5 .h2, .o_colored_level .bg-o-color-5 .h2, .bg-o-color-4 .h2, .o_colored_level .bg-o-color-4 .h2, .bg-o-color-3 .h2, .o_colored_level .bg-o-color-3 .h2, .bg-o-color-2 .h2, .o_colored_level .bg-o-color-2 .h2, .bg-o-color-1 .h2, .o_colored_level .bg-o-color-1 .h2, .bg-white .h2, .o_colored_level .bg-white .h2, .bg-black .h2, .o_colored_level .bg-black .h2, .bg-black-50 .h2, .o_colored_level .bg-black-50 .h2, .bg-black-75 .h2, .o_colored_level .bg-black-75 .h2, .bg-white-50 .h2, .o_colored_level .bg-white-50 .h2, .bg-white-75 .h2, .o_colored_level .bg-white-75 .h2, .bg-white-85 .h2, .o_colored_level .bg-white-85 .h2, .bg-900 .h2, .o_colored_level .bg-900 .h2, .bg-800 .h2, .o_colored_level .bg-800 .h2, .bg-700 .h2, .o_colored_level .bg-700 .h2, .bg-600 .h2, .o_colored_level .bg-600 .h2, .bg-500 .h2, .o_colored_level .bg-500 .h2, .bg-400 .h2, .o_colored_level .bg-400 .h2, .bg-300 .h2, .o_colored_level .bg-300 .h2, .bg-200 .h2, .bg-100 .h2, .toast-body .h2, .card-body .h2, .o_cc5 h3, .o_colored_level .o_cc5 h3, .o_cc4 h3, .o_colored_level .o_cc4 h3, .o_cc3 h3, .o_colored_level .o_cc3 h3, .o_cc2 h3, .o_colored_level .o_cc2 h3, .o_cc1 h3, .o_colored_level .o_cc1 h3, .bg-o-color-5 h3, .o_colored_level .bg-o-color-5 h3, .bg-o-color-4 h3, .o_colored_level .bg-o-color-4 h3, .bg-o-color-3 h3, .o_colored_level .bg-o-color-3 h3, .bg-o-color-2 h3, .o_colored_level .bg-o-color-2 h3, .bg-o-color-1 h3, .o_colored_level .bg-o-color-1 h3, .bg-white h3, .o_colored_level .bg-white h3, .bg-black h3, .o_colored_level .bg-black h3, .bg-black-50 h3, .o_colored_level .bg-black-50 h3, .bg-black-75 h3, .o_colored_level .bg-black-75 h3, .bg-white-50 h3, .o_colored_level .bg-white-50 h3, .bg-white-75 h3, .o_colored_level .bg-white-75 h3, .bg-white-85 h3, .o_colored_level .bg-white-85 h3, .bg-900 h3, .o_colored_level .bg-900 h3, .bg-800 h3, .o_colored_level .bg-800 h3, .bg-700 h3, .o_colored_level .bg-700 h3, .bg-600 h3, .o_colored_level .bg-600 h3, .bg-500 h3, .o_colored_level .bg-500 h3, .bg-400 h3, .o_colored_level .bg-400 h3, .bg-300 h3, .o_colored_level .bg-300 h3, .bg-200 h3, .o_colored_level .bg-200 h3, .bg-100 h3, .o_colored_level .bg-100 h3, .toast-body h3, .o_colored_level .toast-body h3, .card-body h3, .o_colored_level .card-body h3, .o_cc5 .h3, .o_colored_level .o_cc5 .h3, .o_cc4 .h3, .o_colored_level .o_cc4 .h3, .o_cc3 .h3, .o_colored_level .o_cc3 .h3, .o_cc2 .h3, .o_colored_level .o_cc2 .h3, .o_cc1 .h3, .o_colored_level .o_cc1 .h3, .bg-o-color-5 .h3, .o_colored_level .bg-o-color-5 .h3, .bg-o-color-4 .h3, .o_colored_level .bg-o-color-4 .h3, .bg-o-color-3 .h3, .o_colored_level .bg-o-color-3 .h3, .bg-o-color-2 .h3, .o_colored_level .bg-o-color-2 .h3, .bg-o-color-1 .h3, .o_colored_level .bg-o-color-1 .h3, .bg-white .h3, .o_colored_level .bg-white .h3, .bg-black .h3, .o_colored_level .bg-black .h3, .bg-black-50 .h3, .o_colored_level .bg-black-50 .h3, .bg-black-75 .h3, .o_colored_level .bg-black-75 .h3, .bg-white-50 .h3, .o_colored_level .bg-white-50 .h3, .bg-white-75 .h3, .o_colored_level .bg-white-75 .h3, .bg-white-85 .h3, .o_colored_level .bg-white-85 .h3, .bg-900 .h3, .o_colored_level .bg-900 .h3, .bg-800 .h3, .o_colored_level .bg-800 .h3, .bg-700 .h3, .o_colored_level .bg-700 .h3, .bg-600 .h3, .o_colored_level .bg-600 .h3, .bg-500 .h3, .o_colored_level .bg-500 .h3, .bg-400 .h3, .o_colored_level .bg-400 .h3, .bg-300 .h3, .o_colored_level .bg-300 .h3, .bg-200 .h3, .bg-100 .h3, .toast-body .h3, .card-body .h3, .o_cc5 h4, .o_colored_level .o_cc5 h4, .o_cc4 h4, .o_colored_level .o_cc4 h4, .o_cc3 h4, .o_colored_level .o_cc3 h4, .o_cc2 h4, .o_colored_level .o_cc2 h4, .o_cc1 h4, .o_colored_level .o_cc1 h4, .bg-o-color-5 h4, .o_colored_level .bg-o-color-5 h4, .bg-o-color-4 h4, .o_colored_level .bg-o-color-4 h4, .bg-o-color-3 h4, .o_colored_level .bg-o-color-3 h4, .bg-o-color-2 h4, .o_colored_level .bg-o-color-2 h4, .bg-o-color-1 h4, .o_colored_level .bg-o-color-1 h4, .bg-white h4, .o_colored_level .bg-white h4, .bg-black h4, .o_colored_level .bg-black h4, .bg-black-50 h4, .o_colored_level .bg-black-50 h4, .bg-black-75 h4, .o_colored_level .bg-black-75 h4, .bg-white-50 h4, .o_colored_level .bg-white-50 h4, .bg-white-75 h4, .o_colored_level .bg-white-75 h4, .bg-white-85 h4, .o_colored_level .bg-white-85 h4, .bg-900 h4, .o_colored_level .bg-900 h4, .bg-800 h4, .o_colored_level .bg-800 h4, .bg-700 h4, .o_colored_level .bg-700 h4, .bg-600 h4, .o_colored_level .bg-600 h4, .bg-500 h4, .o_colored_level .bg-500 h4, .bg-400 h4, .o_colored_level .bg-400 h4, .bg-300 h4, .o_colored_level .bg-300 h4, .bg-200 h4, .o_colored_level .bg-200 h4, .bg-100 h4, .o_colored_level .bg-100 h4, .toast-body h4, .o_colored_level .toast-body h4, .card-body h4, .o_colored_level .card-body h4, .o_cc5 .h4, .o_colored_level .o_cc5 .h4, .o_cc4 .h4, .o_colored_level .o_cc4 .h4, .o_cc3 .h4, .o_colored_level .o_cc3 .h4, .o_cc2 .h4, .o_colored_level .o_cc2 .h4, .o_cc1 .h4, .o_colored_level .o_cc1 .h4, .bg-o-color-5 .h4, .o_colored_level .bg-o-color-5 .h4, .bg-o-color-4 .h4, .o_colored_level .bg-o-color-4 .h4, .bg-o-color-3 .h4, .o_colored_level .bg-o-color-3 .h4, .bg-o-color-2 .h4, .o_colored_level .bg-o-color-2 .h4, .bg-o-color-1 .h4, .o_colored_level .bg-o-color-1 .h4, .bg-white .h4, .o_colored_level .bg-white .h4, .bg-black .h4, .o_colored_level .bg-black .h4, .bg-black-50 .h4, .o_colored_level .bg-black-50 .h4, .bg-black-75 .h4, .o_colored_level .bg-black-75 .h4, .bg-white-50 .h4, .o_colored_level .bg-white-50 .h4, .bg-white-75 .h4, .o_colored_level .bg-white-75 .h4, .bg-white-85 .h4, .o_colored_level .bg-white-85 .h4, .bg-900 .h4, .o_colored_level .bg-900 .h4, .bg-800 .h4, .o_colored_level .bg-800 .h4, .bg-700 .h4, .o_colored_level .bg-700 .h4, .bg-600 .h4, .o_colored_level .bg-600 .h4, .bg-500 .h4, .o_colored_level .bg-500 .h4, .bg-400 .h4, .o_colored_level .bg-400 .h4, .bg-300 .h4, .o_colored_level .bg-300 .h4, .bg-200 .h4, .bg-100 .h4, .toast-body .h4, .card-body .h4, .o_cc5 h5, .o_colored_level .o_cc5 h5, .o_cc4 h5, .o_colored_level .o_cc4 h5, .o_cc3 h5, .o_colored_level .o_cc3 h5, .o_cc2 h5, .o_colored_level .o_cc2 h5, .o_cc1 h5, .o_colored_level .o_cc1 h5, .bg-o-color-5 h5, .o_colored_level .bg-o-color-5 h5, .bg-o-color-4 h5, .o_colored_level .bg-o-color-4 h5, .bg-o-color-3 h5, .o_colored_level .bg-o-color-3 h5, .bg-o-color-2 h5, .o_colored_level .bg-o-color-2 h5, .bg-o-color-1 h5, .o_colored_level .bg-o-color-1 h5, .bg-white h5, .o_colored_level .bg-white h5, .bg-black h5, .o_colored_level .bg-black h5, .bg-black-50 h5, .o_colored_level .bg-black-50 h5, .bg-black-75 h5, .o_colored_level .bg-black-75 h5, .bg-white-50 h5, .o_colored_level .bg-white-50 h5, .bg-white-75 h5, .o_colored_level .bg-white-75 h5, .bg-white-85 h5, .o_colored_level .bg-white-85 h5, .bg-900 h5, .o_colored_level .bg-900 h5, .bg-800 h5, .o_colored_level .bg-800 h5, .bg-700 h5, .o_colored_level .bg-700 h5, .bg-600 h5, .o_colored_level .bg-600 h5, .bg-500 h5, .o_colored_level .bg-500 h5, .bg-400 h5, .o_colored_level .bg-400 h5, .bg-300 h5, .o_colored_level .bg-300 h5, .bg-200 h5, .o_colored_level .bg-200 h5, .bg-100 h5, .o_colored_level .bg-100 h5, .toast-body h5, .o_colored_level .toast-body h5, .card-body h5, .o_colored_level .card-body h5, .o_cc5 .h5, .o_colored_level .o_cc5 .h5, .o_cc4 .h5, .o_colored_level .o_cc4 .h5, .o_cc3 .h5, .o_colored_level .o_cc3 .h5, .o_cc2 .h5, .o_colored_level .o_cc2 .h5, .o_cc1 .h5, .o_colored_level .o_cc1 .h5, .bg-o-color-5 .h5, .o_colored_level .bg-o-color-5 .h5, .bg-o-color-4 .h5, .o_colored_level .bg-o-color-4 .h5, .bg-o-color-3 .h5, .o_colored_level .bg-o-color-3 .h5, .bg-o-color-2 .h5, .o_colored_level .bg-o-color-2 .h5, .bg-o-color-1 .h5, .o_colored_level .bg-o-color-1 .h5, .bg-white .h5, .o_colored_level .bg-white .h5, .bg-black .h5, .o_colored_level .bg-black .h5, .bg-black-50 .h5, .o_colored_level .bg-black-50 .h5, .bg-black-75 .h5, .o_colored_level .bg-black-75 .h5, .bg-white-50 .h5, .o_colored_level .bg-white-50 .h5, .bg-white-75 .h5, .o_colored_level .bg-white-75 .h5, .bg-white-85 .h5, .o_colored_level .bg-white-85 .h5, .bg-900 .h5, .o_colored_level .bg-900 .h5, .bg-800 .h5, .o_colored_level .bg-800 .h5, .bg-700 .h5, .o_colored_level .bg-700 .h5, .bg-600 .h5, .o_colored_level .bg-600 .h5, .bg-500 .h5, .o_colored_level .bg-500 .h5, .bg-400 .h5, .o_colored_level .bg-400 .h5, .bg-300 .h5, .o_colored_level .bg-300 .h5, .bg-200 .h5, .bg-100 .h5, .toast-body .h5, .card-body .h5, .o_cc5 h6, .o_colored_level .o_cc5 h6, .o_cc4 h6, .o_colored_level .o_cc4 h6, .o_cc3 h6, .o_colored_level .o_cc3 h6, .o_cc2 h6, .o_colored_level .o_cc2 h6, .o_cc1 h6, .o_colored_level .o_cc1 h6, .bg-o-color-5 h6, .o_colored_level .bg-o-color-5 h6, .bg-o-color-4 h6, .o_colored_level .bg-o-color-4 h6, .bg-o-color-3 h6, .o_colored_level .bg-o-color-3 h6, .bg-o-color-2 h6, .o_colored_level .bg-o-color-2 h6, .bg-o-color-1 h6, .o_colored_level .bg-o-color-1 h6, .bg-white h6, .o_colored_level .bg-white h6, .bg-black h6, .o_colored_level .bg-black h6, .bg-black-50 h6, .o_colored_level .bg-black-50 h6, .bg-black-75 h6, .o_colored_level .bg-black-75 h6, .bg-white-50 h6, .o_colored_level .bg-white-50 h6, .bg-white-75 h6, .o_colored_level .bg-white-75 h6, .bg-white-85 h6, .o_colored_level .bg-white-85 h6, .bg-900 h6, .o_colored_level .bg-900 h6, .bg-800 h6, .o_colored_level .bg-800 h6, .bg-700 h6, .o_colored_level .bg-700 h6, .bg-600 h6, .o_colored_level .bg-600 h6, .bg-500 h6, .o_colored_level .bg-500 h6, .bg-400 h6, .o_colored_level .bg-400 h6, .bg-300 h6, .o_colored_level .bg-300 h6, .bg-200 h6, .o_colored_level .bg-200 h6, .bg-100 h6, .o_colored_level .bg-100 h6, .toast-body h6, .o_colored_level .toast-body h6, .card-body h6, .o_colored_level .card-body h6, .o_cc5 .h6, .o_colored_level .o_cc5 .h6, .o_cc4 .h6, .o_colored_level .o_cc4 .h6, .o_cc3 .h6, .o_colored_level .o_cc3 .h6, .o_cc2 .h6, .o_colored_level .o_cc2 .h6, .o_cc1 .h6, .o_colored_level .o_cc1 .h6, .bg-o-color-5 .h6, .o_colored_level .bg-o-color-5 .h6, .bg-o-color-4 .h6, .o_colored_level .bg-o-color-4 .h6, .bg-o-color-3 .h6, .o_colored_level .bg-o-color-3 .h6, .bg-o-color-2 .h6, .o_colored_level .bg-o-color-2 .h6, .bg-o-color-1 .h6, .o_colored_level .bg-o-color-1 .h6, .bg-white .h6, .o_colored_level .bg-white .h6, .bg-black .h6, .o_colored_level .bg-black .h6, .bg-black-50 .h6, .o_colored_level .bg-black-50 .h6, .bg-black-75 .h6, .o_colored_level .bg-black-75 .h6, .bg-white-50 .h6, .o_colored_level .bg-white-50 .h6, .bg-white-75 .h6, .o_colored_level .bg-white-75 .h6, .bg-white-85 .h6, .o_colored_level .bg-white-85 .h6, .bg-900 .h6, .o_colored_level .bg-900 .h6, .bg-800 .h6, .o_colored_level .bg-800 .h6, .bg-700 .h6, .o_colored_level .bg-700 .h6, .bg-600 .h6, .o_colored_level .bg-600 .h6, .bg-500 .h6, .o_colored_level .bg-500 .h6, .bg-400 .h6, .o_colored_level .bg-400 .h6, .bg-300 .h6, .o_colored_level .bg-300 .h6, .bg-200 .h6, .bg-100 .h6, .toast-body .h6, .card-body .h6{color: inherit;}

/* /web/static/src/scss/utils.scss */
 .modal:not(.o_legacy_dialog) .o_select_media_dialog .o_we_existing_attachments .o_existing_attachment_cell.o_we_image .o_we_media_dialog_img_wrapper, .o_colorpicker_widget .o_opacity_slider, .o_colorpicker_widget .o_color_preview{position: relative; z-index: 0;}.modal:not(.o_legacy_dialog) .o_select_media_dialog .o_we_existing_attachments .o_existing_attachment_cell.o_we_image .o_we_media_dialog_img_wrapper::before, .o_colorpicker_widget .o_opacity_slider::before, .o_colorpicker_widget .o_color_preview::before{content: ""; position: absolute; top: 0; left: 0; bottom: 0; right: 0; z-index: -1; background-image: url("/web/static/img/transparent.png"); background-size: 10px auto; border-radius: inherit;}.modal:not(.o_legacy_dialog) .o_select_media_dialog .o_we_existing_attachments .o_existing_attachment_cell.o_we_image .o_we_media_dialog_img_wrapper::after, .o_colorpicker_widget .o_opacity_slider::after, .o_colorpicker_widget .o_color_preview::after{content: ""; position: absolute; top: 0; left: 0; bottom: 0; right: 0; z-index: -1; background: inherit; border-radius: inherit;}.o_nocontent_help .o_empty_folder_image:before{content: ""; display: block; margin: auto; background-size: cover;}.o_nocontent_help .o_empty_folder_image:before{width: 120px; height: 80px; margin-top: 30px; margin-bottom: 30px; background: transparent url(/web/static/img/empty_folder.svg) no-repeat center;}

/* /web/static/src/scss/primary_variables.scss */
 

/* /web/static/src/core/avatar/avatar.variables.scss */
 

/* /web/static/src/core/notifications/notification.variables.scss */
 

/* /web/static/src/search/control_panel/control_panel.variables.scss */
 

/* /web/static/src/search/search_panel/search_panel.variables.scss */
 

/* /web/static/src/views/fields/statusbar/statusbar_field.variables.scss */
 

/* /web/static/src/views/form/form.variables.scss */
 

/* /web/static/src/views/kanban/kanban.variables.scss */
 

/* /web/static/src/webclient/burger_menu/burger_menu.variables.scss */
 

/* /web/static/src/webclient/navbar/navbar.variables.scss */
 .o_main_navbar .o_menu_brand, .o_main_navbar .o_navbar_apps_menu .dropdown-toggle, .o_main_navbar .o_nav_entry, .o_main_navbar .dropdown-toggle{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; width: auto; height: calc(var(--o-navbar-height) - 0px); border-radius: 0; user-select: none; background: transparent; font-size: 1em; color: var(--NavBar-entry-color, rgba(255, 255, 255, 0.9));}.o_main_navbar .o_menu_brand:hover, .o_main_navbar .o_nav_entry:hover, .o_main_navbar .dropdown-toggle:hover, .o_main_navbar .o_menu_brand:focus, .o_main_navbar .o_nav_entry:focus, .o_main_navbar .dropdown-toggle:focus, .o_main_navbar .focus.o_menu_brand, .o_main_navbar .focus.o_nav_entry, .o_main_navbar .focus.dropdown-toggle{color: var(--NavBar-entry-color--hover, #FFFFFF);}.o_main_navbar .o_menu_brand, .o_main_navbar .o_nav_entry, .o_main_navbar .dropdown-toggle{margin: 0; margin-left: var(--NavBar-entry-margin-left, 0); margin-right: var(--NavBar-entry-margin-right, 0); padding: 0; padding-left: var(--NavBar-entry-padding-left, 0.63em); padding-right: var(--NavBar-entry-padding-right, 0.63em); line-height: calc(var(--o-navbar-height) - 0px);}

/* /onboarding/static/src/scss/onboarding.variables.scss */
 

/* /mail/static/src/core/common/primary_variables.scss */
 

/* /mail/static/src/discuss/typing/common/primary_variables.scss */
 

/* /mail/static/src/scss/variables/primary_variables.scss */
 

/* /web_editor/static/src/scss/web_editor.variables.scss */
 

/* /web_editor/static/src/scss/wysiwyg.variables.scss */
 

/* /portal/static/src/scss/primary_variables.scss */
 

/* /account/static/src/scss/variables.scss */
 @keyframes animate-red{0%{color: red;}100%{color: inherit;}}.animate{animation: animate-red 1s ease;}

/* /web/static/src/scss/secondary_variables.scss */
 

/* /web_editor/static/src/scss/secondary_variables.scss */
 

/* /portal/static/src/scss/bootstrap_overridden.scss */
 

/* /web_editor/static/src/scss/bootstrap_overridden.scss */
 

/* /web/static/src/scss/bootstrap_overridden_frontend.scss */
 

/* /web/static/src/scss/pre_variables.scss */
 

/* /web/static/lib/bootstrap/scss/_variables.scss */
 

/* /web/static/src/scss/import_bootstrap.scss */
 :root{--bs-blue: #007bff; --bs-indigo: #6610f2; --bs-purple: #6f42c1; --bs-pink: #e83e8c; --bs-red: #dc3545; --bs-orange: #fd7e14; --bs-yellow: #ffc107; --bs-green: #28a745; --bs-teal: #20c997; --bs-cyan: #17a2b8; --bs-white: #FFFFFF; --bs-gray: #6C757D; --bs-gray-dark: #343A40; --bs-o-cc5-btn-secondary-border: ; --bs-o-cc5-btn-secondary: #F3F2F2; --bs-o-cc5-btn-primary-border: ; --bs-o-cc5-btn-primary: ; --bs-o-cc5-link: ; --bs-o-cc5-h6: ; --bs-o-cc5-h5: ; --bs-o-cc5-h4: ; --bs-o-cc5-h3: ; --bs-o-cc5-h2: ; --bs-o-cc5-headings: #FFFFFF; --bs-o-cc5-text: ; --bs-o-cc5-bg: #111827; --bs-o-cc4-btn-secondary-border: ; --bs-o-cc4-btn-secondary: #F3F2F2; --bs-o-cc4-btn-primary-border: ; --bs-o-cc4-btn-primary: #111827; --bs-o-cc4-link: #111827; --bs-o-cc4-h6: ; --bs-o-cc4-h5: ; --bs-o-cc4-h4: ; --bs-o-cc4-h3: ; --bs-o-cc4-h2: ; --bs-o-cc4-headings: ; --bs-o-cc4-text: ; --bs-o-cc4-bg: #714B67; --bs-o-cc3-btn-secondary-border: ; --bs-o-cc3-btn-secondary: #F3F2F2; --bs-o-cc3-btn-primary-border: ; --bs-o-cc3-btn-primary: ; --bs-o-cc3-link: ; --bs-o-cc3-h6: ; --bs-o-cc3-h5: ; --bs-o-cc3-h4: ; --bs-o-cc3-h3: ; --bs-o-cc3-h2: ; --bs-o-cc3-headings: ; --bs-o-cc3-text: ; --bs-o-cc3-bg: #8595A2; --bs-o-cc2-btn-secondary-border: ; --bs-o-cc2-btn-secondary: ; --bs-o-cc2-btn-primary-border: ; --bs-o-cc2-btn-primary: ; --bs-o-cc2-link: ; --bs-o-cc2-h6: ; --bs-o-cc2-h5: ; --bs-o-cc2-h4: ; --bs-o-cc2-h3: ; --bs-o-cc2-h2: ; --bs-o-cc2-headings: #111827; --bs-o-cc2-text: ; --bs-o-cc2-bg: #F3F2F2; --bs-o-cc1-btn-secondary-border: ; --bs-o-cc1-btn-secondary: ; --bs-o-cc1-btn-primary-border: ; --bs-o-cc1-btn-primary: ; --bs-o-cc1-link: ; --bs-o-cc1-h6: ; --bs-o-cc1-h5: ; --bs-o-cc1-h4: ; --bs-o-cc1-h3: ; --bs-o-cc1-h2: ; --bs-o-cc1-headings: ; --bs-o-cc1-text: ; --bs-o-cc1-bg: #FFFFFF; --bs-o-color-5: #111827; --bs-o-color-4: #FFFFFF; --bs-o-color-3: #F3F2F2; --bs-o-color-2: #8595A2; --bs-o-color-1: #714B67; --bs-gray-100: #F8F9FA; --bs-gray-200: #E9ECEF; --bs-gray-300: #DEE2E6; --bs-gray-400: #CED4DA; --bs-gray-500: #ADB5BD; --bs-gray-600: #6C757D; --bs-gray-700: #495057; --bs-gray-800: #343A40; --bs-gray-900: #212529; --bs-gray-white-85: rgba(255, 255, 255, 0.85); --bs-gray-white-75: rgba(255, 255, 255, 0.75); --bs-gray-white-50: rgba(255, 255, 255, 0.5); --bs-gray-white-25: rgba(255, 255, 255, 0.25); --bs-gray-black-75: rgba(0, 0, 0, 0.75); --bs-gray-black-50: rgba(0, 0, 0, 0.5); --bs-gray-black-25: rgba(0, 0, 0, 0.25); --bs-gray-black-15: rgba(0, 0, 0, 0.15); --bs-gray-black: #000000; --bs-gray-white: #FFFFFF; --bs-primary: #714B67; --bs-secondary: #8595A2; --bs-success: #28a745; --bs-info: #17a2b8; --bs-warning: #ffc107; --bs-danger: #dc3545; --bs-light: #eeeeee; --bs-dark: #212529; --bs-primary-rgb: 113, 75, 103; --bs-secondary-rgb: 133, 149, 162; --bs-success-rgb: 40, 167, 69; --bs-info-rgb: 23, 162, 184; --bs-warning-rgb: 255, 193, 7; --bs-danger-rgb: 220, 53, 69; --bs-light-rgb: 238, 238, 238; --bs-dark-rgb: 33, 37, 41; --bs-white-rgb: 255, 255, 255; --bs-black-rgb: 0, 0, 0; --bs-body-color-rgb: 73, 80, 87; --bs-body-bg-rgb: 255, 255, 255; --bs-font-sans-serif: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Ubuntu, "Noto Sans", Arial, "Odoo Unicode Support Noto", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; --bs-font-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; --bs-gradient: linear-gradient(180deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0)); --bs-body-font-family: var(--bs-font-sans-serif); --bs-body-font-size: 0.875rem; --bs-body-font-weight: 400; --bs-body-line-height: 1.5; --bs-body-color: #495057; --bs-body-bg: white;}*, *::before, *::after{box-sizing: border-box;}@media (prefers-reduced-motion: no-preference){:root{scroll-behavior: smooth;}}body{margin: 0; font-family: var(--bs-body-font-family); font-size: var(--bs-body-font-size); font-weight: var(--bs-body-font-weight); line-height: var(--bs-body-line-height); color: var(--bs-body-color); text-align: var(--bs-body-text-align); background-color: var(--bs-body-bg); -webkit-text-size-adjust: 100%; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);}hr{margin: 1rem 0; color: inherit; background-color: currentColor; border: 0; opacity: 0.25;}hr:not([size]){height: 1px;}h6, .h6, h5, .h5, h4, .h4, h3, .h3, h2, .h2, h1, .h1{margin-top: 0; margin-bottom: 0.5rem; font-weight: 500; line-height: 1.2;}h1, .h1{font-size: calc(1.34375rem + 1.125vw);}@media (min-width: 1200px){h1, .h1{font-size: 2.1875rem;}}h2, .h2{font-size: calc(1.3rem + 0.6vw);}@media (min-width: 1200px){h2, .h2{font-size: 1.75rem;}}h3, .h3{font-size: calc(1.278125rem + 0.3375vw);}@media (min-width: 1200px){h3, .h3{font-size: 1.53125rem;}}h4, .h4{font-size: calc(1.25625rem + 0.075vw);}@media (min-width: 1200px){h4, .h4{font-size: 1.3125rem;}}h5, .h5{font-size: 1.09375rem;}h6, .h6{font-size: 0.875rem;}p{margin-top: 0; margin-bottom: 1rem;}abbr[title], abbr[data-bs-original-title]{text-decoration: underline dotted; cursor: help; text-decoration-skip-ink: none;}address{margin-bottom: 1rem; font-style: normal; line-height: inherit;}ol, ul{padding-left: 2rem;}ol, ul, dl{margin-top: 0; margin-bottom: 1rem;}ol ol, ul ul, ol ul, ul ol{margin-bottom: 0;}dt{font-weight: 700;}dd{margin-bottom: .5rem; margin-left: 0;}blockquote{margin: 0 0 1rem;}b, strong{font-weight: bolder;}small, .small{font-size: 0.875em;}mark, .mark{padding: 0.2em; background-color: #fcf8e3;}sub, sup{position: relative; font-size: 0.75em; line-height: 0; vertical-align: baseline;}sub{bottom: -.25em;}sup{top: -.5em;}a{color: #714B67; text-decoration: none;}a:hover{color: #5a3c52;}a:not([href]):not([class]), a:not([href]):not([class]):hover{color: inherit; text-decoration: none;}pre, code, kbd, samp{font-family: var(--bs-font-monospace); font-size: 1em; direction: ltr ; unicode-bidi: bidi-override;}pre{display: block; margin-top: 0; margin-bottom: 1rem; overflow: auto; font-size: 0.875em;}pre code{font-size: inherit; color: inherit; word-break: normal;}code{font-size: 0.875em; color: #e83e8c; word-wrap: break-word;}a > code{color: inherit;}kbd{padding: 0.2rem 0.4rem; font-size: 0.875em; color: #FFFFFF; background-color: #212529; border-radius: 0.2rem;}kbd kbd{padding: 0; font-size: 1em; font-weight: 700;}figure{margin: 0 0 1rem;}img, svg{vertical-align: middle;}table{caption-side: bottom; border-collapse: collapse;}caption{padding-top: 0.5rem; padding-bottom: 0.5rem; color: #6C757D; text-align: left;}th{text-align: inherit; text-align: -webkit-match-parent;}thead, tbody, tfoot, tr, td, th{border-color: inherit; border-style: solid; border-width: 0;}label{display: inline-block;}button{border-radius: 0;}button:focus:not(:focus-visible){outline: 0;}input, button, select, optgroup, textarea{margin: 0; font-family: inherit; font-size: inherit; line-height: inherit;}button, select{text-transform: none;}[role="button"]{cursor: pointer;}select{word-wrap: normal;}select:disabled, select.o_wysiwyg_loader{opacity: 1;}[list]::-webkit-calendar-picker-indicator{display: none;}button, [type="button"], [type="reset"], [type="submit"]{-webkit--webkit-appearance: button; -moz-appearance: button; appearance: button;}button:not(:disabled):not(.o_wysiwyg_loader), [type="button"]:not(:disabled):not(.o_wysiwyg_loader), [type="reset"]:not(:disabled):not(.o_wysiwyg_loader), [type="submit"]:not(:disabled):not(.o_wysiwyg_loader){cursor: pointer;}::-moz-focus-inner{padding: 0; border-style: none;}textarea{resize: vertical;}fieldset{min-width: 0; padding: 0; margin: 0; border: 0;}legend{float: left; width: 100%; padding: 0; margin-bottom: 0.5rem; font-size: calc(1.275rem + 0.3vw); line-height: inherit;}@media (min-width: 1200px){legend{font-size: 1.5rem;}}legend + *{clear: left;}::-webkit-datetime-edit-fields-wrapper, ::-webkit-datetime-edit-text, ::-webkit-datetime-edit-minute, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-year-field{padding: 0;}::-webkit-inner-spin-button{height: auto;}[type="search"]{outline-offset: -2px; -webkit--webkit-appearance: textfield; -moz-appearance: textfield; appearance: textfield;}::-webkit-search-decoration{-webkit--webkit-appearance: none; -moz-appearance: none; appearance: none;}::-webkit-color-swatch-wrapper{padding: 0;}::file-selector-button{font: inherit;}::-webkit-file-upload-button{font: inherit; -webkit--webkit-appearance: button; -moz-appearance: button; appearance: button;}output{display: inline-block;}iframe{border: 0;}summary{display: list-item; cursor: pointer;}progress{vertical-align: baseline;}[hidden]{display: none !important;}.lead{font-size: 1.09375rem; font-weight: 300;}.display-1{font-size: calc(1.625rem + 4.5vw); font-weight: 300; line-height: 1.2;}@media (min-width: 1200px){.display-1{font-size: 5rem;}}.display-2{font-size: calc(1.575rem + 3.9vw); font-weight: 300; line-height: 1.2;}@media (min-width: 1200px){.display-2{font-size: 4.5rem;}}.display-3{font-size: calc(1.525rem + 3.3vw); font-weight: 300; line-height: 1.2;}@media (min-width: 1200px){.display-3{font-size: 4rem;}}.display-4{font-size: calc(1.475rem + 2.7vw); font-weight: 300; line-height: 1.2;}@media (min-width: 1200px){.display-4{font-size: 3.5rem;}}.display-5{font-size: calc(1.425rem + 2.1vw); font-weight: 300; line-height: 1.2;}@media (min-width: 1200px){.display-5{font-size: 3rem;}}.display-6{font-size: calc(1.375rem + 1.5vw); font-weight: 300; line-height: 1.2;}@media (min-width: 1200px){.display-6{font-size: 2.5rem;}}.list-unstyled{padding-left: 0; list-style: none;}.list-inline{padding-left: 0; list-style: none;}.list-inline-item{display: inline-block;}.list-inline-item:not(:last-child){margin-right: 0.5rem;}.initialism{font-size: 0.875em; text-transform: uppercase;}.blockquote{margin-bottom: 1rem; font-size: 1.09375rem;}.blockquote > :last-child{margin-bottom: 0;}.blockquote-footer{margin-top: -1rem; margin-bottom: 1rem; font-size: 0.875em; color: #6C757D;}.blockquote-footer::before{content: "\2014\00A0";}.img-fluid{max-width: 100%; height: auto;}.img-thumbnail{padding: 0.25rem; background-color: white; border: 1px solid #dee2e6; border-radius: 0.25rem; max-width: 100%; height: auto;}.figure{display: inline-block;}.figure-img{margin-bottom: 0.5rem; line-height: 1;}.figure-caption{font-size: 0.875em; color: inherit;}.container, .o_container_small, .container-fluid, .container-xxl, .container-xl, .container-lg, .container-md, .container-sm{width: 100%; padding-right: var(--bs-gutter-x, 0.75rem); padding-left: var(--bs-gutter-x, 0.75rem); margin-right: auto; margin-left: auto;}@media (min-width: 576px){.container-sm, .container, .o_container_small{max-width: 540px;}}@media (min-width: 768px){.container-md, .container-sm, .container, .o_container_small{max-width: 720px;}}@media (min-width: 992px){.container-lg, .container-md, .container-sm, .container, .o_container_small{max-width: 960px;}}@media (min-width: 1200px){.container-xl, .container-lg, .container-md, .container-sm, .container, .o_container_small{max-width: 1140px;}}@media (min-width: 1400px){.container-xxl, .container-xl, .container-lg, .container-md, .container-sm, .container, .o_container_small{max-width: 1320px;}}.row{--bs-gutter-x: 1.5rem; --bs-gutter-y: 0; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; margin-top: calc(-1 * var(--bs-gutter-y)); margin-right: calc(-.5 * var(--bs-gutter-x)); margin-left: calc(-.5 * var(--bs-gutter-x));}.row > *{flex-shrink: 0; width: 100%; max-width: 100%; padding-right: calc(var(--bs-gutter-x) * .5); padding-left: calc(var(--bs-gutter-x) * .5); margin-top: var(--bs-gutter-y);}.col{flex: 1 0 0%;}.row-cols-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-1{margin-left: 8.33333333%;}.offset-2{margin-left: 16.66666667%;}.offset-3{margin-left: 25%;}.offset-4{margin-left: 33.33333333%;}.offset-5{margin-left: 41.66666667%;}.offset-6{margin-left: 50%;}.offset-7{margin-left: 58.33333333%;}.offset-8{margin-left: 66.66666667%;}.offset-9{margin-left: 75%;}.offset-10{margin-left: 83.33333333%;}.offset-11{margin-left: 91.66666667%;}.g-0, .gx-0{--bs-gutter-x: 0;}.g-0, .gy-0{--bs-gutter-y: 0;}.g-1, .gx-1{--bs-gutter-x: 0.25rem;}.g-1, .gy-1{--bs-gutter-y: 0.25rem;}.g-2, .gx-2{--bs-gutter-x: 0.5rem;}.g-2, .gy-2{--bs-gutter-y: 0.5rem;}.g-3, .gx-3{--bs-gutter-x: 1rem;}.g-3, .gy-3{--bs-gutter-y: 1rem;}.g-4, .gx-4{--bs-gutter-x: 1.5rem;}.g-4, .gy-4{--bs-gutter-y: 1.5rem;}.g-5, .gx-5{--bs-gutter-x: 3rem;}.g-5, .gy-5{--bs-gutter-y: 3rem;}@media (min-width: 576px){.col-sm{flex: 1 0 0%;}.row-cols-sm-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-sm-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-sm-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-sm-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-sm-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-sm-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-sm-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-sm-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-sm-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-sm-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-sm-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-sm-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-sm-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-sm-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-sm-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-sm-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-sm-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-sm-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-sm-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-sm-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-sm-0{margin-left: 0;}.offset-sm-1{margin-left: 8.33333333%;}.offset-sm-2{margin-left: 16.66666667%;}.offset-sm-3{margin-left: 25%;}.offset-sm-4{margin-left: 33.33333333%;}.offset-sm-5{margin-left: 41.66666667%;}.offset-sm-6{margin-left: 50%;}.offset-sm-7{margin-left: 58.33333333%;}.offset-sm-8{margin-left: 66.66666667%;}.offset-sm-9{margin-left: 75%;}.offset-sm-10{margin-left: 83.33333333%;}.offset-sm-11{margin-left: 91.66666667%;}.g-sm-0, .gx-sm-0{--bs-gutter-x: 0;}.g-sm-0, .gy-sm-0{--bs-gutter-y: 0;}.g-sm-1, .gx-sm-1{--bs-gutter-x: 0.25rem;}.g-sm-1, .gy-sm-1{--bs-gutter-y: 0.25rem;}.g-sm-2, .gx-sm-2{--bs-gutter-x: 0.5rem;}.g-sm-2, .gy-sm-2{--bs-gutter-y: 0.5rem;}.g-sm-3, .gx-sm-3{--bs-gutter-x: 1rem;}.g-sm-3, .gy-sm-3{--bs-gutter-y: 1rem;}.g-sm-4, .gx-sm-4{--bs-gutter-x: 1.5rem;}.g-sm-4, .gy-sm-4{--bs-gutter-y: 1.5rem;}.g-sm-5, .gx-sm-5{--bs-gutter-x: 3rem;}.g-sm-5, .gy-sm-5{--bs-gutter-y: 3rem;}}@media (min-width: 768px){.col-md{flex: 1 0 0%;}.row-cols-md-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-md-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-md-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-md-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-md-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-md-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-md-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-md-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-md-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-md-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-md-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-md-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-md-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-md-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-md-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-md-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-md-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-md-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-md-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-md-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-md-0{margin-left: 0;}.offset-md-1{margin-left: 8.33333333%;}.offset-md-2{margin-left: 16.66666667%;}.offset-md-3{margin-left: 25%;}.offset-md-4{margin-left: 33.33333333%;}.offset-md-5{margin-left: 41.66666667%;}.offset-md-6{margin-left: 50%;}.offset-md-7{margin-left: 58.33333333%;}.offset-md-8{margin-left: 66.66666667%;}.offset-md-9{margin-left: 75%;}.offset-md-10{margin-left: 83.33333333%;}.offset-md-11{margin-left: 91.66666667%;}.g-md-0, .gx-md-0{--bs-gutter-x: 0;}.g-md-0, .gy-md-0{--bs-gutter-y: 0;}.g-md-1, .gx-md-1{--bs-gutter-x: 0.25rem;}.g-md-1, .gy-md-1{--bs-gutter-y: 0.25rem;}.g-md-2, .gx-md-2{--bs-gutter-x: 0.5rem;}.g-md-2, .gy-md-2{--bs-gutter-y: 0.5rem;}.g-md-3, .gx-md-3{--bs-gutter-x: 1rem;}.g-md-3, .gy-md-3{--bs-gutter-y: 1rem;}.g-md-4, .gx-md-4{--bs-gutter-x: 1.5rem;}.g-md-4, .gy-md-4{--bs-gutter-y: 1.5rem;}.g-md-5, .gx-md-5{--bs-gutter-x: 3rem;}.g-md-5, .gy-md-5{--bs-gutter-y: 3rem;}}@media (min-width: 992px){.col-lg{flex: 1 0 0%;}.row-cols-lg-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-lg-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-lg-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-lg-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-lg-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-lg-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-lg-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-lg-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-lg-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-lg-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-lg-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-lg-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-lg-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-lg-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-lg-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-lg-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-lg-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-lg-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-lg-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-lg-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-lg-0{margin-left: 0;}.offset-lg-1{margin-left: 8.33333333%;}.offset-lg-2{margin-left: 16.66666667%;}.offset-lg-3{margin-left: 25%;}.offset-lg-4{margin-left: 33.33333333%;}.offset-lg-5{margin-left: 41.66666667%;}.offset-lg-6{margin-left: 50%;}.offset-lg-7{margin-left: 58.33333333%;}.offset-lg-8{margin-left: 66.66666667%;}.offset-lg-9{margin-left: 75%;}.offset-lg-10{margin-left: 83.33333333%;}.offset-lg-11{margin-left: 91.66666667%;}.g-lg-0, .gx-lg-0{--bs-gutter-x: 0;}.g-lg-0, .gy-lg-0{--bs-gutter-y: 0;}.g-lg-1, .gx-lg-1{--bs-gutter-x: 0.25rem;}.g-lg-1, .gy-lg-1{--bs-gutter-y: 0.25rem;}.g-lg-2, .gx-lg-2{--bs-gutter-x: 0.5rem;}.g-lg-2, .gy-lg-2{--bs-gutter-y: 0.5rem;}.g-lg-3, .gx-lg-3{--bs-gutter-x: 1rem;}.g-lg-3, .gy-lg-3{--bs-gutter-y: 1rem;}.g-lg-4, .gx-lg-4{--bs-gutter-x: 1.5rem;}.g-lg-4, .gy-lg-4{--bs-gutter-y: 1.5rem;}.g-lg-5, .gx-lg-5{--bs-gutter-x: 3rem;}.g-lg-5, .gy-lg-5{--bs-gutter-y: 3rem;}}@media (min-width: 1200px){.col-xl{flex: 1 0 0%;}.row-cols-xl-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-xl-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-xl-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-xl-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-xl-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-xl-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-xl-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-xl-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-xl-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-xl-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-xl-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-xl-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-xl-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-xl-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-xl-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-xl-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-xl-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-xl-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-xl-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-xl-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-xl-0{margin-left: 0;}.offset-xl-1{margin-left: 8.33333333%;}.offset-xl-2{margin-left: 16.66666667%;}.offset-xl-3{margin-left: 25%;}.offset-xl-4{margin-left: 33.33333333%;}.offset-xl-5{margin-left: 41.66666667%;}.offset-xl-6{margin-left: 50%;}.offset-xl-7{margin-left: 58.33333333%;}.offset-xl-8{margin-left: 66.66666667%;}.offset-xl-9{margin-left: 75%;}.offset-xl-10{margin-left: 83.33333333%;}.offset-xl-11{margin-left: 91.66666667%;}.g-xl-0, .gx-xl-0{--bs-gutter-x: 0;}.g-xl-0, .gy-xl-0{--bs-gutter-y: 0;}.g-xl-1, .gx-xl-1{--bs-gutter-x: 0.25rem;}.g-xl-1, .gy-xl-1{--bs-gutter-y: 0.25rem;}.g-xl-2, .gx-xl-2{--bs-gutter-x: 0.5rem;}.g-xl-2, .gy-xl-2{--bs-gutter-y: 0.5rem;}.g-xl-3, .gx-xl-3{--bs-gutter-x: 1rem;}.g-xl-3, .gy-xl-3{--bs-gutter-y: 1rem;}.g-xl-4, .gx-xl-4{--bs-gutter-x: 1.5rem;}.g-xl-4, .gy-xl-4{--bs-gutter-y: 1.5rem;}.g-xl-5, .gx-xl-5{--bs-gutter-x: 3rem;}.g-xl-5, .gy-xl-5{--bs-gutter-y: 3rem;}}@media (min-width: 1400px){.col-xxl{flex: 1 0 0%;}.row-cols-xxl-auto > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.row-cols-xxl-1 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.row-cols-xxl-2 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.row-cols-xxl-3 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.row-cols-xxl-4 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.row-cols-xxl-5 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 20%;}.row-cols-xxl-6 > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-xxl-auto{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: auto;}.col-xxl-1{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 8.33333333%;}.col-xxl-2{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 16.66666667%;}.col-xxl-3{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 25%;}.col-xxl-4{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 33.33333333%;}.col-xxl-5{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 41.66666667%;}.col-xxl-6{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 50%;}.col-xxl-7{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 58.33333333%;}.col-xxl-8{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 66.66666667%;}.col-xxl-9{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 75%;}.col-xxl-10{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 83.33333333%;}.col-xxl-11{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 91.66666667%;}.col-xxl-12{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; width: 100%;}.offset-xxl-0{margin-left: 0;}.offset-xxl-1{margin-left: 8.33333333%;}.offset-xxl-2{margin-left: 16.66666667%;}.offset-xxl-3{margin-left: 25%;}.offset-xxl-4{margin-left: 33.33333333%;}.offset-xxl-5{margin-left: 41.66666667%;}.offset-xxl-6{margin-left: 50%;}.offset-xxl-7{margin-left: 58.33333333%;}.offset-xxl-8{margin-left: 66.66666667%;}.offset-xxl-9{margin-left: 75%;}.offset-xxl-10{margin-left: 83.33333333%;}.offset-xxl-11{margin-left: 91.66666667%;}.g-xxl-0, .gx-xxl-0{--bs-gutter-x: 0;}.g-xxl-0, .gy-xxl-0{--bs-gutter-y: 0;}.g-xxl-1, .gx-xxl-1{--bs-gutter-x: 0.25rem;}.g-xxl-1, .gy-xxl-1{--bs-gutter-y: 0.25rem;}.g-xxl-2, .gx-xxl-2{--bs-gutter-x: 0.5rem;}.g-xxl-2, .gy-xxl-2{--bs-gutter-y: 0.5rem;}.g-xxl-3, .gx-xxl-3{--bs-gutter-x: 1rem;}.g-xxl-3, .gy-xxl-3{--bs-gutter-y: 1rem;}.g-xxl-4, .gx-xxl-4{--bs-gutter-x: 1.5rem;}.g-xxl-4, .gy-xxl-4{--bs-gutter-y: 1.5rem;}.g-xxl-5, .gx-xxl-5{--bs-gutter-x: 3rem;}.g-xxl-5, .gy-xxl-5{--bs-gutter-y: 3rem;}}.table{--bs-table-bg: transparent; --bs-table-accent-bg: transparent; --bs-table-striped-color: inherit; --bs-table-striped-bg: rgba(0, 0, 0, 0.05); --bs-table-active-color: inherit; --bs-table-active-bg: rgba(0, 0, 0, 0.1); --bs-table-hover-color: inherit; --bs-table-hover-bg: rgba(0, 0, 0, 0.075); width: 100%; margin-bottom: 1rem; color: inherit; vertical-align: top; border-color: #dee2e6;}.table > :not(caption) > * > *{padding: 0.5rem 0.5rem; background-color: var(--bs-table-bg); border-bottom-width: 1px; box-shadow: inset 0 0 0 9999px var(--bs-table-accent-bg);}.table > tbody{vertical-align: inherit;}.table > thead{vertical-align: bottom;}.table > :not(:first-child){border-top: 2px solid #dee2e6;}.caption-top{caption-side: top;}.table-sm > :not(caption) > * > *{padding: 0.25rem 0.25rem;}.table-bordered > :not(caption) > *{border-width: 1px 0;}.table-bordered > :not(caption) > * > *{border-width: 0 1px;}.table-borderless > :not(caption) > * > *{border-bottom-width: 0;}.table-borderless > :not(:first-child){border-top-width: 0;}.table-striped > tbody > tr:nth-of-type(odd) > *{--bs-table-accent-bg: var(--bs-table-striped-bg); color: var(--bs-table-striped-color);}.table-active{--bs-table-accent-bg: var(--bs-table-active-bg); color: var(--bs-table-active-color);}.table-hover > tbody > tr:hover > *{--bs-table-accent-bg: var(--bs-table-hover-bg); color: var(--bs-table-hover-color);}.table-primary{--bs-table-bg: #e3dbe1; --bs-table-striped-bg: #d8d0d6; --bs-table-striped-color: #000000; --bs-table-active-bg: #ccc5cb; --bs-table-active-color: #000000; --bs-table-hover-bg: #d2cbd0; --bs-table-hover-color: #000000; color: #000000; border-color: #ccc5cb;}.table-secondary{--bs-table-bg: #e7eaec; --bs-table-striped-bg: #dbdee0; --bs-table-striped-color: #000000; --bs-table-active-bg: #d0d3d4; --bs-table-active-color: #000000; --bs-table-hover-bg: #d6d8da; --bs-table-hover-color: #000000; color: #000000; border-color: #d0d3d4;}.table-success{--bs-table-bg: #d4edda; --bs-table-striped-bg: #c9e1cf; --bs-table-striped-color: #000000; --bs-table-active-bg: #bfd5c4; --bs-table-active-color: #000000; --bs-table-hover-bg: #c4dbca; --bs-table-hover-color: #000000; color: #000000; border-color: #bfd5c4;}.table-info{--bs-table-bg: #d1ecf1; --bs-table-striped-bg: #c7e0e5; --bs-table-striped-color: #000000; --bs-table-active-bg: #bcd4d9; --bs-table-active-color: #000000; --bs-table-hover-bg: #c1dadf; --bs-table-hover-color: #000000; color: #000000; border-color: #bcd4d9;}.table-warning{--bs-table-bg: #fff3cd; --bs-table-striped-bg: #f2e7c3; --bs-table-striped-color: #000000; --bs-table-active-bg: #e6dbb9; --bs-table-active-color: #000000; --bs-table-hover-bg: #ece1be; --bs-table-hover-color: #000000; color: #000000; border-color: #e6dbb9;}.table-danger{--bs-table-bg: #f8d7da; --bs-table-striped-bg: #eccccf; --bs-table-striped-color: #000000; --bs-table-active-bg: #dfc2c4; --bs-table-active-color: #000000; --bs-table-hover-bg: #e5c7ca; --bs-table-hover-color: #000000; color: #000000; border-color: #dfc2c4;}.table-light{--bs-table-bg: #eeeeee; --bs-table-striped-bg: #e2e2e2; --bs-table-striped-color: #000000; --bs-table-active-bg: #d6d6d6; --bs-table-active-color: #000000; --bs-table-hover-bg: gainsboro; --bs-table-hover-color: #000000; color: #000000; border-color: #d6d6d6;}.table-dark{--bs-table-bg: #212529; --bs-table-striped-bg: #2c3034; --bs-table-striped-color: #FFFFFF; --bs-table-active-bg: #373b3e; --bs-table-active-color: #FFFFFF; --bs-table-hover-bg: #323539; --bs-table-hover-color: #FFFFFF; color: #FFFFFF; border-color: #373b3e;}.table-responsive{overflow-x: auto; -webkit-overflow-scrolling: touch;}@media (max-width: 575.98px){.table-responsive-sm{overflow-x: auto; -webkit-overflow-scrolling: touch;}}@media (max-width: 767.98px){.table-responsive-md{overflow-x: auto; -webkit-overflow-scrolling: touch;}}@media (max-width: 991.98px){.table-responsive-lg{overflow-x: auto; -webkit-overflow-scrolling: touch;}}@media (max-width: 1199.98px){.table-responsive-xl{overflow-x: auto; -webkit-overflow-scrolling: touch;}}@media (max-width: 1399.98px){.table-responsive-xxl{overflow-x: auto; -webkit-overflow-scrolling: touch;}}.form-label{margin-bottom: 0.5rem;}.col-form-label{padding-top: calc(0.375rem + 1px); padding-bottom: calc(0.375rem + 1px); margin-bottom: 0; font-size: inherit; line-height: 1.5;}.col-form-label-lg{padding-top: calc(0.5rem + 1px); padding-bottom: calc(0.5rem + 1px); font-size: 1.09375rem;}.col-form-label-sm{padding-top: calc(0.25rem + 1px); padding-bottom: calc(0.25rem + 1px); font-size: 0.75rem;}.form-text{margin-top: 0.25rem; font-size: 0.875em; color: #6C757D;}.form-control{display: block; width: 100%; padding: 0.375rem 0.75rem; font-size: 0.875rem; font-weight: 400; line-height: 1.5; color: #495057; background-color: white; background-clip: padding-box; border: 1px solid #CED4DA; -webkit-appearance: none; -moz-appearance: none; appearance: none; border-radius: 0.25rem; transition: background-color 0.05s ease-in-out, border-color 0.05s ease-in-out, box-shadow 0.05s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-control{transition: none;}}.form-control[type="file"]{overflow: hidden;}.form-control[type="file"]:not(:disabled):not(.o_wysiwyg_loader):not([readonly]){cursor: pointer;}.form-control:focus{color: #495057; background-color: white; border-color: #b8a5b3; outline: 0; box-shadow: 0 0 0 0.25rem rgba(113, 75, 103, 0.25);}.form-control::-webkit-date-and-time-value{height: 1.5em;}.form-control::placeholder{color: #6C757D; opacity: 1;}.form-control:disabled, .form-control.o_wysiwyg_loader, .form-control[readonly]{background-color: #E9ECEF; border-color: rgba(73, 80, 87, 0.15); opacity: 1;}.form-control::file-selector-button{padding: 0.375rem 0.75rem; margin: -0.375rem -0.75rem; margin-inline-end: 0.75rem; color: #495057; background-color: #E9ECEF; pointer-events: none; border-color: inherit; border-style: solid; border-width: 0; border-inline-end-width: 1px; border-radius: 0; transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-control::file-selector-button{transition: none;}}.form-control:hover:not(:disabled):not(.o_wysiwyg_loader):not([readonly])::file-selector-button{background-color: #dde0e3;}.form-control::-webkit-file-upload-button{padding: 0.375rem 0.75rem; margin: -0.375rem -0.75rem; margin-inline-end: 0.75rem; color: #495057; background-color: #E9ECEF; pointer-events: none; border-color: inherit; border-style: solid; border-width: 0; border-inline-end-width: 1px; border-radius: 0; transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-control::-webkit-file-upload-button{transition: none;}}.form-control:hover:not(:disabled):not(.o_wysiwyg_loader):not([readonly])::-webkit-file-upload-button{background-color: #dde0e3;}.form-control-plaintext{display: block; width: 100%; padding: 0.375rem 0; margin-bottom: 0; line-height: 1.5; color: #495057; background-color: transparent; border: solid transparent; border-width: 1px 0;}.form-control-plaintext.form-control-sm, .form-control-plaintext.form-control-lg{padding-right: 0; padding-left: 0;}.form-control-sm{min-height: calc(1.5em + 0.5rem + 2px); padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 0.2rem;}.form-control-sm::file-selector-button{padding: 0.25rem 0.5rem; margin: -0.25rem -0.5rem; margin-inline-end: 0.5rem;}.form-control-sm::-webkit-file-upload-button{padding: 0.25rem 0.5rem; margin: -0.25rem -0.5rem; margin-inline-end: 0.5rem;}.form-control-lg{min-height: calc(1.5em + 1rem + 2px); padding: 0.5rem 1rem; font-size: 1.09375rem; border-radius: 0.3rem;}.form-control-lg::file-selector-button{padding: 0.5rem 1rem; margin: -0.5rem -1rem; margin-inline-end: 1rem;}.form-control-lg::-webkit-file-upload-button{padding: 0.5rem 1rem; margin: -0.5rem -1rem; margin-inline-end: 1rem;}textarea.form-control{min-height: calc(1.5em + 0.75rem + 2px);}textarea.form-control-sm{min-height: calc(1.5em + 0.5rem + 2px);}textarea.form-control-lg{min-height: calc(1.5em + 1rem + 2px);}.form-control-color{width: 3rem; height: auto; padding: 0.375rem;}.form-control-color:not(:disabled):not(.o_wysiwyg_loader):not([readonly]){cursor: pointer;}.form-control-color::-moz-color-swatch{height: 1.5em; border-radius: 0.25rem;}.form-control-color::-webkit-color-swatch{height: 1.5em; border-radius: 0.25rem;}.form-select{display: block; width: 100%; padding: 0.375rem 2.25rem 0.375rem 0.75rem; -moz-padding-start: calc(0.75rem - 3px); font-size: 0.875rem; font-weight: 400; line-height: 1.5; color: #495057; background-color: white; background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23495057' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"); background-repeat: no-repeat; background-position: right 0.75rem center; background-size: 16px 12px; border: 1px solid #CED4DA; border-radius: 0.25rem; transition: background-color 0.05s ease-in-out, border-color 0.05s ease-in-out, box-shadow 0.05s ease-in-out; -webkit-appearance: none; -moz-appearance: none; appearance: none;}@media (prefers-reduced-motion: reduce){.form-select{transition: none;}}.form-select:focus{border-color: #b8a5b3; outline: 0; box-shadow: 0 0 0 0.25rem rgba(113, 75, 103, 0.25);}.form-select[multiple], .form-select[size]:not([size="1"]){padding-right: 0.75rem; background-image: none;}.form-select:disabled, .form-select.o_wysiwyg_loader{color: #a4a8ab; background-color: #E9ECEF; border-color: rgba(73, 80, 87, 0.15);}.form-select:-moz-focusring{color: transparent; text-shadow: 0 0 0 #495057;}.form-select-sm{padding-top: 0.25rem; padding-bottom: 0.25rem; padding-left: 0.5rem; font-size: 0.75rem; border-radius: 0.2rem;}.form-select-lg{padding-top: 0.5rem; padding-bottom: 0.5rem; padding-left: 1rem; font-size: 1.09375rem; border-radius: 0.3rem;}.form-check{display: block; min-height: 1.3125rem; padding-left: 1.5em; margin-bottom: 0.125rem;}.form-check .form-check-input{float: left; margin-left: -1.5em;}.form-check-input{width: 1em; height: 1em; margin-top: 0.25em; vertical-align: top; background-color: white; background-repeat: no-repeat; background-position: center; background-size: contain; border: 1px solid #CED4DA; -webkit-appearance: none; -moz-appearance: none; appearance: none; color-adjust: exact;}.form-check-input[type="checkbox"]{border-radius: 0;}.form-check-input[type="radio"]{border-radius: 50%;}.form-check-input:active{filter: brightness(90%);}.form-check-input:focus{border-color: #b8a5b3; outline: 0; box-shadow: 0 0 0 0.25rem rgba(113, 75, 103, 0.25);}.form-check-input:checked{background-color: #714B67; border-color: #714B67;}.form-check-input:checked[type="checkbox"]{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23FFFFFF' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");}.form-check-input:checked[type="radio"]{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23FFFFFF'/%3e%3c/svg%3e");}.form-check-input[type="checkbox"]:indeterminate{background-color: #714B67; border-color: #714B67; background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23FFFFFF' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");}.form-check-input:disabled, .form-check-input.o_wysiwyg_loader{pointer-events: none; filter: none; opacity: 0.5;}.form-check-input[disabled] ~ .form-check-label, .form-check-input:disabled ~ .form-check-label, .form-check-input.o_wysiwyg_loader ~ .form-check-label{opacity: 0.5;}.form-switch{padding-left: 2.5em;}.form-switch .form-check-input{width: 2em; margin-left: -2.5em; background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23495057'/%3e%3c/svg%3e"); background-position: left center; border-radius: 2em; transition: background-position 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-switch .form-check-input{transition: none;}}.form-switch .form-check-input:focus{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23b8a5b3'/%3e%3c/svg%3e");}.form-switch .form-check-input:checked{background-position: right center; background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23FFFFFF'/%3e%3c/svg%3e");}.form-check-inline{display: inline-block; margin-right: 1rem;}.btn-check{position: absolute; clip: rect(0, 0, 0, 0); pointer-events: none;}.btn-check[disabled] + .btn, .btn-check:disabled + .btn, .btn-check.o_wysiwyg_loader + .btn{pointer-events: none; filter: none; opacity: 0.65;}.form-range{width: 100%; height: 1.5rem; padding: 0; background-color: transparent; -webkit-appearance: none; -moz-appearance: none; appearance: none;}.form-range:focus{outline: 0;}.form-range:focus::-webkit-slider-thumb{box-shadow: 0 0 0 1px white, 0 0 0 0.25rem rgba(113, 75, 103, 0.25);}.form-range:focus::-moz-range-thumb{box-shadow: 0 0 0 1px white, 0 0 0 0.25rem rgba(113, 75, 103, 0.25);}.form-range::-moz-focus-outer{border: 0;}.form-range::-webkit-slider-thumb{width: 1rem; height: 1rem; margin-top: -0.45rem; background-color: white; border: 1px solid #714B67; border-radius: 1rem; transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; -webkit-appearance: none; -moz-appearance: none; appearance: none;}@media (prefers-reduced-motion: reduce){.form-range::-webkit-slider-thumb{transition: none;}}.form-range::-webkit-slider-thumb:active{background-color: #714B67;}.form-range::-webkit-slider-runnable-track{width: 100%; height: 0.1rem; color: transparent; cursor: pointer; background-color: #dee2e6; border-color: transparent; border-radius: 1rem;}.form-range::-moz-range-thumb{width: 1rem; height: 1rem; background-color: white; border: 1px solid #714B67; border-radius: 1rem; transition: background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out; -webkit-appearance: none; -moz-appearance: none; appearance: none;}@media (prefers-reduced-motion: reduce){.form-range::-moz-range-thumb{transition: none;}}.form-range::-moz-range-thumb:active{background-color: #714B67;}.form-range::-moz-range-track{width: 100%; height: 0.1rem; color: transparent; cursor: pointer; background-color: #dee2e6; border-color: transparent; border-radius: 1rem;}.form-range:disabled, .form-range.o_wysiwyg_loader{pointer-events: none;}.form-range:disabled::-webkit-slider-thumb, .form-range.o_wysiwyg_loader::-webkit-slider-thumb{background-color: #E9ECEF;}.form-range:disabled::-moz-range-thumb, .form-range.o_wysiwyg_loader::-moz-range-thumb{background-color: #E9ECEF;}.form-floating{position: relative;}.form-floating > .form-control, .form-floating > .form-select{height: calc(3.5rem + 2px); line-height: 1.25;}.form-floating > label{position: absolute; top: 0; left: 0; height: 100%; padding: 1rem 0.75rem; pointer-events: none; border: 1px solid transparent; transform-origin: 0 0; transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;}@media (prefers-reduced-motion: reduce){.form-floating > label{transition: none;}}.form-floating > .form-control{padding: 1rem 0.75rem;}.form-floating > .form-control::placeholder{color: transparent;}.form-floating > .form-control:focus, .form-floating > .form-control:not(:placeholder-shown){padding-top: 1.625rem; padding-bottom: 0.625rem;}.form-floating > .form-control:-webkit-autofill{padding-top: 1.625rem; padding-bottom: 0.625rem;}.form-floating > .form-select{padding-top: 1.625rem; padding-bottom: 0.625rem;}.form-floating > .form-control:focus ~ label, .form-floating > .form-control:not(:placeholder-shown) ~ label, .form-floating > .form-select ~ label{opacity: 0.65; transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);}.form-floating > .form-control:-webkit-autofill ~ label{opacity: 0.65; transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);}.input-group{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; align-items: stretch; width: 100%;}.input-group > .form-control, .input-group > .form-select{position: relative; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; width: 1%; min-width: 0;}.input-group > .form-control:focus, .input-group > .form-select:focus{z-index: 3;}.input-group .btn{position: relative; z-index: 2;}.input-group .btn:focus{z-index: 3;}.input-group-text{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; padding: 0.375rem 0.75rem; font-size: 0.875rem; font-weight: 400; line-height: 1.5; color: #495057; text-align: center; white-space: nowrap; background-color: #E9ECEF; border: 1px solid #CED4DA; border-radius: 0.25rem;}.input-group-lg > .form-control, .input-group-lg > .form-select, .input-group-lg > .input-group-text, .input-group-lg > .btn{padding: 0.5rem 1rem; font-size: 1.09375rem; border-radius: 0.3rem;}.input-group-sm > .form-control, .input-group-sm > .form-select, .input-group-sm > .input-group-text, .input-group-sm > .btn{padding: 0.25rem 0.5rem; font-size: 0.75rem; border-radius: 0.2rem;}.input-group-lg > .form-select, .input-group-sm > .form-select{padding-right: 3rem;}.input-group:not(.has-validation) > :not(:last-child):not(.dropdown-toggle):not(.dropdown-menu), .input-group:not(.has-validation) > .dropdown-toggle:nth-last-child(n + 3){border-top-right-radius: 0; border-bottom-right-radius: 0;}.input-group.has-validation > :nth-last-child(n + 3):not(.dropdown-toggle):not(.dropdown-menu), .input-group.has-validation > .dropdown-toggle:nth-last-child(n + 4){border-top-right-radius: 0; border-bottom-right-radius: 0;}.input-group > :not(:first-child):not(.dropdown-menu):not(.valid-tooltip):not(.valid-feedback):not(.invalid-tooltip):not(.invalid-feedback){margin-left: -1px; border-top-left-radius: 0; border-bottom-left-radius: 0;}.valid-feedback{display: none; width: 100%; margin-top: 0.25rem; font-size: 0.875em; color: #28a745;}.valid-tooltip{position: absolute; top: 100%; z-index: 5; display: none; max-width: 100%; padding: 0.25rem 0.5rem; margin-top: .1rem; font-size: 0.75rem; color: #FFFFFF; background-color: rgba(40, 167, 69, 0.9); border-radius: 0.25rem;}.was-validated :valid ~ .valid-feedback, .was-validated :valid ~ .valid-tooltip, .is-valid ~ .valid-feedback, .is-valid ~ .valid-tooltip{display: block;}.was-validated .form-control:valid, .form-control.is-valid{border-color: #28a745; padding-right: calc(1.5em + 0.75rem); background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e"); background-repeat: no-repeat; background-position: right calc(0.375em + 0.1875rem) center; background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);}.was-validated .form-control:valid:focus, .form-control.is-valid:focus{border-color: #28a745; box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25);}.was-validated textarea.form-control:valid, textarea.form-control.is-valid{padding-right: calc(1.5em + 0.75rem); background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);}.was-validated .form-select:valid, .form-select.is-valid{border-color: #28a745;}.was-validated .form-select:valid:not([multiple]):not([size]), .was-validated .form-select:valid:not([multiple])[size="1"], .form-select.is-valid:not([multiple]):not([size]), .form-select.is-valid:not([multiple])[size="1"]{padding-right: 4.125rem; background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23495057' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2328a745' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e"); background-position: right 0.75rem center, center right 2.25rem; background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);}.was-validated .form-select:valid:focus, .form-select.is-valid:focus{border-color: #28a745; box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25);}.was-validated .form-check-input:valid, .form-check-input.is-valid{border-color: #28a745;}.was-validated .form-check-input:valid:checked, .form-check-input.is-valid:checked{background-color: #28a745;}.was-validated .form-check-input:valid:focus, .form-check-input.is-valid:focus{box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.25);}.was-validated .form-check-input:valid ~ .form-check-label, .form-check-input.is-valid ~ .form-check-label{color: #28a745;}.form-check-inline .form-check-input ~ .valid-feedback{margin-left: .5em;}.was-validated .input-group .form-control:valid, .input-group .form-control.is-valid, .was-validated .input-group .form-select:valid, .input-group .form-select.is-valid{z-index: 1;}.was-validated .input-group .form-control:valid:focus, .input-group .form-control.is-valid:focus, .was-validated .input-group .form-select:valid:focus, .input-group .form-select.is-valid:focus{z-index: 3;}.invalid-feedback{display: none; width: 100%; margin-top: 0.25rem; font-size: 0.875em; color: #dc3545;}.invalid-tooltip{position: absolute; top: 100%; z-index: 5; display: none; max-width: 100%; padding: 0.25rem 0.5rem; margin-top: .1rem; font-size: 0.75rem; color: #FFFFFF; background-color: rgba(220, 53, 69, 0.9); border-radius: 0.25rem;}.was-validated :invalid ~ .invalid-feedback, .was-validated :invalid ~ .invalid-tooltip, .is-invalid ~ .invalid-feedback, .is-invalid ~ .invalid-tooltip{display: block;}.was-validated .form-control:invalid, .form-control.is-invalid{border-color: #dc3545; padding-right: calc(1.5em + 0.75rem); background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e"); background-repeat: no-repeat; background-position: right calc(0.375em + 0.1875rem) center; background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);}.was-validated .form-control:invalid:focus, .form-control.is-invalid:focus{border-color: #dc3545; box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);}.was-validated textarea.form-control:invalid, textarea.form-control.is-invalid{padding-right: calc(1.5em + 0.75rem); background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);}.was-validated .form-select:invalid, .form-select.is-invalid{border-color: #dc3545;}.was-validated .form-select:invalid:not([multiple]):not([size]), .was-validated .form-select:invalid:not([multiple])[size="1"], .form-select.is-invalid:not([multiple]):not([size]), .form-select.is-invalid:not([multiple])[size="1"]{padding-right: 4.125rem; background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23495057' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e"), url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e"); background-position: right 0.75rem center, center right 2.25rem; background-size: 16px 12px, calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);}.was-validated .form-select:invalid:focus, .form-select.is-invalid:focus{border-color: #dc3545; box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);}.was-validated .form-check-input:invalid, .form-check-input.is-invalid{border-color: #dc3545;}.was-validated .form-check-input:invalid:checked, .form-check-input.is-invalid:checked{background-color: #dc3545;}.was-validated .form-check-input:invalid:focus, .form-check-input.is-invalid:focus{box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.25);}.was-validated .form-check-input:invalid ~ .form-check-label, .form-check-input.is-invalid ~ .form-check-label{color: #dc3545;}.form-check-inline .form-check-input ~ .invalid-feedback{margin-left: .5em;}.was-validated .input-group .form-control:invalid, .input-group .form-control.is-invalid, .was-validated .input-group .form-select:invalid, .input-group .form-select.is-invalid{z-index: 2;}.was-validated .input-group .form-control:invalid:focus, .input-group .form-control.is-invalid:focus, .was-validated .input-group .form-select:invalid:focus, .input-group .form-select.is-invalid:focus{z-index: 3;}.btn{display: inline-block; font-weight: 400; line-height: 1.5; color: #495057; text-align: center; vertical-align: middle; cursor: pointer; user-select: none; background-color: transparent; border: 1px solid transparent; padding: 0.375rem 0.75rem; font-size: 0.875rem; border-radius: 0.25rem; transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.btn{transition: none;}}.btn:hover{color: #495057;}.btn-check:focus + .btn, .btn:focus{outline: 0; box-shadow: 0 0 0 0.25rem rgba(113, 75, 103, 0.25);}.btn:disabled, .btn.o_wysiwyg_loader, .btn.disabled, fieldset:disabled .btn, fieldset.o_wysiwyg_loader .btn{pointer-events: none; opacity: 0.65;}.btn-link{font-weight: 400; color: #714B67; text-decoration: none;}.btn-link:hover{color: #5a3c52;}.btn-link:disabled, .btn-link.o_wysiwyg_loader, .btn-link.disabled{color: #6C757D;}.btn-lg, .btn-group-lg > .btn{padding: 0.5rem 1rem; font-size: 1.09375rem; border-radius: 0.3rem;}.btn-sm, .btn-group-sm > .btn{padding: 0.0625rem 0.3125rem; font-size: 0.75rem; border-radius: 0.2rem;}.fade{transition: opacity 0.15s linear;}@media (prefers-reduced-motion: reduce){.fade{transition: none;}}.fade:not(.show){opacity: 0;}.collapse:not(.show){display: none;}.collapsing{height: 0; overflow: hidden; transition: height 0.35s ease;}@media (prefers-reduced-motion: reduce){.collapsing{transition: none;}}.collapsing.collapse-horizontal{width: 0; height: auto; transition: width 0.35s ease;}@media (prefers-reduced-motion: reduce){.collapsing.collapse-horizontal{transition: none;}}.dropup, .dropend, .dropdown, .dropstart{position: relative;}.dropdown-toggle{white-space: nowrap;}.dropdown-toggle::after{display: inline-block; margin-left: 0.255em; vertical-align: 0.255em; content: ""; border-top: 0.3em solid; border-right: 0.3em solid transparent; border-bottom: 0; border-left: 0.3em solid transparent;}.dropdown-toggle:empty::after{margin-left: 0;}.dropdown-menu{position: absolute; z-index: 1000; display: none; min-width: 10rem; padding: 0.5rem 0; margin: 0; font-size: 0.875rem; color: #495057; text-align: left; list-style: none; background-color: #FFFFFF; background-clip: padding-box; border: 1px solid rgba(0, 0, 0, 0.15); border-radius: 0.25rem;}.dropdown-menu[data-bs-popper]{top: 100%; left: 0; margin-top: 0.125rem;}.dropdown-menu-start{--bs-position: start;}.dropdown-menu-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-end{--bs-position: end;}.dropdown-menu-end[data-bs-popper]{right: 0; left: auto;}@media (min-width: 576px){.dropdown-menu-sm-start{--bs-position: start;}.dropdown-menu-sm-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-sm-end{--bs-position: end;}.dropdown-menu-sm-end[data-bs-popper]{right: 0; left: auto;}}@media (min-width: 768px){.dropdown-menu-md-start{--bs-position: start;}.dropdown-menu-md-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-md-end{--bs-position: end;}.dropdown-menu-md-end[data-bs-popper]{right: 0; left: auto;}}@media (min-width: 992px){.dropdown-menu-lg-start{--bs-position: start;}.dropdown-menu-lg-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-lg-end{--bs-position: end;}.dropdown-menu-lg-end[data-bs-popper]{right: 0; left: auto;}}@media (min-width: 1200px){.dropdown-menu-xl-start{--bs-position: start;}.dropdown-menu-xl-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-xl-end{--bs-position: end;}.dropdown-menu-xl-end[data-bs-popper]{right: 0; left: auto;}}@media (min-width: 1400px){.dropdown-menu-xxl-start{--bs-position: start;}.dropdown-menu-xxl-start[data-bs-popper]{right: auto; left: 0;}.dropdown-menu-xxl-end{--bs-position: end;}.dropdown-menu-xxl-end[data-bs-popper]{right: 0; left: auto;}}.dropup .dropdown-menu[data-bs-popper]{top: auto; bottom: 100%; margin-top: 0; margin-bottom: 0.125rem;}.dropup .dropdown-toggle::after{display: inline-block; margin-left: 0.255em; vertical-align: 0.255em; content: ""; border-top: 0; border-right: 0.3em solid transparent; border-bottom: 0.3em solid; border-left: 0.3em solid transparent;}.dropup .dropdown-toggle:empty::after{margin-left: 0;}.dropend .dropdown-menu[data-bs-popper]{top: 0; right: auto; left: 100%; margin-top: 0; margin-left: 0.125rem;}.dropend .dropdown-toggle::after{display: inline-block; margin-left: 0.255em; vertical-align: 0.255em; content: ""; border-top: 0.3em solid transparent; border-right: 0; border-bottom: 0.3em solid transparent; border-left: 0.3em solid;}.dropend .dropdown-toggle:empty::after{margin-left: 0;}.dropend .dropdown-toggle::after{vertical-align: 0;}.dropstart .dropdown-menu[data-bs-popper]{top: 0; right: 100%; left: auto; margin-top: 0; margin-right: 0.125rem;}.dropstart .dropdown-toggle::after{display: inline-block; margin-left: 0.255em; vertical-align: 0.255em; content: "";}.dropstart .dropdown-toggle::after{display: none;}.dropstart .dropdown-toggle::before{display: inline-block; margin-right: 0.255em; vertical-align: 0.255em; content: ""; border-top: 0.3em solid transparent; border-right: 0.3em solid; border-bottom: 0.3em solid transparent;}.dropstart .dropdown-toggle:empty::after{margin-left: 0;}.dropstart .dropdown-toggle::before{vertical-align: 0;}.dropdown-divider{height: 0; margin: 0.5rem 0; overflow: hidden; border-top: 1px solid rgba(0, 0, 0, 0.15);}.dropdown-item{display: block; width: 100%; padding: 0.25rem 1rem; clear: both; font-weight: 400; color: #212529; text-align: inherit; white-space: nowrap; background-color: transparent; border: 0;}.dropdown-item:hover, .dropdown-item:focus{color: #1e2125; background-color: #E9ECEF;}.dropdown-item.active, .dropdown-item:active{color: #FFFFFF; text-decoration: none; background-color: #714B67;}.dropdown-item.disabled, .dropdown-item:disabled, .dropdown-item.o_wysiwyg_loader{color: #ADB5BD; pointer-events: none; background-color: transparent;}.dropdown-menu.show{display: block;}.dropdown-header{display: block; padding: 0.5rem 1rem; margin-bottom: 0; font-size: 0.75rem; color: #6C757D; white-space: nowrap;}.dropdown-item-text{display: block; padding: 0.25rem 1rem; color: #212529;}.dropdown-menu-dark{color: #dee2e6; background-color: #343A40; border-color: rgba(0, 0, 0, 0.15);}.dropdown-menu-dark .dropdown-item{color: #dee2e6;}.dropdown-menu-dark .dropdown-item:hover, .dropdown-menu-dark .dropdown-item:focus{color: #FFFFFF; background-color: rgba(255, 255, 255, 0.15);}.dropdown-menu-dark .dropdown-item.active, .dropdown-menu-dark .dropdown-item:active{color: #FFFFFF; background-color: #714B67;}.dropdown-menu-dark .dropdown-item.disabled, .dropdown-menu-dark .dropdown-item:disabled, .dropdown-menu-dark .dropdown-item.o_wysiwyg_loader{color: #ADB5BD;}.dropdown-menu-dark .dropdown-divider{border-color: rgba(0, 0, 0, 0.15);}.dropdown-menu-dark .dropdown-item-text{color: #dee2e6;}.dropdown-menu-dark .dropdown-header{color: #ADB5BD;}.btn-group, .btn-group-vertical{position: relative; display: -webkit-inline-box; display: -webkit-inline-flex; display: inline-flex; vertical-align: middle;}.btn-group > .btn, .btn-group-vertical > .btn{position: relative; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto;}.btn-group > .btn-check:checked + .btn, .btn-group > .btn-check:focus + .btn, .btn-group > .btn:hover, .btn-group > .btn:focus, .btn-group > .btn:active, .btn-group > .btn.active, .btn-group-vertical > .btn-check:checked + .btn, .btn-group-vertical > .btn-check:focus + .btn, .btn-group-vertical > .btn:hover, .btn-group-vertical > .btn:focus, .btn-group-vertical > .btn:active, .btn-group-vertical > .btn.active{z-index: 1;}.btn-toolbar{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; -webkit-box-pack: start; justify-content: flex-start;}.btn-toolbar .input-group{width: auto;}.btn-group > .btn:not(:first-child), .btn-group > .btn-group:not(:first-child){margin-left: -1px;}.btn-group > .btn:not(:last-child):not(.dropdown-toggle), .btn-group > .btn-group:not(:last-child) > .btn{border-top-right-radius: 0; border-bottom-right-radius: 0;}.btn-group > .btn:nth-child(n + 3), .btn-group > :not(.btn-check) + .btn, .btn-group > .btn-group:not(:first-child) > .btn{border-top-left-radius: 0; border-bottom-left-radius: 0;}.dropdown-toggle-split{padding-right: 0.5625rem; padding-left: 0.5625rem;}.dropdown-toggle-split::after, .dropup .dropdown-toggle-split::after, .dropend .dropdown-toggle-split::after{margin-left: 0;}.dropstart .dropdown-toggle-split::before{margin-right: 0;}.btn-sm + .dropdown-toggle-split, .btn-group-sm > .btn + .dropdown-toggle-split{padding-right: 0.234375rem; padding-left: 0.234375rem;}.btn-lg + .dropdown-toggle-split, .btn-group-lg > .btn + .dropdown-toggle-split{padding-right: 0.75rem; padding-left: 0.75rem;}.btn-group-vertical{-webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; align-items: flex-start; justify-content: center;}.btn-group-vertical > .btn, .btn-group-vertical > .btn-group{width: 100%;}.btn-group-vertical > .btn:not(:first-child), .btn-group-vertical > .btn-group:not(:first-child){margin-top: -1px;}.btn-group-vertical > .btn:not(:last-child):not(.dropdown-toggle), .btn-group-vertical > .btn-group:not(:last-child) > .btn{border-bottom-right-radius: 0; border-bottom-left-radius: 0;}.btn-group-vertical > .btn ~ .btn, .btn-group-vertical > .btn-group:not(:first-child) > .btn{border-top-left-radius: 0; border-top-right-radius: 0;}.nav{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; padding-left: 0; margin-bottom: 0; list-style: none;}.nav-link{display: block; padding: 0.5rem 1rem; color: #714B67; transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.nav-link{transition: none;}}.nav-link:hover, .nav-link:focus{color: #5a3c52;}.nav-link.disabled{color: #6C757D; pointer-events: none; cursor: default;}.nav-tabs{border-bottom: 1px solid #dee2e6;}.nav-tabs .nav-link{margin-bottom: -1px; background: none; border: 1px solid transparent; border-top-left-radius: 0.25rem; border-top-right-radius: 0.25rem;}.nav-tabs .nav-link:hover, .nav-tabs .nav-link:focus{border-color: #E9ECEF #E9ECEF #dee2e6; isolation: isolate;}.nav-tabs .nav-link.disabled{color: #6C757D; background-color: transparent; border-color: transparent;}.nav-tabs .nav-link.active, .nav-tabs .nav-item.show .nav-link{color: #495057; background-color: white; border-color: #dee2e6 #dee2e6 white;}.nav-tabs .dropdown-menu{margin-top: -1px; border-top-left-radius: 0; border-top-right-radius: 0;}.nav-pills .nav-link{background: none; border: 0; border-radius: 0.25rem;}.nav-pills .nav-link.active, .nav-pills .show > .nav-link{color: #FFFFFF; background-color: #714B67;}.nav-fill > .nav-link, .nav-fill .nav-item{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; text-align: center;}.nav-justified > .nav-link, .nav-justified .nav-item{flex-basis: 0; flex-grow: 1; text-align: center;}.nav-fill .nav-item .nav-link, .nav-justified .nav-item .nav-link{width: 100%;}.tab-content > .tab-pane{display: none;}.tab-content > .active{display: block;}.navbar{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; align-items: center; justify-content: space-between; padding-top: 0.5rem; padding-bottom: 0.5rem;}.navbar > .container, .navbar > .o_container_small, .navbar > .container-fluid, .navbar > .container-sm, .navbar > .container-md, .navbar > .container-lg, .navbar > .container-xl, .navbar > .container-xxl{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: inherit; flex-wrap: inherit; align-items: center; justify-content: space-between;}.navbar-brand{padding-top: 0.3359375rem; padding-bottom: 0.3359375rem; margin-right: 1rem; font-size: 1.09375rem; white-space: nowrap;}.navbar-nav{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; padding-left: 0; margin-bottom: 0; list-style: none;}.navbar-nav .nav-link{padding-right: 0; padding-left: 0;}.navbar-nav .dropdown-menu{position: static;}.navbar-text{padding-top: 0.5rem; padding-bottom: 0.5rem;}.navbar-collapse{flex-basis: 100%; flex-grow: 1; align-items: center;}.navbar-toggler{padding: 0.25rem 0.75rem; font-size: 1.09375rem; line-height: 1; background-color: transparent; border: 1px solid transparent; border-radius: 0.25rem; transition: box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.navbar-toggler{transition: none;}}.navbar-toggler:hover{text-decoration: none;}.navbar-toggler:focus{text-decoration: none; outline: 0; box-shadow: 0 0 0 0.25rem;}.navbar-toggler-icon{display: inline-block; width: 1.5em; height: 1.5em; vertical-align: middle; background-repeat: no-repeat; background-position: center; background-size: 100%;}.navbar-nav-scroll{max-height: var(--bs-scroll-height, 75vh); overflow-y: auto;}@media (min-width: 576px){.navbar-expand-sm{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-sm .navbar-nav{flex-direction: row;}.navbar-expand-sm .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-sm .navbar-nav .nav-link{padding-right: 0.5rem; padding-left: 0.5rem;}.navbar-expand-sm .navbar-nav-scroll{overflow: visible;}.navbar-expand-sm .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-sm .navbar-toggler{display: none;}.navbar-expand-sm .offcanvas-header{display: none;}.navbar-expand-sm .offcanvas{position: inherit; bottom: 0; z-index: 1000; flex-grow: 1; visibility: visible !important; background-color: transparent; border-right: 0; border-left: 0; transition: none; transform: none;}.navbar-expand-sm .offcanvas-top, .navbar-expand-sm .offcanvas-bottom{height: auto; border-top: 0; border-bottom: 0;}.navbar-expand-sm .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}}@media (min-width: 768px){.navbar-expand-md{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-md .navbar-nav{flex-direction: row;}.navbar-expand-md .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-md .navbar-nav .nav-link{padding-right: 0.5rem; padding-left: 0.5rem;}.navbar-expand-md .navbar-nav-scroll{overflow: visible;}.navbar-expand-md .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-md .navbar-toggler{display: none;}.navbar-expand-md .offcanvas-header{display: none;}.navbar-expand-md .offcanvas{position: inherit; bottom: 0; z-index: 1000; flex-grow: 1; visibility: visible !important; background-color: transparent; border-right: 0; border-left: 0; transition: none; transform: none;}.navbar-expand-md .offcanvas-top, .navbar-expand-md .offcanvas-bottom{height: auto; border-top: 0; border-bottom: 0;}.navbar-expand-md .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}}@media (min-width: 992px){.navbar-expand-lg{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-lg .navbar-nav{flex-direction: row;}.navbar-expand-lg .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-lg .navbar-nav .nav-link{padding-right: 0.5rem; padding-left: 0.5rem;}.navbar-expand-lg .navbar-nav-scroll{overflow: visible;}.navbar-expand-lg .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-lg .navbar-toggler{display: none;}.navbar-expand-lg .offcanvas-header{display: none;}.navbar-expand-lg .offcanvas{position: inherit; bottom: 0; z-index: 1000; flex-grow: 1; visibility: visible !important; background-color: transparent; border-right: 0; border-left: 0; transition: none; transform: none;}.navbar-expand-lg .offcanvas-top, .navbar-expand-lg .offcanvas-bottom{height: auto; border-top: 0; border-bottom: 0;}.navbar-expand-lg .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}}@media (min-width: 1200px){.navbar-expand-xl{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-xl .navbar-nav{flex-direction: row;}.navbar-expand-xl .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-xl .navbar-nav .nav-link{padding-right: 0.5rem; padding-left: 0.5rem;}.navbar-expand-xl .navbar-nav-scroll{overflow: visible;}.navbar-expand-xl .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-xl .navbar-toggler{display: none;}.navbar-expand-xl .offcanvas-header{display: none;}.navbar-expand-xl .offcanvas{position: inherit; bottom: 0; z-index: 1000; flex-grow: 1; visibility: visible !important; background-color: transparent; border-right: 0; border-left: 0; transition: none; transform: none;}.navbar-expand-xl .offcanvas-top, .navbar-expand-xl .offcanvas-bottom{height: auto; border-top: 0; border-bottom: 0;}.navbar-expand-xl .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}}@media (min-width: 1400px){.navbar-expand-xxl{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand-xxl .navbar-nav{flex-direction: row;}.navbar-expand-xxl .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand-xxl .navbar-nav .nav-link{padding-right: 0.5rem; padding-left: 0.5rem;}.navbar-expand-xxl .navbar-nav-scroll{overflow: visible;}.navbar-expand-xxl .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand-xxl .navbar-toggler{display: none;}.navbar-expand-xxl .offcanvas-header{display: none;}.navbar-expand-xxl .offcanvas{position: inherit; bottom: 0; z-index: 1000; flex-grow: 1; visibility: visible !important; background-color: transparent; border-right: 0; border-left: 0; transition: none; transform: none;}.navbar-expand-xxl .offcanvas-top, .navbar-expand-xxl .offcanvas-bottom{height: auto; border-top: 0; border-bottom: 0;}.navbar-expand-xxl .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}}.navbar-expand{-webkit-flex-wrap: nowrap; flex-wrap: nowrap; -webkit-box-pack: start; justify-content: flex-start;}.navbar-expand .navbar-nav{flex-direction: row;}.navbar-expand .navbar-nav .dropdown-menu{position: absolute;}.navbar-expand .navbar-nav .nav-link{padding-right: 0.5rem; padding-left: 0.5rem;}.navbar-expand .navbar-nav-scroll{overflow: visible;}.navbar-expand .navbar-collapse{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; flex-basis: auto;}.navbar-expand .navbar-toggler{display: none;}.navbar-expand .offcanvas-header{display: none;}.navbar-expand .offcanvas{position: inherit; bottom: 0; z-index: 1000; flex-grow: 1; visibility: visible !important; background-color: transparent; border-right: 0; border-left: 0; transition: none; transform: none;}.navbar-expand .offcanvas-top, .navbar-expand .offcanvas-bottom{height: auto; border-top: 0; border-bottom: 0;}.navbar-expand .offcanvas-body{display: -webkit-box; display: -webkit-flex; display: flex; flex-grow: 0; padding: 0; overflow-y: visible;}.navbar-light .navbar-brand{color: rgba(0, 0, 0, 0.9);}.navbar-light .navbar-brand:hover, .navbar-light .navbar-brand:focus{color: rgba(0, 0, 0, 0.9);}.navbar-light .navbar-nav .nav-link{color: rgba(0, 0, 0, 0.55);}.navbar-light .navbar-nav .nav-link:hover, .navbar-light .navbar-nav .nav-link:focus{color: rgba(0, 0, 0, 0.7);}.navbar-light .navbar-nav .nav-link.disabled{color: rgba(0, 0, 0, 0.3);}.navbar-light .navbar-nav .show > .nav-link, .navbar-light .navbar-nav .nav-link.active{color: rgba(0, 0, 0, 0.9);}.navbar-light .navbar-toggler{color: rgba(0, 0, 0, 0.55); border-color: transparent;}.navbar-light .navbar-toggler-icon{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%280, 0, 0, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");}.navbar-light .navbar-text{color: rgba(0, 0, 0, 0.55);}.navbar-light .navbar-text a, .navbar-light .navbar-text a:hover, .navbar-light .navbar-text a:focus{color: rgba(0, 0, 0, 0.9);}.navbar-dark .navbar-brand{color: #FFFFFF;}.navbar-dark .navbar-brand:hover, .navbar-dark .navbar-brand:focus{color: #FFFFFF;}.navbar-dark .navbar-nav .nav-link{color: rgba(255, 255, 255, 0.55);}.navbar-dark .navbar-nav .nav-link:hover, .navbar-dark .navbar-nav .nav-link:focus{color: rgba(255, 255, 255, 0.75);}.navbar-dark .navbar-nav .nav-link.disabled{color: rgba(255, 255, 255, 0.25);}.navbar-dark .navbar-nav .show > .nav-link, .navbar-dark .navbar-nav .nav-link.active{color: #FFFFFF;}.navbar-dark .navbar-toggler{color: rgba(255, 255, 255, 0.55); border-color: transparent;}.navbar-dark .navbar-toggler-icon{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 30 30'%3e%3cpath stroke='rgba%28255, 255, 255, 0.55%29' stroke-linecap='round' stroke-miterlimit='10' stroke-width='2' d='M4 7h22M4 15h22M4 23h22'/%3e%3c/svg%3e");}.navbar-dark .navbar-text{color: rgba(255, 255, 255, 0.55);}.navbar-dark .navbar-text a, .navbar-dark .navbar-text a:hover, .navbar-dark .navbar-text a:focus{color: #FFFFFF;}.card{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; min-width: 0; word-wrap: break-word; background-color: #FFFFFF; background-clip: border-box; border: 1px solid rgba(0, 0, 0, 0.125); border-radius: 0.25rem;}.card > hr{margin-right: 0; margin-left: 0;}.card > .list-group{border-top: inherit; border-bottom: inherit;}.card > .list-group:first-child{border-top-width: 0; border-top-left-radius: calc(0.25rem - 1px); border-top-right-radius: calc(0.25rem - 1px);}.card > .list-group:last-child{border-bottom-width: 0; border-bottom-right-radius: calc(0.25rem - 1px); border-bottom-left-radius: calc(0.25rem - 1px);}.card > .card-header + .list-group, .card > .list-group + .card-footer{border-top: 0;}.card-body{-webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; padding: 1rem 1rem;}.card-title{margin-bottom: 0.5rem;}.card-subtitle{margin-top: -0.25rem; margin-bottom: 0;}.card-text:last-child{margin-bottom: 0;}.card-link + .card-link{margin-left: 1rem;}.card-header{padding: 0.5rem 1rem; margin-bottom: 0; background-color: rgba(0, 0, 0, 0.03); border-bottom: 1px solid rgba(0, 0, 0, 0.125);}.card-header:first-child{border-radius: calc(0.25rem - 1px) calc(0.25rem - 1px) 0 0;}.card-footer{padding: 0.5rem 1rem; background-color: rgba(0, 0, 0, 0.03); border-top: 1px solid rgba(0, 0, 0, 0.125);}.card-footer:last-child{border-radius: 0 0 calc(0.25rem - 1px) calc(0.25rem - 1px);}.card-header-tabs{margin-right: -0.5rem; margin-bottom: -0.5rem; margin-left: -0.5rem; border-bottom: 0;}.card-header-pills{margin-right: -0.5rem; margin-left: -0.5rem;}.card-img-overlay{position: absolute; top: 0; right: 0; bottom: 0; left: 0; padding: 1rem; border-radius: calc(0.25rem - 1px);}.card-img, .card-img-top, .card-img-bottom{width: 100%;}.card-img, .card-img-top{border-top-left-radius: calc(0.25rem - 1px); border-top-right-radius: calc(0.25rem - 1px);}.card-img, .card-img-bottom{border-bottom-right-radius: calc(0.25rem - 1px); border-bottom-left-radius: calc(0.25rem - 1px);}.card-group > .card{margin-bottom: 0.75rem;}@media (min-width: 576px){.card-group{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: row wrap; flex-flow: row wrap;}.card-group > .card{flex: 1 0 0%; margin-bottom: 0;}.card-group > .card + .card{margin-left: 0; border-left: 0;}.card-group > .card:not(:last-child){border-top-right-radius: 0; border-bottom-right-radius: 0;}.card-group > .card:not(:last-child) .card-img-top, .card-group > .card:not(:last-child) .card-header{border-top-right-radius: 0;}.card-group > .card:not(:last-child) .card-img-bottom, .card-group > .card:not(:last-child) .card-footer{border-bottom-right-radius: 0;}.card-group > .card:not(:first-child){border-top-left-radius: 0; border-bottom-left-radius: 0;}.card-group > .card:not(:first-child) .card-img-top, .card-group > .card:not(:first-child) .card-header{border-top-left-radius: 0;}.card-group > .card:not(:first-child) .card-img-bottom, .card-group > .card:not(:first-child) .card-footer{border-bottom-left-radius: 0;}}.accordion-button{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; width: 100%; padding: 1rem 1.25rem; font-size: 0.875rem; color: #495057; text-align: left; background-color: white; border: 0; border-radius: 0; overflow-anchor: none; transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out, border-radius 0.15s ease;}@media (prefers-reduced-motion: reduce){.accordion-button{transition: none;}}.accordion-button:not(.collapsed){color: #66445d; background-color: #f1edf0; box-shadow: inset 0 -1px 0 rgba(0, 0, 0, 0.125);}.accordion-button:not(.collapsed)::after{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%2366445d'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e"); transform: rotate(-180deg);}.accordion-button::after{flex-shrink: 0; width: 1.25rem; height: 1.25rem; margin-left: auto; content: ""; background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23495057'%3e%3cpath fill-rule='evenodd' d='M1.646 4.646a.5.5 0 0 1 .708 0L8 10.293l5.646-5.647a.5.5 0 0 1 .708.708l-6 6a.5.5 0 0 1-.708 0l-6-6a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e"); background-repeat: no-repeat; background-size: 1.25rem; transition: transform 0.2s ease-in-out;}@media (prefers-reduced-motion: reduce){.accordion-button::after{transition: none;}}.accordion-button:hover{z-index: 2;}.accordion-button:focus{z-index: 3; border-color: #b8a5b3; outline: 0; box-shadow: 0 0 0 0.25rem rgba(113, 75, 103, 0.25);}.accordion-header{margin-bottom: 0;}.accordion-item{background-color: white; border: 1px solid rgba(0, 0, 0, 0.125);}.accordion-item:first-of-type{border-top-left-radius: 0.25rem; border-top-right-radius: 0.25rem;}.accordion-item:first-of-type .accordion-button{border-top-left-radius: calc(0.25rem - 1px); border-top-right-radius: calc(0.25rem - 1px);}.accordion-item:not(:first-of-type){border-top: 0;}.accordion-item:last-of-type{border-bottom-right-radius: 0.25rem; border-bottom-left-radius: 0.25rem;}.accordion-item:last-of-type .accordion-button.collapsed{border-bottom-right-radius: calc(0.25rem - 1px); border-bottom-left-radius: calc(0.25rem - 1px);}.accordion-item:last-of-type .accordion-collapse{border-bottom-right-radius: 0.25rem; border-bottom-left-radius: 0.25rem;}.accordion-body{padding: 1rem 1.25rem;}.accordion-flush .accordion-collapse{border-width: 0;}.accordion-flush .accordion-item{border-right: 0; border-left: 0; border-radius: 0;}.accordion-flush .accordion-item:first-child{border-top: 0;}.accordion-flush .accordion-item:last-child{border-bottom: 0;}.accordion-flush .accordion-item .accordion-button{border-radius: 0;}.breadcrumb{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; padding: 0 0; margin-bottom: 1rem; list-style: none;}.breadcrumb-item + .breadcrumb-item{padding-left: 0.5rem;}.breadcrumb-item + .breadcrumb-item::before{float: left; padding-right: 0.5rem; color: #6C757D; content: var(--bs-breadcrumb-divider, "/") ;}.breadcrumb-item.active{color: #6C757D;}.pagination{display: -webkit-box; display: -webkit-flex; display: flex; padding-left: 0; list-style: none;}.page-link{position: relative; display: block; color: #714B67; background-color: #FFFFFF; border: 1px solid #dee2e6; transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;}@media (prefers-reduced-motion: reduce){.page-link{transition: none;}}.page-link:hover{z-index: 2; color: #5a3c52; background-color: #E9ECEF; border-color: #dee2e6;}.page-link:focus{z-index: 3; color: #5a3c52; background-color: #E9ECEF; outline: 0; box-shadow: 0 0 0 0.25rem rgba(113, 75, 103, 0.25);}.page-item:not(:first-child) .page-link{margin-left: -1px;}.page-item.active .page-link{z-index: 3; color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.page-item.disabled .page-link{color: #6C757D; pointer-events: none; background-color: #FFFFFF; border-color: #dee2e6;}.page-link{padding: 0.375rem 0.75rem;}.page-item:first-child .page-link{border-top-left-radius: 0.25rem; border-bottom-left-radius: 0.25rem;}.page-item:last-child .page-link{border-top-right-radius: 0.25rem; border-bottom-right-radius: 0.25rem;}.pagination-lg .page-link{padding: 0.75rem 1.5rem; font-size: 1.09375rem;}.pagination-lg .page-item:first-child .page-link{border-top-left-radius: 0.3rem; border-bottom-left-radius: 0.3rem;}.pagination-lg .page-item:last-child .page-link{border-top-right-radius: 0.3rem; border-bottom-right-radius: 0.3rem;}.pagination-sm .page-link{padding: 0.25rem 0.5rem; font-size: 0.75rem;}.pagination-sm .page-item:first-child .page-link{border-top-left-radius: 0.2rem; border-bottom-left-radius: 0.2rem;}.pagination-sm .page-item:last-child .page-link{border-top-right-radius: 0.2rem; border-bottom-right-radius: 0.2rem;}.badge{display: inline-block; padding: 0.35em 0.65em; font-size: 0.75em; font-weight: 700; line-height: 1; color: #FFFFFF; text-align: center; white-space: nowrap; vertical-align: baseline; border-radius: 0.25rem;}.badge:empty{display: none;}.btn .badge{position: relative; top: -1px;}.alert{position: relative; padding: 1rem 1rem; margin-bottom: 1rem; border: 1px solid transparent; border-radius: 0.25rem;}.alert-heading{color: inherit;}.alert-link{font-weight: 700;}.alert-dismissible{padding-right: 3rem;}.alert-dismissible .btn-close{position: absolute; top: 0; right: 0; z-index: 2; padding: 1.25rem 1rem;}.alert-primary{color: #442d3e; background-color: #e3dbe1; border-color: #d4c9d1;}.alert-primary .alert-link{color: #362432;}.alert-secondary{color: #505961; background-color: #e7eaec; border-color: #dadfe3;}.alert-secondary .alert-link{color: #40474e;}.alert-success{color: #186429; background-color: #d4edda; border-color: #bfe5c7;}.alert-success .alert-link{color: #135021;}.alert-info{color: #0e616e; background-color: #d1ecf1; border-color: #b9e3ea;}.alert-info .alert-link{color: #0b4e58;}.alert-warning{color: #997404; background-color: #fff3cd; border-color: #ffecb5;}.alert-warning .alert-link{color: #7a5d03;}.alert-danger{color: #842029; background-color: #f8d7da; border-color: #f5c2c7;}.alert-danger .alert-link{color: #6a1a21;}.alert-light{color: #8f8f8f; background-color: #fcfcfc; border-color: #fafafa;}.alert-light .alert-link{color: #727272;}.alert-dark{color: #141619; background-color: #d3d3d4; border-color: #bcbebf;}.alert-dark .alert-link{color: #101214;}@keyframes progress-bar-stripes{0%{background-position-x: 1rem;}}.progress{display: -webkit-box; display: -webkit-flex; display: flex; height: 1rem; overflow: hidden; font-size: 0.65625rem; background-color: #E9ECEF; border-radius: 0.25rem;}.progress-bar{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; justify-content: center; overflow: hidden; color: #FFFFFF; text-align: center; white-space: nowrap; background-color: #714B67; transition: width 0.6s ease;}@media (prefers-reduced-motion: reduce){.progress-bar{transition: none;}}.progress-bar-striped{background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.15) 50%, rgba(255, 255, 255, 0.15) 75%, transparent 75%, transparent); background-size: 1rem 1rem;}.progress-bar-animated{animation: 1s linear infinite progress-bar-stripes;}@media (prefers-reduced-motion: reduce){.progress-bar-animated{animation: none;}}.list-group{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; padding-left: 0; margin-bottom: 0; border-radius: 0.25rem;}.list-group-numbered{list-style-type: none; counter-reset: section;}.list-group-numbered > li::before{content: counters(section, ".") ". "; counter-increment: section;}.list-group-item-action{width: 100%; color: #495057; text-align: inherit;}.list-group-item-action:hover, .list-group-item-action:focus{z-index: 1; color: #495057; text-decoration: none; background-color: #F8F9FA;}.list-group-item-action:active{color: #495057; background-color: #E9ECEF;}.list-group-item{position: relative; display: block; padding: 0.5rem 1rem; color: #212529; background-color: #FFFFFF; border: 1px solid rgba(0, 0, 0, 0.125);}.list-group-item:first-child{border-top-left-radius: inherit; border-top-right-radius: inherit;}.list-group-item:last-child{border-bottom-right-radius: inherit; border-bottom-left-radius: inherit;}.list-group-item.disabled, .list-group-item:disabled, .list-group-item.o_wysiwyg_loader{color: #6C757D; pointer-events: none; background-color: #FFFFFF;}.list-group-item.active{z-index: 2; color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.list-group-item + .list-group-item{border-top-width: 0;}.list-group-item + .list-group-item.active{margin-top: -1px; border-top-width: 1px;}.list-group-horizontal{flex-direction: row;}.list-group-horizontal > .list-group-item:first-child{border-bottom-left-radius: 0.25rem; border-top-right-radius: 0;}.list-group-horizontal > .list-group-item:last-child{border-top-right-radius: 0.25rem; border-bottom-left-radius: 0;}.list-group-horizontal > .list-group-item.active{margin-top: 0;}.list-group-horizontal > .list-group-item + .list-group-item{border-top-width: 1px; border-left-width: 0;}.list-group-horizontal > .list-group-item + .list-group-item.active{margin-left: -1px; border-left-width: 1px;}@media (min-width: 576px){.list-group-horizontal-sm{flex-direction: row;}.list-group-horizontal-sm > .list-group-item:first-child{border-bottom-left-radius: 0.25rem; border-top-right-radius: 0;}.list-group-horizontal-sm > .list-group-item:last-child{border-top-right-radius: 0.25rem; border-bottom-left-radius: 0;}.list-group-horizontal-sm > .list-group-item.active{margin-top: 0;}.list-group-horizontal-sm > .list-group-item + .list-group-item{border-top-width: 1px; border-left-width: 0;}.list-group-horizontal-sm > .list-group-item + .list-group-item.active{margin-left: -1px; border-left-width: 1px;}}@media (min-width: 768px){.list-group-horizontal-md{flex-direction: row;}.list-group-horizontal-md > .list-group-item:first-child{border-bottom-left-radius: 0.25rem; border-top-right-radius: 0;}.list-group-horizontal-md > .list-group-item:last-child{border-top-right-radius: 0.25rem; border-bottom-left-radius: 0;}.list-group-horizontal-md > .list-group-item.active{margin-top: 0;}.list-group-horizontal-md > .list-group-item + .list-group-item{border-top-width: 1px; border-left-width: 0;}.list-group-horizontal-md > .list-group-item + .list-group-item.active{margin-left: -1px; border-left-width: 1px;}}@media (min-width: 992px){.list-group-horizontal-lg{flex-direction: row;}.list-group-horizontal-lg > .list-group-item:first-child{border-bottom-left-radius: 0.25rem; border-top-right-radius: 0;}.list-group-horizontal-lg > .list-group-item:last-child{border-top-right-radius: 0.25rem; border-bottom-left-radius: 0;}.list-group-horizontal-lg > .list-group-item.active{margin-top: 0;}.list-group-horizontal-lg > .list-group-item + .list-group-item{border-top-width: 1px; border-left-width: 0;}.list-group-horizontal-lg > .list-group-item + .list-group-item.active{margin-left: -1px; border-left-width: 1px;}}@media (min-width: 1200px){.list-group-horizontal-xl{flex-direction: row;}.list-group-horizontal-xl > .list-group-item:first-child{border-bottom-left-radius: 0.25rem; border-top-right-radius: 0;}.list-group-horizontal-xl > .list-group-item:last-child{border-top-right-radius: 0.25rem; border-bottom-left-radius: 0;}.list-group-horizontal-xl > .list-group-item.active{margin-top: 0;}.list-group-horizontal-xl > .list-group-item + .list-group-item{border-top-width: 1px; border-left-width: 0;}.list-group-horizontal-xl > .list-group-item + .list-group-item.active{margin-left: -1px; border-left-width: 1px;}}@media (min-width: 1400px){.list-group-horizontal-xxl{flex-direction: row;}.list-group-horizontal-xxl > .list-group-item:first-child{border-bottom-left-radius: 0.25rem; border-top-right-radius: 0;}.list-group-horizontal-xxl > .list-group-item:last-child{border-top-right-radius: 0.25rem; border-bottom-left-radius: 0;}.list-group-horizontal-xxl > .list-group-item.active{margin-top: 0;}.list-group-horizontal-xxl > .list-group-item + .list-group-item{border-top-width: 1px; border-left-width: 0;}.list-group-horizontal-xxl > .list-group-item + .list-group-item.active{margin-left: -1px; border-left-width: 1px;}}.list-group-flush{border-radius: 0;}.list-group-flush > .list-group-item{border-width: 0 0 1px;}.list-group-flush > .list-group-item:last-child{border-bottom-width: 0;}.list-group-item-primary{color: #442d3e; background-color: #e3dbe1;}.list-group-item-primary.list-group-item-action:hover, .list-group-item-primary.list-group-item-action:focus{color: #442d3e; background-color: #ccc5cb;}.list-group-item-primary.list-group-item-action.active{color: #FFFFFF; background-color: #442d3e; border-color: #442d3e;}.list-group-item-secondary{color: #505961; background-color: #e7eaec;}.list-group-item-secondary.list-group-item-action:hover, .list-group-item-secondary.list-group-item-action:focus{color: #505961; background-color: #d0d3d4;}.list-group-item-secondary.list-group-item-action.active{color: #FFFFFF; background-color: #505961; border-color: #505961;}.list-group-item-success{color: #186429; background-color: #d4edda;}.list-group-item-success.list-group-item-action:hover, .list-group-item-success.list-group-item-action:focus{color: #186429; background-color: #bfd5c4;}.list-group-item-success.list-group-item-action.active{color: #FFFFFF; background-color: #186429; border-color: #186429;}.list-group-item-info{color: #0e616e; background-color: #d1ecf1;}.list-group-item-info.list-group-item-action:hover, .list-group-item-info.list-group-item-action:focus{color: #0e616e; background-color: #bcd4d9;}.list-group-item-info.list-group-item-action.active{color: #FFFFFF; background-color: #0e616e; border-color: #0e616e;}.list-group-item-warning{color: #997404; background-color: #fff3cd;}.list-group-item-warning.list-group-item-action:hover, .list-group-item-warning.list-group-item-action:focus{color: #997404; background-color: #e6dbb9;}.list-group-item-warning.list-group-item-action.active{color: #FFFFFF; background-color: #997404; border-color: #997404;}.list-group-item-danger{color: #842029; background-color: #f8d7da;}.list-group-item-danger.list-group-item-action:hover, .list-group-item-danger.list-group-item-action:focus{color: #842029; background-color: #dfc2c4;}.list-group-item-danger.list-group-item-action.active{color: #FFFFFF; background-color: #842029; border-color: #842029;}.list-group-item-light{color: #8f8f8f; background-color: #fcfcfc;}.list-group-item-light.list-group-item-action:hover, .list-group-item-light.list-group-item-action:focus{color: #8f8f8f; background-color: #e3e3e3;}.list-group-item-light.list-group-item-action.active{color: #FFFFFF; background-color: #8f8f8f; border-color: #8f8f8f;}.list-group-item-dark{color: #141619; background-color: #d3d3d4;}.list-group-item-dark.list-group-item-action:hover, .list-group-item-dark.list-group-item-action:focus{color: #141619; background-color: #bebebf;}.list-group-item-dark.list-group-item-action.active{color: #FFFFFF; background-color: #141619; border-color: #141619;}.btn-close{box-sizing: content-box; width: 1em; height: 1em; padding: 0.25em 0.25em; color: #000000; background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23000000'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat; border: 0; border-radius: 0.25rem; opacity: 0.5;}.btn-close:hover{color: #000000; text-decoration: none; opacity: 0.75;}.btn-close:focus{outline: 0; box-shadow: 0 0 0 0.25rem rgba(113, 75, 103, 0.25); opacity: 1;}.btn-close:disabled, .btn-close.o_wysiwyg_loader, .btn-close.disabled{pointer-events: none; user-select: none; opacity: 0.25;}.btn-close-white{filter: invert(1) grayscale(100%) brightness(200%);}.toast{width: 350px; max-width: 100%; font-size: 0.875rem; pointer-events: auto; background-color: rgba(255, 255, 255, 0.85); background-clip: padding-box; border: 1px solid rgba(0, 0, 0, 0.1); box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15); border-radius: 0.25rem;}.toast.showing{opacity: 0;}.toast:not(.show){display: none;}.toast-container{width: max-content; max-width: 100%; pointer-events: none;}.toast-container > :not(:last-child){margin-bottom: 0.75rem;}.toast-header{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; padding: 0.5rem 0.75rem; color: #6C757D; background-color: rgba(255, 255, 255, 0.85); background-clip: padding-box; border-bottom: 1px solid rgba(0, 0, 0, 0.05); border-top-left-radius: calc(0.25rem - 1px); border-top-right-radius: calc(0.25rem - 1px);}.toast-header .btn-close{margin-right: -0.375rem; margin-left: 0.75rem;}.toast-body{padding: 0.75rem; word-wrap: break-word;}.modal{position: fixed; top: 0; left: 0; z-index: 1055; display: none; width: 100%; height: 100%; overflow-x: hidden; overflow-y: auto; outline: 0;}.modal-dialog{position: relative; width: auto; margin: 0.5rem; pointer-events: none;}.modal.fade .modal-dialog{transition: transform 0.3s ease-out; transform: translate(0, -50px);}@media (prefers-reduced-motion: reduce){.modal.fade .modal-dialog{transition: none;}}.modal.show .modal-dialog{transform: none;}.modal.modal-static .modal-dialog{transform: scale(1.02);}.modal-dialog-scrollable{height: calc(100% - 1rem);}.modal-dialog-scrollable .modal-content{max-height: 100%; overflow: hidden;}.modal-dialog-scrollable .modal-body{overflow-y: auto;}.modal-dialog-centered{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; min-height: calc(100% - 1rem);}.modal-content{position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; width: 100%; pointer-events: auto; background-color: #FFFFFF; background-clip: padding-box; border: 1px solid rgba(0, 0, 0, 0.2); border-radius: 0.3rem; outline: 0;}.modal-backdrop{position: fixed; top: 0; left: 0; z-index: 1050; width: 100vw; height: 100vh; background-color: #000000;}.modal-backdrop.fade{opacity: 0;}.modal-backdrop.show{opacity: 0.5;}.modal-header{display: -webkit-box; display: -webkit-flex; display: flex; flex-shrink: 0; align-items: center; justify-content: space-between; padding: 1rem 1rem; border-bottom: 1px solid #dee2e6; border-top-left-radius: calc(0.3rem - 1px); border-top-right-radius: calc(0.3rem - 1px);}.modal-header .btn-close{padding: 0.5rem 0.5rem; margin: -0.5rem -0.5rem -0.5rem auto;}.modal-title{margin-bottom: 0; line-height: 1.5;}.modal-body{position: relative; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; padding: 1rem;}.modal-footer{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; flex-shrink: 0; align-items: center; -webkit-box-pack: end; justify-content: flex-end; padding: 0.75rem; border-top: 1px solid #dee2e6; border-bottom-right-radius: calc(0.3rem - 1px); border-bottom-left-radius: calc(0.3rem - 1px);}.modal-footer > *{margin: 0.25rem;}@media (min-width: 576px){.modal-dialog{max-width: 650px; margin: 1.75rem auto;}.modal-dialog-scrollable{height: calc(100% - 3.5rem);}.modal-dialog-centered{min-height: calc(100% - 3.5rem);}.modal-sm{max-width: 300px;}}@media (min-width: 992px){.modal-lg, .modal-xl{max-width: 980px;}}@media (min-width: 1200px){.modal-xl{max-width: 1140px;}}.modal-fullscreen{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen .modal-header{border-radius: 0;}.modal-fullscreen .modal-body{overflow-y: auto;}.modal-fullscreen .modal-footer{border-radius: 0;}@media (max-width: 575.98px){.modal-fullscreen-sm-down{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen-sm-down .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen-sm-down .modal-header{border-radius: 0;}.modal-fullscreen-sm-down .modal-body{overflow-y: auto;}.modal-fullscreen-sm-down .modal-footer{border-radius: 0;}}@media (max-width: 767.98px){.modal-fullscreen-md-down{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen-md-down .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen-md-down .modal-header{border-radius: 0;}.modal-fullscreen-md-down .modal-body{overflow-y: auto;}.modal-fullscreen-md-down .modal-footer{border-radius: 0;}}@media (max-width: 991.98px){.modal-fullscreen-lg-down{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen-lg-down .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen-lg-down .modal-header{border-radius: 0;}.modal-fullscreen-lg-down .modal-body{overflow-y: auto;}.modal-fullscreen-lg-down .modal-footer{border-radius: 0;}}@media (max-width: 1199.98px){.modal-fullscreen-xl-down{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen-xl-down .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen-xl-down .modal-header{border-radius: 0;}.modal-fullscreen-xl-down .modal-body{overflow-y: auto;}.modal-fullscreen-xl-down .modal-footer{border-radius: 0;}}@media (max-width: 1399.98px){.modal-fullscreen-xxl-down{width: 100vw; max-width: none; height: 100%; margin: 0;}.modal-fullscreen-xxl-down .modal-content{height: 100%; border: 0; border-radius: 0;}.modal-fullscreen-xxl-down .modal-header{border-radius: 0;}.modal-fullscreen-xxl-down .modal-body{overflow-y: auto;}.modal-fullscreen-xxl-down .modal-footer{border-radius: 0;}}.tooltip{position: absolute; z-index: 1080; display: block; margin: 0; font-family: var(--bs-font-sans-serif); font-style: normal; font-weight: 400; line-height: 1.5; text-align: left; text-align: start; text-decoration: none; text-shadow: none; text-transform: none; letter-spacing: normal; word-break: normal; word-spacing: normal; white-space: normal; line-break: auto; font-size: 0.75rem; word-wrap: break-word; opacity: 0;}.tooltip.show{opacity: 0.9;}.tooltip .tooltip-arrow{position: absolute; display: block; width: 0.8rem; height: 0.4rem;}.tooltip .tooltip-arrow::before{position: absolute; content: ""; border-color: transparent; border-style: solid;}.bs-tooltip-top, .bs-tooltip-auto[data-popper-placement^="top"]{padding: 0.4rem 0;}.bs-tooltip-top .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^="top"] .tooltip-arrow{bottom: 0;}.bs-tooltip-top .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^="top"] .tooltip-arrow::before{top: -1px; border-width: 0.4rem 0.4rem 0; border-top-color: #000000;}.bs-tooltip-end, .bs-tooltip-auto[data-popper-placement^="right"]{padding: 0 0.4rem;}.bs-tooltip-end .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^="right"] .tooltip-arrow{left: 0; width: 0.4rem; height: 0.8rem;}.bs-tooltip-end .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^="right"] .tooltip-arrow::before{right: -1px; border-width: 0.4rem 0.4rem 0.4rem 0; border-right-color: #000000;}.bs-tooltip-bottom, .bs-tooltip-auto[data-popper-placement^="bottom"]{padding: 0.4rem 0;}.bs-tooltip-bottom .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^="bottom"] .tooltip-arrow{top: 0;}.bs-tooltip-bottom .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^="bottom"] .tooltip-arrow::before{bottom: -1px; border-width: 0 0.4rem 0.4rem; border-bottom-color: #000000;}.bs-tooltip-start, .bs-tooltip-auto[data-popper-placement^="left"]{padding: 0 0.4rem;}.bs-tooltip-start .tooltip-arrow, .bs-tooltip-auto[data-popper-placement^="left"] .tooltip-arrow{right: 0; width: 0.4rem; height: 0.8rem;}.bs-tooltip-start .tooltip-arrow::before, .bs-tooltip-auto[data-popper-placement^="left"] .tooltip-arrow::before{left: -1px; border-width: 0.4rem 0 0.4rem 0.4rem; border-left-color: #000000;}.tooltip-inner{max-width: 200px; padding: 0.25rem 0.5rem; color: #FFFFFF; text-align: center; background-color: #000000; border-radius: 0.25rem;}.popover{position: absolute; top: 0; left: 0 ; z-index: 1070; display: block; max-width: 276px; font-family: var(--bs-font-sans-serif); font-style: normal; font-weight: 400; line-height: 1.5; text-align: left; text-align: start; text-decoration: none; text-shadow: none; text-transform: none; letter-spacing: normal; word-break: normal; word-spacing: normal; white-space: normal; line-break: auto; font-size: 0.75rem; word-wrap: break-word; background-color: #FFFFFF; background-clip: padding-box; border: 1px solid rgba(0, 0, 0, 0.2); border-radius: 0.3rem;}.popover .popover-arrow{position: absolute; display: block; width: 1rem; height: 0.5rem;}.popover .popover-arrow::before, .popover .popover-arrow::after{position: absolute; display: block; content: ""; border-color: transparent; border-style: solid;}.bs-popover-top > .popover-arrow, .bs-popover-auto[data-popper-placement^="top"] > .popover-arrow{bottom: calc(-0.5rem - 1px);}.bs-popover-top > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::before{bottom: 0; border-width: 0.5rem 0.5rem 0; border-top-color: rgba(0, 0, 0, 0.25);}.bs-popover-top > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="top"] > .popover-arrow::after{bottom: 1px; border-width: 0.5rem 0.5rem 0; border-top-color: #FFFFFF;}.bs-popover-end > .popover-arrow, .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow{left: calc(-0.5rem - 1px); width: 0.5rem; height: 1rem;}.bs-popover-end > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::before{left: 0; border-width: 0.5rem 0.5rem 0.5rem 0; border-right-color: rgba(0, 0, 0, 0.25);}.bs-popover-end > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="right"] > .popover-arrow::after{left: 1px; border-width: 0.5rem 0.5rem 0.5rem 0; border-right-color: #FFFFFF;}.bs-popover-bottom > .popover-arrow, .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow{top: calc(-0.5rem - 1px);}.bs-popover-bottom > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::before{top: 0; border-width: 0 0.5rem 0.5rem 0.5rem; border-bottom-color: rgba(0, 0, 0, 0.25);}.bs-popover-bottom > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="bottom"] > .popover-arrow::after{top: 1px; border-width: 0 0.5rem 0.5rem 0.5rem; border-bottom-color: #FFFFFF;}.bs-popover-bottom .popover-header::before, .bs-popover-auto[data-popper-placement^="bottom"] .popover-header::before{position: absolute; top: 0; left: 50%; display: block; width: 1rem; margin-left: -0.5rem; content: ""; border-bottom: 1px solid #f0f0f0;}.bs-popover-start > .popover-arrow, .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow{right: calc(-0.5rem - 1px); width: 0.5rem; height: 1rem;}.bs-popover-start > .popover-arrow::before, .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::before{right: 0; border-width: 0.5rem 0 0.5rem 0.5rem; border-left-color: rgba(0, 0, 0, 0.25);}.bs-popover-start > .popover-arrow::after, .bs-popover-auto[data-popper-placement^="left"] > .popover-arrow::after{right: 1px; border-width: 0.5rem 0 0.5rem 0.5rem; border-left-color: #FFFFFF;}.popover-header{padding: 0.5rem 1rem; margin-bottom: 0; font-size: 0.875rem; background-color: #f0f0f0; border-bottom: 1px solid rgba(0, 0, 0, 0.2); border-top-left-radius: calc(0.3rem - 1px); border-top-right-radius: calc(0.3rem - 1px);}.popover-header:empty{display: none;}.popover-body{padding: 1rem 1rem; color: #495057;}.carousel{position: relative;}.carousel.pointer-event{touch-action: pan-y;}.carousel-inner{position: relative; width: 100%; overflow: hidden;}.carousel-inner::after{display: block; clear: both; content: "";}.carousel-item{position: relative; display: none; float: left; width: 100%; margin-right: -100%; backface-visibility: hidden; transition: transform 0.6s ease-in-out;}@media (prefers-reduced-motion: reduce){.carousel-item{transition: none;}}.carousel-item.active, .carousel-item-next, .carousel-item-prev{display: block;}.carousel-item-next:not(.carousel-item-start), .active.carousel-item-end{transform: translateX(100%);}.carousel-item-prev:not(.carousel-item-end), .active.carousel-item-start{transform: translateX(-100%);}.carousel-fade .carousel-item{opacity: 0; transition-property: opacity; transform: none;}.carousel-fade .carousel-item.active, .carousel-fade .carousel-item-next.carousel-item-start, .carousel-fade .carousel-item-prev.carousel-item-end{z-index: 1; opacity: 1;}.carousel-fade .active.carousel-item-start, .carousel-fade .active.carousel-item-end{z-index: 0; opacity: 0; transition: opacity 0s 0.6s;}@media (prefers-reduced-motion: reduce){.carousel-fade .active.carousel-item-start, .carousel-fade .active.carousel-item-end{transition: none;}}.carousel-control-prev, .carousel-control-next{position: absolute; top: 0; bottom: 0; z-index: 1; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; justify-content: center; width: 15%; padding: 0; color: #FFFFFF; text-align: center; background: none; border: 0; opacity: 0.5; transition: opacity 0.15s ease;}@media (prefers-reduced-motion: reduce){.carousel-control-prev, .carousel-control-next{transition: none;}}.carousel-control-prev:hover, .carousel-control-prev:focus, .carousel-control-next:hover, .carousel-control-next:focus{color: #FFFFFF; text-decoration: none; outline: 0; opacity: 0.9;}.carousel-control-prev{left: 0;}.carousel-control-next{right: 0;}.carousel-control-prev-icon, .carousel-control-next-icon{display: inline-block; width: 2rem; height: 2rem; background-repeat: no-repeat; background-position: 50%; background-size: 100% 100%;}.carousel-control-prev-icon{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23FFFFFF'%3e%3cpath d='M11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3e");}.carousel-control-next-icon{background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23FFFFFF'%3e%3cpath d='M4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");}.carousel-indicators{position: absolute; right: 0; bottom: 0; left: 0; z-index: 2; display: -webkit-box; display: -webkit-flex; display: flex; justify-content: center; padding: 0; margin-right: 15%; margin-bottom: 1rem; margin-left: 15%; list-style: none;}.carousel-indicators [data-bs-target]{box-sizing: content-box; -webkit-box-flex: 0; -webkit-flex: 0 1 auto; flex: 0 1 auto; width: 30px; height: 3px; padding: 0; margin-right: 3px; margin-left: 3px; text-indent: -999px; cursor: pointer; background-color: #FFFFFF; background-clip: padding-box; border: 0; border-top: 10px solid transparent; border-bottom: 10px solid transparent; opacity: 0.5; transition: opacity 0.6s ease;}@media (prefers-reduced-motion: reduce){.carousel-indicators [data-bs-target]{transition: none;}}.carousel-indicators .active{opacity: 1;}.carousel-caption{position: absolute; right: 15%; bottom: 1.25rem; left: 15%; padding-top: 1.25rem; padding-bottom: 1.25rem; color: #FFFFFF; text-align: center;}.carousel-dark .carousel-control-prev-icon, .carousel-dark .carousel-control-next-icon{filter: invert(1) grayscale(100);}.carousel-dark .carousel-indicators [data-bs-target]{background-color: #000000;}.carousel-dark .carousel-caption{color: #000000;}@keyframes spinner-border{to{transform: rotate(360deg) ;}}.spinner-border{display: inline-block; width: 2rem; height: 2rem; vertical-align: -0.125em; border: 0.25em solid currentColor; border-right-color: transparent; border-radius: 50%; animation: 0.75s linear infinite spinner-border;}.spinner-border-sm{width: 1rem; height: 1rem; border-width: 0.2em;}@keyframes spinner-grow{0%{transform: scale(0);}50%{opacity: 1; transform: none;}}.spinner-grow{display: inline-block; width: 2rem; height: 2rem; vertical-align: -0.125em; background-color: currentColor; border-radius: 50%; opacity: 0; animation: 0.75s linear infinite spinner-grow;}.spinner-grow-sm{width: 1rem; height: 1rem;}@media (prefers-reduced-motion: reduce){.spinner-border, .spinner-grow{animation-duration: 1.5s;}}.offcanvas{position: fixed; bottom: 0; z-index: 1045; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; max-width: 100%; visibility: hidden; background-color: #FFFFFF; background-clip: padding-box; outline: 0; transition: transform 0.3s ease-in-out;}@media (prefers-reduced-motion: reduce){.offcanvas{transition: none;}}.offcanvas-backdrop{position: fixed; top: 0; left: 0; z-index: 1040; width: 100vw; height: 100vh; background-color: #000000;}.offcanvas-backdrop.fade{opacity: 0;}.offcanvas-backdrop.show{opacity: 0.3;}.offcanvas-header{display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; justify-content: space-between; padding: 1rem 1rem;}.offcanvas-header .btn-close{padding: 0.5rem 0.5rem; margin-top: -0.5rem; margin-right: -0.5rem; margin-bottom: -0.5rem;}.offcanvas-title{margin-bottom: 0; line-height: 1.5;}.offcanvas-body{flex-grow: 1; padding: 1rem 1rem; overflow-y: auto;}.offcanvas-start{top: 0; left: 0; width: 400px; border-right: 0 solid rgba(0, 0, 0, 0.2); transform: translateX(-100%);}.offcanvas-end{top: 0; right: 0; width: 400px; border-left: 0 solid rgba(0, 0, 0, 0.2); transform: translateX(100%);}.offcanvas-top{top: 0; right: 0; left: 0; height: 30vh; max-height: 100%; border-bottom: 0 solid rgba(0, 0, 0, 0.2); transform: translateY(-100%);}.offcanvas-bottom{right: 0; left: 0; height: 30vh; max-height: 100%; border-top: 0 solid rgba(0, 0, 0, 0.2); transform: translateY(100%);}.offcanvas.show{transform: none;}.placeholder{display: inline-block; min-height: 1em; vertical-align: middle; cursor: wait; background-color: currentColor; opacity: 0.5;}.placeholder.btn::before{display: inline-block; content: "";}.placeholder-xs{min-height: .6em;}.placeholder-sm{min-height: .8em;}.placeholder-lg{min-height: 1.2em;}.placeholder-glow .placeholder{animation: placeholder-glow 2s ease-in-out infinite;}@keyframes placeholder-glow{50%{opacity: 0.2;}}.placeholder-wave{mask-image: linear-gradient(130deg, #000000 55%, rgba(0, 0, 0, 0.8) 75%, #000000 95%); mask-size: 200% 100%; animation: placeholder-wave 2s linear infinite;}@keyframes placeholder-wave{100%{mask-position: -200% 0%;}}.clearfix::after{display: block; clear: both; content: "";}.link-primary{color: #714B67;}.link-primary:hover, .link-primary:focus{color: #5a3c52;}.link-secondary{color: #8595A2;}.link-secondary:hover, .link-secondary:focus{color: #6a7782;}.link-success{color: #28a745;}.link-success:hover, .link-success:focus{color: #208637;}.link-info{color: #17a2b8;}.link-info:hover, .link-info:focus{color: #128293;}.link-warning{color: #ffc107;}.link-warning:hover, .link-warning:focus{color: #ffcd39;}.link-danger{color: #dc3545;}.link-danger:hover, .link-danger:focus{color: #b02a37;}.link-light{color: #eeeeee;}.link-light:hover, .link-light:focus{color: #f1f1f1;}.link-dark{color: #212529;}.link-dark:hover, .link-dark:focus{color: #1a1e21;}.ratio{position: relative; width: 100%;}.ratio::before{display: block; padding-top: var(--bs-aspect-ratio); content: "";}.ratio > *{position: absolute; top: 0; left: 0; width: 100%; height: 100%;}.ratio-1x1{--bs-aspect-ratio: 100%;}.ratio-4x3{--bs-aspect-ratio: calc(3 / 4 * 100%);}.ratio-16x9{--bs-aspect-ratio: calc(9 / 16 * 100%);}.ratio-21x9{--bs-aspect-ratio: calc(9 / 21 * 100%);}.fixed-top{position: fixed; top: 0; right: 0; left: 0; z-index: 1030;}.fixed-bottom{position: fixed; right: 0; bottom: 0; left: 0; z-index: 1030;}.sticky-top{position: sticky; top: 0; z-index: 1020;}@media (min-width: 576px){.sticky-sm-top{position: sticky; top: 0; z-index: 1020;}}@media (min-width: 768px){.sticky-md-top{position: sticky; top: 0; z-index: 1020;}}@media (min-width: 992px){.sticky-lg-top{position: sticky; top: 0; z-index: 1020;}}@media (min-width: 1200px){.sticky-xl-top{position: sticky; top: 0; z-index: 1020;}}@media (min-width: 1400px){.sticky-xxl-top{position: sticky; top: 0; z-index: 1020;}}.hstack{display: -webkit-box; display: -webkit-flex; display: flex; flex-direction: row; align-items: center; align-self: stretch;}.vstack{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; align-self: stretch;}.visually-hidden, .visually-hidden-focusable:not(:focus):not(:focus-within){position: absolute !important; width: 1px !important; height: 1px !important; padding: 0 !important; margin: -1px !important; overflow: hidden !important; clip: rect(0, 0, 0, 0) !important; white-space: nowrap !important; border: 0 !important;}.stretched-link::after{position: absolute; top: 0; right: 0; bottom: 0; left: 0; z-index: 1; content: "";}.text-truncate{overflow: hidden; text-overflow: ellipsis; white-space: nowrap;}.vr{display: inline-block; align-self: stretch; width: 1px; min-height: 1em; background-color: currentColor; opacity: 0.25;}

/* /web/static/src/scss/helpers_backport.scss */
 .text-bg-primary{color: #FFFFFF !important; background-color: RGBA(113, 75, 103, var(--bg-opacity, 1)) !important;}.text-bg-secondary{color: #FFFFFF !important; background-color: RGBA(133, 149, 162, var(--bg-opacity, 1)) !important;}.text-bg-success{color: #FFFFFF !important; background-color: RGBA(40, 167, 69, var(--bg-opacity, 1)) !important;}.text-bg-info{color: #FFFFFF !important; background-color: RGBA(23, 162, 184, var(--bg-opacity, 1)) !important;}.text-bg-warning{color: #000000 !important; background-color: RGBA(255, 193, 7, var(--bg-opacity, 1)) !important;}.text-bg-danger{color: #FFFFFF !important; background-color: RGBA(220, 53, 69, var(--bg-opacity, 1)) !important;}.text-bg-light{color: #000000 !important; background-color: RGBA(238, 238, 238, var(--bg-opacity, 1)) !important;}.text-bg-dark{color: #FFFFFF !important; background-color: RGBA(33, 37, 41, var(--bg-opacity, 1)) !important;}

/* /web/static/src/scss/utilities_custom.scss */
 .opacity-0-hover:hover, .opacity-trigger-hover:hover .opacity-0-hover{opacity: 0 !important;}.opacity-25-hover:hover, .opacity-trigger-hover:hover .opacity-25-hover{opacity: 0.25 !important;}.opacity-50-hover:hover, .opacity-trigger-hover:hover .opacity-50-hover{opacity: 0.5 !important;}.opacity-75-hover:hover, .opacity-trigger-hover:hover .opacity-75-hover{opacity: 0.75 !important;}.opacity-100-hover:hover, .opacity-trigger-hover:hover .opacity-100-hover{opacity: 1 !important;}.opacity-disabled-hover:hover, .opacity-trigger-hover:hover .opacity-disabled-hover{opacity: 0.5 !important;}.opacity-muted-hover:hover, .opacity-trigger-hover:hover .opacity-muted-hover{opacity: 0.76 !important;}.d-empty-none:empty{display: none !important;}.smaller{font-size: 0.75rem;}

/* /web/static/lib/bootstrap/scss/utilities/_api.scss */
 .align-baseline{vertical-align: baseline !important;}.align-top{vertical-align: top !important;}.align-middle{vertical-align: middle !important;}.align-bottom{vertical-align: bottom !important;}.align-text-bottom{vertical-align: text-bottom !important;}.align-text-top{vertical-align: text-top !important;}.float-start{float: left !important;}.float-end{float: right !important;}.float-none{float: none !important;}.opacity-0{opacity: 0 !important;}.opacity-25{opacity: 0.25 !important;}.opacity-50{opacity: 0.5 !important;}.opacity-75{opacity: 0.75 !important;}.opacity-100{opacity: 1 !important;}.opacity-disabled{opacity: 0.5 !important;}.opacity-muted{opacity: 0.76 !important;}.overflow-auto{overflow: auto !important;}.overflow-hidden{overflow: hidden !important;}.overflow-visible{overflow: visible !important;}.overflow-scroll{overflow: scroll !important;}.d-inline{display: inline !important;}.d-inline-block{display: inline-block !important;}.d-block{display: block !important;}.d-grid{display: grid !important;}.d-table{display: table !important;}.d-table-row{display: table-row !important;}.d-table-cell{display: table-cell !important;}.d-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-none{display: none !important;}.d-contents{display: contents !important;}.shadow{box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;}.shadow-sm{box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;}.shadow-lg{box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;}.shadow-none{box-shadow: none !important;}.position-static{position: static !important;}.position-relative{position: relative !important;}.position-absolute{position: absolute !important;}.position-fixed{position: fixed !important;}.position-sticky{position: sticky !important;}.top-0{top: 0 !important;}.top-50{top: 50% !important;}.top-100{top: 100% !important;}.bottom-0{bottom: 0 !important;}.bottom-50{bottom: 50% !important;}.bottom-100{bottom: 100% !important;}.start-0{left: 0 !important;}.start-50{left: 50% !important;}.start-100{left: 100% !important;}.end-0{right: 0 !important;}.end-50{right: 50% !important;}.end-100{right: 100% !important;}.translate-middle{transform: translate(-50%, -50%) !important;}.translate-middle-x{transform: translateX(-50%) !important;}.translate-middle-y{transform: translateY(-50%) !important;}.border{border: 1px solid #dee2e6 !important;}.border-0{border: 0 !important;}.border-top{border-top: 1px solid #dee2e6 !important;}.border-top-0{border-top: 0 !important;}.border-end{border-right: 1px solid #dee2e6 !important;}.border-end-0{border-right: 0 !important;}.border-bottom{border-bottom: 1px solid #dee2e6 !important;}.border-bottom-0{border-bottom: 0 !important;}.border-start{border-left: 1px solid #dee2e6 !important;}.border-start-0{border-left: 0 !important;}.border-primary{border-color: #714B67 !important;}.border-secondary{border-color: #8595A2 !important;}.border-success{border-color: #28a745 !important;}.border-info{border-color: #17a2b8 !important;}.border-warning{border-color: #ffc107 !important;}.border-danger{border-color: #dc3545 !important;}.border-light{border-color: #eeeeee !important;}.border-dark{border-color: #212529 !important;}.border-white{border-color: #FFFFFF !important;}.border-transparent{border-color: transparent !important;}.border-1{border-width: 1px !important;}.border-2{border-width: 2px !important;}.border-3{border-width: 3px !important;}.border-4{border-width: 4px !important;}.border-5{border-width: 5px !important;}.w-0{width: 0 !important;}.w-25{width: 25% !important;}.w-50{width: 50% !important;}.w-75{width: 75% !important;}.w-100{width: 100% !important;}.w-auto{width: auto !important;}.mw-0{max-width: 0 !important;}.mw-25{max-width: 25% !important;}.mw-50{max-width: 50% !important;}.mw-75{max-width: 75% !important;}.mw-100{max-width: 100% !important;}.mw-auto{max-width: auto !important;}.vw-100{width: 100vw !important;}.min-vw-100{min-width: 100vw !important;}.h-0{height: 0 !important;}.h-25{height: 25% !important;}.h-50{height: 50% !important;}.h-75{height: 75% !important;}.h-100{height: 100% !important;}.h-auto{height: auto !important;}.mh-0{max-height: 0 !important;}.mh-25{max-height: 25% !important;}.mh-50{max-height: 50% !important;}.mh-75{max-height: 75% !important;}.mh-100{max-height: 100% !important;}.mh-auto{max-height: auto !important;}.vh-100{height: 100vh !important;}.min-vh-100{min-height: 100vh !important;}.flex-fill{flex: 1 1 auto !important;}.flex-row{flex-direction: row !important;}.flex-column{flex-direction: column !important;}.flex-row-reverse{flex-direction: row-reverse !important;}.flex-column-reverse{flex-direction: column-reverse !important;}.flex-grow-0{flex-grow: 0 !important;}.flex-grow-1{flex-grow: 1 !important;}.flex-shrink-0{flex-shrink: 0 !important;}.flex-shrink-1{flex-shrink: 1 !important;}.flex-wrap{flex-wrap: wrap !important;}.flex-nowrap{flex-wrap: nowrap !important;}.flex-wrap-reverse{flex-wrap: wrap-reverse !important;}.gap-0{gap: 0 !important;}.gap-1{gap: 0.25rem !important;}.gap-2{gap: 0.5rem !important;}.gap-3{gap: 1rem !important;}.gap-4{gap: 1.5rem !important;}.gap-5{gap: 3rem !important;}.justify-content-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-center{justify-content: center !important;}.justify-content-between{justify-content: space-between !important;}.justify-content-around{justify-content: space-around !important;}.justify-content-evenly{justify-content: space-evenly !important;}.align-items-start{align-items: flex-start !important;}.align-items-end{align-items: flex-end !important;}.align-items-center{align-items: center !important;}.align-items-baseline{align-items: baseline !important;}.align-items-stretch{align-items: stretch !important;}.align-content-start{align-content: flex-start !important;}.align-content-end{align-content: flex-end !important;}.align-content-center{align-content: center !important;}.align-content-between{align-content: space-between !important;}.align-content-around{align-content: space-around !important;}.align-content-stretch{align-content: stretch !important;}.align-self-auto{align-self: auto !important;}.align-self-start{align-self: flex-start !important;}.align-self-end{align-self: flex-end !important;}.align-self-center{align-self: center !important;}.align-self-baseline{align-self: baseline !important;}.align-self-stretch{align-self: stretch !important;}.order-first{order: -1 !important;}.order-last{order: 13 !important;}.order-0{order: 0 !important;}.order-1{order: 1 !important;}.order-2{order: 2 !important;}.order-3{order: 3 !important;}.order-4{order: 4 !important;}.order-5{order: 5 !important;}.order-6{order: 6 !important;}.order-7{order: 7 !important;}.order-8{order: 8 !important;}.order-9{order: 9 !important;}.order-10{order: 10 !important;}.order-11{order: 11 !important;}.order-12{order: 12 !important;}.m-0{margin: 0 !important;}.m-1{margin: 0.25rem !important;}.m-2{margin: 0.5rem !important;}.m-3{margin: 1rem !important;}.m-4{margin: 1.5rem !important;}.m-5{margin: 3rem !important;}.m-auto{margin: auto !important;}.mx-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-1{margin-right: 0.25rem !important; margin-left: 0.25rem !important;}.mx-2{margin-right: 0.5rem !important; margin-left: 0.5rem !important;}.mx-3{margin-right: 1rem !important; margin-left: 1rem !important;}.mx-4{margin-right: 1.5rem !important; margin-left: 1.5rem !important;}.mx-5{margin-right: 3rem !important; margin-left: 3rem !important;}.mx-auto{margin-right: auto !important; margin-left: auto !important;}.my-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-1{margin-top: 0.25rem !important; margin-bottom: 0.25rem !important;}.my-2{margin-top: 0.5rem !important; margin-bottom: 0.5rem !important;}.my-3{margin-top: 1rem !important; margin-bottom: 1rem !important;}.my-4{margin-top: 1.5rem !important; margin-bottom: 1.5rem !important;}.my-5{margin-top: 3rem !important; margin-bottom: 3rem !important;}.my-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-0{margin-top: 0 !important;}.mt-1{margin-top: 0.25rem !important;}.mt-2{margin-top: 0.5rem !important;}.mt-3{margin-top: 1rem !important;}.mt-4{margin-top: 1.5rem !important;}.mt-5{margin-top: 3rem !important;}.mt-auto{margin-top: auto !important;}.me-0{margin-right: 0 !important;}.me-1{margin-right: 0.25rem !important;}.me-2{margin-right: 0.5rem !important;}.me-3{margin-right: 1rem !important;}.me-4{margin-right: 1.5rem !important;}.me-5{margin-right: 3rem !important;}.me-auto{margin-right: auto !important;}.mb-0{margin-bottom: 0 !important;}.mb-1{margin-bottom: 0.25rem !important;}.mb-2{margin-bottom: 0.5rem !important;}.mb-3{margin-bottom: 1rem !important;}.mb-4{margin-bottom: 1.5rem !important;}.mb-5{margin-bottom: 3rem !important;}.mb-auto{margin-bottom: auto !important;}.ms-0{margin-left: 0 !important;}.ms-1{margin-left: 0.25rem !important;}.ms-2{margin-left: 0.5rem !important;}.ms-3{margin-left: 1rem !important;}.ms-4{margin-left: 1.5rem !important;}.ms-5{margin-left: 3rem !important;}.ms-auto{margin-left: auto !important;}.p-0{padding: 0 !important;}.p-1{padding: 0.25rem !important;}.p-2{padding: 0.5rem !important;}.p-3{padding: 1rem !important;}.p-4{padding: 1.5rem !important;}.p-5{padding: 3rem !important;}.px-0{padding-right: 0 !important; padding-left: 0 !important;}.px-1{padding-right: 0.25rem !important; padding-left: 0.25rem !important;}.px-2{padding-right: 0.5rem !important; padding-left: 0.5rem !important;}.px-3{padding-right: 1rem !important; padding-left: 1rem !important;}.px-4{padding-right: 1.5rem !important; padding-left: 1.5rem !important;}.px-5{padding-right: 3rem !important; padding-left: 3rem !important;}.py-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-1{padding-top: 0.25rem !important; padding-bottom: 0.25rem !important;}.py-2{padding-top: 0.5rem !important; padding-bottom: 0.5rem !important;}.py-3{padding-top: 1rem !important; padding-bottom: 1rem !important;}.py-4{padding-top: 1.5rem !important; padding-bottom: 1.5rem !important;}.py-5{padding-top: 3rem !important; padding-bottom: 3rem !important;}.pt-0{padding-top: 0 !important;}.pt-1{padding-top: 0.25rem !important;}.pt-2{padding-top: 0.5rem !important;}.pt-3{padding-top: 1rem !important;}.pt-4{padding-top: 1.5rem !important;}.pt-5{padding-top: 3rem !important;}.pe-0{padding-right: 0 !important;}.pe-1{padding-right: 0.25rem !important;}.pe-2{padding-right: 0.5rem !important;}.pe-3{padding-right: 1rem !important;}.pe-4{padding-right: 1.5rem !important;}.pe-5{padding-right: 3rem !important;}.pb-0{padding-bottom: 0 !important;}.pb-1{padding-bottom: 0.25rem !important;}.pb-2{padding-bottom: 0.5rem !important;}.pb-3{padding-bottom: 1rem !important;}.pb-4{padding-bottom: 1.5rem !important;}.pb-5{padding-bottom: 3rem !important;}.ps-0{padding-left: 0 !important;}.ps-1{padding-left: 0.25rem !important;}.ps-2{padding-left: 0.5rem !important;}.ps-3{padding-left: 1rem !important;}.ps-4{padding-left: 1.5rem !important;}.ps-5{padding-left: 3rem !important;}.font-monospace{font-family: var(--bs-font-monospace) !important;}.font-sans-serif{font-family: var(--bs-font-sans-serif) !important;}.fs-1{font-size: calc(1.34375rem + 1.125vw) !important;}.fs-2{font-size: calc(1.3rem + 0.6vw) !important;}.fs-3{font-size: calc(1.278125rem + 0.3375vw) !important;}.fs-4{font-size: calc(1.25625rem + 0.075vw) !important;}.fs-5{font-size: 1.09375rem !important;}.fs-6{font-size: 0.875rem !important;}.fst-italic{font-style: italic !important;}.fst-normal{font-style: normal !important;}.fw-light{font-weight: 300 !important;}.fw-lighter{font-weight: lighter !important;}.fw-normal{font-weight: 400 !important;}.fw-bold{font-weight: 700 !important;}.fw-bolder{font-weight: bolder !important;}.lh-1{line-height: 1 !important;}.lh-sm{line-height: 1.25 !important;}.lh-base{line-height: 1.5 !important;}.lh-lg{line-height: 2 !important;}.text-start{text-align: left !important;}.text-end{text-align: right !important;}.text-center{text-align: center !important;}.text-decoration-none{text-decoration: none !important;}.text-decoration-underline{text-decoration: underline !important;}.text-decoration-line-through{text-decoration: line-through !important;}.text-lowercase{text-transform: lowercase !important;}.text-uppercase{text-transform: uppercase !important;}.text-capitalize{text-transform: capitalize !important;}.text-wrap{white-space: normal !important;}.text-nowrap{white-space: nowrap !important;}.text-prewrap{white-space: pre-wrap !important;}.text-break{word-wrap: break-word !important; word-break: break-word !important;}.text-primary{--bs-text-opacity: 1; color: rgba(var(--bs-primary-rgb), var(--bs-text-opacity)) !important;}.text-secondary{--bs-text-opacity: 1; color: rgba(var(--bs-secondary-rgb), var(--bs-text-opacity)) !important;}.text-success{--bs-text-opacity: 1; color: rgba(var(--bs-success-rgb), var(--bs-text-opacity)) !important;}.text-info{--bs-text-opacity: 1; color: rgba(var(--bs-info-rgb), var(--bs-text-opacity)) !important;}.text-warning{--bs-text-opacity: 1; color: rgba(var(--bs-warning-rgb), var(--bs-text-opacity)) !important;}.text-danger{--bs-text-opacity: 1; color: rgba(var(--bs-danger-rgb), var(--bs-text-opacity)) !important;}.text-light{--bs-text-opacity: 1; color: rgba(var(--bs-light-rgb), var(--bs-text-opacity)) !important;}.text-dark{--bs-text-opacity: 1; color: rgba(var(--bs-dark-rgb), var(--bs-text-opacity)) !important;}.text-black{--bs-text-opacity: 1; color: rgba(var(--bs-black-rgb), var(--bs-text-opacity)) !important;}.text-white{--bs-text-opacity: 1; color: rgba(var(--bs-white-rgb), var(--bs-text-opacity)) !important;}.text-body{--bs-text-opacity: 1; color: rgba(var(--bs-body-color-rgb), var(--bs-text-opacity)) !important;}.text-muted{--bs-text-opacity: 1; color: #6C757D !important;}.text-black-50{--bs-text-opacity: 1; color: rgba(0, 0, 0, 0.5) !important;}.text-white-50{--bs-text-opacity: 1; color: rgba(255, 255, 255, 0.5) !important;}.text-reset{--bs-text-opacity: 1; color: inherit !important;}.text-opacity-25{--bs-text-opacity: 0.25;}.text-opacity-50{--bs-text-opacity: 0.5;}.text-opacity-75{--bs-text-opacity: 0.75;}.text-opacity-100{--bs-text-opacity: 1;}.bg-primary{--bs-bg-opacity: 1; background-color: rgba(var(--bs-primary-rgb), var(--bs-bg-opacity)) !important;}.bg-secondary{--bs-bg-opacity: 1; background-color: rgba(var(--bs-secondary-rgb), var(--bs-bg-opacity)) !important;}.bg-success{--bs-bg-opacity: 1; background-color: rgba(var(--bs-success-rgb), var(--bs-bg-opacity)) !important;}.bg-info{--bs-bg-opacity: 1; background-color: rgba(var(--bs-info-rgb), var(--bs-bg-opacity)) !important;}.bg-warning{--bs-bg-opacity: 1; background-color: rgba(var(--bs-warning-rgb), var(--bs-bg-opacity)) !important;}.bg-danger{--bs-bg-opacity: 1; background-color: rgba(var(--bs-danger-rgb), var(--bs-bg-opacity)) !important;}.bg-light{--bs-bg-opacity: 1; background-color: rgba(var(--bs-light-rgb), var(--bs-bg-opacity)) !important;}.bg-dark{--bs-bg-opacity: 1; background-color: rgba(var(--bs-dark-rgb), var(--bs-bg-opacity)) !important;}.bg-black{--bs-bg-opacity: 1; background-color: rgba(var(--bs-black-rgb), var(--bs-bg-opacity)) !important;}.bg-white{--bs-bg-opacity: 1; background-color: rgba(var(--bs-white-rgb), var(--bs-bg-opacity)) !important;}.bg-body{--bs-bg-opacity: 1; background-color: rgba(var(--bs-body-bg-rgb), var(--bs-bg-opacity)) !important;}.bg-transparent{--bs-bg-opacity: 1; background-color: transparent !important;}.bg-opacity-0{--bs-bg-opacity: 0;}.bg-opacity-25{--bs-bg-opacity: 0.25;}.bg-opacity-50{--bs-bg-opacity: 0.5;}.bg-opacity-75{--bs-bg-opacity: 0.75;}.bg-opacity-100{--bs-bg-opacity: 1;}.bg-opacity-disabled{--bs-bg-opacity: 0.5;}.bg-opacity-muted{--bs-bg-opacity: 0.76;}.bg-gradient{background-image: var(--bs-gradient) !important;}.user-select-all{user-select: all !important;}.user-select-auto{user-select: auto !important;}.user-select-none{user-select: none !important;}.pe-none{pointer-events: none !important;}.pe-auto{pointer-events: auto !important;}.rounded{border-radius: 0.25rem !important;}.rounded-0{border-radius: 0 !important;}.rounded-1{border-radius: 0.2rem !important;}.rounded-2{border-radius: 0.25rem !important;}.rounded-3{border-radius: 0.3rem !important;}.rounded-circle{border-radius: 50% !important;}.rounded-pill{border-radius: 50rem !important;}.rounded-top{border-top-left-radius: 0.25rem !important; border-top-right-radius: 0.25rem !important;}.rounded-top-0{border-top-left-radius: 0 !important; border-top-right-radius: 0 !important;}.rounded-top-1{border-top-left-radius: 0.2rem !important; border-top-right-radius: 0.2rem !important;}.rounded-top-2{border-top-left-radius: 0.25rem !important; border-top-right-radius: 0.25rem !important;}.rounded-top-3{border-top-left-radius: 0.3rem !important; border-top-right-radius: 0.3rem !important;}.rounded-top-circle{border-top-left-radius: 50% !important; border-top-right-radius: 50% !important;}.rounded-top-pill{border-top-left-radius: 50rem !important; border-top-right-radius: 50rem !important;}.rounded-end{border-top-right-radius: 0.25rem !important; border-bottom-right-radius: 0.25rem !important;}.rounded-end-0{border-top-right-radius: 0 !important; border-bottom-right-radius: 0 !important;}.rounded-end-1{border-top-right-radius: 0.2rem !important; border-bottom-right-radius: 0.2rem !important;}.rounded-end-2{border-top-right-radius: 0.25rem !important; border-bottom-right-radius: 0.25rem !important;}.rounded-end-3{border-top-right-radius: 0.3rem !important; border-bottom-right-radius: 0.3rem !important;}.rounded-end-circle{border-top-right-radius: 50% !important; border-bottom-right-radius: 50% !important;}.rounded-end-pill{border-top-right-radius: 50rem !important; border-bottom-right-radius: 50rem !important;}.rounded-bottom{border-bottom-right-radius: 0.25rem !important; border-bottom-left-radius: 0.25rem !important;}.rounded-bottom-0{border-bottom-right-radius: 0 !important; border-bottom-left-radius: 0 !important;}.rounded-bottom-1{border-bottom-right-radius: 0.2rem !important; border-bottom-left-radius: 0.2rem !important;}.rounded-bottom-2{border-bottom-right-radius: 0.25rem !important; border-bottom-left-radius: 0.25rem !important;}.rounded-bottom-3{border-bottom-right-radius: 0.3rem !important; border-bottom-left-radius: 0.3rem !important;}.rounded-bottom-circle{border-bottom-right-radius: 50% !important; border-bottom-left-radius: 50% !important;}.rounded-bottom-pill{border-bottom-right-radius: 50rem !important; border-bottom-left-radius: 50rem !important;}.rounded-start{border-bottom-left-radius: 0.25rem !important; border-top-left-radius: 0.25rem !important;}.rounded-start-0{border-bottom-left-radius: 0 !important; border-top-left-radius: 0 !important;}.rounded-start-1{border-bottom-left-radius: 0.2rem !important; border-top-left-radius: 0.2rem !important;}.rounded-start-2{border-bottom-left-radius: 0.25rem !important; border-top-left-radius: 0.25rem !important;}.rounded-start-3{border-bottom-left-radius: 0.3rem !important; border-top-left-radius: 0.3rem !important;}.rounded-start-circle{border-bottom-left-radius: 50% !important; border-top-left-radius: 50% !important;}.rounded-start-pill{border-bottom-left-radius: 50rem !important; border-top-left-radius: 50rem !important;}.visible{visibility: visible !important;}.invisible{visibility: hidden !important;}.cursor-default{cursor: default !important;}.cursor-pointer{cursor: pointer !important;}.flex-basis-0{flex-basis: 0 !important;}.flex-basis-25{flex-basis: 25% !important;}.flex-basis-50{flex-basis: 50% !important;}.flex-basis-75{flex-basis: 75% !important;}.flex-basis-100{flex-basis: 100% !important;}.flex-basis-auto{flex-basis: auto !important;}.z-index-0{z-index: 0 !important;}.z-index-1{z-index: 1 !important;}.overflow-x-auto{overflow-x: auto !important;}.overflow-x-hidden{overflow-x: hidden !important;}.overflow-x-visible{overflow-x: visible !important;}.overflow-x-scroll{overflow-x: scroll !important;}.overflow-y-auto{overflow-y: auto !important;}.overflow-y-hidden{overflow-y: hidden !important;}.overflow-y-visible{overflow-y: visible !important;}.overflow-y-scroll{overflow-y: scroll !important;}.transition-none{transition: none !important;}.transition-base{transition: all 0.2s ease-in-out !important;}.transition-fade{transition: opacity 0.15s linear !important;}.min-w-0{min-width: 0 !important;}@media (min-width: 576px){.float-sm-start{float: left !important;}.float-sm-end{float: right !important;}.float-sm-none{float: none !important;}.d-sm-inline{display: inline !important;}.d-sm-inline-block{display: inline-block !important;}.d-sm-block{display: block !important;}.d-sm-grid{display: grid !important;}.d-sm-table{display: table !important;}.d-sm-table-row{display: table-row !important;}.d-sm-table-cell{display: table-cell !important;}.d-sm-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-sm-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-sm-none{display: none !important;}.d-sm-contents{display: contents !important;}.position-sm-static{position: static !important;}.position-sm-relative{position: relative !important;}.position-sm-absolute{position: absolute !important;}.position-sm-fixed{position: fixed !important;}.position-sm-sticky{position: sticky !important;}.w-sm-0{width: 0 !important;}.w-sm-25{width: 25% !important;}.w-sm-50{width: 50% !important;}.w-sm-75{width: 75% !important;}.w-sm-100{width: 100% !important;}.w-sm-auto{width: auto !important;}.mw-sm-0{max-width: 0 !important;}.mw-sm-25{max-width: 25% !important;}.mw-sm-50{max-width: 50% !important;}.mw-sm-75{max-width: 75% !important;}.mw-sm-100{max-width: 100% !important;}.mw-sm-auto{max-width: auto !important;}.h-sm-0{height: 0 !important;}.h-sm-25{height: 25% !important;}.h-sm-50{height: 50% !important;}.h-sm-75{height: 75% !important;}.h-sm-100{height: 100% !important;}.h-sm-auto{height: auto !important;}.mh-sm-0{max-height: 0 !important;}.mh-sm-25{max-height: 25% !important;}.mh-sm-50{max-height: 50% !important;}.mh-sm-75{max-height: 75% !important;}.mh-sm-100{max-height: 100% !important;}.mh-sm-auto{max-height: auto !important;}.flex-sm-fill{flex: 1 1 auto !important;}.flex-sm-row{flex-direction: row !important;}.flex-sm-column{flex-direction: column !important;}.flex-sm-row-reverse{flex-direction: row-reverse !important;}.flex-sm-column-reverse{flex-direction: column-reverse !important;}.flex-sm-grow-0{flex-grow: 0 !important;}.flex-sm-grow-1{flex-grow: 1 !important;}.flex-sm-shrink-0{flex-shrink: 0 !important;}.flex-sm-shrink-1{flex-shrink: 1 !important;}.flex-sm-wrap{flex-wrap: wrap !important;}.flex-sm-nowrap{flex-wrap: nowrap !important;}.flex-sm-wrap-reverse{flex-wrap: wrap-reverse !important;}.gap-sm-0{gap: 0 !important;}.gap-sm-1{gap: 0.25rem !important;}.gap-sm-2{gap: 0.5rem !important;}.gap-sm-3{gap: 1rem !important;}.gap-sm-4{gap: 1.5rem !important;}.gap-sm-5{gap: 3rem !important;}.justify-content-sm-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-sm-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-sm-center{justify-content: center !important;}.justify-content-sm-between{justify-content: space-between !important;}.justify-content-sm-around{justify-content: space-around !important;}.justify-content-sm-evenly{justify-content: space-evenly !important;}.align-items-sm-start{align-items: flex-start !important;}.align-items-sm-end{align-items: flex-end !important;}.align-items-sm-center{align-items: center !important;}.align-items-sm-baseline{align-items: baseline !important;}.align-items-sm-stretch{align-items: stretch !important;}.align-content-sm-start{align-content: flex-start !important;}.align-content-sm-end{align-content: flex-end !important;}.align-content-sm-center{align-content: center !important;}.align-content-sm-between{align-content: space-between !important;}.align-content-sm-around{align-content: space-around !important;}.align-content-sm-stretch{align-content: stretch !important;}.align-self-sm-auto{align-self: auto !important;}.align-self-sm-start{align-self: flex-start !important;}.align-self-sm-end{align-self: flex-end !important;}.align-self-sm-center{align-self: center !important;}.align-self-sm-baseline{align-self: baseline !important;}.align-self-sm-stretch{align-self: stretch !important;}.order-sm-first{order: -1 !important;}.order-sm-last{order: 13 !important;}.order-sm-0{order: 0 !important;}.order-sm-1{order: 1 !important;}.order-sm-2{order: 2 !important;}.order-sm-3{order: 3 !important;}.order-sm-4{order: 4 !important;}.order-sm-5{order: 5 !important;}.order-sm-6{order: 6 !important;}.order-sm-7{order: 7 !important;}.order-sm-8{order: 8 !important;}.order-sm-9{order: 9 !important;}.order-sm-10{order: 10 !important;}.order-sm-11{order: 11 !important;}.order-sm-12{order: 12 !important;}.m-sm-0{margin: 0 !important;}.m-sm-1{margin: 0.25rem !important;}.m-sm-2{margin: 0.5rem !important;}.m-sm-3{margin: 1rem !important;}.m-sm-4{margin: 1.5rem !important;}.m-sm-5{margin: 3rem !important;}.m-sm-auto{margin: auto !important;}.mx-sm-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-sm-1{margin-right: 0.25rem !important; margin-left: 0.25rem !important;}.mx-sm-2{margin-right: 0.5rem !important; margin-left: 0.5rem !important;}.mx-sm-3{margin-right: 1rem !important; margin-left: 1rem !important;}.mx-sm-4{margin-right: 1.5rem !important; margin-left: 1.5rem !important;}.mx-sm-5{margin-right: 3rem !important; margin-left: 3rem !important;}.mx-sm-auto{margin-right: auto !important; margin-left: auto !important;}.my-sm-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-sm-1{margin-top: 0.25rem !important; margin-bottom: 0.25rem !important;}.my-sm-2{margin-top: 0.5rem !important; margin-bottom: 0.5rem !important;}.my-sm-3{margin-top: 1rem !important; margin-bottom: 1rem !important;}.my-sm-4{margin-top: 1.5rem !important; margin-bottom: 1.5rem !important;}.my-sm-5{margin-top: 3rem !important; margin-bottom: 3rem !important;}.my-sm-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-sm-0{margin-top: 0 !important;}.mt-sm-1{margin-top: 0.25rem !important;}.mt-sm-2{margin-top: 0.5rem !important;}.mt-sm-3{margin-top: 1rem !important;}.mt-sm-4{margin-top: 1.5rem !important;}.mt-sm-5{margin-top: 3rem !important;}.mt-sm-auto{margin-top: auto !important;}.me-sm-0{margin-right: 0 !important;}.me-sm-1{margin-right: 0.25rem !important;}.me-sm-2{margin-right: 0.5rem !important;}.me-sm-3{margin-right: 1rem !important;}.me-sm-4{margin-right: 1.5rem !important;}.me-sm-5{margin-right: 3rem !important;}.me-sm-auto{margin-right: auto !important;}.mb-sm-0{margin-bottom: 0 !important;}.mb-sm-1{margin-bottom: 0.25rem !important;}.mb-sm-2{margin-bottom: 0.5rem !important;}.mb-sm-3{margin-bottom: 1rem !important;}.mb-sm-4{margin-bottom: 1.5rem !important;}.mb-sm-5{margin-bottom: 3rem !important;}.mb-sm-auto{margin-bottom: auto !important;}.ms-sm-0{margin-left: 0 !important;}.ms-sm-1{margin-left: 0.25rem !important;}.ms-sm-2{margin-left: 0.5rem !important;}.ms-sm-3{margin-left: 1rem !important;}.ms-sm-4{margin-left: 1.5rem !important;}.ms-sm-5{margin-left: 3rem !important;}.ms-sm-auto{margin-left: auto !important;}.p-sm-0{padding: 0 !important;}.p-sm-1{padding: 0.25rem !important;}.p-sm-2{padding: 0.5rem !important;}.p-sm-3{padding: 1rem !important;}.p-sm-4{padding: 1.5rem !important;}.p-sm-5{padding: 3rem !important;}.px-sm-0{padding-right: 0 !important; padding-left: 0 !important;}.px-sm-1{padding-right: 0.25rem !important; padding-left: 0.25rem !important;}.px-sm-2{padding-right: 0.5rem !important; padding-left: 0.5rem !important;}.px-sm-3{padding-right: 1rem !important; padding-left: 1rem !important;}.px-sm-4{padding-right: 1.5rem !important; padding-left: 1.5rem !important;}.px-sm-5{padding-right: 3rem !important; padding-left: 3rem !important;}.py-sm-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-sm-1{padding-top: 0.25rem !important; padding-bottom: 0.25rem !important;}.py-sm-2{padding-top: 0.5rem !important; padding-bottom: 0.5rem !important;}.py-sm-3{padding-top: 1rem !important; padding-bottom: 1rem !important;}.py-sm-4{padding-top: 1.5rem !important; padding-bottom: 1.5rem !important;}.py-sm-5{padding-top: 3rem !important; padding-bottom: 3rem !important;}.pt-sm-0{padding-top: 0 !important;}.pt-sm-1{padding-top: 0.25rem !important;}.pt-sm-2{padding-top: 0.5rem !important;}.pt-sm-3{padding-top: 1rem !important;}.pt-sm-4{padding-top: 1.5rem !important;}.pt-sm-5{padding-top: 3rem !important;}.pe-sm-0{padding-right: 0 !important;}.pe-sm-1{padding-right: 0.25rem !important;}.pe-sm-2{padding-right: 0.5rem !important;}.pe-sm-3{padding-right: 1rem !important;}.pe-sm-4{padding-right: 1.5rem !important;}.pe-sm-5{padding-right: 3rem !important;}.pb-sm-0{padding-bottom: 0 !important;}.pb-sm-1{padding-bottom: 0.25rem !important;}.pb-sm-2{padding-bottom: 0.5rem !important;}.pb-sm-3{padding-bottom: 1rem !important;}.pb-sm-4{padding-bottom: 1.5rem !important;}.pb-sm-5{padding-bottom: 3rem !important;}.ps-sm-0{padding-left: 0 !important;}.ps-sm-1{padding-left: 0.25rem !important;}.ps-sm-2{padding-left: 0.5rem !important;}.ps-sm-3{padding-left: 1rem !important;}.ps-sm-4{padding-left: 1.5rem !important;}.ps-sm-5{padding-left: 3rem !important;}.text-sm-start{text-align: left !important;}.text-sm-end{text-align: right !important;}.text-sm-center{text-align: center !important;}.flex-basis-sm-0{flex-basis: 0 !important;}.flex-basis-sm-25{flex-basis: 25% !important;}.flex-basis-sm-50{flex-basis: 50% !important;}.flex-basis-sm-75{flex-basis: 75% !important;}.flex-basis-sm-100{flex-basis: 100% !important;}.flex-basis-sm-auto{flex-basis: auto !important;}}@media (min-width: 768px){.float-md-start{float: left !important;}.float-md-end{float: right !important;}.float-md-none{float: none !important;}.d-md-inline{display: inline !important;}.d-md-inline-block{display: inline-block !important;}.d-md-block{display: block !important;}.d-md-grid{display: grid !important;}.d-md-table{display: table !important;}.d-md-table-row{display: table-row !important;}.d-md-table-cell{display: table-cell !important;}.d-md-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-md-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-md-none{display: none !important;}.d-md-contents{display: contents !important;}.position-md-static{position: static !important;}.position-md-relative{position: relative !important;}.position-md-absolute{position: absolute !important;}.position-md-fixed{position: fixed !important;}.position-md-sticky{position: sticky !important;}.w-md-0{width: 0 !important;}.w-md-25{width: 25% !important;}.w-md-50{width: 50% !important;}.w-md-75{width: 75% !important;}.w-md-100{width: 100% !important;}.w-md-auto{width: auto !important;}.mw-md-0{max-width: 0 !important;}.mw-md-25{max-width: 25% !important;}.mw-md-50{max-width: 50% !important;}.mw-md-75{max-width: 75% !important;}.mw-md-100{max-width: 100% !important;}.mw-md-auto{max-width: auto !important;}.h-md-0{height: 0 !important;}.h-md-25{height: 25% !important;}.h-md-50{height: 50% !important;}.h-md-75{height: 75% !important;}.h-md-100{height: 100% !important;}.h-md-auto{height: auto !important;}.mh-md-0{max-height: 0 !important;}.mh-md-25{max-height: 25% !important;}.mh-md-50{max-height: 50% !important;}.mh-md-75{max-height: 75% !important;}.mh-md-100{max-height: 100% !important;}.mh-md-auto{max-height: auto !important;}.flex-md-fill{flex: 1 1 auto !important;}.flex-md-row{flex-direction: row !important;}.flex-md-column{flex-direction: column !important;}.flex-md-row-reverse{flex-direction: row-reverse !important;}.flex-md-column-reverse{flex-direction: column-reverse !important;}.flex-md-grow-0{flex-grow: 0 !important;}.flex-md-grow-1{flex-grow: 1 !important;}.flex-md-shrink-0{flex-shrink: 0 !important;}.flex-md-shrink-1{flex-shrink: 1 !important;}.flex-md-wrap{flex-wrap: wrap !important;}.flex-md-nowrap{flex-wrap: nowrap !important;}.flex-md-wrap-reverse{flex-wrap: wrap-reverse !important;}.gap-md-0{gap: 0 !important;}.gap-md-1{gap: 0.25rem !important;}.gap-md-2{gap: 0.5rem !important;}.gap-md-3{gap: 1rem !important;}.gap-md-4{gap: 1.5rem !important;}.gap-md-5{gap: 3rem !important;}.justify-content-md-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-md-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-md-center{justify-content: center !important;}.justify-content-md-between{justify-content: space-between !important;}.justify-content-md-around{justify-content: space-around !important;}.justify-content-md-evenly{justify-content: space-evenly !important;}.align-items-md-start{align-items: flex-start !important;}.align-items-md-end{align-items: flex-end !important;}.align-items-md-center{align-items: center !important;}.align-items-md-baseline{align-items: baseline !important;}.align-items-md-stretch{align-items: stretch !important;}.align-content-md-start{align-content: flex-start !important;}.align-content-md-end{align-content: flex-end !important;}.align-content-md-center{align-content: center !important;}.align-content-md-between{align-content: space-between !important;}.align-content-md-around{align-content: space-around !important;}.align-content-md-stretch{align-content: stretch !important;}.align-self-md-auto{align-self: auto !important;}.align-self-md-start{align-self: flex-start !important;}.align-self-md-end{align-self: flex-end !important;}.align-self-md-center{align-self: center !important;}.align-self-md-baseline{align-self: baseline !important;}.align-self-md-stretch{align-self: stretch !important;}.order-md-first{order: -1 !important;}.order-md-last{order: 13 !important;}.order-md-0{order: 0 !important;}.order-md-1{order: 1 !important;}.order-md-2{order: 2 !important;}.order-md-3{order: 3 !important;}.order-md-4{order: 4 !important;}.order-md-5{order: 5 !important;}.order-md-6{order: 6 !important;}.order-md-7{order: 7 !important;}.order-md-8{order: 8 !important;}.order-md-9{order: 9 !important;}.order-md-10{order: 10 !important;}.order-md-11{order: 11 !important;}.order-md-12{order: 12 !important;}.m-md-0{margin: 0 !important;}.m-md-1{margin: 0.25rem !important;}.m-md-2{margin: 0.5rem !important;}.m-md-3{margin: 1rem !important;}.m-md-4{margin: 1.5rem !important;}.m-md-5{margin: 3rem !important;}.m-md-auto{margin: auto !important;}.mx-md-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-md-1{margin-right: 0.25rem !important; margin-left: 0.25rem !important;}.mx-md-2{margin-right: 0.5rem !important; margin-left: 0.5rem !important;}.mx-md-3{margin-right: 1rem !important; margin-left: 1rem !important;}.mx-md-4{margin-right: 1.5rem !important; margin-left: 1.5rem !important;}.mx-md-5{margin-right: 3rem !important; margin-left: 3rem !important;}.mx-md-auto{margin-right: auto !important; margin-left: auto !important;}.my-md-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-md-1{margin-top: 0.25rem !important; margin-bottom: 0.25rem !important;}.my-md-2{margin-top: 0.5rem !important; margin-bottom: 0.5rem !important;}.my-md-3{margin-top: 1rem !important; margin-bottom: 1rem !important;}.my-md-4{margin-top: 1.5rem !important; margin-bottom: 1.5rem !important;}.my-md-5{margin-top: 3rem !important; margin-bottom: 3rem !important;}.my-md-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-md-0{margin-top: 0 !important;}.mt-md-1{margin-top: 0.25rem !important;}.mt-md-2{margin-top: 0.5rem !important;}.mt-md-3{margin-top: 1rem !important;}.mt-md-4{margin-top: 1.5rem !important;}.mt-md-5{margin-top: 3rem !important;}.mt-md-auto{margin-top: auto !important;}.me-md-0{margin-right: 0 !important;}.me-md-1{margin-right: 0.25rem !important;}.me-md-2{margin-right: 0.5rem !important;}.me-md-3{margin-right: 1rem !important;}.me-md-4{margin-right: 1.5rem !important;}.me-md-5{margin-right: 3rem !important;}.me-md-auto{margin-right: auto !important;}.mb-md-0{margin-bottom: 0 !important;}.mb-md-1{margin-bottom: 0.25rem !important;}.mb-md-2{margin-bottom: 0.5rem !important;}.mb-md-3{margin-bottom: 1rem !important;}.mb-md-4{margin-bottom: 1.5rem !important;}.mb-md-5{margin-bottom: 3rem !important;}.mb-md-auto{margin-bottom: auto !important;}.ms-md-0{margin-left: 0 !important;}.ms-md-1{margin-left: 0.25rem !important;}.ms-md-2{margin-left: 0.5rem !important;}.ms-md-3{margin-left: 1rem !important;}.ms-md-4{margin-left: 1.5rem !important;}.ms-md-5{margin-left: 3rem !important;}.ms-md-auto{margin-left: auto !important;}.p-md-0{padding: 0 !important;}.p-md-1{padding: 0.25rem !important;}.p-md-2{padding: 0.5rem !important;}.p-md-3{padding: 1rem !important;}.p-md-4{padding: 1.5rem !important;}.p-md-5{padding: 3rem !important;}.px-md-0{padding-right: 0 !important; padding-left: 0 !important;}.px-md-1{padding-right: 0.25rem !important; padding-left: 0.25rem !important;}.px-md-2{padding-right: 0.5rem !important; padding-left: 0.5rem !important;}.px-md-3{padding-right: 1rem !important; padding-left: 1rem !important;}.px-md-4{padding-right: 1.5rem !important; padding-left: 1.5rem !important;}.px-md-5{padding-right: 3rem !important; padding-left: 3rem !important;}.py-md-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-md-1{padding-top: 0.25rem !important; padding-bottom: 0.25rem !important;}.py-md-2{padding-top: 0.5rem !important; padding-bottom: 0.5rem !important;}.py-md-3{padding-top: 1rem !important; padding-bottom: 1rem !important;}.py-md-4{padding-top: 1.5rem !important; padding-bottom: 1.5rem !important;}.py-md-5{padding-top: 3rem !important; padding-bottom: 3rem !important;}.pt-md-0{padding-top: 0 !important;}.pt-md-1{padding-top: 0.25rem !important;}.pt-md-2{padding-top: 0.5rem !important;}.pt-md-3{padding-top: 1rem !important;}.pt-md-4{padding-top: 1.5rem !important;}.pt-md-5{padding-top: 3rem !important;}.pe-md-0{padding-right: 0 !important;}.pe-md-1{padding-right: 0.25rem !important;}.pe-md-2{padding-right: 0.5rem !important;}.pe-md-3{padding-right: 1rem !important;}.pe-md-4{padding-right: 1.5rem !important;}.pe-md-5{padding-right: 3rem !important;}.pb-md-0{padding-bottom: 0 !important;}.pb-md-1{padding-bottom: 0.25rem !important;}.pb-md-2{padding-bottom: 0.5rem !important;}.pb-md-3{padding-bottom: 1rem !important;}.pb-md-4{padding-bottom: 1.5rem !important;}.pb-md-5{padding-bottom: 3rem !important;}.ps-md-0{padding-left: 0 !important;}.ps-md-1{padding-left: 0.25rem !important;}.ps-md-2{padding-left: 0.5rem !important;}.ps-md-3{padding-left: 1rem !important;}.ps-md-4{padding-left: 1.5rem !important;}.ps-md-5{padding-left: 3rem !important;}.text-md-start{text-align: left !important;}.text-md-end{text-align: right !important;}.text-md-center{text-align: center !important;}.flex-basis-md-0{flex-basis: 0 !important;}.flex-basis-md-25{flex-basis: 25% !important;}.flex-basis-md-50{flex-basis: 50% !important;}.flex-basis-md-75{flex-basis: 75% !important;}.flex-basis-md-100{flex-basis: 100% !important;}.flex-basis-md-auto{flex-basis: auto !important;}}@media (min-width: 992px){.float-lg-start{float: left !important;}.float-lg-end{float: right !important;}.float-lg-none{float: none !important;}.d-lg-inline{display: inline !important;}.d-lg-inline-block{display: inline-block !important;}.d-lg-block{display: block !important;}.d-lg-grid{display: grid !important;}.d-lg-table{display: table !important;}.d-lg-table-row{display: table-row !important;}.d-lg-table-cell{display: table-cell !important;}.d-lg-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-lg-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-lg-none{display: none !important;}.d-lg-contents{display: contents !important;}.position-lg-static{position: static !important;}.position-lg-relative{position: relative !important;}.position-lg-absolute{position: absolute !important;}.position-lg-fixed{position: fixed !important;}.position-lg-sticky{position: sticky !important;}.w-lg-0{width: 0 !important;}.w-lg-25{width: 25% !important;}.w-lg-50{width: 50% !important;}.w-lg-75{width: 75% !important;}.w-lg-100{width: 100% !important;}.w-lg-auto{width: auto !important;}.mw-lg-0{max-width: 0 !important;}.mw-lg-25{max-width: 25% !important;}.mw-lg-50{max-width: 50% !important;}.mw-lg-75{max-width: 75% !important;}.mw-lg-100{max-width: 100% !important;}.mw-lg-auto{max-width: auto !important;}.h-lg-0{height: 0 !important;}.h-lg-25{height: 25% !important;}.h-lg-50{height: 50% !important;}.h-lg-75{height: 75% !important;}.h-lg-100{height: 100% !important;}.h-lg-auto{height: auto !important;}.mh-lg-0{max-height: 0 !important;}.mh-lg-25{max-height: 25% !important;}.mh-lg-50{max-height: 50% !important;}.mh-lg-75{max-height: 75% !important;}.mh-lg-100{max-height: 100% !important;}.mh-lg-auto{max-height: auto !important;}.flex-lg-fill{flex: 1 1 auto !important;}.flex-lg-row{flex-direction: row !important;}.flex-lg-column{flex-direction: column !important;}.flex-lg-row-reverse{flex-direction: row-reverse !important;}.flex-lg-column-reverse{flex-direction: column-reverse !important;}.flex-lg-grow-0{flex-grow: 0 !important;}.flex-lg-grow-1{flex-grow: 1 !important;}.flex-lg-shrink-0{flex-shrink: 0 !important;}.flex-lg-shrink-1{flex-shrink: 1 !important;}.flex-lg-wrap{flex-wrap: wrap !important;}.flex-lg-nowrap{flex-wrap: nowrap !important;}.flex-lg-wrap-reverse{flex-wrap: wrap-reverse !important;}.gap-lg-0{gap: 0 !important;}.gap-lg-1{gap: 0.25rem !important;}.gap-lg-2{gap: 0.5rem !important;}.gap-lg-3{gap: 1rem !important;}.gap-lg-4{gap: 1.5rem !important;}.gap-lg-5{gap: 3rem !important;}.justify-content-lg-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-lg-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-lg-center{justify-content: center !important;}.justify-content-lg-between{justify-content: space-between !important;}.justify-content-lg-around{justify-content: space-around !important;}.justify-content-lg-evenly{justify-content: space-evenly !important;}.align-items-lg-start{align-items: flex-start !important;}.align-items-lg-end{align-items: flex-end !important;}.align-items-lg-center{align-items: center !important;}.align-items-lg-baseline{align-items: baseline !important;}.align-items-lg-stretch{align-items: stretch !important;}.align-content-lg-start{align-content: flex-start !important;}.align-content-lg-end{align-content: flex-end !important;}.align-content-lg-center{align-content: center !important;}.align-content-lg-between{align-content: space-between !important;}.align-content-lg-around{align-content: space-around !important;}.align-content-lg-stretch{align-content: stretch !important;}.align-self-lg-auto{align-self: auto !important;}.align-self-lg-start{align-self: flex-start !important;}.align-self-lg-end{align-self: flex-end !important;}.align-self-lg-center{align-self: center !important;}.align-self-lg-baseline{align-self: baseline !important;}.align-self-lg-stretch{align-self: stretch !important;}.order-lg-first{order: -1 !important;}.order-lg-last{order: 13 !important;}.order-lg-0{order: 0 !important;}.order-lg-1{order: 1 !important;}.order-lg-2{order: 2 !important;}.order-lg-3{order: 3 !important;}.order-lg-4{order: 4 !important;}.order-lg-5{order: 5 !important;}.order-lg-6{order: 6 !important;}.order-lg-7{order: 7 !important;}.order-lg-8{order: 8 !important;}.order-lg-9{order: 9 !important;}.order-lg-10{order: 10 !important;}.order-lg-11{order: 11 !important;}.order-lg-12{order: 12 !important;}.m-lg-0{margin: 0 !important;}.m-lg-1{margin: 0.25rem !important;}.m-lg-2{margin: 0.5rem !important;}.m-lg-3{margin: 1rem !important;}.m-lg-4{margin: 1.5rem !important;}.m-lg-5{margin: 3rem !important;}.m-lg-auto{margin: auto !important;}.mx-lg-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-lg-1{margin-right: 0.25rem !important; margin-left: 0.25rem !important;}.mx-lg-2{margin-right: 0.5rem !important; margin-left: 0.5rem !important;}.mx-lg-3{margin-right: 1rem !important; margin-left: 1rem !important;}.mx-lg-4{margin-right: 1.5rem !important; margin-left: 1.5rem !important;}.mx-lg-5{margin-right: 3rem !important; margin-left: 3rem !important;}.mx-lg-auto{margin-right: auto !important; margin-left: auto !important;}.my-lg-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-lg-1{margin-top: 0.25rem !important; margin-bottom: 0.25rem !important;}.my-lg-2{margin-top: 0.5rem !important; margin-bottom: 0.5rem !important;}.my-lg-3{margin-top: 1rem !important; margin-bottom: 1rem !important;}.my-lg-4{margin-top: 1.5rem !important; margin-bottom: 1.5rem !important;}.my-lg-5{margin-top: 3rem !important; margin-bottom: 3rem !important;}.my-lg-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-lg-0{margin-top: 0 !important;}.mt-lg-1{margin-top: 0.25rem !important;}.mt-lg-2{margin-top: 0.5rem !important;}.mt-lg-3{margin-top: 1rem !important;}.mt-lg-4{margin-top: 1.5rem !important;}.mt-lg-5{margin-top: 3rem !important;}.mt-lg-auto{margin-top: auto !important;}.me-lg-0{margin-right: 0 !important;}.me-lg-1{margin-right: 0.25rem !important;}.me-lg-2{margin-right: 0.5rem !important;}.me-lg-3{margin-right: 1rem !important;}.me-lg-4{margin-right: 1.5rem !important;}.me-lg-5{margin-right: 3rem !important;}.me-lg-auto{margin-right: auto !important;}.mb-lg-0{margin-bottom: 0 !important;}.mb-lg-1{margin-bottom: 0.25rem !important;}.mb-lg-2{margin-bottom: 0.5rem !important;}.mb-lg-3{margin-bottom: 1rem !important;}.mb-lg-4{margin-bottom: 1.5rem !important;}.mb-lg-5{margin-bottom: 3rem !important;}.mb-lg-auto{margin-bottom: auto !important;}.ms-lg-0{margin-left: 0 !important;}.ms-lg-1{margin-left: 0.25rem !important;}.ms-lg-2{margin-left: 0.5rem !important;}.ms-lg-3{margin-left: 1rem !important;}.ms-lg-4{margin-left: 1.5rem !important;}.ms-lg-5{margin-left: 3rem !important;}.ms-lg-auto{margin-left: auto !important;}.p-lg-0{padding: 0 !important;}.p-lg-1{padding: 0.25rem !important;}.p-lg-2{padding: 0.5rem !important;}.p-lg-3{padding: 1rem !important;}.p-lg-4{padding: 1.5rem !important;}.p-lg-5{padding: 3rem !important;}.px-lg-0{padding-right: 0 !important; padding-left: 0 !important;}.px-lg-1{padding-right: 0.25rem !important; padding-left: 0.25rem !important;}.px-lg-2{padding-right: 0.5rem !important; padding-left: 0.5rem !important;}.px-lg-3{padding-right: 1rem !important; padding-left: 1rem !important;}.px-lg-4{padding-right: 1.5rem !important; padding-left: 1.5rem !important;}.px-lg-5{padding-right: 3rem !important; padding-left: 3rem !important;}.py-lg-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-lg-1{padding-top: 0.25rem !important; padding-bottom: 0.25rem !important;}.py-lg-2{padding-top: 0.5rem !important; padding-bottom: 0.5rem !important;}.py-lg-3{padding-top: 1rem !important; padding-bottom: 1rem !important;}.py-lg-4{padding-top: 1.5rem !important; padding-bottom: 1.5rem !important;}.py-lg-5{padding-top: 3rem !important; padding-bottom: 3rem !important;}.pt-lg-0{padding-top: 0 !important;}.pt-lg-1{padding-top: 0.25rem !important;}.pt-lg-2{padding-top: 0.5rem !important;}.pt-lg-3{padding-top: 1rem !important;}.pt-lg-4{padding-top: 1.5rem !important;}.pt-lg-5{padding-top: 3rem !important;}.pe-lg-0{padding-right: 0 !important;}.pe-lg-1{padding-right: 0.25rem !important;}.pe-lg-2{padding-right: 0.5rem !important;}.pe-lg-3{padding-right: 1rem !important;}.pe-lg-4{padding-right: 1.5rem !important;}.pe-lg-5{padding-right: 3rem !important;}.pb-lg-0{padding-bottom: 0 !important;}.pb-lg-1{padding-bottom: 0.25rem !important;}.pb-lg-2{padding-bottom: 0.5rem !important;}.pb-lg-3{padding-bottom: 1rem !important;}.pb-lg-4{padding-bottom: 1.5rem !important;}.pb-lg-5{padding-bottom: 3rem !important;}.ps-lg-0{padding-left: 0 !important;}.ps-lg-1{padding-left: 0.25rem !important;}.ps-lg-2{padding-left: 0.5rem !important;}.ps-lg-3{padding-left: 1rem !important;}.ps-lg-4{padding-left: 1.5rem !important;}.ps-lg-5{padding-left: 3rem !important;}.text-lg-start{text-align: left !important;}.text-lg-end{text-align: right !important;}.text-lg-center{text-align: center !important;}.flex-basis-lg-0{flex-basis: 0 !important;}.flex-basis-lg-25{flex-basis: 25% !important;}.flex-basis-lg-50{flex-basis: 50% !important;}.flex-basis-lg-75{flex-basis: 75% !important;}.flex-basis-lg-100{flex-basis: 100% !important;}.flex-basis-lg-auto{flex-basis: auto !important;}}@media (min-width: 1200px){.float-xl-start{float: left !important;}.float-xl-end{float: right !important;}.float-xl-none{float: none !important;}.d-xl-inline{display: inline !important;}.d-xl-inline-block{display: inline-block !important;}.d-xl-block{display: block !important;}.d-xl-grid{display: grid !important;}.d-xl-table{display: table !important;}.d-xl-table-row{display: table-row !important;}.d-xl-table-cell{display: table-cell !important;}.d-xl-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-xl-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-xl-none{display: none !important;}.d-xl-contents{display: contents !important;}.position-xl-static{position: static !important;}.position-xl-relative{position: relative !important;}.position-xl-absolute{position: absolute !important;}.position-xl-fixed{position: fixed !important;}.position-xl-sticky{position: sticky !important;}.w-xl-0{width: 0 !important;}.w-xl-25{width: 25% !important;}.w-xl-50{width: 50% !important;}.w-xl-75{width: 75% !important;}.w-xl-100{width: 100% !important;}.w-xl-auto{width: auto !important;}.mw-xl-0{max-width: 0 !important;}.mw-xl-25{max-width: 25% !important;}.mw-xl-50{max-width: 50% !important;}.mw-xl-75{max-width: 75% !important;}.mw-xl-100{max-width: 100% !important;}.mw-xl-auto{max-width: auto !important;}.h-xl-0{height: 0 !important;}.h-xl-25{height: 25% !important;}.h-xl-50{height: 50% !important;}.h-xl-75{height: 75% !important;}.h-xl-100{height: 100% !important;}.h-xl-auto{height: auto !important;}.mh-xl-0{max-height: 0 !important;}.mh-xl-25{max-height: 25% !important;}.mh-xl-50{max-height: 50% !important;}.mh-xl-75{max-height: 75% !important;}.mh-xl-100{max-height: 100% !important;}.mh-xl-auto{max-height: auto !important;}.flex-xl-fill{flex: 1 1 auto !important;}.flex-xl-row{flex-direction: row !important;}.flex-xl-column{flex-direction: column !important;}.flex-xl-row-reverse{flex-direction: row-reverse !important;}.flex-xl-column-reverse{flex-direction: column-reverse !important;}.flex-xl-grow-0{flex-grow: 0 !important;}.flex-xl-grow-1{flex-grow: 1 !important;}.flex-xl-shrink-0{flex-shrink: 0 !important;}.flex-xl-shrink-1{flex-shrink: 1 !important;}.flex-xl-wrap{flex-wrap: wrap !important;}.flex-xl-nowrap{flex-wrap: nowrap !important;}.flex-xl-wrap-reverse{flex-wrap: wrap-reverse !important;}.gap-xl-0{gap: 0 !important;}.gap-xl-1{gap: 0.25rem !important;}.gap-xl-2{gap: 0.5rem !important;}.gap-xl-3{gap: 1rem !important;}.gap-xl-4{gap: 1.5rem !important;}.gap-xl-5{gap: 3rem !important;}.justify-content-xl-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-xl-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-xl-center{justify-content: center !important;}.justify-content-xl-between{justify-content: space-between !important;}.justify-content-xl-around{justify-content: space-around !important;}.justify-content-xl-evenly{justify-content: space-evenly !important;}.align-items-xl-start{align-items: flex-start !important;}.align-items-xl-end{align-items: flex-end !important;}.align-items-xl-center{align-items: center !important;}.align-items-xl-baseline{align-items: baseline !important;}.align-items-xl-stretch{align-items: stretch !important;}.align-content-xl-start{align-content: flex-start !important;}.align-content-xl-end{align-content: flex-end !important;}.align-content-xl-center{align-content: center !important;}.align-content-xl-between{align-content: space-between !important;}.align-content-xl-around{align-content: space-around !important;}.align-content-xl-stretch{align-content: stretch !important;}.align-self-xl-auto{align-self: auto !important;}.align-self-xl-start{align-self: flex-start !important;}.align-self-xl-end{align-self: flex-end !important;}.align-self-xl-center{align-self: center !important;}.align-self-xl-baseline{align-self: baseline !important;}.align-self-xl-stretch{align-self: stretch !important;}.order-xl-first{order: -1 !important;}.order-xl-last{order: 13 !important;}.order-xl-0{order: 0 !important;}.order-xl-1{order: 1 !important;}.order-xl-2{order: 2 !important;}.order-xl-3{order: 3 !important;}.order-xl-4{order: 4 !important;}.order-xl-5{order: 5 !important;}.order-xl-6{order: 6 !important;}.order-xl-7{order: 7 !important;}.order-xl-8{order: 8 !important;}.order-xl-9{order: 9 !important;}.order-xl-10{order: 10 !important;}.order-xl-11{order: 11 !important;}.order-xl-12{order: 12 !important;}.m-xl-0{margin: 0 !important;}.m-xl-1{margin: 0.25rem !important;}.m-xl-2{margin: 0.5rem !important;}.m-xl-3{margin: 1rem !important;}.m-xl-4{margin: 1.5rem !important;}.m-xl-5{margin: 3rem !important;}.m-xl-auto{margin: auto !important;}.mx-xl-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-xl-1{margin-right: 0.25rem !important; margin-left: 0.25rem !important;}.mx-xl-2{margin-right: 0.5rem !important; margin-left: 0.5rem !important;}.mx-xl-3{margin-right: 1rem !important; margin-left: 1rem !important;}.mx-xl-4{margin-right: 1.5rem !important; margin-left: 1.5rem !important;}.mx-xl-5{margin-right: 3rem !important; margin-left: 3rem !important;}.mx-xl-auto{margin-right: auto !important; margin-left: auto !important;}.my-xl-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-xl-1{margin-top: 0.25rem !important; margin-bottom: 0.25rem !important;}.my-xl-2{margin-top: 0.5rem !important; margin-bottom: 0.5rem !important;}.my-xl-3{margin-top: 1rem !important; margin-bottom: 1rem !important;}.my-xl-4{margin-top: 1.5rem !important; margin-bottom: 1.5rem !important;}.my-xl-5{margin-top: 3rem !important; margin-bottom: 3rem !important;}.my-xl-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-xl-0{margin-top: 0 !important;}.mt-xl-1{margin-top: 0.25rem !important;}.mt-xl-2{margin-top: 0.5rem !important;}.mt-xl-3{margin-top: 1rem !important;}.mt-xl-4{margin-top: 1.5rem !important;}.mt-xl-5{margin-top: 3rem !important;}.mt-xl-auto{margin-top: auto !important;}.me-xl-0{margin-right: 0 !important;}.me-xl-1{margin-right: 0.25rem !important;}.me-xl-2{margin-right: 0.5rem !important;}.me-xl-3{margin-right: 1rem !important;}.me-xl-4{margin-right: 1.5rem !important;}.me-xl-5{margin-right: 3rem !important;}.me-xl-auto{margin-right: auto !important;}.mb-xl-0{margin-bottom: 0 !important;}.mb-xl-1{margin-bottom: 0.25rem !important;}.mb-xl-2{margin-bottom: 0.5rem !important;}.mb-xl-3{margin-bottom: 1rem !important;}.mb-xl-4{margin-bottom: 1.5rem !important;}.mb-xl-5{margin-bottom: 3rem !important;}.mb-xl-auto{margin-bottom: auto !important;}.ms-xl-0{margin-left: 0 !important;}.ms-xl-1{margin-left: 0.25rem !important;}.ms-xl-2{margin-left: 0.5rem !important;}.ms-xl-3{margin-left: 1rem !important;}.ms-xl-4{margin-left: 1.5rem !important;}.ms-xl-5{margin-left: 3rem !important;}.ms-xl-auto{margin-left: auto !important;}.p-xl-0{padding: 0 !important;}.p-xl-1{padding: 0.25rem !important;}.p-xl-2{padding: 0.5rem !important;}.p-xl-3{padding: 1rem !important;}.p-xl-4{padding: 1.5rem !important;}.p-xl-5{padding: 3rem !important;}.px-xl-0{padding-right: 0 !important; padding-left: 0 !important;}.px-xl-1{padding-right: 0.25rem !important; padding-left: 0.25rem !important;}.px-xl-2{padding-right: 0.5rem !important; padding-left: 0.5rem !important;}.px-xl-3{padding-right: 1rem !important; padding-left: 1rem !important;}.px-xl-4{padding-right: 1.5rem !important; padding-left: 1.5rem !important;}.px-xl-5{padding-right: 3rem !important; padding-left: 3rem !important;}.py-xl-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-xl-1{padding-top: 0.25rem !important; padding-bottom: 0.25rem !important;}.py-xl-2{padding-top: 0.5rem !important; padding-bottom: 0.5rem !important;}.py-xl-3{padding-top: 1rem !important; padding-bottom: 1rem !important;}.py-xl-4{padding-top: 1.5rem !important; padding-bottom: 1.5rem !important;}.py-xl-5{padding-top: 3rem !important; padding-bottom: 3rem !important;}.pt-xl-0{padding-top: 0 !important;}.pt-xl-1{padding-top: 0.25rem !important;}.pt-xl-2{padding-top: 0.5rem !important;}.pt-xl-3{padding-top: 1rem !important;}.pt-xl-4{padding-top: 1.5rem !important;}.pt-xl-5{padding-top: 3rem !important;}.pe-xl-0{padding-right: 0 !important;}.pe-xl-1{padding-right: 0.25rem !important;}.pe-xl-2{padding-right: 0.5rem !important;}.pe-xl-3{padding-right: 1rem !important;}.pe-xl-4{padding-right: 1.5rem !important;}.pe-xl-5{padding-right: 3rem !important;}.pb-xl-0{padding-bottom: 0 !important;}.pb-xl-1{padding-bottom: 0.25rem !important;}.pb-xl-2{padding-bottom: 0.5rem !important;}.pb-xl-3{padding-bottom: 1rem !important;}.pb-xl-4{padding-bottom: 1.5rem !important;}.pb-xl-5{padding-bottom: 3rem !important;}.ps-xl-0{padding-left: 0 !important;}.ps-xl-1{padding-left: 0.25rem !important;}.ps-xl-2{padding-left: 0.5rem !important;}.ps-xl-3{padding-left: 1rem !important;}.ps-xl-4{padding-left: 1.5rem !important;}.ps-xl-5{padding-left: 3rem !important;}.text-xl-start{text-align: left !important;}.text-xl-end{text-align: right !important;}.text-xl-center{text-align: center !important;}.flex-basis-xl-0{flex-basis: 0 !important;}.flex-basis-xl-25{flex-basis: 25% !important;}.flex-basis-xl-50{flex-basis: 50% !important;}.flex-basis-xl-75{flex-basis: 75% !important;}.flex-basis-xl-100{flex-basis: 100% !important;}.flex-basis-xl-auto{flex-basis: auto !important;}}@media (min-width: 1400px){.float-xxl-start{float: left !important;}.float-xxl-end{float: right !important;}.float-xxl-none{float: none !important;}.d-xxl-inline{display: inline !important;}.d-xxl-inline-block{display: inline-block !important;}.d-xxl-block{display: block !important;}.d-xxl-grid{display: grid !important;}.d-xxl-table{display: table !important;}.d-xxl-table-row{display: table-row !important;}.d-xxl-table-cell{display: table-cell !important;}.d-xxl-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-xxl-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-xxl-none{display: none !important;}.d-xxl-contents{display: contents !important;}.position-xxl-static{position: static !important;}.position-xxl-relative{position: relative !important;}.position-xxl-absolute{position: absolute !important;}.position-xxl-fixed{position: fixed !important;}.position-xxl-sticky{position: sticky !important;}.w-xxl-0{width: 0 !important;}.w-xxl-25{width: 25% !important;}.w-xxl-50{width: 50% !important;}.w-xxl-75{width: 75% !important;}.w-xxl-100{width: 100% !important;}.w-xxl-auto{width: auto !important;}.mw-xxl-0{max-width: 0 !important;}.mw-xxl-25{max-width: 25% !important;}.mw-xxl-50{max-width: 50% !important;}.mw-xxl-75{max-width: 75% !important;}.mw-xxl-100{max-width: 100% !important;}.mw-xxl-auto{max-width: auto !important;}.h-xxl-0{height: 0 !important;}.h-xxl-25{height: 25% !important;}.h-xxl-50{height: 50% !important;}.h-xxl-75{height: 75% !important;}.h-xxl-100{height: 100% !important;}.h-xxl-auto{height: auto !important;}.mh-xxl-0{max-height: 0 !important;}.mh-xxl-25{max-height: 25% !important;}.mh-xxl-50{max-height: 50% !important;}.mh-xxl-75{max-height: 75% !important;}.mh-xxl-100{max-height: 100% !important;}.mh-xxl-auto{max-height: auto !important;}.flex-xxl-fill{flex: 1 1 auto !important;}.flex-xxl-row{flex-direction: row !important;}.flex-xxl-column{flex-direction: column !important;}.flex-xxl-row-reverse{flex-direction: row-reverse !important;}.flex-xxl-column-reverse{flex-direction: column-reverse !important;}.flex-xxl-grow-0{flex-grow: 0 !important;}.flex-xxl-grow-1{flex-grow: 1 !important;}.flex-xxl-shrink-0{flex-shrink: 0 !important;}.flex-xxl-shrink-1{flex-shrink: 1 !important;}.flex-xxl-wrap{flex-wrap: wrap !important;}.flex-xxl-nowrap{flex-wrap: nowrap !important;}.flex-xxl-wrap-reverse{flex-wrap: wrap-reverse !important;}.gap-xxl-0{gap: 0 !important;}.gap-xxl-1{gap: 0.25rem !important;}.gap-xxl-2{gap: 0.5rem !important;}.gap-xxl-3{gap: 1rem !important;}.gap-xxl-4{gap: 1.5rem !important;}.gap-xxl-5{gap: 3rem !important;}.justify-content-xxl-start{-webkit-box-pack: start !important; justify-content: flex-start !important;}.justify-content-xxl-end{-webkit-box-pack: end !important; justify-content: flex-end !important;}.justify-content-xxl-center{justify-content: center !important;}.justify-content-xxl-between{justify-content: space-between !important;}.justify-content-xxl-around{justify-content: space-around !important;}.justify-content-xxl-evenly{justify-content: space-evenly !important;}.align-items-xxl-start{align-items: flex-start !important;}.align-items-xxl-end{align-items: flex-end !important;}.align-items-xxl-center{align-items: center !important;}.align-items-xxl-baseline{align-items: baseline !important;}.align-items-xxl-stretch{align-items: stretch !important;}.align-content-xxl-start{align-content: flex-start !important;}.align-content-xxl-end{align-content: flex-end !important;}.align-content-xxl-center{align-content: center !important;}.align-content-xxl-between{align-content: space-between !important;}.align-content-xxl-around{align-content: space-around !important;}.align-content-xxl-stretch{align-content: stretch !important;}.align-self-xxl-auto{align-self: auto !important;}.align-self-xxl-start{align-self: flex-start !important;}.align-self-xxl-end{align-self: flex-end !important;}.align-self-xxl-center{align-self: center !important;}.align-self-xxl-baseline{align-self: baseline !important;}.align-self-xxl-stretch{align-self: stretch !important;}.order-xxl-first{order: -1 !important;}.order-xxl-last{order: 13 !important;}.order-xxl-0{order: 0 !important;}.order-xxl-1{order: 1 !important;}.order-xxl-2{order: 2 !important;}.order-xxl-3{order: 3 !important;}.order-xxl-4{order: 4 !important;}.order-xxl-5{order: 5 !important;}.order-xxl-6{order: 6 !important;}.order-xxl-7{order: 7 !important;}.order-xxl-8{order: 8 !important;}.order-xxl-9{order: 9 !important;}.order-xxl-10{order: 10 !important;}.order-xxl-11{order: 11 !important;}.order-xxl-12{order: 12 !important;}.m-xxl-0{margin: 0 !important;}.m-xxl-1{margin: 0.25rem !important;}.m-xxl-2{margin: 0.5rem !important;}.m-xxl-3{margin: 1rem !important;}.m-xxl-4{margin: 1.5rem !important;}.m-xxl-5{margin: 3rem !important;}.m-xxl-auto{margin: auto !important;}.mx-xxl-0{margin-right: 0 !important; margin-left: 0 !important;}.mx-xxl-1{margin-right: 0.25rem !important; margin-left: 0.25rem !important;}.mx-xxl-2{margin-right: 0.5rem !important; margin-left: 0.5rem !important;}.mx-xxl-3{margin-right: 1rem !important; margin-left: 1rem !important;}.mx-xxl-4{margin-right: 1.5rem !important; margin-left: 1.5rem !important;}.mx-xxl-5{margin-right: 3rem !important; margin-left: 3rem !important;}.mx-xxl-auto{margin-right: auto !important; margin-left: auto !important;}.my-xxl-0{margin-top: 0 !important; margin-bottom: 0 !important;}.my-xxl-1{margin-top: 0.25rem !important; margin-bottom: 0.25rem !important;}.my-xxl-2{margin-top: 0.5rem !important; margin-bottom: 0.5rem !important;}.my-xxl-3{margin-top: 1rem !important; margin-bottom: 1rem !important;}.my-xxl-4{margin-top: 1.5rem !important; margin-bottom: 1.5rem !important;}.my-xxl-5{margin-top: 3rem !important; margin-bottom: 3rem !important;}.my-xxl-auto{margin-top: auto !important; margin-bottom: auto !important;}.mt-xxl-0{margin-top: 0 !important;}.mt-xxl-1{margin-top: 0.25rem !important;}.mt-xxl-2{margin-top: 0.5rem !important;}.mt-xxl-3{margin-top: 1rem !important;}.mt-xxl-4{margin-top: 1.5rem !important;}.mt-xxl-5{margin-top: 3rem !important;}.mt-xxl-auto{margin-top: auto !important;}.me-xxl-0{margin-right: 0 !important;}.me-xxl-1{margin-right: 0.25rem !important;}.me-xxl-2{margin-right: 0.5rem !important;}.me-xxl-3{margin-right: 1rem !important;}.me-xxl-4{margin-right: 1.5rem !important;}.me-xxl-5{margin-right: 3rem !important;}.me-xxl-auto{margin-right: auto !important;}.mb-xxl-0{margin-bottom: 0 !important;}.mb-xxl-1{margin-bottom: 0.25rem !important;}.mb-xxl-2{margin-bottom: 0.5rem !important;}.mb-xxl-3{margin-bottom: 1rem !important;}.mb-xxl-4{margin-bottom: 1.5rem !important;}.mb-xxl-5{margin-bottom: 3rem !important;}.mb-xxl-auto{margin-bottom: auto !important;}.ms-xxl-0{margin-left: 0 !important;}.ms-xxl-1{margin-left: 0.25rem !important;}.ms-xxl-2{margin-left: 0.5rem !important;}.ms-xxl-3{margin-left: 1rem !important;}.ms-xxl-4{margin-left: 1.5rem !important;}.ms-xxl-5{margin-left: 3rem !important;}.ms-xxl-auto{margin-left: auto !important;}.p-xxl-0{padding: 0 !important;}.p-xxl-1{padding: 0.25rem !important;}.p-xxl-2{padding: 0.5rem !important;}.p-xxl-3{padding: 1rem !important;}.p-xxl-4{padding: 1.5rem !important;}.p-xxl-5{padding: 3rem !important;}.px-xxl-0{padding-right: 0 !important; padding-left: 0 !important;}.px-xxl-1{padding-right: 0.25rem !important; padding-left: 0.25rem !important;}.px-xxl-2{padding-right: 0.5rem !important; padding-left: 0.5rem !important;}.px-xxl-3{padding-right: 1rem !important; padding-left: 1rem !important;}.px-xxl-4{padding-right: 1.5rem !important; padding-left: 1.5rem !important;}.px-xxl-5{padding-right: 3rem !important; padding-left: 3rem !important;}.py-xxl-0{padding-top: 0 !important; padding-bottom: 0 !important;}.py-xxl-1{padding-top: 0.25rem !important; padding-bottom: 0.25rem !important;}.py-xxl-2{padding-top: 0.5rem !important; padding-bottom: 0.5rem !important;}.py-xxl-3{padding-top: 1rem !important; padding-bottom: 1rem !important;}.py-xxl-4{padding-top: 1.5rem !important; padding-bottom: 1.5rem !important;}.py-xxl-5{padding-top: 3rem !important; padding-bottom: 3rem !important;}.pt-xxl-0{padding-top: 0 !important;}.pt-xxl-1{padding-top: 0.25rem !important;}.pt-xxl-2{padding-top: 0.5rem !important;}.pt-xxl-3{padding-top: 1rem !important;}.pt-xxl-4{padding-top: 1.5rem !important;}.pt-xxl-5{padding-top: 3rem !important;}.pe-xxl-0{padding-right: 0 !important;}.pe-xxl-1{padding-right: 0.25rem !important;}.pe-xxl-2{padding-right: 0.5rem !important;}.pe-xxl-3{padding-right: 1rem !important;}.pe-xxl-4{padding-right: 1.5rem !important;}.pe-xxl-5{padding-right: 3rem !important;}.pb-xxl-0{padding-bottom: 0 !important;}.pb-xxl-1{padding-bottom: 0.25rem !important;}.pb-xxl-2{padding-bottom: 0.5rem !important;}.pb-xxl-3{padding-bottom: 1rem !important;}.pb-xxl-4{padding-bottom: 1.5rem !important;}.pb-xxl-5{padding-bottom: 3rem !important;}.ps-xxl-0{padding-left: 0 !important;}.ps-xxl-1{padding-left: 0.25rem !important;}.ps-xxl-2{padding-left: 0.5rem !important;}.ps-xxl-3{padding-left: 1rem !important;}.ps-xxl-4{padding-left: 1.5rem !important;}.ps-xxl-5{padding-left: 3rem !important;}.text-xxl-start{text-align: left !important;}.text-xxl-end{text-align: right !important;}.text-xxl-center{text-align: center !important;}.flex-basis-xxl-0{flex-basis: 0 !important;}.flex-basis-xxl-25{flex-basis: 25% !important;}.flex-basis-xxl-50{flex-basis: 50% !important;}.flex-basis-xxl-75{flex-basis: 75% !important;}.flex-basis-xxl-100{flex-basis: 100% !important;}.flex-basis-xxl-auto{flex-basis: auto !important;}}@media (min-width: 1200px){.fs-1{font-size: 2.1875rem !important;}.fs-2{font-size: 1.75rem !important;}.fs-3{font-size: 1.53125rem !important;}.fs-4{font-size: 1.3125rem !important;}}@media print{.d-print-inline{display: inline !important;}.d-print-inline-block{display: inline-block !important;}.d-print-block{display: block !important;}.d-print-grid{display: grid !important;}.d-print-table{display: table !important;}.d-print-table-row{display: table-row !important;}.d-print-table-cell{display: table-cell !important;}.d-print-flex{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important;}.d-print-inline-flex{display: -webkit-inline-box !important; display: -webkit-inline-flex !important; display: inline-flex !important;}.d-print-none{display: none !important;}.d-print-contents{display: contents !important;}}

/* /web/static/src/scss/bootstrap_review.scss */
 .alert{clear: both;}.card-body{background-color: rgba(255, 255, 255, 0.9) !important; color: #000000;}.card-body .text-muted{color: rgba(0, 0, 0, 0.7) !important;}.card-body:first-child{border-top-left-radius: calc(0.25rem - 1px); border-top-right-radius: calc(0.25rem - 1px);}.card-body:last-child{border-bottom-right-radius: calc(0.25rem - 1px); border-bottom-left-radius: calc(0.25rem - 1px);}.row.card-body{background-color: transparent !important;}.accordion .collapsing > .card-body:first-child, .accordion .collapse.show > .card-body:first-child{margin-top: 1px;}.toast-header{background-clip: border-box;}.toast-body{background-color: rgba(255, 255, 255, 0.93) !important; color: #000000;}.toast-body .text-muted, .o_colored_level .toast-body .text-muted{color: rgba(0, 0, 0, 0.7) !important;}@media (min-width: 576px){:not(.s_popup) > .modal .modal-dialog{height: 100%; padding: 1.75rem 0; margin: 0 auto;}:not(.s_popup) > .modal .modal-content{max-height: 100%;}:not(.s_popup) > .modal .modal-header, :not(.s_popup) > .modal .modal-footer{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;}:not(.s_popup) > .modal .modal-body{overflow: auto; min-height: 0;}}.modal-backdrop{display: none;}.modal:not([data-bs-backdrop="false"]){background-color: rgba(0, 0, 0, 0.5);}.form-check .form-check-input:not(:disabled):not(.o_wysiwyg_loader), .form-check .form-check-input:not(:disabled):not(.o_wysiwyg_loader) + label{cursor: pointer;}.form-check:hover, .form-check:hover .form-check-input:not(:disabled):not(.o_wysiwyg_loader){border-color: #714B67;}.form-select:where(:not(:disabled)):hover{border-color: #714B67;}.dropdown-menu[x-placement^="top"], .dropdown-menu[x-placement^="right"], .dropdown-menu[x-placement^="bottom"], .dropdown-menu[x-placement^="left"]{right: auto;}.popover{right: auto;}

/* /web/static/src/scss/bootstrap_review_frontend.scss */
 .bg-100{background-color: #F8F9FA !important; color: #000000;}.bg-100 .text-muted, .o_colored_level .bg-100 .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-100:hover, a.bg-100:focus, button.bg-100:hover, button.bg-100:focus{background-color: #dae0e5 !important; color: #000000;}.text-100{color: #F8F9FA !important;}a.text-100:hover, a.text-100:focus{color: #bdc6d0 !important;}.bg-200{background-color: #E9ECEF !important; color: #000000;}.bg-200 .text-muted, .o_colored_level .bg-200 .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-200:hover, a.bg-200:focus, button.bg-200:hover, button.bg-200:focus{background-color: #cbd3da !important; color: #000000;}.text-200{color: #E9ECEF !important;}a.text-200:hover, a.text-200:focus{color: #aeb9c4 !important;}.bg-300{background-color: #DEE2E6 !important; color: #000000;}.bg-300 .text-muted, .o_colored_level .bg-300 .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-300:hover, a.bg-300:focus, button.bg-300:hover, button.bg-300:focus{background-color: #c1c9d0 !important; color: #000000;}.text-300{color: #DEE2E6 !important;}a.text-300:hover, a.text-300:focus{color: #a4afba !important;}.bg-400{background-color: #CED4DA !important; color: #000000;}.bg-400 .text-muted, .o_colored_level .bg-400 .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-400:hover, a.bg-400:focus, button.bg-400:hover, button.bg-400:focus{background-color: #b1bbc4 !important; color: #000000;}.text-400{color: #CED4DA !important;}a.text-400:hover, a.text-400:focus{color: #94a1ae !important;}.bg-500{background-color: #ADB5BD !important; color: #000000;}.bg-500 .text-muted, .o_colored_level .bg-500 .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-500:hover, a.bg-500:focus, button.bg-500:hover, button.bg-500:focus{background-color: #919ca6 !important; color: #000000;}.text-500{color: #ADB5BD !important;}a.text-500:hover, a.text-500:focus{color: #748290 !important;}.bg-600{background-color: #6C757D !important; color: #FFFFFF;}.bg-600 .text-muted, .o_colored_level .bg-600 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}a.bg-600:hover, a.bg-600:focus, button.bg-600:hover, button.bg-600:focus{background-color: #545b62 !important; color: #FFFFFF;}.text-600{color: #6C757D !important;}a.text-600:hover, a.text-600:focus{color: #3d4246 !important;}.bg-700{background-color: #495057 !important; color: #FFFFFF;}.bg-700 .text-muted, .o_colored_level .bg-700 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}a.bg-700:hover, a.bg-700:focus, button.bg-700:hover, button.bg-700:focus{background-color: #32373b !important; color: #FFFFFF;}.text-700{color: #495057 !important;}a.text-700:hover, a.text-700:focus{color: #1a1d20 !important;}.bg-800{background-color: #343A40 !important; color: #FFFFFF;}.bg-800 .text-muted, .o_colored_level .bg-800 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}a.bg-800:hover, a.bg-800:focus, button.bg-800:hover, button.bg-800:focus{background-color: #1d2124 !important; color: #FFFFFF;}.text-800{color: #343A40 !important;}a.text-800:hover, a.text-800:focus{color: #060708 !important;}.bg-900{background-color: #212529 !important; color: #FFFFFF;}.bg-900 .text-muted, .o_colored_level .bg-900 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}a.bg-900:hover, a.bg-900:focus, button.bg-900:hover, button.bg-900:focus{background-color: #0a0c0d !important; color: #FFFFFF;}.text-900{color: #212529 !important;}a.text-900:hover, a.text-900:focus{color: black !important;}.bg-white-85{background-color: rgba(255, 255, 255, 0.85) !important; color: #000000;}.bg-white-85 .text-muted, .o_colored_level .bg-white-85 .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-white-85:hover, a.bg-white-85:focus, button.bg-white-85:hover, button.bg-white-85:focus{background-color: rgba(230, 230, 230, 0.85) !important; color: #000000;}.text-white-85{color: rgba(255, 255, 255, 0.85) !important;}a.text-white-85:hover, a.text-white-85:focus{color: rgba(204, 204, 204, 0.85) !important;}.bg-white-75{background-color: rgba(255, 255, 255, 0.75) !important; color: #000000;}.bg-white-75 .text-muted, .o_colored_level .bg-white-75 .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-white-75:hover, a.bg-white-75:focus, button.bg-white-75:hover, button.bg-white-75:focus{background-color: rgba(230, 230, 230, 0.75) !important; color: #000000;}.text-white-75{color: rgba(255, 255, 255, 0.75) !important;}a.text-white-75:hover, a.text-white-75:focus{color: rgba(204, 204, 204, 0.75) !important;}.bg-white-50{background-color: rgba(255, 255, 255, 0.5) !important; color: #000000;}.bg-white-50 .text-muted, .o_colored_level .bg-white-50 .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-white-50:hover, a.bg-white-50:focus, button.bg-white-50:hover, button.bg-white-50:focus{background-color: rgba(230, 230, 230, 0.5) !important; color: #000000;}.text-white-50{color: rgba(255, 255, 255, 0.5) !important;}a.text-white-50:hover, a.text-white-50:focus{color: rgba(204, 204, 204, 0.5) !important;}.bg-white-25{background-color: rgba(255, 255, 255, 0.25) !important;}a.bg-white-25:hover, a.bg-white-25:focus, button.bg-white-25:hover, button.bg-white-25:focus{background-color: rgba(230, 230, 230, 0.25) !important;}.text-white-25{color: rgba(255, 255, 255, 0.25) !important;}a.text-white-25:hover, a.text-white-25:focus{color: rgba(204, 204, 204, 0.25) !important;}.bg-black-75{background-color: rgba(0, 0, 0, 0.75) !important; color: #FFFFFF;}.bg-black-75 .text-muted, .o_colored_level .bg-black-75 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}a.bg-black-75:hover, a.bg-black-75:focus, button.bg-black-75:hover, button.bg-black-75:focus{background-color: rgba(0, 0, 0, 0.75) !important; color: #FFFFFF;}.text-black-75{color: rgba(0, 0, 0, 0.75) !important;}a.text-black-75:hover, a.text-black-75:focus{color: rgba(0, 0, 0, 0.75) !important;}.bg-black-50{background-color: rgba(0, 0, 0, 0.5) !important; color: #FFFFFF;}.bg-black-50 .text-muted, .o_colored_level .bg-black-50 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}a.bg-black-50:hover, a.bg-black-50:focus, button.bg-black-50:hover, button.bg-black-50:focus{background-color: rgba(0, 0, 0, 0.5) !important; color: #FFFFFF;}.text-black-50{color: rgba(0, 0, 0, 0.5) !important;}a.text-black-50:hover, a.text-black-50:focus{color: rgba(0, 0, 0, 0.5) !important;}.bg-black-25{background-color: rgba(0, 0, 0, 0.25) !important;}a.bg-black-25:hover, a.bg-black-25:focus, button.bg-black-25:hover, button.bg-black-25:focus{background-color: rgba(0, 0, 0, 0.25) !important;}.text-black-25{color: rgba(0, 0, 0, 0.25) !important;}a.text-black-25:hover, a.text-black-25:focus{color: rgba(0, 0, 0, 0.25) !important;}.bg-black-15{background-color: rgba(0, 0, 0, 0.15) !important;}a.bg-black-15:hover, a.bg-black-15:focus, button.bg-black-15:hover, button.bg-black-15:focus{background-color: rgba(0, 0, 0, 0.15) !important;}.text-black-15{color: rgba(0, 0, 0, 0.15) !important;}a.text-black-15:hover, a.text-black-15:focus{color: rgba(0, 0, 0, 0.15) !important;}.bg-black{background-color: #000000 !important; color: #FFFFFF;}.bg-black .text-muted, .o_colored_level .bg-black .text-muted{color: rgba(255, 255, 255, 0.7) !important;}a.bg-black:hover, a.bg-black:focus, button.bg-black:hover, button.bg-black:focus{background-color: black !important; color: #FFFFFF;}.text-black{color: #000000 !important;}a.text-black:hover, a.text-black:focus{color: black !important;}.bg-white{background-color: #FFFFFF !important; color: #000000;}.bg-white .text-muted, .o_colored_level .bg-white .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-white:hover, a.bg-white:focus, button.bg-white:hover, button.bg-white:focus{background-color: #e6e6e6 !important; color: #000000;}.text-white{color: #FFFFFF !important;}a.text-white:hover, a.text-white:focus{color: #cccccc !important;}.text-primary{color: #714B67 !important;}a.text-primary:hover, a.text-primary:focus{color: #34222f !important;}.text-secondary{color: #8595A2 !important;}a.text-secondary:hover, a.text-secondary:focus{color: #53626e !important;}.text-success{color: #28a745 !important;}a.text-success:hover, a.text-success:focus{color: #145523 !important;}.text-info{color: #17a2b8 !important;}a.text-info:hover, a.text-info:focus{color: #0c525d !important;}.text-warning{color: #ffc107 !important;}a.text-warning:hover, a.text-warning:focus{color: #a07800 !important;}.text-danger{color: #dc3545 !important;}a.text-danger:hover, a.text-danger:focus{color: #921925 !important;}.text-light{color: #eeeeee !important;}a.text-light:hover, a.text-light:focus{color: #bbbbbb !important;}.text-dark{color: #212529 !important;}a.text-dark:hover, a.text-dark:focus{color: black !important;}.btn-fill-primary, .btn-primary{color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.btn-fill-primary:hover, .btn-primary:hover{color: #FFFFFF; background-color: #604058; border-color: #5a3c52;}.btn-check:focus + .btn-fill-primary, .btn-check:focus + .btn-primary, .btn-fill-primary:focus, .btn-primary:focus{color: #FFFFFF; background-color: #604058; border-color: #5a3c52; box-shadow: 0 0 0 0.25rem rgba(134, 102, 126, 0.5);}.btn-check:checked + .btn-fill-primary, .btn-check:checked + .btn-primary, .btn-check:active + .btn-fill-primary, .btn-check:active + .btn-primary, .btn-fill-primary:active, .btn-primary:active, .btn-fill-primary.active, .active.btn-primary, .show > .btn-fill-primary.dropdown-toggle, .show > .dropdown-toggle.btn-primary{color: #FFFFFF; background-color: #5a3c52; border-color: #55384d;}.btn-check:checked + .btn-fill-primary:focus, .btn-check:checked + .btn-primary:focus, .btn-check:active + .btn-fill-primary:focus, .btn-check:active + .btn-primary:focus, .btn-fill-primary:active:focus, .btn-primary:active:focus, .btn-fill-primary.active:focus, .active.btn-primary:focus, .show > .btn-fill-primary.dropdown-toggle:focus, .show > .dropdown-toggle.btn-primary:focus{box-shadow: 0 0 0 0.25rem rgba(134, 102, 126, 0.5);}.btn-fill-primary:disabled, .btn-fill-primary.o_wysiwyg_loader, .btn-primary:disabled, .btn-primary.o_wysiwyg_loader, .btn-fill-primary.disabled, .disabled.btn-primary{color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.btn-fill-secondary, .btn-secondary{color: #FFFFFF; background-color: #8595A2; border-color: #8595A2;}.btn-fill-secondary:hover, .btn-secondary:hover{color: #FFFFFF; background-color: #717f8a; border-color: #6a7782;}.btn-check:focus + .btn-fill-secondary, .btn-check:focus + .btn-secondary, .btn-fill-secondary:focus, .btn-secondary:focus{color: #FFFFFF; background-color: #717f8a; border-color: #6a7782; box-shadow: 0 0 0 0.25rem rgba(151, 165, 176, 0.5);}.btn-check:checked + .btn-fill-secondary, .btn-check:checked + .btn-secondary, .btn-check:active + .btn-fill-secondary, .btn-check:active + .btn-secondary, .btn-fill-secondary:active, .btn-secondary:active, .btn-fill-secondary.active, .active.btn-secondary, .show > .btn-fill-secondary.dropdown-toggle, .show > .dropdown-toggle.btn-secondary{color: #FFFFFF; background-color: #6a7782; border-color: #64707a;}.btn-check:checked + .btn-fill-secondary:focus, .btn-check:checked + .btn-secondary:focus, .btn-check:active + .btn-fill-secondary:focus, .btn-check:active + .btn-secondary:focus, .btn-fill-secondary:active:focus, .btn-secondary:active:focus, .btn-fill-secondary.active:focus, .active.btn-secondary:focus, .show > .btn-fill-secondary.dropdown-toggle:focus, .show > .dropdown-toggle.btn-secondary:focus{box-shadow: 0 0 0 0.25rem rgba(151, 165, 176, 0.5);}.btn-fill-secondary:disabled, .btn-fill-secondary.o_wysiwyg_loader, .btn-secondary:disabled, .btn-secondary.o_wysiwyg_loader, .btn-fill-secondary.disabled, .disabled.btn-secondary{color: #FFFFFF; background-color: #8595A2; border-color: #8595A2;}.btn-fill-success, .btn-success{color: #FFFFFF; background-color: #28a745; border-color: #28a745;}.btn-fill-success:hover, .btn-success:hover{color: #FFFFFF; background-color: #228e3b; border-color: #208637;}.btn-check:focus + .btn-fill-success, .btn-check:focus + .btn-success, .btn-fill-success:focus, .btn-success:focus{color: #FFFFFF; background-color: #228e3b; border-color: #208637; box-shadow: 0 0 0 0.25rem rgba(72, 180, 97, 0.5);}.btn-check:checked + .btn-fill-success, .btn-check:checked + .btn-success, .btn-check:active + .btn-fill-success, .btn-check:active + .btn-success, .btn-fill-success:active, .btn-success:active, .btn-fill-success.active, .active.btn-success, .show > .btn-fill-success.dropdown-toggle, .show > .dropdown-toggle.btn-success{color: #FFFFFF; background-color: #208637; border-color: #1e7d34;}.btn-check:checked + .btn-fill-success:focus, .btn-check:checked + .btn-success:focus, .btn-check:active + .btn-fill-success:focus, .btn-check:active + .btn-success:focus, .btn-fill-success:active:focus, .btn-success:active:focus, .btn-fill-success.active:focus, .active.btn-success:focus, .show > .btn-fill-success.dropdown-toggle:focus, .show > .dropdown-toggle.btn-success:focus{box-shadow: 0 0 0 0.25rem rgba(72, 180, 97, 0.5);}.btn-fill-success:disabled, .btn-fill-success.o_wysiwyg_loader, .btn-success:disabled, .btn-success.o_wysiwyg_loader, .btn-fill-success.disabled, .disabled.btn-success{color: #FFFFFF; background-color: #28a745; border-color: #28a745;}.btn-fill-info, .btn-info{color: #FFFFFF; background-color: #17a2b8; border-color: #17a2b8;}.btn-fill-info:hover, .btn-info:hover{color: #FFFFFF; background-color: #148a9c; border-color: #128293;}.btn-check:focus + .btn-fill-info, .btn-check:focus + .btn-info, .btn-fill-info:focus, .btn-info:focus{color: #FFFFFF; background-color: #148a9c; border-color: #128293; box-shadow: 0 0 0 0.25rem rgba(58, 176, 195, 0.5);}.btn-check:checked + .btn-fill-info, .btn-check:checked + .btn-info, .btn-check:active + .btn-fill-info, .btn-check:active + .btn-info, .btn-fill-info:active, .btn-info:active, .btn-fill-info.active, .active.btn-info, .show > .btn-fill-info.dropdown-toggle, .show > .dropdown-toggle.btn-info{color: #FFFFFF; background-color: #128293; border-color: #117a8a;}.btn-check:checked + .btn-fill-info:focus, .btn-check:checked + .btn-info:focus, .btn-check:active + .btn-fill-info:focus, .btn-check:active + .btn-info:focus, .btn-fill-info:active:focus, .btn-info:active:focus, .btn-fill-info.active:focus, .active.btn-info:focus, .show > .btn-fill-info.dropdown-toggle:focus, .show > .dropdown-toggle.btn-info:focus{box-shadow: 0 0 0 0.25rem rgba(58, 176, 195, 0.5);}.btn-fill-info:disabled, .btn-fill-info.o_wysiwyg_loader, .btn-info:disabled, .btn-info.o_wysiwyg_loader, .btn-fill-info.disabled, .disabled.btn-info{color: #FFFFFF; background-color: #17a2b8; border-color: #17a2b8;}.btn-fill-warning, .btn-warning{color: #000000; background-color: #ffc107; border-color: #ffc107;}.btn-fill-warning:hover, .btn-warning:hover{color: #000000; background-color: #ffca2c; border-color: #ffc720;}.btn-check:focus + .btn-fill-warning, .btn-check:focus + .btn-warning, .btn-fill-warning:focus, .btn-warning:focus{color: #000000; background-color: #ffca2c; border-color: #ffc720; box-shadow: 0 0 0 0.25rem rgba(217, 164, 6, 0.5);}.btn-check:checked + .btn-fill-warning, .btn-check:checked + .btn-warning, .btn-check:active + .btn-fill-warning, .btn-check:active + .btn-warning, .btn-fill-warning:active, .btn-warning:active, .btn-fill-warning.active, .active.btn-warning, .show > .btn-fill-warning.dropdown-toggle, .show > .dropdown-toggle.btn-warning{color: #000000; background-color: #ffcd39; border-color: #ffc720;}.btn-check:checked + .btn-fill-warning:focus, .btn-check:checked + .btn-warning:focus, .btn-check:active + .btn-fill-warning:focus, .btn-check:active + .btn-warning:focus, .btn-fill-warning:active:focus, .btn-warning:active:focus, .btn-fill-warning.active:focus, .active.btn-warning:focus, .show > .btn-fill-warning.dropdown-toggle:focus, .show > .dropdown-toggle.btn-warning:focus{box-shadow: 0 0 0 0.25rem rgba(217, 164, 6, 0.5);}.btn-fill-warning:disabled, .btn-fill-warning.o_wysiwyg_loader, .btn-warning:disabled, .btn-warning.o_wysiwyg_loader, .btn-fill-warning.disabled, .disabled.btn-warning{color: #000000; background-color: #ffc107; border-color: #ffc107;}.btn-fill-danger, .btn-danger{color: #FFFFFF; background-color: #dc3545; border-color: #dc3545;}.btn-fill-danger:hover, .btn-danger:hover{color: #FFFFFF; background-color: #bb2d3b; border-color: #b02a37;}.btn-check:focus + .btn-fill-danger, .btn-check:focus + .btn-danger, .btn-fill-danger:focus, .btn-danger:focus{color: #FFFFFF; background-color: #bb2d3b; border-color: #b02a37; box-shadow: 0 0 0 0.25rem rgba(225, 83, 97, 0.5);}.btn-check:checked + .btn-fill-danger, .btn-check:checked + .btn-danger, .btn-check:active + .btn-fill-danger, .btn-check:active + .btn-danger, .btn-fill-danger:active, .btn-danger:active, .btn-fill-danger.active, .active.btn-danger, .show > .btn-fill-danger.dropdown-toggle, .show > .dropdown-toggle.btn-danger{color: #FFFFFF; background-color: #b02a37; border-color: #a52834;}.btn-check:checked + .btn-fill-danger:focus, .btn-check:checked + .btn-danger:focus, .btn-check:active + .btn-fill-danger:focus, .btn-check:active + .btn-danger:focus, .btn-fill-danger:active:focus, .btn-danger:active:focus, .btn-fill-danger.active:focus, .active.btn-danger:focus, .show > .btn-fill-danger.dropdown-toggle:focus, .show > .dropdown-toggle.btn-danger:focus{box-shadow: 0 0 0 0.25rem rgba(225, 83, 97, 0.5);}.btn-fill-danger:disabled, .btn-fill-danger.o_wysiwyg_loader, .btn-danger:disabled, .btn-danger.o_wysiwyg_loader, .btn-fill-danger.disabled, .disabled.btn-danger{color: #FFFFFF; background-color: #dc3545; border-color: #dc3545;}.btn-fill-light, .btn-light{color: #000000; background-color: #eeeeee; border-color: #eeeeee;}.btn-fill-light:hover, .btn-light:hover{color: #000000; background-color: #f1f1f1; border-color: #f0f0f0;}.btn-check:focus + .btn-fill-light, .btn-check:focus + .btn-light, .btn-fill-light:focus, .btn-light:focus{color: #000000; background-color: #f1f1f1; border-color: #f0f0f0; box-shadow: 0 0 0 0.25rem rgba(202, 202, 202, 0.5);}.btn-check:checked + .btn-fill-light, .btn-check:checked + .btn-light, .btn-check:active + .btn-fill-light, .btn-check:active + .btn-light, .btn-fill-light:active, .btn-light:active, .btn-fill-light.active, .active.btn-light, .show > .btn-fill-light.dropdown-toggle, .show > .dropdown-toggle.btn-light{color: #000000; background-color: #f1f1f1; border-color: #f0f0f0;}.btn-check:checked + .btn-fill-light:focus, .btn-check:checked + .btn-light:focus, .btn-check:active + .btn-fill-light:focus, .btn-check:active + .btn-light:focus, .btn-fill-light:active:focus, .btn-light:active:focus, .btn-fill-light.active:focus, .active.btn-light:focus, .show > .btn-fill-light.dropdown-toggle:focus, .show > .dropdown-toggle.btn-light:focus{box-shadow: 0 0 0 0.25rem rgba(202, 202, 202, 0.5);}.btn-fill-light:disabled, .btn-fill-light.o_wysiwyg_loader, .btn-light:disabled, .btn-light.o_wysiwyg_loader, .btn-fill-light.disabled, .disabled.btn-light{color: #000000; background-color: #eeeeee; border-color: #eeeeee;}.btn-fill-dark, .btn-dark{color: #FFFFFF; background-color: #212529; border-color: #212529;}.btn-fill-dark:hover, .btn-dark:hover{color: #FFFFFF; background-color: #1c1f23; border-color: #1a1e21;}.btn-check:focus + .btn-fill-dark, .btn-check:focus + .btn-dark, .btn-fill-dark:focus, .btn-dark:focus{color: #FFFFFF; background-color: #1c1f23; border-color: #1a1e21; box-shadow: 0 0 0 0.25rem rgba(66, 70, 73, 0.5);}.btn-check:checked + .btn-fill-dark, .btn-check:checked + .btn-dark, .btn-check:active + .btn-fill-dark, .btn-check:active + .btn-dark, .btn-fill-dark:active, .btn-dark:active, .btn-fill-dark.active, .active.btn-dark, .show > .btn-fill-dark.dropdown-toggle, .show > .dropdown-toggle.btn-dark{color: #FFFFFF; background-color: #1a1e21; border-color: #191c1f;}.btn-check:checked + .btn-fill-dark:focus, .btn-check:checked + .btn-dark:focus, .btn-check:active + .btn-fill-dark:focus, .btn-check:active + .btn-dark:focus, .btn-fill-dark:active:focus, .btn-dark:active:focus, .btn-fill-dark.active:focus, .active.btn-dark:focus, .show > .btn-fill-dark.dropdown-toggle:focus, .show > .dropdown-toggle.btn-dark:focus{box-shadow: 0 0 0 0.25rem rgba(66, 70, 73, 0.5);}.btn-fill-dark:disabled, .btn-fill-dark.o_wysiwyg_loader, .btn-dark:disabled, .btn-dark.o_wysiwyg_loader, .btn-fill-dark.disabled, .disabled.btn-dark{color: #FFFFFF; background-color: #212529; border-color: #212529;}.btn-outline-primary{color: #714B67; border-color: #714B67;}.btn-outline-primary:hover{color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.btn-check:focus + .btn-outline-primary, .btn-outline-primary:focus{box-shadow: 0 0 0 0.25rem rgba(113, 75, 103, 0.5);}.btn-check:checked + .btn-outline-primary, .btn-check:active + .btn-outline-primary, .btn-outline-primary:active, .btn-outline-primary.active, .btn-outline-primary.dropdown-toggle.show{color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.btn-check:checked + .btn-outline-primary:focus, .btn-check:active + .btn-outline-primary:focus, .btn-outline-primary:active:focus, .btn-outline-primary.active:focus, .btn-outline-primary.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(113, 75, 103, 0.5);}.btn-outline-primary:disabled, .btn-outline-primary.o_wysiwyg_loader, .btn-outline-primary.disabled{color: #714B67; background-color: transparent;}.btn-outline-secondary{color: #8595A2; border-color: #8595A2;}.btn-outline-secondary:hover{color: #FFFFFF; background-color: #8595A2; border-color: #8595A2;}.btn-check:focus + .btn-outline-secondary, .btn-outline-secondary:focus{box-shadow: 0 0 0 0.25rem rgba(133, 149, 162, 0.5);}.btn-check:checked + .btn-outline-secondary, .btn-check:active + .btn-outline-secondary, .btn-outline-secondary:active, .btn-outline-secondary.active, .btn-outline-secondary.dropdown-toggle.show{color: #FFFFFF; background-color: #8595A2; border-color: #8595A2;}.btn-check:checked + .btn-outline-secondary:focus, .btn-check:active + .btn-outline-secondary:focus, .btn-outline-secondary:active:focus, .btn-outline-secondary.active:focus, .btn-outline-secondary.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(133, 149, 162, 0.5);}.btn-outline-secondary:disabled, .btn-outline-secondary.o_wysiwyg_loader, .btn-outline-secondary.disabled{color: #8595A2; background-color: transparent;}.btn-outline-success{color: #28a745; border-color: #28a745;}.btn-outline-success:hover{color: #FFFFFF; background-color: #28a745; border-color: #28a745;}.btn-check:focus + .btn-outline-success, .btn-outline-success:focus{box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.5);}.btn-check:checked + .btn-outline-success, .btn-check:active + .btn-outline-success, .btn-outline-success:active, .btn-outline-success.active, .btn-outline-success.dropdown-toggle.show{color: #FFFFFF; background-color: #28a745; border-color: #28a745;}.btn-check:checked + .btn-outline-success:focus, .btn-check:active + .btn-outline-success:focus, .btn-outline-success:active:focus, .btn-outline-success.active:focus, .btn-outline-success.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(40, 167, 69, 0.5);}.btn-outline-success:disabled, .btn-outline-success.o_wysiwyg_loader, .btn-outline-success.disabled{color: #28a745; background-color: transparent;}.btn-outline-info{color: #17a2b8; border-color: #17a2b8;}.btn-outline-info:hover{color: #FFFFFF; background-color: #17a2b8; border-color: #17a2b8;}.btn-check:focus + .btn-outline-info, .btn-outline-info:focus{box-shadow: 0 0 0 0.25rem rgba(23, 162, 184, 0.5);}.btn-check:checked + .btn-outline-info, .btn-check:active + .btn-outline-info, .btn-outline-info:active, .btn-outline-info.active, .btn-outline-info.dropdown-toggle.show{color: #FFFFFF; background-color: #17a2b8; border-color: #17a2b8;}.btn-check:checked + .btn-outline-info:focus, .btn-check:active + .btn-outline-info:focus, .btn-outline-info:active:focus, .btn-outline-info.active:focus, .btn-outline-info.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(23, 162, 184, 0.5);}.btn-outline-info:disabled, .btn-outline-info.o_wysiwyg_loader, .btn-outline-info.disabled{color: #17a2b8; background-color: transparent;}.btn-outline-warning{color: #ffc107; border-color: #ffc107;}.btn-outline-warning:hover{color: #000000; background-color: #ffc107; border-color: #ffc107;}.btn-check:focus + .btn-outline-warning, .btn-outline-warning:focus{box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.5);}.btn-check:checked + .btn-outline-warning, .btn-check:active + .btn-outline-warning, .btn-outline-warning:active, .btn-outline-warning.active, .btn-outline-warning.dropdown-toggle.show{color: #000000; background-color: #ffc107; border-color: #ffc107;}.btn-check:checked + .btn-outline-warning:focus, .btn-check:active + .btn-outline-warning:focus, .btn-outline-warning:active:focus, .btn-outline-warning.active:focus, .btn-outline-warning.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(255, 193, 7, 0.5);}.btn-outline-warning:disabled, .btn-outline-warning.o_wysiwyg_loader, .btn-outline-warning.disabled{color: #ffc107; background-color: transparent;}.btn-outline-danger{color: #dc3545; border-color: #dc3545;}.btn-outline-danger:hover{color: #FFFFFF; background-color: #dc3545; border-color: #dc3545;}.btn-check:focus + .btn-outline-danger, .btn-outline-danger:focus{box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.5);}.btn-check:checked + .btn-outline-danger, .btn-check:active + .btn-outline-danger, .btn-outline-danger:active, .btn-outline-danger.active, .btn-outline-danger.dropdown-toggle.show{color: #FFFFFF; background-color: #dc3545; border-color: #dc3545;}.btn-check:checked + .btn-outline-danger:focus, .btn-check:active + .btn-outline-danger:focus, .btn-outline-danger:active:focus, .btn-outline-danger.active:focus, .btn-outline-danger.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(220, 53, 69, 0.5);}.btn-outline-danger:disabled, .btn-outline-danger.o_wysiwyg_loader, .btn-outline-danger.disabled{color: #dc3545; background-color: transparent;}.btn-outline-light{color: #eeeeee; border-color: #eeeeee;}.btn-outline-light:hover{color: #000000; background-color: #eeeeee; border-color: #eeeeee;}.btn-check:focus + .btn-outline-light, .btn-outline-light:focus{box-shadow: 0 0 0 0.25rem rgba(238, 238, 238, 0.5);}.btn-check:checked + .btn-outline-light, .btn-check:active + .btn-outline-light, .btn-outline-light:active, .btn-outline-light.active, .btn-outline-light.dropdown-toggle.show{color: #000000; background-color: #eeeeee; border-color: #eeeeee;}.btn-check:checked + .btn-outline-light:focus, .btn-check:active + .btn-outline-light:focus, .btn-outline-light:active:focus, .btn-outline-light.active:focus, .btn-outline-light.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(238, 238, 238, 0.5);}.btn-outline-light:disabled, .btn-outline-light.o_wysiwyg_loader, .btn-outline-light.disabled{color: #eeeeee; background-color: transparent;}.btn-outline-dark{color: #212529; border-color: #212529;}.btn-outline-dark:hover{color: #FFFFFF; background-color: #212529; border-color: #212529;}.btn-check:focus + .btn-outline-dark, .btn-outline-dark:focus{box-shadow: 0 0 0 0.25rem rgba(33, 37, 41, 0.5);}.btn-check:checked + .btn-outline-dark, .btn-check:active + .btn-outline-dark, .btn-outline-dark:active, .btn-outline-dark.active, .btn-outline-dark.dropdown-toggle.show{color: #FFFFFF; background-color: #212529; border-color: #212529;}.btn-check:checked + .btn-outline-dark:focus, .btn-check:active + .btn-outline-dark:focus, .btn-outline-dark:active:focus, .btn-outline-dark.active:focus, .btn-outline-dark.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(33, 37, 41, 0.5);}.btn-outline-dark:disabled, .btn-outline-dark.o_wysiwyg_loader, .btn-outline-dark.disabled{color: #212529; background-color: transparent;}.btn-group .btn-light.active{box-shadow: inset 0 0 0 1px #714B67; border-color: transparent; background-color: #e2dee1;}.navbar-dark .navbar-toggler{padding-left: 0; padding-right: 0;}.navbar-light .navbar-toggler{padding-left: 0; padding-right: 0;}.navbar-nav.nav-pills .nav-link{padding-right: 1rem; padding-left: 1rem;}.carousel-control-next .visually-hidden{left: 50%;}.pagination{font-variant-numeric: tabular-nums;}.modal-content{color: #495057;}.modal-content:where(:not(.oe_structure)) .form-control{background-color: #FFFFFF; color: #000000;}.modal-content:where(:not(.oe_structure)) .form-select{background-color: #FFFFFF; color: #000000;}.modal-content:where(:not(.oe_structure)) .form-check-input:not(:checked){background-color: #FFFFFF;}.modal-content .text-muted{color: #6C757D !important;}.popover .popover-header{color: #495057;}.popover .popover-body{color: #495057;}.form-check-input{color: inherit;}.form-control.bg-light{color: #495057;}.input-group-text{color: #495057;}.form-control::file-selector-button{color: #495057;}.offcanvas{color: #495057;}.offcanvas .form-check-input:where(:not(:checked)){background-color: #FFFFFF;}.offcanvas .form-range::-webkit-slider-thumb:where(:not(:active)){background-color: #FFFFFF;}.offcanvas .form-range::-moz-range-thumb:where(:not(:active)){background-color: #FFFFFF;}.input-group .btn:first-child, .input-group .btn:last-child{border-radius: 0.25rem;}.dropdown .dropdown-menu .text-muted{color: #6C757D !important;}.form-select{background-clip: padding-box;}.form-select:disabled:not([multiple]):where(:not([size]), [size="1"]), .form-select.o_wysiwyg_loader:not([multiple]):where(:not([size]), [size="1"]){background-image: url("data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'><path fill='none' stroke='%23a4a8ab' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/></svg>");}.form-control:disabled, .form-control.o_wysiwyg_loader, .form-control[readonly]{color: #a4a8ab;}.form-check-input:disabled:not(:checked), .form-check-input.o_wysiwyg_loader:not(:checked){background-color: #E9ECEF;}.form-switch .form-check-input:disabled:not(:checked), .form-switch .form-check-input.o_wysiwyg_loader:not(:checked){background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23a4a8ab'/%3e%3c/svg%3e");}.form-range:disabled::-webkit-slider-thumb, .form-range.o_wysiwyg_loader::-webkit-slider-thumb{border-color: rgba(73, 80, 87, 0.15);}

/* /web/static/src/libs/fontawesome/css/font-awesome.css */
 @font-face{font-family: 'FontAwesome'; src: url('/web/static/src/libs/fontawesome/css/../fonts/fontawesome-webfont.woff2?v=4.7.0') format('woff2'), url('/web/static/src/libs/fontawesome/css/../fonts/fontawesome-webfont.woff?v=4.7.0') format('woff'); font-weight: normal; font-style: normal; font-display: block;}.fa{display: inline-block; font: normal normal normal 14px/1 FontAwesome; font-size: inherit; text-rendering: auto; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;}.fa-lg{font-size: 1.315em; vertical-align: -6%;}.fa-2x{font-size: 2em;}.fa-3x{font-size: 3em;}.fa-4x{font-size: 4em;}.fa-5x{font-size: 5em;}.fa-fw{width: 1.28571429em; text-align: center;}.fa-ul{padding-left: 0; margin-left: 2.14285714em; list-style-type: none;}.fa-ul > li{position: relative;}.fa-li{position: absolute; left: -2.14285714em; width: 2.14285714em; top: 0.14285714em; text-align: center;}.fa-li.fa-lg{left: -1.85714286em;}.fa-border{padding: .2em .25em .15em; border: solid 0.08em #eeeeee; border-radius: .1em;}.fa-pull-left{float: left;}.fa-pull-right{float: right;}.fa.fa-pull-left{margin-right: .3em;}.fa.fa-pull-right{margin-left: .3em;}.fa-spin{animation: fa-spin 2s infinite linear;}.fa-pulse{animation: fa-spin 1s infinite steps(8);}@keyframes fa-spin{0%{transform: rotate(0deg);}100%{transform: rotate(359deg);}}.fa-rotate-90{transform: rotate(90deg);}.fa-rotate-180{transform: rotate(180deg);}.fa-rotate-270{transform: rotate(270deg);}.fa-flip-horizontal{transform: scale(-1, 1);}.fa-flip-vertical{transform: scale(1, -1);}:root .fa-rotate-90, :root .fa-rotate-180, :root .fa-rotate-270, :root .fa-flip-horizontal, :root .fa-flip-vertical{filter: none;}.fa-stack{position: relative; display: inline-block; width: 2em; height: 2em; line-height: 2em; vertical-align: middle;}.fa-stack-1x, .fa-stack-2x{position: absolute; left: 0; width: 100%; text-align: center;}.fa-stack-1x{line-height: inherit;}.fa-stack-2x{font-size: 2em;}.fa-inverse{color: #ffffff;}.fa-glass:before{content: "\f000";}.fa-music:before{content: "\f001";}.fa-search:before{content: "\f002";}.fa-envelope-o:before{content: "\f003";}.fa-heart:before{content: "\f004";}.fa-star:before{content: "\f005";}.fa-star-o:before{content: "\f006";}.fa-user:before{content: "\f007";}.fa-film:before{content: "\f008";}.fa-th-large:before{content: "\f009";}.fa-th:before{content: "\f00a";}.fa-th-list:before{content: "\f00b";}.fa-check:before{content: "\f00c";}.fa-remove:before, .fa-close:before, .fa-times:before{content: "\f00d";}.fa-search-plus:before{content: "\f00e";}.fa-search-minus:before{content: "\f010";}.fa-power-off:before{content: "\f011";}.fa-signal:before{content: "\f012";}.fa-gear:before, .fa-cog:before{content: "\f013";}.fa-trash-o:before{content: "\f014";}.fa-home:before{content: "\f015";}.fa-file-o:before{content: "\f016";}.fa-clock-o:before{content: "\f017";}.fa-road:before{content: "\f018";}.fa-download:before{content: "\f019";}.fa-arrow-circle-o-down:before{content: "\f01a";}.fa-arrow-circle-o-up:before{content: "\f01b";}.fa-inbox:before{content: "\f01c";}.fa-play-circle-o:before{content: "\f01d";}.fa-rotate-right:before, .fa-repeat:before{content: "\f01e";}.fa-refresh:before{content: "\f021";}.fa-list-alt:before{content: "\f022";}.fa-lock:before{content: "\f023";}.fa-flag:before{content: "\f024";}.fa-headphones:before{content: "\f025";}.fa-volume-off:before{content: "\f026";}.fa-volume-down:before{content: "\f027";}.fa-volume-up:before{content: "\f028";}.fa-qrcode:before{content: "\f029";}.fa-barcode:before{content: "\f02a";}.fa-tag:before{content: "\f02b";}.fa-tags:before{content: "\f02c";}.fa-book:before{content: "\f02d";}.fa-bookmark:before{content: "\f02e";}.fa-print:before{content: "\f02f";}.fa-camera:before{content: "\f030";}.fa-font:before{content: "\f031";}.fa-bold:before{content: "\f032";}.fa-italic:before{content: "\f033";}.fa-text-height:before{content: "\f034";}.fa-text-width:before{content: "\f035";}.fa-align-left:before{content: "\f036";}.fa-align-center:before{content: "\f037";}.fa-align-right:before{content: "\f038";}.fa-align-justify:before{content: "\f039";}.fa-list:before{content: "\f03a";}.fa-dedent:before, .fa-outdent:before{content: "\f03b";}.fa-indent:before{content: "\f03c";}.fa-video-camera:before{content: "\f03d";}.fa-photo:before, .fa-image:before, .fa-picture-o:before{content: "\f03e";}.fa-pencil:before{content: "\f040";}.fa-map-marker:before{content: "\f041";}.fa-adjust:before{content: "\f042";}.fa-tint:before{content: "\f043";}.fa-edit:before, .fa-pencil-square-o:before{content: "\f044";}.fa-share-square-o:before{content: "\f045";}.fa-check-square-o:before{content: "\f046";}.fa-arrows:before{content: "\f047";}.fa-step-backward:before{content: "\f048";}.fa-fast-backward:before{content: "\f049";}.fa-backward:before{content: "\f04a";}.fa-play:before{content: "\f04b";}.fa-pause:before{content: "\f04c";}.fa-stop:before{content: "\f04d";}.fa-forward:before{content: "\f04e";}.fa-fast-forward:before{content: "\f050";}.fa-step-forward:before{content: "\f051";}.fa-eject:before{content: "\f052";}.fa-chevron-left:before{content: "\f053";}.fa-chevron-right:before{content: "\f054";}.fa-plus-circle:before{content: "\f055";}.fa-minus-circle:before{content: "\f056";}.fa-times-circle:before{content: "\f057";}.fa-check-circle:before{content: "\f058";}.fa-question-circle:before{content: "\f059";}.fa-info-circle:before{content: "\f05a";}.fa-crosshairs:before{content: "\f05b";}.fa-times-circle-o:before{content: "\f05c";}.fa-check-circle-o:before{content: "\f05d";}.fa-ban:before{content: "\f05e";}.fa-arrow-left:before{content: "\f060";}.fa-arrow-right:before{content: "\f061";}.fa-arrow-up:before{content: "\f062";}.fa-arrow-down:before{content: "\f063";}.fa-mail-forward:before, .fa-share:before{content: "\f064";}.fa-expand:before{content: "\f065";}.fa-compress:before{content: "\f066";}.fa-plus:before{content: "\f067";}.fa-minus:before{content: "\f068";}.fa-asterisk:before{content: "\f069";}.fa-exclamation-circle:before{content: "\f06a";}.fa-gift:before{content: "\f06b";}.fa-leaf:before{content: "\f06c";}.fa-fire:before{content: "\f06d";}.fa-eye:before{content: "\f06e";}.fa-eye-slash:before{content: "\f070";}.fa-warning:before, .fa-exclamation-triangle:before{content: "\f071";}.fa-plane:before{content: "\f072";}.fa-calendar:before{content: "\f073";}.fa-random:before{content: "\f074";}.fa-comment:before{content: "\f075";}.fa-magnet:before{content: "\f076";}.fa-chevron-up:before{content: "\f077";}.fa-chevron-down:before{content: "\f078";}.fa-retweet:before{content: "\f079";}.fa-shopping-cart:before{content: "\f07a";}.fa-folder:before{content: "\f07b";}.fa-folder-open:before{content: "\f07c";}.fa-arrows-v:before{content: "\f07d";}.fa-arrows-h:before{content: "\f07e";}.fa-bar-chart-o:before, .fa-bar-chart:before{content: "\f080";}.fa-twitter-square:before{content: "\f081";}.fa-facebook-square:before{content: "\f082";}.fa-camera-retro:before{content: "\f083";}.fa-key:before{content: "\f084";}.fa-gears:before, .fa-cogs:before{content: "\f085";}.fa-comments:before{content: "\f086";}.fa-thumbs-o-up:before{content: "\f087";}.fa-thumbs-o-down:before{content: "\f088";}.fa-star-half:before{content: "\f089";}.fa-heart-o:before{content: "\f08a";}.fa-sign-out:before{content: "\f08b";}.fa-linkedin-square:before{content: "\f08c";}.fa-thumb-tack:before{content: "\f08d";}.fa-external-link:before{content: "\f08e";}.fa-sign-in:before{content: "\f090";}.fa-trophy:before{content: "\f091";}.fa-github-square:before{content: "\f092";}.fa-upload:before{content: "\f093";}.fa-lemon-o:before{content: "\f094";}.fa-phone:before{content: "\f095";}.fa-square-o:before{content: "\f096";}.fa-bookmark-o:before{content: "\f097";}.fa-phone-square:before{content: "\f098";}.fa-twitter:before{content: "\f099";}.fa-facebook-f:before, .fa-facebook:before{content: "\f09a";}.fa-github:before{content: "\f09b";}.fa-unlock:before{content: "\f09c";}.fa-credit-card:before{content: "\f09d";}.fa-feed:before, .fa-rss:before{content: "\f09e";}.fa-hdd-o:before{content: "\f0a0";}.fa-bullhorn:before{content: "\f0a1";}.fa-bell:before{content: "\f0f3";}.fa-certificate:before{content: "\f0a3";}.fa-hand-o-right:before{content: "\f0a4";}.fa-hand-o-left:before{content: "\f0a5";}.fa-hand-o-up:before{content: "\f0a6";}.fa-hand-o-down:before{content: "\f0a7";}.fa-arrow-circle-left:before{content: "\f0a8";}.fa-arrow-circle-right:before{content: "\f0a9";}.fa-arrow-circle-up:before{content: "\f0aa";}.fa-arrow-circle-down:before{content: "\f0ab";}.fa-globe:before{content: "\f0ac";}.fa-wrench:before{content: "\f0ad";}.fa-tasks:before{content: "\f0ae";}.fa-filter:before{content: "\f0b0";}.fa-briefcase:before{content: "\f0b1";}.fa-arrows-alt:before{content: "\f0b2";}.fa-group:before, .fa-users:before{content: "\f0c0";}.fa-chain:before, .fa-link:before{content: "\f0c1";}.fa-cloud:before{content: "\f0c2";}.fa-flask:before{content: "\f0c3";}.fa-cut:before, .fa-scissors:before{content: "\f0c4";}.fa-copy:before, .fa-files-o:before{content: "\f0c5";}.fa-paperclip:before{content: "\f0c6";}.fa-save:before, .fa-floppy-o:before{content: "\f0c7";}.fa-square:before{content: "\f0c8";}.fa-navicon:before, .fa-reorder:before, .fa-bars:before{content: "\f0c9";}.fa-list-ul:before{content: "\f0ca";}.fa-list-ol:before{content: "\f0cb";}.fa-strikethrough:before{content: "\f0cc";}.fa-underline:before{content: "\f0cd";}.fa-table:before{content: "\f0ce";}.fa-magic:before{content: "\f0d0";}.fa-truck:before{content: "\f0d1";}.fa-pinterest:before{content: "\f0d2";}.fa-pinterest-square:before{content: "\f0d3";}.fa-google-plus-square:before{content: "\f0d4";}.fa-google-plus:before{content: "\f0d5";}.fa-money:before{content: "\f0d6";}.fa-caret-down:before{content: "\f0d7";}.fa-caret-up:before{content: "\f0d8";}.fa-caret-left:before{content: "\f0d9";}.fa-caret-right:before{content: "\f0da";}.fa-columns:before{content: "\f0db";}.fa-unsorted:before, .fa-sort:before{content: "\f0dc";}.fa-sort-down:before, .fa-sort-desc:before{content: "\f0dd";}.fa-sort-up:before, .fa-sort-asc:before{content: "\f0de";}.fa-envelope:before{content: "\f0e0";}.fa-linkedin:before{content: "\f0e1";}.fa-rotate-left:before, .fa-undo:before{content: "\f0e2";}.fa-legal:before, .fa-gavel:before{content: "\f0e3";}.fa-dashboard:before, .fa-tachometer:before{content: "\f0e4";}.fa-comment-o:before{content: "\f0e5";}.fa-comments-o:before{content: "\f0e6";}.fa-flash:before, .fa-bolt:before{content: "\f0e7";}.fa-sitemap:before{content: "\f0e8";}.fa-umbrella:before{content: "\f0e9";}.fa-paste:before, .fa-clipboard:before{content: "\f0ea";}.fa-lightbulb-o:before{content: "\f0eb";}.fa-exchange:before{content: "\f0ec";}.fa-cloud-download:before{content: "\f0ed";}.fa-cloud-upload:before{content: "\f0ee";}.fa-user-md:before{content: "\f0f0";}.fa-stethoscope:before{content: "\f0f1";}.fa-suitcase:before{content: "\f0f2";}.fa-bell-o:before{content: "\f0a2";}.fa-coffee:before{content: "\f0f4";}.fa-cutlery:before{content: "\f0f5";}.fa-file-text-o:before{content: "\f0f6";}.fa-building-o:before{content: "\f0f7";}.fa-hospital-o:before{content: "\f0f8";}.fa-ambulance:before{content: "\f0f9";}.fa-medkit:before{content: "\f0fa";}.fa-fighter-jet:before{content: "\f0fb";}.fa-beer:before{content: "\f0fc";}.fa-h-square:before{content: "\f0fd";}.fa-plus-square:before{content: "\f0fe";}.fa-angle-double-left:before{content: "\f100";}.fa-angle-double-right:before{content: "\f101";}.fa-angle-double-up:before{content: "\f102";}.fa-angle-double-down:before{content: "\f103";}.fa-angle-left:before{content: "\f104";}.fa-angle-right:before{content: "\f105";}.fa-angle-up:before{content: "\f106";}.fa-angle-down:before{content: "\f107";}.fa-desktop:before{content: "\f108";}.fa-laptop:before{content: "\f109";}.fa-tablet:before{content: "\f10a";}.fa-mobile-phone:before, .fa-mobile:before{content: "\f10b";}.fa-circle-o:before{content: "\f10c";}.fa-quote-left:before{content: "\f10d";}.fa-quote-right:before{content: "\f10e";}.fa-spinner:before{content: "\f110";}.fa-circle:before{content: "\f111";}.fa-mail-reply:before, .fa-reply:before{content: "\f112";}.fa-github-alt:before{content: "\f113";}.fa-folder-o:before{content: "\f114";}.fa-folder-open-o:before{content: "\f115";}.fa-smile-o:before{content: "\f118";}.fa-frown-o:before{content: "\f119";}.fa-meh-o:before{content: "\f11a";}.fa-gamepad:before{content: "\f11b";}.fa-keyboard-o:before{content: "\f11c";}.fa-flag-o:before{content: "\f11d";}.fa-flag-checkered:before{content: "\f11e";}.fa-terminal:before{content: "\f120";}.fa-code:before{content: "\f121";}.fa-mail-reply-all:before, .fa-reply-all:before{content: "\f122";}.fa-star-half-empty:before, .fa-star-half-full:before, .fa-star-half-o:before{content: "\f123";}.fa-location-arrow:before{content: "\f124";}.fa-crop:before{content: "\f125";}.fa-code-fork:before{content: "\f126";}.fa-unlink:before, .fa-chain-broken:before{content: "\f127";}.fa-question:before{content: "\f128";}.fa-info:before{content: "\f129";}.fa-exclamation:before{content: "\f12a";}.fa-superscript:before{content: "\f12b";}.fa-subscript:before{content: "\f12c";}.fa-eraser:before{content: "\f12d";}.fa-puzzle-piece:before{content: "\f12e";}.fa-microphone:before{content: "\f130";}.fa-microphone-slash:before{content: "\f131";}.fa-shield:before{content: "\f132";}.fa-calendar-o:before{content: "\f133";}.fa-fire-extinguisher:before{content: "\f134";}.fa-rocket:before{content: "\f135";}.fa-maxcdn:before{content: "\f136";}.fa-chevron-circle-left:before{content: "\f137";}.fa-chevron-circle-right:before{content: "\f138";}.fa-chevron-circle-up:before{content: "\f139";}.fa-chevron-circle-down:before{content: "\f13a";}.fa-html5:before{content: "\f13b";}.fa-css3:before{content: "\f13c";}.fa-anchor:before{content: "\f13d";}.fa-unlock-alt:before{content: "\f13e";}.fa-bullseye:before{content: "\f140";}.fa-ellipsis-h:before{content: "\f141";}.fa-ellipsis-v:before{content: "\f142";}.fa-rss-square:before{content: "\f143";}.fa-play-circle:before{content: "\f144";}.fa-ticket:before{content: "\f145";}.fa-minus-square:before{content: "\f146";}.fa-minus-square-o:before{content: "\f147";}.fa-level-up:before{content: "\f148";}.fa-level-down:before{content: "\f149";}.fa-check-square:before{content: "\f14a";}.fa-pencil-square:before{content: "\f14b";}.fa-external-link-square:before{content: "\f14c";}.fa-share-square:before{content: "\f14d";}.fa-compass:before{content: "\f14e";}.fa-toggle-down:before, .fa-caret-square-o-down:before{content: "\f150";}.fa-toggle-up:before, .fa-caret-square-o-up:before{content: "\f151";}.fa-toggle-right:before, .fa-caret-square-o-right:before{content: "\f152";}.fa-euro:before, .fa-eur:before{content: "\f153";}.fa-gbp:before{content: "\f154";}.fa-dollar:before, .fa-usd:before{content: "\f155";}.fa-rupee:before, .fa-inr:before{content: "\f156";}.fa-cny:before, .fa-rmb:before, .fa-yen:before, .fa-jpy:before{content: "\f157";}.fa-ruble:before, .fa-rouble:before, .fa-rub:before{content: "\f158";}.fa-won:before, .fa-krw:before{content: "\f159";}.fa-bitcoin:before, .fa-btc:before{content: "\f15a";}.fa-file:before{content: "\f15b";}.fa-file-text:before{content: "\f15c";}.fa-sort-alpha-asc:before{content: "\f15d";}.fa-sort-alpha-desc:before{content: "\f15e";}.fa-sort-amount-asc:before{content: "\f160";}.fa-sort-amount-desc:before{content: "\f161";}.fa-sort-numeric-asc:before{content: "\f162";}.fa-sort-numeric-desc:before{content: "\f163";}.fa-thumbs-up:before{content: "\f164";}.fa-thumbs-down:before{content: "\f165";}.fa-youtube-square:before{content: "\f166";}.fa-youtube:before{content: "\f167";}.fa-xing:before{content: "\f168";}.fa-xing-square:before{content: "\f169";}.fa-youtube-play:before{content: "\f16a";}.fa-dropbox:before{content: "\f16b";}.fa-stack-overflow:before{content: "\f16c";}.fa-instagram:before{content: "\f16d";}.fa-flickr:before{content: "\f16e";}.fa-adn:before{content: "\f170";}.fa-bitbucket:before{content: "\f171";}.fa-bitbucket-square:before{content: "\f172";}.fa-tumblr:before{content: "\f173";}.fa-tumblr-square:before{content: "\f174";}.fa-long-arrow-down:before{content: "\f175";}.fa-long-arrow-up:before{content: "\f176";}.fa-long-arrow-left:before{content: "\f177";}.fa-long-arrow-right:before{content: "\f178";}.fa-apple:before{content: "\f179";}.fa-windows:before{content: "\f17a";}.fa-android:before{content: "\f17b";}.fa-linux:before{content: "\f17c";}.fa-dribbble:before{content: "\f17d";}.fa-skype:before{content: "\f17e";}.fa-foursquare:before{content: "\f180";}.fa-trello:before{content: "\f181";}.fa-female:before{content: "\f182";}.fa-male:before{content: "\f183";}.fa-gittip:before, .fa-gratipay:before{content: "\f184";}.fa-sun-o:before{content: "\f185";}.fa-moon-o:before{content: "\f186";}.fa-archive:before{content: "\f187";}.fa-bug:before{content: "\f188";}.fa-vk:before{content: "\f189";}.fa-weibo:before{content: "\f18a";}.fa-renren:before{content: "\f18b";}.fa-pagelines:before{content: "\f18c";}.fa-stack-exchange:before{content: "\f18d";}.fa-arrow-circle-o-right:before{content: "\f18e";}.fa-arrow-circle-o-left:before{content: "\f190";}.fa-toggle-left:before, .fa-caret-square-o-left:before{content: "\f191";}.fa-dot-circle-o:before{content: "\f192";}.fa-wheelchair:before{content: "\f193";}.fa-vimeo-square:before{content: "\f194";}.fa-turkish-lira:before, .fa-try:before{content: "\f195";}.fa-plus-square-o:before{content: "\f196";}.fa-space-shuttle:before{content: "\f197";}.fa-slack:before{content: "\f198";}.fa-envelope-square:before{content: "\f199";}.fa-wordpress:before{content: "\f19a";}.fa-openid:before{content: "\f19b";}.fa-institution:before, .fa-bank:before, .fa-university:before{content: "\f19c";}.fa-mortar-board:before, .fa-graduation-cap:before{content: "\f19d";}.fa-yahoo:before{content: "\f19e";}.fa-google:before{content: "\f1a0";}.fa-reddit:before{content: "\f1a1";}.fa-reddit-square:before{content: "\f1a2";}.fa-stumbleupon-circle:before{content: "\f1a3";}.fa-stumbleupon:before{content: "\f1a4";}.fa-delicious:before{content: "\f1a5";}.fa-digg:before{content: "\f1a6";}.fa-pied-piper-pp:before{content: "\f1a7";}.fa-pied-piper-alt:before{content: "\f1a8";}.fa-drupal:before{content: "\f1a9";}.fa-joomla:before{content: "\f1aa";}.fa-language:before{content: "\f1ab";}.fa-fax:before{content: "\f1ac";}.fa-building:before{content: "\f1ad";}.fa-child:before{content: "\f1ae";}.fa-paw:before{content: "\f1b0";}.fa-spoon:before{content: "\f1b1";}.fa-cube:before{content: "\f1b2";}.fa-cubes:before{content: "\f1b3";}.fa-behance:before{content: "\f1b4";}.fa-behance-square:before{content: "\f1b5";}.fa-steam:before{content: "\f1b6";}.fa-steam-square:before{content: "\f1b7";}.fa-recycle:before{content: "\f1b8";}.fa-automobile:before, .fa-car:before{content: "\f1b9";}.fa-cab:before, .fa-taxi:before{content: "\f1ba";}.fa-tree:before{content: "\f1bb";}.fa-spotify:before{content: "\f1bc";}.fa-deviantart:before{content: "\f1bd";}.fa-soundcloud:before{content: "\f1be";}.fa-database:before{content: "\f1c0";}.fa-file-pdf-o:before{content: "\f1c1";}.fa-file-word-o:before{content: "\f1c2";}.fa-file-excel-o:before{content: "\f1c3";}.fa-file-powerpoint-o:before{content: "\f1c4";}.fa-file-photo-o:before, .fa-file-picture-o:before, .fa-file-image-o:before{content: "\f1c5";}.fa-file-zip-o:before, .fa-file-archive-o:before{content: "\f1c6";}.fa-file-sound-o:before, .fa-file-audio-o:before{content: "\f1c7";}.fa-file-movie-o:before, .fa-file-video-o:before{content: "\f1c8";}.fa-file-code-o:before{content: "\f1c9";}.fa-vine:before{content: "\f1ca";}.fa-codepen:before{content: "\f1cb";}.fa-jsfiddle:before{content: "\f1cc";}.fa-life-bouy:before, .fa-life-buoy:before, .fa-life-saver:before, .fa-support:before, .fa-life-ring:before{content: "\f1cd";}.fa-circle-o-notch:before{content: "\f1ce";}.fa-ra:before, .fa-resistance:before, .fa-rebel:before{content: "\f1d0";}.fa-ge:before, .fa-empire:before{content: "\f1d1";}.fa-git-square:before{content: "\f1d2";}.fa-git:before{content: "\f1d3";}.fa-y-combinator-square:before, .fa-yc-square:before, .fa-hacker-news:before{content: "\f1d4";}.fa-tencent-weibo:before{content: "\f1d5";}.fa-qq:before{content: "\f1d6";}.fa-wechat:before, .fa-weixin:before{content: "\f1d7";}.fa-send:before, .fa-paper-plane:before{content: "\f1d8";}.fa-send-o:before, .fa-paper-plane-o:before{content: "\f1d9";}.fa-history:before{content: "\f1da";}.fa-circle-thin:before{content: "\f1db";}.fa-header:before{content: "\f1dc";}.fa-paragraph:before{content: "\f1dd";}.fa-sliders:before{content: "\f1de";}.fa-share-alt:before{content: "\f1e0";}.fa-share-alt-square:before{content: "\f1e1";}.fa-bomb:before{content: "\f1e2";}.fa-soccer-ball-o:before, .fa-futbol-o:before{content: "\f1e3";}.fa-tty:before{content: "\f1e4";}.fa-binoculars:before{content: "\f1e5";}.fa-plug:before{content: "\f1e6";}.fa-slideshare:before{content: "\f1e7";}.fa-twitch:before{content: "\f1e8";}.fa-yelp:before{content: "\f1e9";}.fa-newspaper-o:before{content: "\f1ea";}.fa-wifi:before{content: "\f1eb";}.fa-calculator:before{content: "\f1ec";}.fa-paypal:before{content: "\f1ed";}.fa-google-wallet:before{content: "\f1ee";}.fa-cc-visa:before{content: "\f1f0";}.fa-cc-mastercard:before{content: "\f1f1";}.fa-cc-discover:before{content: "\f1f2";}.fa-cc-amex:before{content: "\f1f3";}.fa-cc-paypal:before{content: "\f1f4";}.fa-cc-stripe:before{content: "\f1f5";}.fa-bell-slash:before{content: "\f1f6";}.fa-bell-slash-o:before{content: "\f1f7";}.fa-trash:before{content: "\f1f8";}.fa-copyright:before{content: "\f1f9";}.fa-at:before{content: "\f1fa";}.fa-eyedropper:before{content: "\f1fb";}.fa-paint-brush:before{content: "\f1fc";}.fa-birthday-cake:before{content: "\f1fd";}.fa-area-chart:before{content: "\f1fe";}.fa-pie-chart:before{content: "\f200";}.fa-line-chart:before{content: "\f201";}.fa-lastfm:before{content: "\f202";}.fa-lastfm-square:before{content: "\f203";}.fa-toggle-off:before{content: "\f204";}.fa-toggle-on:before{content: "\f205";}.fa-bicycle:before{content: "\f206";}.fa-bus:before{content: "\f207";}.fa-ioxhost:before{content: "\f208";}.fa-angellist:before{content: "\f209";}.fa-cc:before{content: "\f20a";}.fa-shekel:before, .fa-sheqel:before, .fa-ils:before{content: "\f20b";}.fa-meanpath:before{content: "\f20c";}.fa-buysellads:before{content: "\f20d";}.fa-connectdevelop:before{content: "\f20e";}.fa-dashcube:before{content: "\f210";}.fa-forumbee:before{content: "\f211";}.fa-leanpub:before{content: "\f212";}.fa-sellsy:before{content: "\f213";}.fa-shirtsinbulk:before{content: "\f214";}.fa-simplybuilt:before{content: "\f215";}.fa-skyatlas:before{content: "\f216";}.fa-cart-plus:before{content: "\f217";}.fa-cart-arrow-down:before{content: "\f218";}.fa-diamond:before{content: "\f219";}.fa-ship:before{content: "\f21a";}.fa-user-secret:before{content: "\f21b";}.fa-motorcycle:before{content: "\f21c";}.fa-street-view:before{content: "\f21d";}.fa-heartbeat:before{content: "\f21e";}.fa-venus:before{content: "\f221";}.fa-mars:before{content: "\f222";}.fa-mercury:before{content: "\f223";}.fa-intersex:before, .fa-transgender:before{content: "\f224";}.fa-transgender-alt:before{content: "\f225";}.fa-venus-double:before{content: "\f226";}.fa-mars-double:before{content: "\f227";}.fa-venus-mars:before{content: "\f228";}.fa-mars-stroke:before{content: "\f229";}.fa-mars-stroke-v:before{content: "\f22a";}.fa-mars-stroke-h:before{content: "\f22b";}.fa-neuter:before{content: "\f22c";}.fa-genderless:before{content: "\f22d";}.fa-facebook-official:before{content: "\f230";}.fa-pinterest-p:before{content: "\f231";}.fa-whatsapp:before{content: "\f232";}.fa-server:before{content: "\f233";}.fa-user-plus:before{content: "\f234";}.fa-user-times:before{content: "\f235";}.fa-hotel:before, .fa-bed:before{content: "\f236";}.fa-viacoin:before{content: "\f237";}.fa-train:before{content: "\f238";}.fa-subway:before{content: "\f239";}.fa-medium:before{content: "\f23a";}.fa-yc:before, .fa-y-combinator:before{content: "\f23b";}.fa-optin-monster:before{content: "\f23c";}.fa-opencart:before{content: "\f23d";}.fa-expeditedssl:before{content: "\f23e";}.fa-battery-4:before, .fa-battery:before, .fa-battery-full:before{content: "\f240";}.fa-battery-3:before, .fa-battery-three-quarters:before{content: "\f241";}.fa-battery-2:before, .fa-battery-half:before{content: "\f242";}.fa-battery-1:before, .fa-battery-quarter:before{content: "\f243";}.fa-battery-0:before, .fa-battery-empty:before{content: "\f244";}.fa-mouse-pointer:before{content: "\f245";}.fa-i-cursor:before{content: "\f246";}.fa-object-group:before{content: "\f247";}.fa-object-ungroup:before{content: "\f248";}.fa-sticky-note:before{content: "\f249";}.fa-sticky-note-o:before{content: "\f24a";}.fa-cc-jcb:before{content: "\f24b";}.fa-cc-diners-club:before{content: "\f24c";}.fa-clone:before{content: "\f24d";}.fa-balance-scale:before{content: "\f24e";}.fa-hourglass-o:before{content: "\f250";}.fa-hourglass-1:before, .fa-hourglass-start:before{content: "\f251";}.fa-hourglass-2:before, .fa-hourglass-half:before{content: "\f252";}.fa-hourglass-3:before, .fa-hourglass-end:before{content: "\f253";}.fa-hourglass:before{content: "\f254";}.fa-hand-grab-o:before, .fa-hand-rock-o:before{content: "\f255";}.fa-hand-stop-o:before, .fa-hand-paper-o:before{content: "\f256";}.fa-hand-scissors-o:before{content: "\f257";}.fa-hand-lizard-o:before{content: "\f258";}.fa-hand-spock-o:before{content: "\f259";}.fa-hand-pointer-o:before{content: "\f25a";}.fa-hand-peace-o:before{content: "\f25b";}.fa-trademark:before{content: "\f25c";}.fa-registered:before{content: "\f25d";}.fa-creative-commons:before{content: "\f25e";}.fa-gg:before{content: "\f260";}.fa-gg-circle:before{content: "\f261";}.fa-tripadvisor:before{content: "\f262";}.fa-odnoklassniki:before{content: "\f263";}.fa-odnoklassniki-square:before{content: "\f264";}.fa-get-pocket:before{content: "\f265";}.fa-wikipedia-w:before{content: "\f266";}.fa-safari:before{content: "\f267";}.fa-chrome:before{content: "\f268";}.fa-firefox:before{content: "\f269";}.fa-opera:before{content: "\f26a";}.fa-internet-explorer:before{content: "\f26b";}.fa-tv:before, .fa-television:before{content: "\f26c";}.fa-contao:before{content: "\f26d";}.fa-500px:before{content: "\f26e";}.fa-amazon:before{content: "\f270";}.fa-calendar-plus-o:before{content: "\f271";}.fa-calendar-minus-o:before{content: "\f272";}.fa-calendar-times-o:before{content: "\f273";}.fa-calendar-check-o:before{content: "\f274";}.fa-industry:before{content: "\f275";}.fa-map-pin:before{content: "\f276";}.fa-map-signs:before{content: "\f277";}.fa-map-o:before{content: "\f278";}.fa-map:before{content: "\f279";}.fa-commenting:before{content: "\f27a";}.fa-commenting-o:before{content: "\f27b";}.fa-houzz:before{content: "\f27c";}.fa-vimeo:before{content: "\f27d";}.fa-black-tie:before{content: "\f27e";}.fa-fonticons:before{content: "\f280";}.fa-reddit-alien:before{content: "\f281";}.fa-edge:before{content: "\f282";}.fa-credit-card-alt:before{content: "\f283";}.fa-codiepie:before{content: "\f284";}.fa-modx:before{content: "\f285";}.fa-fort-awesome:before{content: "\f286";}.fa-usb:before{content: "\f287";}.fa-product-hunt:before{content: "\f288";}.fa-mixcloud:before{content: "\f289";}.fa-scribd:before{content: "\f28a";}.fa-pause-circle:before{content: "\f28b";}.fa-pause-circle-o:before{content: "\f28c";}.fa-stop-circle:before{content: "\f28d";}.fa-stop-circle-o:before{content: "\f28e";}.fa-shopping-bag:before{content: "\f290";}.fa-shopping-basket:before{content: "\f291";}.fa-hashtag:before{content: "\f292";}.fa-bluetooth:before{content: "\f293";}.fa-bluetooth-b:before{content: "\f294";}.fa-percent:before{content: "\f295";}.fa-gitlab:before{content: "\f296";}.fa-wpbeginner:before{content: "\f297";}.fa-wpforms:before{content: "\f298";}.fa-envira:before{content: "\f299";}.fa-universal-access:before{content: "\f29a";}.fa-wheelchair-alt:before{content: "\f29b";}.fa-question-circle-o:before{content: "\f29c";}.fa-blind:before{content: "\f29d";}.fa-audio-description:before{content: "\f29e";}.fa-volume-control-phone:before{content: "\f2a0";}.fa-braille:before{content: "\f2a1";}.fa-assistive-listening-systems:before{content: "\f2a2";}.fa-asl-interpreting:before, .fa-american-sign-language-interpreting:before{content: "\f2a3";}.fa-deafness:before, .fa-hard-of-hearing:before, .fa-deaf:before{content: "\f2a4";}.fa-glide:before{content: "\f2a5";}.fa-glide-g:before{content: "\f2a6";}.fa-signing:before, .fa-sign-language:before{content: "\f2a7";}.fa-low-vision:before{content: "\f2a8";}.fa-viadeo:before{content: "\f2a9";}.fa-viadeo-square:before{content: "\f2aa";}.fa-snapchat:before{content: "\f2ab";}.fa-snapchat-ghost:before{content: "\f2ac";}.fa-snapchat-square:before{content: "\f2ad";}.fa-pied-piper:before{content: "\f2ae";}.fa-first-order:before{content: "\f2b0";}.fa-yoast:before{content: "\f2b1";}.fa-themeisle:before{content: "\f2b2";}.fa-google-plus-circle:before, .fa-google-plus-official:before{content: "\f2b3";}.fa-fa:before, .fa-font-awesome:before{content: "\f2b4";}.fa-handshake-o:before{content: "\f2b5";}.fa-envelope-open:before{content: "\f2b6";}.fa-envelope-open-o:before{content: "\f2b7";}.fa-linode:before{content: "\f2b8";}.fa-address-book:before{content: "\f2b9";}.fa-address-book-o:before{content: "\f2ba";}.fa-vcard:before, .fa-address-card:before{content: "\f2bb";}.fa-vcard-o:before, .fa-address-card-o:before{content: "\f2bc";}.fa-user-circle:before{content: "\f2bd";}.fa-user-circle-o:before{content: "\f2be";}.fa-user-o:before{content: "\f2c0";}.fa-id-badge:before{content: "\f2c1";}.fa-drivers-license:before, .fa-id-card:before{content: "\f2c2";}.fa-drivers-license-o:before, .fa-id-card-o:before{content: "\f2c3";}.fa-quora:before{content: "\f2c4";}.fa-free-code-camp:before{content: "\f2c5";}.fa-telegram:before{content: "\f2c6";}.fa-thermometer-4:before, .fa-thermometer:before, .fa-thermometer-full:before{content: "\f2c7";}.fa-thermometer-3:before, .fa-thermometer-three-quarters:before{content: "\f2c8";}.fa-thermometer-2:before, .fa-thermometer-half:before{content: "\f2c9";}.fa-thermometer-1:before, .fa-thermometer-quarter:before{content: "\f2ca";}.fa-thermometer-0:before, .fa-thermometer-empty:before{content: "\f2cb";}.fa-shower:before{content: "\f2cc";}.fa-bathtub:before, .fa-s15:before, .fa-bath:before{content: "\f2cd";}.fa-podcast:before{content: "\f2ce";}.fa-window-maximize:before{content: "\f2d0";}.fa-window-minimize:before{content: "\f2d1";}.fa-window-restore:before{content: "\f2d2";}.fa-times-rectangle:before, .fa-window-close:before{content: "\f2d3";}.fa-times-rectangle-o:before, .fa-window-close-o:before{content: "\f2d4";}.fa-bandcamp:before{content: "\f2d5";}.fa-grav:before{content: "\f2d6";}.fa-etsy:before{content: "\f2d7";}.fa-imdb:before{content: "\f2d8";}.fa-ravelry:before{content: "\f2d9";}.fa-eercast:before{content: "\f2da";}.fa-microchip:before{content: "\f2db";}.fa-snowflake-o:before{content: "\f2dc";}.fa-superpowers:before{content: "\f2dd";}.fa-wpexplorer:before{content: "\f2de";}.fa-meetup:before{content: "\f2e0";}.visually-hidden{position: absolute; width: 1px; height: 1px; padding: 0; margin: -1px; overflow: hidden; clip: rect(0, 0, 0, 0); border: 0;}.visually-hidden-focusable:active, .visually-hidden-focusable:focus{position: static; width: auto; height: auto; margin: 0; overflow: visible; clip: auto;}

/* /web/static/lib/odoo_ui_icons/style.css */
@font-face{font-family: 'odoo_ui_icons'; src: url('/web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff2') format('woff2'), url('/web/static/lib/odoo_ui_icons/fonts/odoo_ui_icons.woff') format('woff'); font-weight: normal; font-style: normal; font-display: block;}.oi{display: inline-block; font-family: 'odoo_ui_icons'; speak: never; font-style: normal; font-weight: normal; font-variant: normal; text-transform: none; line-height: 1; -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;}.oi-view-pivot:before{content: '\e800';}.oi-text-break:before{content: '\e801';}.oi-text-inline:before{content: '\e802';}.oi-voip:before{content: '\e803';}.oi-odoo:before{content: '\e806';}.oi-search:before{content: '\e808';}.oi-group:before{content: '\e80a';}.oi-settings-adjust:before{content: '\e80c';}.oi-apps:before{content: '\e80d';}.oi-panel-right:before{content: '\e810';}.oi-launch:before{content: '\e812';}.oi-studio:before{content: '\e813';}.oi-view-kanban:before{content: '\e814';}.oi-text-wrap:before{content: '\e815';}.oi-view-cohort:before{content: '\e816';}.oi-view-list:before{content: '\e817';}.oi-gif-picker:before{content: '\e82e';}.oi-chevron-down:before{content: '\e839';}.oi-chevron-left:before{content: '\e83a';}.oi-chevron-right:before{content: '\e83b';}.oi-chevron-up:before{content: '\e83c';}.oi-arrows-h:before{content: '\e83d';}.oi-arrows-v:before{content: '\e83e';}.oi-arrow-down-left:before{content: '\e83f';}.oi-arrow-down-right:before{content: '\e840';}.oi-arrow-down:before{content: '\e841';}.oi-arrow-left:before{content: '\e842';}.oi-arrow-right:before{content: '\e843';}.oi-arrow-up-left:before{content: '\e844';}.oi-arrow-up-right:before{content: '\e845';}.oi-arrow-up:before{content: '\e846';}.oi-draggable:before{content: '\e847';}.oi-view:before{content: '\e861';}.oi-archive:before{content: '\e862';}.oi-unarchive:before{content: '\e863';}.oi-text-effect:before{content: '\e827';}.oi-smile-add:before{content: '\e84e';}.oi-close:before{content: '\e852';}.o_rtl .oi-chevron-left, .o_rtl .oi-chevron-right, .o_rtl .oi-arrow-down-left, .o_rtl .oi-arrow-down-right, .o_rtl .oi-arrow-left, .o_rtl .oi-arrow-right, .o_rtl .oi-arrow-up-left, .o_rtl .oi-arrow-up-right{transform: rotate(180deg);}

/* /web/static/lib/select2/select2.css */
 .select2-container{margin: 0; position: relative; display: inline-block; vertical-align: middle;}.select2-container, .select2-drop, .select2-search, .select2-search input{-webkit-box-sizing: border-box; -moz-box-sizing: border-box; box-sizing: border-box;}.select2-container .select2-choice{display: block; height: 26px; padding: 0 0 0 8px; overflow: hidden; position: relative; border: 1px solid #aaa; white-space: nowrap; line-height: 26px; color: #444; text-decoration: none; border-radius: 4px; background-clip: padding-box; -webkit-touch-callout: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; background-color: #fff; background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #eee), color-stop(0.5, #fff)); background-image: -webkit-linear-gradient(center bottom, #eee 0%, #fff 50%); background-image: -moz-linear-gradient(center bottom, #eee 0%, #fff 50%); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr = '#ffffff', endColorstr = '#eeeeee', GradientType = 0); background-image: linear-gradient(to top, #eee 0%, #fff 50%);}html[dir="rtl"] .select2-container .select2-choice{padding: 0 8px 0 0;}.select2-container.select2-drop-above .select2-choice{border-bottom-color: #aaa; border-radius: 0 0 4px 4px; background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #eee), color-stop(0.9, #fff)); background-image: -webkit-linear-gradient(center bottom, #eee 0%, #fff 90%); background-image: -moz-linear-gradient(center bottom, #eee 0%, #fff 90%); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#eeeeee', GradientType=0); background-image: linear-gradient(to bottom, #eee 0%, #fff 90%);}.select2-container.select2-allowclear .select2-choice .select2-chosen{margin-right: 42px;}.select2-container .select2-choice > .select2-chosen{margin-right: 26px; display: block; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; float: none; width: auto;}html[dir="rtl"] .select2-container .select2-choice > .select2-chosen{margin-left: 26px; margin-right: 0;}.select2-container .select2-choice abbr{display: none; width: 12px; height: 12px; position: absolute; right: 24px; top: 8px; font-size: 1px; text-decoration: none; border: 0; background: url('/web/static/lib/select2/select2.png') right top no-repeat; cursor: pointer; outline: 0;}.select2-container.select2-allowclear .select2-choice abbr{display: inline-block;}.select2-container .select2-choice abbr:hover{background-position: right -11px; cursor: pointer;}.select2-drop-mask{border: 0; margin: 0; padding: 0; position: fixed; left: 0; top: 0; min-height: 100%; min-width: 100%; height: auto; width: auto; opacity: 0; z-index: 9998; background-color: #fff; filter: alpha(opacity=0);}.select2-drop{width: 100%; margin-top: -1px; position: absolute; z-index: 9999; top: 100%; background: #fff; color: #000; border: 1px solid #aaa; border-top: 0; border-radius: 0 0 4px 4px; -webkit-box-shadow: 0 4px 5px rgba(0, 0, 0, .15); box-shadow: 0 4px 5px rgba(0, 0, 0, .15);}.select2-drop.select2-drop-above{margin-top: 1px; border-top: 1px solid #aaa; border-bottom: 0; border-radius: 4px 4px 0 0; -webkit-box-shadow: 0 -4px 5px rgba(0, 0, 0, .15); box-shadow: 0 -4px 5px rgba(0, 0, 0, .15);}.select2-drop-active{border: 1px solid #5897fb; border-top: none;}.select2-drop.select2-drop-above.select2-drop-active{border-top: 1px solid #5897fb;}.select2-drop-auto-width{border-top: 1px solid #aaa; width: auto;}.select2-container .select2-choice .select2-arrow{display: inline-block; width: 18px; height: 100%; position: absolute; right: 0; top: 0; border-left: 1px solid #aaa; border-radius: 0 4px 4px 0; background-clip: padding-box; background: #ccc; background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #ccc), color-stop(0.6, #eee)); background-image: -webkit-linear-gradient(center bottom, #ccc 0%, #eee 60%); background-image: -moz-linear-gradient(center bottom, #ccc 0%, #eee 60%); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr = '#eeeeee', endColorstr = '#cccccc', GradientType = 0); background-image: linear-gradient(to top, #ccc 0%, #eee 60%);}html[dir="rtl"] .select2-container .select2-choice .select2-arrow{left: 0; right: auto; border-left: none; border-right: 1px solid #aaa; border-radius: 4px 0 0 4px;}.select2-container .select2-choice .select2-arrow b{display: block; width: 100%; height: 100%; background: url('/web/static/lib/select2/select2.png') no-repeat 0 1px;}html[dir="rtl"] .select2-container .select2-choice .select2-arrow b{background-position: 2px 1px;}.select2-search{display: inline-block; width: 100%; min-height: 26px; margin: 0; padding: 4px 4px 0 4px; position: relative; z-index: 10000; white-space: nowrap;}.select2-search input{width: 100%; height: auto !important; min-height: 26px; padding: 4px 20px 4px 5px; margin: 0; outline: 0; font-family: sans-serif; font-size: 1em; border: 1px solid #aaa; border-radius: 0; -webkit-box-shadow: none; box-shadow: none; background: #fff url('/web/static/lib/select2/select2.png') no-repeat 100% -22px; background: url('/web/static/lib/select2/select2.png') no-repeat 100% -22px, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, #fff), color-stop(0.99, #eee)); background: url('/web/static/lib/select2/select2.png') no-repeat 100% -22px, -webkit-linear-gradient(center bottom, #fff 85%, #eee 99%); background: url('/web/static/lib/select2/select2.png') no-repeat 100% -22px, -moz-linear-gradient(center bottom, #fff 85%, #eee 99%); background: url('/web/static/lib/select2/select2.png') no-repeat 100% -22px, linear-gradient(to bottom, #fff 85%, #eee 99%) 0 0;}html[dir="rtl"] .select2-search input{padding: 4px 5px 4px 20px; background: #fff url('/web/static/lib/select2/select2.png') no-repeat -37px -22px; background: url('/web/static/lib/select2/select2.png') no-repeat -37px -22px, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, #fff), color-stop(0.99, #eee)); background: url('/web/static/lib/select2/select2.png') no-repeat -37px -22px, -webkit-linear-gradient(center bottom, #fff 85%, #eee 99%); background: url('/web/static/lib/select2/select2.png') no-repeat -37px -22px, -moz-linear-gradient(center bottom, #fff 85%, #eee 99%); background: url('/web/static/lib/select2/select2.png') no-repeat -37px -22px, linear-gradient(to bottom, #fff 85%, #eee 99%) 0 0;}.select2-search input.select2-active{background: #fff url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%; background: url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%, -webkit-gradient(linear, left bottom, left top, color-stop(0.85, #fff), color-stop(0.99, #eee)); background: url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%, -webkit-linear-gradient(center bottom, #fff 85%, #eee 99%); background: url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%, -moz-linear-gradient(center bottom, #fff 85%, #eee 99%); background: url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%, linear-gradient(to bottom, #fff 85%, #eee 99%) 0 0;}.select2-container-active .select2-choice, .select2-container-active .select2-choices{border: 1px solid #5897fb; outline: none; -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, .3); box-shadow: 0 0 5px rgba(0, 0, 0, .3);}.select2-dropdown-open .select2-choice{border-bottom-color: transparent; -webkit-box-shadow: 0 1px 0 #fff inset; box-shadow: 0 1px 0 #fff inset; border-bottom-left-radius: 0; border-bottom-right-radius: 0; background-color: #eee; background-image: -webkit-gradient(linear, left bottom, left top, color-stop(0, #fff), color-stop(0.5, #eee)); background-image: -webkit-linear-gradient(center bottom, #fff 0%, #eee 50%); background-image: -moz-linear-gradient(center bottom, #fff 0%, #eee 50%); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#ffffff', GradientType=0); background-image: linear-gradient(to top, #fff 0%, #eee 50%);}.select2-dropdown-open.select2-drop-above .select2-choice, .select2-dropdown-open.select2-drop-above .select2-choices{border: 1px solid #5897fb; border-top-color: transparent; background-image: -webkit-gradient(linear, left top, left bottom, color-stop(0, #fff), color-stop(0.5, #eee)); background-image: -webkit-linear-gradient(center top, #fff 0%, #eee 50%); background-image: -moz-linear-gradient(center top, #fff 0%, #eee 50%); filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#ffffff', GradientType=0); background-image: linear-gradient(to bottom, #fff 0%, #eee 50%);}.select2-dropdown-open .select2-choice .select2-arrow{background: transparent; border-left: none; filter: none;}html[dir="rtl"] .select2-dropdown-open .select2-choice .select2-arrow{border-right: none;}.select2-dropdown-open .select2-choice .select2-arrow b{background-position: -18px 1px;}html[dir="rtl"] .select2-dropdown-open .select2-choice .select2-arrow b{background-position: -16px 1px;}.select2-hidden-accessible{border: 0; clip: rect(0 0 0 0); height: 1px; margin: -1px; overflow: hidden; padding: 0; position: absolute; width: 1px;}.select2-results{max-height: 200px; padding: 0 0 0 4px; margin: 4px 4px 4px 0; position: relative; overflow-x: hidden; overflow-y: auto; -webkit-tap-highlight-color: rgba(0, 0, 0, 0);}html[dir="rtl"] .select2-results{padding: 0 4px 0 0; margin: 4px 0 4px 4px;}.select2-results ul.select2-result-sub{margin: 0; padding-left: 0;}.select2-results li{list-style: none; display: list-item; background-image: none;}.select2-results li.select2-result-with-children > .select2-result-label{font-weight: bold;}.select2-results .select2-result-label{padding: 3px 7px 4px; margin: 0; cursor: pointer; min-height: 1em; -webkit-touch-callout: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none;}.select2-results-dept-1 .select2-result-label{padding-left: 20px}.select2-results-dept-2 .select2-result-label{padding-left: 40px}.select2-results-dept-3 .select2-result-label{padding-left: 60px}.select2-results-dept-4 .select2-result-label{padding-left: 80px}.select2-results-dept-5 .select2-result-label{padding-left: 100px}.select2-results-dept-6 .select2-result-label{padding-left: 110px}.select2-results-dept-7 .select2-result-label{padding-left: 120px}.select2-results .select2-highlighted{background: #3875d7; color: #fff;}.select2-results li em{background: #feffde; font-style: normal;}.select2-results .select2-highlighted em{background: transparent;}.select2-results .select2-highlighted ul{background: #fff; color: #000;}.select2-results .select2-no-results, .select2-results .select2-searching, .select2-results .select2-ajax-error, .select2-results .select2-selection-limit{background: #f4f4f4; display: list-item; padding-left: 5px;}.select2-results .select2-disabled.select2-highlighted{color: #666; background: #f4f4f4; display: list-item; cursor: default;}.select2-results .select2-disabled{background: #f4f4f4; display: list-item; cursor: default;}.select2-results .select2-selected{display: none;}.select2-more-results.select2-active{background: #f4f4f4 url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100%;}.select2-results .select2-ajax-error{background: rgba(255, 50, 50, .2);}.select2-more-results{background: #f4f4f4; display: list-item;}.select2-container.select2-container-disabled .select2-choice{background-color: #f4f4f4; background-image: none; border: 1px solid #ddd; cursor: default;}.select2-container.select2-container-disabled .select2-choice .select2-arrow{background-color: #f4f4f4; background-image: none; border-left: 0;}.select2-container.select2-container-disabled .select2-choice abbr{display: none;}.select2-container-multi .select2-choices{height: auto !important; height: 1%; margin: 0; padding: 0 5px 0 0; position: relative; border: 1px solid #aaa; cursor: text; overflow: hidden; background-color: #fff; background-image: -webkit-gradient(linear, 0% 0%, 0% 100%, color-stop(1%, #eee), color-stop(15%, #fff)); background-image: -webkit-linear-gradient(top, #eee 1%, #fff 15%); background-image: -moz-linear-gradient(top, #eee 1%, #fff 15%); background-image: linear-gradient(to bottom, #eee 1%, #fff 15%);}html[dir="rtl"] .select2-container-multi .select2-choices{padding: 0 0 0 5px;}.select2-locked{padding: 3px 5px 3px 5px !important;}.select2-container-multi .select2-choices{min-height: 26px;}.select2-container-multi.select2-container-active .select2-choices{border: 1px solid #5897fb; outline: none; -webkit-box-shadow: 0 0 5px rgba(0, 0, 0, .3); box-shadow: 0 0 5px rgba(0, 0, 0, .3);}.select2-container-multi .select2-choices li{float: left; list-style: none;}html[dir="rtl"] .select2-container-multi .select2-choices li{float: right;}.select2-container-multi .select2-choices .select2-search-field{margin: 0; padding: 0; white-space: nowrap;}.select2-container-multi .select2-choices .select2-search-field input{padding: 5px; margin: 1px 0; font-family: sans-serif; font-size: 100%; color: #666; outline: 0; border: 0; -webkit-box-shadow: none; box-shadow: none; background: transparent !important;}.select2-container-multi .select2-choices .select2-search-field input.select2-active{background: #fff url('/web/static/lib/select2/select2-spinner.gif') no-repeat 100% !important;}.select2-default{color: #999 !important;}.select2-container-multi .select2-choices .select2-search-choice{padding: 3px 5px 3px 18px; margin: 3px 0 3px 5px; position: relative; line-height: 13px; color: #333; cursor: default; border: 1px solid #aaaaaa; border-radius: 3px; -webkit-box-shadow: 0 0 2px #fff inset, 0 1px 0 rgba(0, 0, 0, 0.05); box-shadow: 0 0 2px #fff inset, 0 1px 0 rgba(0, 0, 0, 0.05); background-clip: padding-box; -webkit-touch-callout: none; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; background-color: #e4e4e4; filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#eeeeee', endColorstr='#f4f4f4', GradientType=0); background-image: -webkit-gradient(linear, 0% 0%, 0% 100%, color-stop(20%, #f4f4f4), color-stop(50%, #f0f0f0), color-stop(52%, #e8e8e8), color-stop(100%, #eee)); background-image: -webkit-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eee 100%); background-image: -moz-linear-gradient(top, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eee 100%); background-image: linear-gradient(to bottom, #f4f4f4 20%, #f0f0f0 50%, #e8e8e8 52%, #eee 100%);}html[dir="rtl"] .select2-container-multi .select2-choices .select2-search-choice{margin: 3px 5px 3px 0; padding: 3px 18px 3px 5px;}.select2-container-multi .select2-choices .select2-search-choice .select2-chosen{cursor: default;}.select2-container-multi .select2-choices .select2-search-choice-focus{background: #d4d4d4;}.select2-search-choice-close{display: block; width: 12px; height: 13px; position: absolute; right: 3px; top: 4px; font-size: 1px; outline: none; background: url('/web/static/lib/select2/select2.png') right top no-repeat;}html[dir="rtl"] .select2-search-choice-close{right: auto; left: 3px;}.select2-container-multi .select2-search-choice-close{left: 3px;}html[dir="rtl"] .select2-container-multi .select2-search-choice-close{left: auto; right: 2px;}.select2-container-multi .select2-choices .select2-search-choice .select2-search-choice-close:hover{background-position: right -11px;}.select2-container-multi .select2-choices .select2-search-choice-focus .select2-search-choice-close{background-position: right -11px;}.select2-container-multi.select2-container-disabled .select2-choices{background-color: #f4f4f4; background-image: none; border: 1px solid #ddd; cursor: default;}.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice{padding: 3px 5px 3px 5px; border: 1px solid #ddd; background-image: none; background-color: #f4f4f4;}.select2-container-multi.select2-container-disabled .select2-choices .select2-search-choice .select2-search-choice-close{display: none; background: none;}.select2-result-selectable .select2-match, .select2-result-unselectable .select2-match{text-decoration: underline;}.select2-offscreen, .select2-offscreen:focus{clip: rect(0 0 0 0) !important; width: 1px !important; height: 1px !important; border: 0 !important; margin: 0 !important; padding: 0 !important; overflow: hidden !important; position: absolute !important; outline: 0 !important; left: 0px !important; top: 0px !important;}.select2-display-none{display: none;}.select2-measure-scrollbar{position: absolute; top: -10000px; left: -10000px; width: 100px; height: 100px; overflow: scroll;}@media only screen and (-webkit-min-device-pixel-ratio: 1.5), only screen and (min-resolution: 2dppx){.select2-search input, .select2-search-choice-close, .select2-container .select2-choice abbr, .select2-container .select2-choice .select2-arrow b{background-image: url('/web/static/lib/select2/select2x2.png') !important; background-repeat: no-repeat !important; background-size: 60px 40px !important;}.select2-search input{background-position: 100% -21px !important;}}

/* /web/static/lib/select2-bootstrap-css/select2-bootstrap.css */
 .form-control .select2-choice{border: 0; border-radius: 2px;}.form-control .select2-choice .select2-arrow{border-radius: 0 2px 2px 0;}.form-control.select2-container{height: auto !important; padding: 0;}.form-control.select2-container.select2-dropdown-open{border-color: #5897FB; border-radius: 3px 3px 0 0;}.form-control .select2-container.select2-dropdown-open .select2-choices{border-radius: 3px 3px 0 0;}.form-control.select2-container .select2-choices{border: 0 !important; border-radius: 3px;}.control-group.warning .select2-container .select2-choice, .control-group.warning .select2-container .select2-choices, .control-group.warning .select2-container-active .select2-choice, .control-group.warning .select2-container-active .select2-choices, .control-group.warning .select2-dropdown-open.select2-drop-above .select2-choice, .control-group.warning .select2-dropdown-open.select2-drop-above .select2-choices, .control-group.warning .select2-container-multi.select2-container-active .select2-choices{border: 1px solid #C09853 !important;}.control-group.warning .select2-container .select2-choice div{border-left: 1px solid #C09853 !important; background: #FCF8E3 !important;}.control-group.error .select2-container .select2-choice, .control-group.error .select2-container .select2-choices, .control-group.error .select2-container-active .select2-choice, .control-group.error .select2-container-active .select2-choices, .control-group.error .select2-dropdown-open.select2-drop-above .select2-choice, .control-group.error .select2-dropdown-open.select2-drop-above .select2-choices, .control-group.error .select2-container-multi.select2-container-active .select2-choices{border: 1px solid #B94A48 !important;}.control-group.error .select2-container .select2-choice div{border-left: 1px solid #B94A48 !important; background: #F2DEDE !important;}.control-group.info .select2-container .select2-choice, .control-group.info .select2-container .select2-choices, .control-group.info .select2-container-active .select2-choice, .control-group.info .select2-container-active .select2-choices, .control-group.info .select2-dropdown-open.select2-drop-above .select2-choice, .control-group.info .select2-dropdown-open.select2-drop-above .select2-choices, .control-group.info .select2-container-multi.select2-container-active .select2-choices{border: 1px solid #3A87AD !important;}.control-group.info .select2-container .select2-choice div{border-left: 1px solid #3A87AD !important; background: #D9EDF7 !important;}.control-group.success .select2-container .select2-choice, .control-group.success .select2-container .select2-choices, .control-group.success .select2-container-active .select2-choice, .control-group.success .select2-container-active .select2-choices, .control-group.success .select2-dropdown-open.select2-drop-above .select2-choice, .control-group.success .select2-dropdown-open.select2-drop-above .select2-choices, .control-group.success .select2-container-multi.select2-container-active .select2-choices{border: 1px solid #468847 !important;}.control-group.success .select2-container .select2-choice div{border-left: 1px solid #468847 !important; background: #DFF0D8 !important;}

/* /web/static/src/webclient/navbar/navbar.scss */
 .o_main_navbar{--o-navbar-height: 46px; --Dropdown_menu-margin-y: 0; display: -webkit-box; display: -webkit-flex; display: flex; height: var(--o-navbar-height); min-width: min-content; padding-top: 0px; padding-bottom: 0px; border-bottom: 1px solid #5a4f7f; background: #71639e; font-size: 0.875rem;}.o_main_navbar > ul{padding: 0; margin: 0; list-style: none;}.o_main_navbar .o_nav_entry, .o_main_navbar .dropdown-toggle{border-color: transparent;}.o_main_navbar .o_menu_sections .o_nav_entry, .o_main_navbar .o_menu_sections .dropdown-toggle{background: var(--NavBar-entry-backgroundColor, #71639e); border: 1px solid transparent;}.o_main_navbar .o_menu_sections .o_nav_entry:hover, .o_main_navbar .o_menu_sections .dropdown-toggle:hover{background: var(--NavBar-entry-backgroundColor--hover, rgba(0, 0, 0, 0.08));}.o_main_navbar .o_menu_sections .o_nav_entry:focus, .o_main_navbar .o_menu_sections .dropdown-toggle:focus{background: var(--NavBar-entry-backgroundColor--focus, rgba(0, 0, 0, 0.08));}.o_main_navbar .o_menu_sections .o_nav_entry:active, .o_main_navbar .o_menu_sections .dropdown-toggle:active{background: var(--NavBar-entry-backgroundColor--active, rgba(0, 0, 0, 0.08));}.o_main_navbar .o_menu_sections .dropdown.show > .dropdown-toggle{border-color: var(--NavBar-entry-borderColor-active, transparent); background: var(--NavBar-entry-backgroundColor--active, rgba(0, 0, 0, 0.08)); color: var(--NavBar-entry-color--active, #FFFFFF);}.o_main_navbar .dropdown-menu{border-top: 0; border-radius: 0 0 0.25rem 0.25rem;}.o_main_navbar .dropdown-menu .disabled{cursor: default;}.o_main_navbar .dropdown-header.dropdown-menu_group{margin-top: 0;}.o_main_navbar .dropdown-item + .dropdown-header:not(.o_more_dropdown_section_group){margin-top: .3em;}.o_main_navbar .o_dropdown_menu_group_entry.dropdown-item{padding-left: 30px;}.o_main_navbar .o_dropdown_menu_group_entry.dropdown-item + .dropdown-item:not(.o_dropdown_menu_group_entry){margin-top: .8em;}.o_main_navbar .o_navbar_apps_menu .dropdown-toggle{--NavBar-entry-padding-left: 16px; font-size: 1.2em;}.o_main_navbar .o_menu_brand{padding-left: 0; font-size: 1.2em; color: var(--NavBar-brand-color, rgba(255, 255, 255, 0.9));}.o_main_navbar .o_menu_brand:hover{background: none;}.o_main_navbar .o_menu_sections .o_more_dropdown_section_group{margin-top: .8em;}.o_main_navbar .o_menu_sections .o_more_dropdown_section_group:first-child{margin-top: -0.5rem; padding-top: 0.75rem;}.o_main_navbar .o_menu_systray{--NavBar-entry-padding-left: 0.315em; --NavBar-entry-padding-right: 0.315em;}.o_main_navbar .o_menu_systray .badge{margin-right: -.5em; border: 0; padding: 2px 4px; background-color: var(--o-navbar-badge-bg, #28a745); font-size: 11px; color: var(--o-navbar-badge-color, inherit); text-shadow: var(--o-navbar-badge-text-shadow, 1px 1px 0 rgba(0, 0, 0, 0.3)); transform: translate(-0.6em, -30%);}body.o_is_superuser .o_menu_systray{border-image: repeating-linear-gradient(135deg, #d9b904, #d9b904 10px, #373435 10px, #373435 20px) 2; border-image-width: 2px;}

/* /web/static/src/scss/animation.scss */
 @keyframes bounceIn{0%, 20%, 40%, 60%, 80%, 100%{transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);}0%{opacity: 0; transform: scale3d(0.3, 0.3, 0.3);}20%{transform: scale3d(1.1, 1.1, 1.1);}40%{transform: scale3d(0.9, 0.9, 0.9);}60%{opacity: 1; transform: scale3d(1.03, 1.03, 1.03);}80%{transform: scale3d(0.97, 0.97, 0.97);}100%{opacity: 1; transform: scale3d(1, 1, 1);}}@keyframes flash{from, 50%, to{opacity: 1;}25%, 75%{opacity: 0;}}

/* /web/static/src/scss/base_frontend.scss */
 html, body, #wrapwrap{width: 100%; height: 100%;}#wrapwrap{z-index: 0; position: relative; display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-flow: column nowrap; flex-flow: column nowrap;}#wrapwrap > *{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto;}#wrapwrap > main{-webkit-box-flex: 1; -webkit-flex: 1 0 auto; flex: 1 0 auto;}.modal-open #wrapwrap{overflow: hidden;}@media screen{html, body{overflow: hidden;}#wrapwrap{overflow: auto;}}

/* /web/static/src/scss/fontawesome_overridden.scss */
 @font-face{font-family: 'FontAwesome-tiktok-only'; src: url("/web/static/src/scss/../../fonts/tiktok_only.woff"); font-weight: normal; font-style: normal; font-display: block;}@font-face{font-family: 'FontAwesome-twitter-x-only'; src: url("/web/static/src/scss/../../fonts/twitter_x_only.woff"); font-weight: normal; font-style: normal; font-display: block;}.fa.fa-tiktok{font-family: 'FontAwesome-tiktok-only' !important;}.fa.fa-tiktok:before{content: '\e07b';}.fa-twitter.fa{font-family: 'FontAwesome-twitter-x-only' !important;}.fa-twitter.fa:before{content: '\e800';}.fa-twitter-square.fa{font-family: 'FontAwesome-twitter-x-only' !important;}.fa-twitter-square.fa:before{content: '\e803';}.o_rtl .fa.fa-caret-square-o-left, .o_rtl .fa.fa-arrow-circle-o-right, .o_rtl .fa.fa-arrow-circle-o-left, .o_rtl .fa.fa-caret-square-o-right, .o_rtl .fa.fa-toggle-left, .o_rtl .fa.fa-toggle-right, .o_rtl .fa.fa-long-arrow-left, .o_rtl .fa.fa-long-arrow-right, .o_rtl .fa.fa-chevron-circle-left, .o_rtl .fa.fa-chevron-circle-right, .o_rtl .fa.fa-quote-left, .o_rtl .fa.fa-quote-right, .o_rtl .fa.fa-angle-left, .o_rtl .fa.fa-angle-right, .o_rtl .fa.fa-angle-double-left, .o_rtl .fa.fa-angle-double-right, .o_rtl .fa.fa-rotate-left, .o_rtl .fa.fa-rotate-right, .o_rtl .fa.fa-caret-left, .o_rtl .fa.fa-caret-right, .o_rtl .fa.fa-arrow-circle-left, .o_rtl .fa.fa-arrow-circle-right, .o_rtl .fa.fa-hand-o-left, .o_rtl .fa.fa-hand-o-right, .o_rtl .fa.fa-arrow-left, .o_rtl .fa.fa-arrow-right, .o_rtl .fa.fa-chevron-left, .o_rtl .fa.fa-chevron-right, .o_rtl .fa.fa-align-left, .o_rtl .fa.fa-align-right{transform: rotate(180deg);}

/* /web/static/src/scss/mimetypes.scss */
 .o_image{display: inline-block; width: 38px; height: 38px; background-image: url("/web/static/img/mimetypes/unknown.svg"); background-size: contain; background-repeat: no-repeat; background-position: center;}.o_image[data-mimetype^='image']{background-image: url("/web/static/img/mimetypes/image.svg");}.o_image[data-mimetype^='audio']{background-image: url("/web/static/img/mimetypes/audio.svg");}.o_image[data-mimetype^='text'], .o_image[data-mimetype$='rtf']{background-image: url("/web/static/img/mimetypes/text.svg");}.o_image[data-mimetype*='octet-stream'], .o_image[data-mimetype*='download'], .o_image[data-mimetype*='python']{background-image: url("/web/static/img/mimetypes/binary.svg");}.o_image[data-mimetype^='video'], .o_image[title$='.mp4'], .o_image[title$='.avi']{background-image: url("/web/static/img/mimetypes/video.svg");}.o_image[data-mimetype$='archive'], .o_image[data-mimetype$='compressed'], .o_image[data-mimetype*='zip'], .o_image[data-mimetype$='tar'], .o_image[data-mimetype*='package']{background-image: url("/web/static/img/mimetypes/archive.svg");}.o_image[data-mimetype='application/pdf']{background-image: url("/web/static/img/mimetypes/pdf.svg");}.o_image[data-mimetype^='text-master'], .o_image[data-mimetype*='document'], .o_image[data-mimetype*='msword'], .o_image[data-mimetype*='wordprocessing']{background-image: url("/web/static/img/mimetypes/document.svg");}.o_image[data-mimetype*='application/xml'], .o_image[data-mimetype$='html']{background-image: url("/web/static/img/mimetypes/web_code.svg");}.o_image[data-mimetype$='css'], .o_image[data-mimetype$='less'], .o_image[data-ext$='less']{background-image: url("/web/static/img/mimetypes/web_style.svg");}.o_image[data-mimetype*='-image'], .o_image[data-mimetype*='diskimage'], .o_image[data-ext$='dmg']{background-image: url("/web/static/img/mimetypes/disk.svg");}.o_image[data-mimetype$='csv'], .o_image[data-mimetype*='vc'], .o_image[data-mimetype*='excel'], .o_image[data-mimetype$='numbers'], .o_image[data-mimetype$='calc'], .o_image[data-mimetype*='mods'], .o_image[data-mimetype*='spreadsheet']{background-image: url("/web/static/img/mimetypes/spreadsheet.svg");}.o_image[data-mimetype^='key']{background-image: url("/web/static/img/mimetypes/certificate.svg");}.o_image[data-mimetype*='presentation'], .o_image[data-mimetype*='keynote'], .o_image[data-mimetype*='teacher'], .o_image[data-mimetype*='slideshow'], .o_image[data-mimetype*='powerpoint']{background-image: url("/web/static/img/mimetypes/presentation.svg");}.o_image[data-mimetype*='cert'], .o_image[data-mimetype*='rules'], .o_image[data-mimetype*='pkcs'], .o_image[data-mimetype$='stl'], .o_image[data-mimetype$='crl']{background-image: url("/web/static/img/mimetypes/certificate.svg");}.o_image[data-mimetype*='-font'], .o_image[data-mimetype*='font-'], .o_image[data-ext$='ttf']{background-image: url("/web/static/img/mimetypes/font.svg");}.o_image[data-mimetype*='-dvi']{background-image: url("/web/static/img/mimetypes/print.svg");}.o_image[data-mimetype*='script'], .o_image[data-mimetype*='x-sh'], .o_image[data-ext*='bat'], .o_image[data-mimetype$='bat'], .o_image[data-mimetype$='cgi'], .o_image[data-mimetype$='-c'], .o_image[data-mimetype*='java'], .o_image[data-mimetype*='ruby']{background-image: url("/web/static/img/mimetypes/script.svg");}.o_image[data-mimetype*='javascript']{background-image: url("/web/static/img/mimetypes/javascript.svg");}.o_image[data-mimetype*='calendar'], .o_image[data-mimetype$='ldif']{background-image: url("/web/static/img/mimetypes/calendar.svg");}.o_image[data-mimetype$='postscript'], .o_image[data-mimetype$='cdr'], .o_image[data-mimetype$='xara'], .o_image[data-mimetype$='cgm'], .o_image[data-mimetype$='graphics'], .o_image[data-mimetype$='draw'], .o_image[data-mimetype*='svg']{background-image: url("/web/static/img/mimetypes/vector.svg");}

/* /web/static/src/scss/ui.scss */
 :root .o_hidden{display: none !important;}.o_disabled{pointer-events: none; opacity: 0.5;}.o_text_overflow{display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top;}.dropdown-menu{max-height: 70vh; overflow: auto; background-clip: border-box;}.dropdown-toggle{white-space: nowrap;}.dropdown-toggle.o-no-caret::before, .dropdown-toggle.o-no-caret::after{content: normal;}.o_catch_attention{position: relative; z-index: 1; animation: catchAttention 200ms ease 0s infinite normal;}.o_treeEntry{padding-left: var(--treeEntry-padding-h, 1.5rem); position: relative;}.o_treeEntry:before, .o_treeEntry:after{position: absolute; left: var(--treeEntry--beforeAfter-left, calc(var(--treeEntry-padding-h, 1.5rem) * .5)); background: var(--treeEntry--beforeAfter-color, #dee2e6); content: '';}.o_treeEntry:before{top: var(--treeEntry--before-top, 0); width: 1px; height: 100%;}.o_treeEntry:after{display: var(--treeEntry--after-display, initial); top: calc(.5em + var(--treeEntry-padding-v, 0.5rem)); width: calc(var(--treeEntry-padding-h, 1.5rem) * .5); height: 1px;}.o_treeEntry:last-of-type:before{height: calc(.5em + var(--treeEntry-padding-v, 0.5rem));}@keyframes catchAttention{0%, 20%, 40%, 60%, 80%, 100%{transition-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);}0%{transform: translateY(-30%);}20%{transform: translateY(-25%);}40%{transform: translateY(-20%);}60%{transform: translateY(-15%);}80%{transform: translateY(-10%);}100%{transform: translateY(-5%);}}span.o_force_ltr{display: inline;}.o_force_ltr{unicode-bidi: embed; direction: ltr;}.o_object_fit_cover{object-fit: cover;}.o_object_fit_contain{object-fit: contain;}.o_image_24_cover{width: 24px; height: 24px; object-fit: cover;}.o_image_40_cover{width: 40px; height: 40px; object-fit: cover;}.o_image_64_cover{width: 64px; height: 64px; object-fit: cover;}.o_image_64_contain{width: 64px; height: 64px; object-fit: contain;}.o_image_64_max{max-width: 64px; max-height: 64px;}

/* /web/static/src/views/fields/translation_dialog.scss */
 .o_translation_dialog .o_language_current{font-weight: bold;}.o_translation_dialog .row{margin-bottom: 9px;}

/* /web/static/src/views/fields/signature/signature_field.scss */
 .o_field_widget .o_signature{outline: 1px solid rgba(133, 149, 162, 0.3); position: relative;}.o_field_widget .o_signature.o_signature_empty{display: -webkit-box; display: -webkit-flex; display: flex;}.o_field_widget .o_signature > p{position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);}.o_field_invalid .o_signature{outline: 3px solid #dc3545; cursor: pointer;}.o_form_editable .o_signature:hover{outline: 3px solid #017e84; cursor: pointer;}

/* /web/static/src/legacy/scss/ui.scss */
 .ui-autocomplete{z-index: 1056; max-width: 600px;}.ui-autocomplete .ui-menu-item > a{display: block;}.o_rtl .ui-autocomplete{direction: ltr; right: 0; left: auto;}

/* /web/static/src/legacy/scss/modal.scss */
 .modal.o_technical_modal .modal-content .modal-header .o_subtitle{margin-left: 10px;}@media (max-width: 575.98px){.modal.o_technical_modal.o_modal_full .modal-dialog .modal-content .modal-header .o_subtitle{color: rgba(255, 255, 255, 0.9);}}

/* /web/static/src/legacy/scss/lazyloader.scss */
 

/* /web/static/src/core/utils/transitions.scss */
 

/* /web/static/src/core/action_swiper/action_swiper.scss */
 .o_actionswiper{position: relative; touch-action: pan-y;}.o_actionswiper_target_container{transition: transform 0.4s;}.o_actionswiper_swiping{transition: none;}.o_actionswiper_right_swipe_area{transform: translateX(-100%); inset: 0 auto auto 0;}.o_actionswiper_left_swipe_area{transform: translateX(100%); inset: 0 0 auto auto;}

/* /web/static/src/core/autocomplete/autocomplete.scss */
 .o-autocomplete .o-autocomplete--input{width: 100%;}

/* /web/static/src/core/avatar/avatar.scss */
 .o_avatar img, .o_avatar .o_avatar_empty, img.o_avatar{height: var(--Avatar-size, 1.7145em); aspect-ratio: 1; object-fit: cover;}.o_avatar_empty{background: #000000; opacity: .1;}

/* /web/static/src/core/checkbox/checkbox.scss */
 .o-checkbox{width: fit-content;}

/* /web/static/src/core/colorlist/colorlist.scss */
 .o_colorlist button{border: 1px solid #FFFFFF; box-shadow: 0 0 0 1px #ADB5BD; width: 22px; height: 17px;}.o_colorlist .o_colorlist_selected{box-shadow: 0 0 0 2px #71639e !important;}.o_colorlist_item_color_1{--background-color: RGBA(255, 155.5, 155.5, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(67.15870044, 11.84129956, 11.84129956, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_2{--background-color: RGBA(247.0375, 198.06116071, 152.4625, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(52.9, 33.325, 15.1, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_3{--background-color: RGBA(252.88960843, 226.89175248, 135.61039157, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(47.45993976, 39.05405514, 9.54006024, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_4{--background-color: RGBA(187.45210396, 215.03675558, 248.04789604, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(25.3049505, 49.60939855, 78.6950495, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_5{--background-color: RGBA(216.79194664, 167.70805336, 203.91748283, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(28.91432806, 24.08567194, 27.64779531, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_6{--background-color: RGBA(247.84539474, 213.9484835, 199.65460526, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(85.32105263, 46.88635147, 30.67894737, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_7{--background-color: RGBA(136.6125, 224.8875, 218.94591346, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(10.58333333, 19.41666667, 18.82211538, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_8{--background-color: RGBA(150.60535714, 165.68382711, 248.89464286, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(13.95714286, 20.10665584, 54.04285714, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_9{--background-color: RGBA(254.94583333, 157.55416667, 203.95543194, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(68.805, 12.195, 39.16625654, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_10{--background-color: RGBA(182.62075688, 236.87924312, 189.81831118, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(30.82018349, 57.17981651, 34.3168695, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_11{--background-color: RGBA(230.11575613, 219.41069277, 252.08930723, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(56.75321978, 31.58433735, 108.41566265, var(--text-opacity, 1)); color: var(--color) !important;}.o_colorlist_item_color_0{background: linear-gradient(45deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0) 48%, #dc3545 48%, #dc3545 52%, rgba(255, 255, 255, 0) 52%, rgba(255, 255, 255, 0) 100%);}

/* /web/static/src/core/colorpicker/colorpicker.scss */
 .o_colorpicker_widget .o_color_pick_area{height: 125px; background-image: linear-gradient(to bottom, white 0%, rgba(255, 255, 255, 0) 50%, rgba(0, 0, 0, 0) 50%, black 100%), linear-gradient(to right, gray 0%, rgba(128, 128, 128, 0) 100%); cursor: crosshair;}.o_colorpicker_widget .o_color_slider{background: linear-gradient(#F00 0%, #FF0 16.66%, #0F0 33.33%, #0FF 50%, #00F 66.66%, #F0F 83.33%, #F00 100%);}.o_colorpicker_widget .o_color_slider, .o_colorpicker_widget .o_opacity_slider{width: 4%; margin-right: 2%; cursor: pointer;}.o_colorpicker_widget .o_slider_pointer, .o_colorpicker_widget .o_opacity_pointer{position: absolute; top: auto; left: -50%; bottom: auto; right: auto; width: 200%; height: 8px; margin-top: -2px;}.o_colorpicker_widget .o_slider_pointer, .o_colorpicker_widget .o_opacity_pointer, .o_colorpicker_widget .o_picker_pointer, .o_colorpicker_widget .o_color_preview{box-shadow: inset 0 0 0 1px rgba(255, 255, 255, 0.9); border: 1px solid black;}.o_colorpicker_widget .o_color_picker_inputs{font-size: 10px;}.o_colorpicker_widget .o_color_picker_inputs input{font-family: monospace !important; height: 18px; font-size: 11px;}.o_colorpicker_widget .o_color_picker_inputs .o_hex_div input{width: 7ch;}.o_colorpicker_widget .o_color_picker_inputs .o_rgba_div input{margin-right: 3px; width: 3ch;}

/* /web/static/src/core/datetime/datetime_picker.scss */
 .o_datetime_picker{--DateTimePicker__Cell-size-md: 3rem; --DateTimePicker__Cell-size-lg: 5rem; --DateTimePicker__Template-rows: 3; --DateTimePicker__Template-columns: 4; --DateTimePicker__Day-template-rows: 6;}.o_datetime_picker .o_datetime_picker_header .o_header_part{text-transform: none;}.o_datetime_picker .o_date_item_cell{position: relative; border-radius: 0;}.o_datetime_picker .o_current, .o_datetime_picker .o_selected{color: #212529;}.o_datetime_picker .o_selected:not(.o_select_start):not(.o_select_end){background: #dddbe8;}.o_datetime_picker .o_current:before, .o_datetime_picker .o_highlight_start:not(.o_selected):before, .o_datetime_picker .o_highlight_end:not(.o_selected):before, .o_datetime_picker .o_select_start:before, .o_datetime_picker .o_select_end:before{content: ""; position: absolute; box-shadow: inset 0 0 0 1px #71639e; width: 100%; aspect-ratio: 1; border-radius: 100%; z-index: 1;}.o_datetime_picker .o_select_start:before, .o_datetime_picker .o_select_end:before{background: #d1cee0;}.o_datetime_picker .o_select_start:after, .o_datetime_picker .o_select_end:after{content: ""; position: absolute; background: transparent; width: 50%; aspect-ratio: 1/2;}.o_datetime_picker .o_select_start:not(.o_select_end):after, .o_datetime_picker .o_select_end:not(.o_select_end):after{right: 0; background: #dddbe8;}.o_datetime_picker .o_select_start:not(.o_select_start):after, .o_datetime_picker .o_select_end:not(.o_select_start):after{right: 50%; background: #dddbe8;}.o_datetime_picker .o_today:not(.o_out_of_range) span{position: relative;}.o_datetime_picker .o_today:not(.o_out_of_range) span::after{content: ""; position: absolute; left: 50%; bottom: -0.25rem; transform: translateX(-50%); width: 0.95em; height: 0.2em; border-radius: 50rem; background: #dc3545;}.o_datetime_picker .o_out_of_range{color: var(--gray-400);}.o_datetime_picker .o_time_picker_select{background: none;}.o_datetime_picker .o_time_picker_select:focus, .o_datetime_picker .o_time_picker_select:hover{border-color: var(--primary);}.o_datetime_picker .o_date_picker{grid-template-rows: repeat(var(--DateTimePicker__Day-template-rows), 1fr); grid-template-columns: repeat(var(--DateTimePicker__Day-template-columns), 1fr);}.o_datetime_picker .o_date_item_picker{grid-template-rows: repeat(var(--DateTimePicker__Template-rows), 1fr); grid-template-columns: repeat(var(--DateTimePicker__Template-columns), 1fr);}.o_datetime_picker .o_date_item_picker .o_datetime_button.o_selected:not(.o_select_start):not(.o_select_end), .o_datetime_picker .o_date_item_picker .o_datetime_button:hover:not(.o_select_start):not(.o_select_end), .o_datetime_picker .o_date_item_picker .o_datetime_button.o_today:not(.o_selected):hover:not(.o_select_start):not(.o_select_end){background: #dddbe8; color: #212529;}.o_datetime_picker .o_center{display: grid; place-items: center;}.o_datetime_picker .o_zoom_out{gap: 3.359375rem;}.o_datetime_picker .o_cell_md{aspect-ratio: 1;}@media (min-width: 768px){.o_datetime_picker .o_cell_md{padding: 0.4rem; width: var(--DateTimePicker__Cell-size-md); height: var(--DateTimePicker__Cell-size-md);}}.o_datetime_picker .o_cell_lg{width: var(--DateTimePicker__Cell-size-lg); height: var(--DateTimePicker__Cell-size-lg);}.o_datetime_picker .o_text_sm{font-size: 0.875rem;}.o_datetime_picker .o_time_picker{direction: ltr;}

/* /web/static/src/core/debug/debug_menu.scss */
 .o_dialog .o_debug_manager .dropdown-toggle{padding: 0 4px; margin: 2px 10px 2px 0;}

/* /web/static/src/core/debug/profiling/profiling_item.scss */
 .o_debug_manager .dropdown-menu .o_debug_profiling_item_wrapper.dropdown-item.focus{background: inherit;}.o_debug_manager .o_debug_profiling_item{cursor: auto;}.o_debug_manager .form-switch{cursor: pointer;}.o_debug_recording{animation: 2s flash infinite;}

/* /web/static/src/core/debug/profiling/profiling_qweb.scss */
 .o_form_view .o_ace_view_editor{background: transparent;}.o_profiling_qweb_view{user-select: none;}.o_profiling_qweb_view .o_select_view_profiling{margin-bottom: 10px;}.o_profiling_qweb_view .o_select_view_profiling .dropdown-menu{overflow: auto; max-height: 240px;}.o_profiling_qweb_view .o_select_view_profiling a{margin: 3px 0; display: block;}.o_profiling_qweb_view .o_select_view_profiling a .o_delay, .o_profiling_qweb_view .o_select_view_profiling a .o_query{font-size: 0.8em; display: inline-block; color: #495057; text-align: right; width: 50px; margin-right: 10px; white-space: nowrap;}.o_profiling_qweb_view .o_select_view_profiling a .o_key{display: inline-block; margin-left: 10px; font-size: 0.8em;}.o_profiling_qweb_view .ace_editor{overflow: visible;}.o_profiling_qweb_view .ace_editor .ace_qweb, .o_profiling_qweb_view .ace_editor .ace_tag-name{cursor: default; pointer-events: all; position: relative;}.o_profiling_qweb_view .ace_editor .ace_qweb .o_info, .o_profiling_qweb_view .ace_editor .ace_tag-name .o_info{display: none; left: 8px; top: 14px; width: 100px;}.o_profiling_qweb_view .ace_editor .ace_qweb .o_info .o_delay span, .o_profiling_qweb_view .ace_editor .ace_qweb .o_info .o_query span, .o_profiling_qweb_view .ace_editor .ace_tag-name .o_info .o_delay span, .o_profiling_qweb_view .ace_editor .ace_tag-name .o_info .o_query span{text-align: left; display: inline-block; width: 40px;}.o_profiling_qweb_view .ace_editor .ace_qweb:hover .o_info, .o_profiling_qweb_view .ace_editor .ace_tag-name:hover .o_info{display: block;}.o_profiling_qweb_view .ace_editor .ace_qweb:hover .o_info:hover, .o_profiling_qweb_view .ace_editor .ace_tag-name:hover .o_info:hover{display: none;}.o_profiling_qweb_view .ace_editor .ace_gutter{overflow: visible;}.o_profiling_qweb_view .ace_editor .ace_gutter-layer{width: 134px !important; overflow: visible;}.o_profiling_qweb_view .ace_editor .ace_gutter-cell .o_info{display: block; float: left; font-size: 0.8em; white-space: nowrap;}.o_profiling_qweb_view .ace_editor .ace_gutter-cell .o_info .o_more{float: left; position: relative;}.o_profiling_qweb_view .ace_editor .ace_gutter-cell .o_info .o_more span{color: orange !important; cursor: default; margin-left: -12px;}.o_profiling_qweb_view .ace_editor .ace_gutter-cell .o_info .o_more .o_detail{left: 30px; top: -30px; min-width: 120px; display: none;}.o_profiling_qweb_view .ace_editor .ace_gutter-cell .o_info .o_more .o_detail th{text-align: center;}.o_profiling_qweb_view .ace_editor .ace_gutter-cell .o_info .o_more .o_detail td{min-width: 60px; vertical-align: top; text-align: left;}.o_profiling_qweb_view .ace_editor .ace_gutter-cell .o_info .o_more .o_detail tr td:first-child{padding-right: 10px; white-space: nowrap;}.o_profiling_qweb_view .ace_editor .ace_gutter-cell .o_info .o_more .o_detail tr th:last-child, .o_profiling_qweb_view .ace_editor .ace_gutter-cell .o_info .o_more .o_detail tr td:last-child{padding-left: 10px;}.o_profiling_qweb_view .ace_editor .ace_gutter-cell .o_info .o_more:hover > .o_detail{display: block;}.o_profiling_qweb_view .ace_editor .ace_gutter-cell .o_info .o_more:hover > .o_detail:hover{display: none;}.o_profiling_qweb_view .ace_editor .ace_gutter-cell .o_info .o_delay, .o_profiling_qweb_view .ace_editor .ace_gutter-cell .o_info .o_query{display: block; float: left; margin-right: 10px; width: 30px;}.o_profiling_qweb_view .ace_editor .ace_line{border-bottom: 1px #dddddd dotted;}.o_profiling_qweb_view .ace_editor .ace_scrollbar-h{z-index: 3;}.o_profiling_qweb_view .ace_editor .o_detail{position: absolute; z-index: 1; background: #ffedcb; color: orange !important; border: 1px orange solid; padding: 6px; white-space: normal; text-align: right;}

/* /web/static/src/core/dialog/dialog.scss */
 .modal.o_technical_modal .modal-content .modal-header .modal-title{overflow: hidden; white-space: nowrap; text-overflow: ellipsis;}.modal.o_technical_modal .modal-footer{text-align: left;}.modal.o_technical_modal .modal-footer footer, .modal.o_technical_modal .modal-footer .o_form_buttons_edit, .modal.o_technical_modal .modal-footer .o_form_buttons_view{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-flex-wrap: wrap; flex-wrap: wrap; -webkit-box-flex: 1; -webkit-flex: 1 1 auto; flex: 1 1 auto; justify-content: space-around; gap: 0.25rem;}@media (min-width: 768px){.modal.o_technical_modal .modal-footer footer, .modal.o_technical_modal .modal-footer .o_form_buttons_edit, .modal.o_technical_modal .modal-footer .o_form_buttons_view{-webkit-box-pack: start; justify-content: flex-start;}}.modal.o_technical_modal .modal-footer button{margin: 0;}@media (max-width: 767.98px){.modal.o_technical_modal .modal-footer .btn{width: 45%; text-overflow: ellipsis; white-space: inherit;}}@media (max-width: 575.98px){.modal.o_technical_modal.o_modal_full .modal-dialog{margin: 0px; height: 100%;}.modal.o_technical_modal.o_modal_full .modal-dialog .modal-content{height: 100%; border: none;}.modal.o_technical_modal.o_modal_full .modal-dialog .modal-content .modal-body{height: 100%; overflow-y: auto;}}.modal.o_inactive_modal{z-index: 1049;}.o_dialog > .modal{display: block;}@media (min-width: 576px){.modal-fs{width: calc(100% - 3.5rem); max-width: none;}}@media (max-width: 767.98px){.modal.o_modal_full .modal-content .modal-header{align-items: center; height: 46px; padding: 0 1rem;}.modal.o_modal_full .modal-content .modal-footer{padding-top: 1rem; padding-right: 16px; padding-bottom: 0.5rem; padding-left: 16px; box-shadow: 0 1rem 2rem black; z-index: 0;}}

/* /web/static/src/core/dropdown/accordion_item.scss */
 .o_accordion_toggle::after{display: inline-block; margin-left: 0.255em; vertical-align: 0.255em; content: ""; border-top: 0.3em solid; border-right: 0.3em solid transparent; border-bottom: 0; border-left: 0.3em solid transparent;}.o_accordion_toggle:empty::after{margin-left: 0;}.o_accordion_toggle.open::after{display: inline-block; margin-left: 0.255em; vertical-align: 0.255em; content: ""; border-top: 0; border-right: 0.3em solid transparent; border-bottom: 0.3em solid; border-left: 0.3em solid transparent;}.o_accordion_toggle.open:empty::after{margin-left: 0;}.o_accordion_toggle::after{position: absolute; top: 0; left: auto; bottom: auto; right: 0; transform: translate(-0.6em, 0.8em);}

/* /web/static/src/core/dropdown/dropdown.scss */
 .o-dropdown{}.o-dropdown--menu{position: fixed; margin-top: var(--Dropdown_menu-margin-y, 0.25rem); margin-bottom: var(--Dropdown_menu-margin-y, 0.25rem);}.o-dropdown--menu.o-dropdown--menu-submenu{--Dropdown_menu-margin-y: 0;}.o-dropdown--menu .dropdown-toggle:focus, .o-dropdown--menu .dropdown-item:focus{background-color: transparent; outline: none;}.o-dropdown--menu .dropdown-toggle.focus, .o-dropdown--menu .dropdown-item.focus{background-color: #E9ECEF;}.o-dropdown--menu .dropdown-item:not(.disabled):not(:disabled):not(.o_wysiwyg_loader), .o-dropdown--menu .dropdown-item:not(.disabled):not(:disabled):not(.o_wysiwyg_loader) label{cursor: pointer;}.o-dropdown--menu .o-dropdown > .o-dropdown--menu{--o-dropdown--submenu-margin: calc(-.5rem - 1px); margin-top: var(--o-dropdown--submenu-margin); margin-bottom: var(--o-dropdown--submenu-margin);}.o-dropdown.dropup > .o-dropdown--menu, .o-dropdown.dropdown > .o-dropdown--menu, .o-dropdown.dropstart > .o-dropdown--menu, .o-dropdown.dropend > .o-dropdown--menu{left: auto; right: auto; margin-left: 0; margin-right: 0;}.o-dropdown--no-caret > .dropdown-toggle::before, .o-dropdown--no-caret > .dropdown-toggle::after{content: normal;}.o-dropdown button.dropdown-toggle.active, .o-dropdown button.dropdown-toggle:hover, .o-dropdown button.dropdown-toggle:focus, .o-dropdown button.dropdown-toggle:active{outline: none; box-shadow: none !important;}.o-dropdown button.dropdown-toggle.dropdown-item::after, .o-dropdown button.dropdown-toggle.dropdown-item::before{position: absolute; top: 0; left: auto; bottom: auto; right: 0; transform: translate(-0.6em, 0.6em) ;}

/* /web/static/src/core/effects/rainbow_man.scss */
 .o_reward{will-change: transform; z-index: 1055; animation: reward-fading 0.7s ease-in-out forwards;}.o_reward .o_reward_box{transform-box: fill-box;}.o_reward.o_reward_fading{animation: reward-fading-reverse 0.56s ease-in-out forwards;}.o_reward.o_reward_fading .o_reward_face_group{animation: reward-jump-reverse 0.56s ease-in-out forwards;}.o_reward.o_reward_fading .o_reward_rainbow_line{animation: reward-rainbow-reverse 0.7s ease-out forwards;}.o_reward .o_reward_rainbow_man{max-width: 400px;}.o_reward .o_reward_rainbow_line{animation: reward-rainbow 1.12s ease-out 1 forwards;}.o_reward .o_reward_face_group{animation: reward-jump 1.12s ease-in-out 1;}.o_reward .o_reward_face_wrap{animation: reward-rotate 1.12s cubic-bezier(0.51, 0.92, 0.24, 1.15) 1;}.o_reward .o_reward_face{animation: reward-float 1.4s ease-in-out 1.4s infinite alternate;}.o_reward .o_reward_star_01, .o_reward .o_reward_star_03{animation: reward-stars 1.4s ease-in-out infinite alternate-reverse;}.o_reward .o_reward_star_02, .o_reward .o_reward_star_04{animation: reward-stars 1.68s ease-in-out infinite alternate;}.o_reward .o_reward_thumbup{animation: reward-scale 0.7s ease-in-out 0s infinite alternate;}.o_reward .o_reward_shadow_container{animation: reward-float 1.4s ease-in-out infinite alternate;}.o_reward .o_reward_shadow{animation: reward-scale 1.4s ease-in-out infinite alternate;}.o_reward .o_reward_msg_container{aspect-ratio: 1 / 1; animation: reward-float-reverse 1.4s ease-in-out infinite alternate-reverse;}@keyframes reward-fading{0%{opacity: 0;}}@keyframes reward-fading-reverse{100%{opacity: 0;}}@keyframes reward-jump{0%{transform: scale(0.5);}50%{transform: scale(1.05);}}@keyframes reward-jump-reverse{50%{transform: scale(1.05);}to{transform: scale(0.5);}}@keyframes reward-rainbow{to{stroke-dashoffset: 0;}}@keyframes reward-rainbow-reverse{from{stroke-dashoffset: 0;}}@keyframes reward-float{to{transform: translateY(5px);}}@keyframes reward-float-reverse{from{transform: translateY(5px);}}@keyframes reward-stars{from{transform: scale(0.3) rotate(0deg);}50%{transform: scale(1) rotate(20deg);}to{transform: scale(0.3) rotate(80deg);}}@keyframes reward-scale{from{transform: scale(0.8);}}@keyframes reward-rotate{from{transform: scale(0.5) rotate(-30deg);}}

/* /web/static/src/core/emoji_picker/emoji_picker.dark.scss */
 .o-EmojiPicker{--o-emoji-picker-active: #FFFFFF;}

/* /web/static/src/core/emoji_picker/emoji_picker.scss */
 .popover .o-EmojiPicker{width: 285px; height: 350px;}.o-EmojiPicker{--o-emoji-picker-active: #E9ECEF;}.o-EmojiPicker .o-active{background-color: var(--o-emoji-picker-active) !important;}.o-EmojiPicker .o-Emoji{width: 30px; font-size: 0.8rem;}.o-EmojiPicker .o-Emoji:hover{background-color: var(--o-emoji-picker-active) !important;}.o-EmojiPicker .o-EmojiPicker-navbar .o-Emoji{filter: grayscale(1);}.o-EmojiPicker .o-EmojiPicker-sectionIcon{filter: grayscale(1);}.o-EmojiPicker .o-EmojiPicker-empty{font-size: 5rem !important; filter: grayscale(0.25);}.o-EmojiPicker-category:before{content: "\200b";}.o-EmojiPicker-search input:not(:focus) + .oi-search{color: #6C757D;}

/* /web/static/src/core/errors/error_dialog.scss */
 @media (min-width: 768px){.o_error_dialog{padding: 1rem;}.o_error_dialog .o_error_detail{overflow: auto;}.o_error_dialog .o_error_detail pre{overflow: initial; max-height: 30vh;}}.o_error_dialog .modal-header{border: none; padding-bottom: 0; margin-bottom: -1rem;}.o_error_dialog .modal-header .modal-title{font-size: 1.625rem;}.o_error_dialog .modal-footer{border: none; padding-top: 0;}

/* /web/static/src/core/file_upload/file_upload_progress_bar.scss */
 .o-file-upload-progress-bar-value{transition: width 0.1s; border-right: 1px solid #605487; background-color: #71639e; opacity: 0.5;}.o-file-upload-progress-bar-abort{padding: 4px; color: #963535; font-size: 16px;}.o-file-upload-progress-bar-abort:active{opacity: 0.7;}div:not(:hover) .o-file-upload-progress-bar-abort{display: none;}

/* /web/static/src/core/file_upload/file_upload_progress_record.scss */
 .o_kanban_record .o_kanban_progress_card{min-height: 80px;}.o_kanban_record .o_kanban_progress_card .o_kanban_record_bottom{color: #212529;}.o_kanban_record .o_kanban_progress_card .o_kanban_image_wrapper{opacity: 0.7;}.o_data_row.o_list_progress_card{height: 25px; border: 1px solid #dfdfdf;}.o_data_row.o_list_progress_card .o_file_upload_upload_title{font-size: 13px; font-weight: 500;}

/* /web/static/src/core/file_viewer/file_viewer.scss */
 .o-FileViewer{z-index: -1; outline: none;}.o-FileViewer-navigation{width: 40px; height: 40px;}.o-FileViewer-header{height: 46px;}.o-FileViewer-main{z-index: -1; padding: 51.75px 0;}.o-FileViewer-zoomer{padding: 51.75px 0;}.o-FileViewer-headerButton:hover{background-color: rgba(255, 255, 255, 0.1); color: #fafafb;}.o-FileViewer-toolbarButton{background-color: var(--file-viewer-toolbarButton-background-color, #343a40); color: #fff;}.o-FileViewer-toolbarButton:hover{filter: brightness(1.3);}.o-FileViewer-view{background-color: #000000; box-shadow: 0 0 40px #000000; outline: none;}.o-FileViewer-view.o-isText{background: #FFFFFF;}

/* /web/static/src/core/install_prompt/install_prompt.scss */
 .o_install_prompt{border-radius: 20px; height: unset !important; -webkit-backdrop-filter: blur(5px); backdrop-filter: blur(5px); background: rgba(255, 255, 255, 0.7); inset: 0 auto auto 0 !important; width: fit-content !important;}.o_install_prompt.o_touch_bounce{animation: none;}@media screen and (max-width: 768px){.o_install_prompt{inset: auto auto 0 0 !important; width: 90% !important; margin: 5% !important;}.o_install_prompt .modal-header{background: none !important; border: none !important;}.o_install_prompt .modal-header button{color: #000000 !important;}}

/* /web/static/src/core/model_field_selector/model_field_selector.scss */
 .o_model_field_selector{position: relative;}.o_model_field_selector.o_edit_mode{cursor: pointer;}.o_model_field_selector > .o_model_field_selector_value{min-height: 20px; max-width: 100%; word-wrap: break-word;}.o_model_field_selector > .o_model_field_selector_value:active, .o_model_field_selector > .o_model_field_selector_value:focus, .o_model_field_selector > .o_model_field_selector_value:active:focus{outline: none;}.o_model_field_selector > .o_model_field_selector_value > .o_model_field_selector_chain_part{cursor: inherit; border: 1px solid #dae0e5; background: #f8f9fa;}.o_model_field_selector > .o_model_field_selector_value > i{font-size: 10px;}

/* /web/static/src/core/model_field_selector/model_field_selector_popover.scss */
 .o_popover_field_selector.o-popover-top > .popover-arrow::after{border-top-color: #dee2e6;}.o_popover_field_selector.o-popover-bottom > .popover-arrow::after{border-bottom-color: #dee2e6;}.o_model_field_selector_popover{width: 265px; background-color: #FFFFFF; --o-input-background-color: #FFFFFF;}.o_model_field_selector_popover:focus{outline: none;}.o_model_field_selector_popover .o_model_field_selector_popover_header, .o_model_field_selector_popover .o_model_field_selector_popover_footer{color: #212529; background: #dee2e6; font-weight: bold; padding: 5px 0.4em;}.o_model_field_selector_popover .o_model_field_selector_popover_header .o_model_field_selector_popover_title, .o_model_field_selector_popover .o_model_field_selector_popover_footer .o_model_field_selector_popover_title{width: 100%; display: inline-block; max-width: 100%; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; vertical-align: top; padding: 0px 35px; text-align: center;}.o_model_field_selector_popover .o_model_field_selector_popover_header .o_model_field_selector_popover_option, .o_model_field_selector_popover .o_model_field_selector_popover_footer .o_model_field_selector_popover_option{position: absolute; top: 0; left: auto; bottom: auto; right: auto; padding: 8px; cursor: pointer;}.o_model_field_selector_popover .o_model_field_selector_popover_header .o_model_field_selector_popover_option.o_model_field_selector_popover_prev_page, .o_model_field_selector_popover .o_model_field_selector_popover_footer .o_model_field_selector_popover_option.o_model_field_selector_popover_prev_page{left: 0;}.o_model_field_selector_popover .o_model_field_selector_popover_header .o_model_field_selector_popover_option.o_model_field_selector_popover_close, .o_model_field_selector_popover .o_model_field_selector_popover_footer .o_model_field_selector_popover_option.o_model_field_selector_popover_close{right: 0;}.o_model_field_selector_popover .o_model_field_selector_popover_header .o_model_field_selector_popover_option:hover, .o_model_field_selector_popover .o_model_field_selector_popover_footer .o_model_field_selector_popover_option:hover{color: #0a0c0d;}.o_model_field_selector_popover .o_model_field_selector_popover_body .o_model_field_selector_popover_page{position: relative; height: 320px; overflow: auto; margin: 0; padding: 0;}.o_model_field_selector_popover .o_model_field_selector_popover_body .o_model_field_selector_popover_page > .o_model_field_selector_popover_item{list-style: none; cursor: pointer; font-family: Arial; font-size: 13px; border-bottom: 1px solid #d6d9dc;}.o_model_field_selector_popover .o_model_field_selector_popover_body .o_model_field_selector_popover_page > .o_model_field_selector_popover_item.active button{background: #E9ECEF; border: none;}.o_model_field_selector_popover .o_model_field_selector_popover_body .o_model_field_selector_popover_page > .o_model_field_selector_popover_item .o_model_field_selector_popover_item_title{font-size: 12px;}.o_model_field_selector_popover .o_model_field_selector_popover_body .o_model_field_selector_popover_page > .o_model_field_selector_popover_item .o_model_field_selector_popover_item_relation{border-radius: 0px; border-left: 1px solid #d6d9dc;}

/* /web/static/src/core/model_selector/model_selector.scss */
 .o_model_selector .o-autocomplete--dropdown-menu{width: 25ch; max-height: 350px !important;}.o_model_selector .o-autocomplete--dropdown-menu .o-autocomplete--dropdown-item a{text-overflow: ellipsis; width: inherit;}

/* /web/static/src/core/notebook/notebook.scss */
 .o_notebook{--notebook-margin-x: 0; --notebook-padding-x: 0; --notebook-link-border-color: transparent; --notebook-link-border-color-active: #dee2e6; --notebook-link-border-color-hover: #E9ECEF; --notebook-link-border-color-active-accent: #dee2e6;}.o_notebook .o_notebook_headers{margin: 0 var(--notebook-margin-x, 0); overflow-x: auto;}@media (max-width: 767.98px){.o_notebook .o_notebook_headers::-webkit-scrollbar{display: none;}}.o_notebook .nav{padding: 0 var(--notebook-padding-x, 0); background-color: white;}.o_notebook .nav-item{white-space: nowrap; margin: 0 -1px 0 0;}.o_notebook .nav-item.disabled .nav-link{cursor: not-allowed; opacity: .3;}.o_notebook .nav-link{border-color: var(--notebook-link-border-color, transparent);}.o_notebook .nav-link.active, .o_notebook .nav-link.active:hover, .o_notebook .nav-link.active:focus, .o_notebook .nav-link.active:active{border-color: var(--notebook-link-border-color-active); border-top-color: var(--notebook-link-border-color-active-accent, var(--notebook-link-border-color-active)); border-bottom-color: white;}.o_notebook .nav-link:hover, .o_notebook .nav-link:focus, .o_notebook .nav-link:active{outline: none;}.o_notebook .nav-link:hover{border-color: var(--notebook-link-border-color-hover);}.o_notebook.vertical .o_notebook_headers{overflow-x: visible;}.o_notebook.vertical .nav{width: max-content; border-bottom-color: transparent;}.o_notebook.vertical .nav-item{margin: 0 0 -1px 0;}.o_notebook.vertical .nav-item:first-child .nav-link{border-top-width: 0;}.o_notebook.vertical .nav-link{margin-bottom: 0;}.o_notebook.vertical .nav-link.active, .o_notebook.vertical .nav-link.active:hover, .o_notebook.vertical .nav-link.active:focus, .o_notebook.vertical .nav-link.active:active{border-color: var(--notebook-link-border-color-active); border-left-color: var(--notebook-link-border-color-active-accent, var(--notebook-link-border-color-active)); border-right-color: white;}@media (max-width: 991.98px){.o_notebook .o_notebook_content .oe-toolbar{position: -webkit-sticky; position: sticky; top: 0px; left: auto; bottom: auto; right: auto; margin-left: var(--notebook-margin-x, 16px); margin-right: var(--notebook-margin-x, 16px); width: auto;}}

/* /web/static/src/core/notifications/notification.scss */
 .o_notification_manager{position: absolute; top: 52.9px; left: calc(100vw - 320px); bottom: auto; right: 0.5rem; z-index: 1055;}.o_notification_manager .o_notification{background-color: var(--Notification__background-color, white); border-left-width: 0.75rem !important; box-shadow: 0 0.25rem 0.75rem rgba(0, 0, 0, 0.1);}.o_notification_manager .o_notification_close{position: absolute; top: 0; left: auto; bottom: auto; right: 0;}.o_notification_fade{transition: all 0.5s;}.o_notification_fade-enter{opacity: 0;}

/* /web/static/src/core/popover/popover.scss */
 .o_popover.o-popover-top, .o_popover.o-popover-auto[x-placement^="top"]{margin-top: -0.5rem;}.o_popover.o-popover-right, .o_popover.o-popover-auto[x-placement^="right"]{margin-left: 0.5rem;}.o_popover.o-popover-bottom, .o_popover.o-popover-auto[x-placement^="bottom"]{margin-top: 0.5rem;}.o_popover.o-popover-left, .o_popover.o-popover-auto[x-placement^="left"]{margin-left: -0.5rem;}.o_popover.o-popover--ts .popover-arrow, .o_popover.o-popover--bs .popover-arrow{left: 0.3rem;}.o_popover.o-popover--te .popover-arrow, .o_popover.o-popover--be .popover-arrow{right: 0.3rem;}.o_popover.o-popover--ls .popover-arrow, .o_popover.o-popover--rs .popover-arrow{top: 0.3rem;}.o_popover.o-popover--le .popover-arrow, .o_popover.o-popover--re .popover-arrow{bottom: 0.3rem;}

/* /web/static/src/core/record_selectors/record_selectors.scss */
 .o_record_selector:hover .o_dropdown_button:after, .o_record_selector:focus-within .o_dropdown_button:after, .o_multi_record_selector:hover .o_dropdown_button:after, .o_multi_record_selector:focus-within .o_dropdown_button:after{content: ""; display: inline-block; width: 0; height: 0; vertical-align: middle; -moz-transform: scale(0.9999); border-bottom: 0; border-left: 0.3em solid transparent; border-right: 0.3em solid transparent; border-top: 0.3em solid var(--o-caret-color, currentColor);}.o_record_selector .o_record_autocomplete_with_caret, .o_multi_record_selector .o_record_autocomplete_with_caret{display: -webkit-box; display: -webkit-flex; display: flex; min-width: 100%;}.o_record_selector .o_record_autocomplete_with_caret:hover::after, .o_record_selector .o_record_autocomplete_with_caret:focus-within::after, .o_multi_record_selector .o_record_autocomplete_with_caret:hover::after, .o_multi_record_selector .o_record_autocomplete_with_caret:focus-within::after{content: ""; display: inline-block; width: 0; height: 0; vertical-align: middle; -moz-transform: scale(0.9999); border-bottom: 0; border-left: 0.3em solid transparent; border-right: 0.3em solid transparent; border-top: 0.3em solid var(--o-caret-color, currentColor); align-self: center;}

/* /web/static/src/core/resizable_panel/resizable_panel.scss */
 .o_resizable_panel{max-width: 100vw; flex-grow: 0;}.o_resizable_panel_handle{cursor: col-resize; z-index: 10; width: 5px;}

/* /web/static/src/core/select_menu/select_menu.scss */
 .o_select_menu .o_select_menu_toggler{display: grid; grid-template-columns: auto 25px;}.o_select_menu .o_select_menu_toggler.o_can_deselect{grid-template-columns: auto 25px 25px;}.o_select_menu .o_select_menu_toggler_slot{flex-grow: 2;}.o_select_menu .o_select_menu_toggler_caret{grid-column: 2;}.o_select_menu .o_can_deselect .o_select_menu_toggler_caret{grid-column: 3;}.o_select_menu .o_select_menu_toggler_clear{grid-column: 2;}.o_select_menu .o_select_menu_toggler_clear:hover i{color: red;}.o_select_menu .o_select_menu_menu{min-width: fit-content; max-height: 350px;}.o_select_menu .o_select_menu_menu input{cursor: text !important;}.o_select_menu .o_select_menu_menu .o_select_menu_sticky{background-color: #FFFFFF !important;}.o_select_menu .o_select_menu_menu .o_select_menu_sticky.o_select_menu_item.focus{background: rgba(0, 0, 0, 0.15) !important;}.o_select_menu .o_select_menu_menu .o_select_menu_group{top: 50px;}.o_select_menu .o_select_active{color: white;}.o_select_menu .o_select_menu_multi_select .o_select_active:hover{background: #dc3545 !important; transition: background .5s;}.o_select_menu .o_tag{margin: 2px;}.dropup .o_select_menu_menu{box-shadow: 0 -7px 10px rgba(8, 8, 8, 0.319);}.dropdown .o_select_menu_menu{box-shadow: 0 7px 10px rgba(8, 8, 8, 0.319);}

/* /web/static/src/core/signature/name_and_signature.scss */
 .o_web_sign_name_and_signature{position: relative;}.o_signature_stroke{position: absolute; border-top: #D1D0CE solid 2px; bottom: 33%; width: 72%; left: 14%;}

/* /web/static/src/core/tags_list/tags_list.scss */
 .o_tag{font-size: var(--Tag-font-size, 0.75rem); max-width: var(--Tag-max-width, 100%);}.o_tag.o_tag_color_0, .o_tag.o_tag_color_0::after{--background-color: RGBA(230.1375, 221.3625, 221.3625, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(60, 60, 60, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_1, .o_tag.o_tag_color_1::after{--background-color: RGBA(255, 155.5, 155.5, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(67.15870044, 11.84129956, 11.84129956, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_2, .o_tag.o_tag_color_2::after{--background-color: RGBA(247.0375, 198.06116071, 152.4625, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(52.9, 33.325, 15.1, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_3, .o_tag.o_tag_color_3::after{--background-color: RGBA(252.88960843, 226.89175248, 135.61039157, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(47.45993976, 39.05405514, 9.54006024, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_4, .o_tag.o_tag_color_4::after{--background-color: RGBA(187.45210396, 215.03675558, 248.04789604, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(25.3049505, 49.60939855, 78.6950495, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_5, .o_tag.o_tag_color_5::after{--background-color: RGBA(216.79194664, 167.70805336, 203.91748283, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(28.91432806, 24.08567194, 27.64779531, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_6, .o_tag.o_tag_color_6::after{--background-color: RGBA(247.84539474, 213.9484835, 199.65460526, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(85.32105263, 46.88635147, 30.67894737, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_7, .o_tag.o_tag_color_7::after{--background-color: RGBA(136.6125, 224.8875, 218.94591346, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(10.58333333, 19.41666667, 18.82211538, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_8, .o_tag.o_tag_color_8::after{--background-color: RGBA(150.60535714, 165.68382711, 248.89464286, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(13.95714286, 20.10665584, 54.04285714, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_9, .o_tag.o_tag_color_9::after{--background-color: RGBA(254.94583333, 157.55416667, 203.95543194, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(68.805, 12.195, 39.16625654, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_10, .o_tag.o_tag_color_10::after{--background-color: RGBA(182.62075688, 236.87924312, 189.81831118, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(30.82018349, 57.17981651, 34.3168695, var(--text-opacity, 1)); color: var(--color) !important;}.o_tag.o_tag_color_11, .o_tag.o_tag_color_11::after{--background-color: RGBA(230.11575613, 219.41069277, 252.08930723, var(--bg-opacity, 1)); background-color: var(--background-color) !important; --color: RGBA(56.75321978, 31.58433735, 108.41566265, var(--text-opacity, 1)); color: var(--color) !important;}

/* /web/static/src/core/tooltip/tooltip.scss */
 .o-tooltip{font-family: var(--bs-font-sans-serif); font-style: normal; font-weight: 400; line-height: 1.5; text-align: left; text-align: start; text-decoration: none; text-shadow: none; text-transform: none; letter-spacing: normal; word-break: normal; word-spacing: normal; white-space: normal; line-break: auto; font-size: 0.75rem;}.o-tooltip:has(.o-tooltip--technical){max-width: 400px;}.o-tooltip:has(.o-tooltip--technical) .o-tooltip--help{background-color: #17a2b8; color: #FFFFFF;}.o-tooltip:has(.o-tooltip--technical) .o-tooltip--string, .o-tooltip:has(.o-tooltip--technical) .o-tooltip--help{max-width: MIN(200px, 100%);}.o-tooltip .o-tooltip--string, .o-tooltip .o-tooltip--technical--title{padding: 0.25rem 0; color: #FFFFFF; font-weight: 700;}.o-tooltip .o-tooltip--help, .o-tooltip .o-tooltip--technical{margin: 0.25rem 0.5rem 0.5rem;}.o-tooltip .o-tooltip--help{white-space: pre-line; padding: 0 0.5rem;}.o-tooltip .o-tooltip--technical{padding-left: 1.3em; font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; font-size: 0.75rem; list-style-type: disc;}.o-tooltip .o-tooltip--technical .o-tooltip--technical--title{margin-right: 0.25rem;}.o-tooltip + .popover-arrow{--popover-arrow-color: #000000;}

/* /web/static/src/core/tree_editor/tree_editor.scss */
 .o_tree_editor .o_tree_editor_node .o_tree_editor_node_control_panel > button{opacity: .2;}.o_tree_editor .o_tree_editor_node.o_hovered_button .o_tree_editor_node_control_panel > button{opacity: .5;}.o_tree_editor .o_tree_editor_node.o_hovered_button .o_tree_editor_node_control_panel > button:hover{opacity: 1;}

/* /web/static/src/core/ui/block_ui.scss */
 .o_blockUI{cursor: wait; -webkit-backdrop-filter: blur(2px); backdrop-filter: blur(2px); background: rgba(0, 0, 0, 0.5); color: #fff; z-index: 1070 !important;}

/* /web/static/src/core/utils/draggable_hook_builder.scss */
 @keyframes o-draggable-bounce{0%{transform: scale(1);}60%{transform: scale(0.95);}100%{transform: scale(1);}}.o_draggable{-webkit-touch-callout: none;}.o_dragged{z-index: 1000; pointer-events: none;}.o_touch_bounce{animation: o-draggable-bounce .4s forwards; user-select: none;}

/* /web/static/src/core/utils/nested_sortable.scss */
 .o_nested_sortable_placeholder{background-clip: content-box; background-color: deepskyblue; height: 5px; padding-top: 0 !important; padding-bottom: 0 !important;}.o_nested_sortable_placeholder_realsize{outline: 1px dashed #6c757d; background-color: #f8f9fa;}

/* /web_tour/static/src/tour_pointer/tour_pointer.scss */
 @keyframes o-tour-pointer-bounce-horizontal{from{transform: translateX(calc(var(--TourPointer__bounce-offset) * -1));}to{transform: translateX(var(--TourPointer__bounce-offset));}}@keyframes o-tour-pointer-bounce-vertical{from{transform: translateY(calc(var(--TourPointer__bounce-offset) * -1));}to{transform: translateY(var(--TourPointer__bounce-offset));}}@keyframes o-tour-pointer-fade-in{from{opacity: 0;}}@keyframes o-tour-pointer-info-expand{from{width: 0; height: 0;}}.o_tour_pointer{--TourPointer__anchor-space: 0; --TourPointer__bounce-offset: 3px; --TourPointer__offset: 8px; --TourPointer__scale: 1.12; --TourPointer__color: #714B67; --TourPointer__color-accent: #86597b; --TourPointer__border-width: 1px; --TourPointer__border-color-rgb: 255, 255, 255; --TourPointer__border-color: rgba(var(--TourPointer__border-color-rgb), 1); --TourPointer__arrow-size: 1rem; --TourPointer__animation-duration: 500ms; --TourPointer__expand-duration: 200ms; --TourPointer__text-color: black; --TourPointer__reveal-animation: o-tour-pointer-fade-in 400ms ease; --TourPointer__translate-x: 0; --TourPointer__translate-y: 0; z-index: 1080; max-width: 270px; border: var(--TourPointer__border-width) solid transparent; transform: translate(var(--TourPointer__translate-x), var(--TourPointer__translate-y)); transition: width var(--TourPointer__expand-duration), height var(--TourPointer__expand-duration);}.o_tour_pointer.o_bouncing.o_left, .o_tour_pointer.o_bouncing.o_right{animation: o-tour-pointer-bounce-horizontal var(--TourPointer__animation-duration) ease-in infinite alternate, var(--TourPointer__reveal-animation);}.o_tour_pointer.o_bouncing.o_top, .o_tour_pointer.o_bouncing.o_bottom{animation: o-tour-pointer-bounce-vertical var(--TourPointer__animation-duration) ease-in infinite alternate, var(--TourPointer__reveal-animation);}.o_tour_pointer .o_tour_pointer_tip{width: var(--TourPointer__width); height: var(--TourPointer__height); border: var(--TourPointer__border-width) solid white; border-radius: 0 50% 50% 50%; background-image: radial-gradient(var(--TourPointer__color-accent), var(--TourPointer__color)); box-shadow: 0 0 40px 2px rgba(var(--TourPointer__border-color-rgb), 0.5);}.o_tour_pointer .o_tour_pointer_content{background-color: #eeeeee; color: transparent; transition: color 0s ease var(--TourPointer__expand-duration); line-height: 1.5; font-size: 0.875rem; font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Ubuntu, "Noto Sans", Arial, "Odoo Unicode Support Noto", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; font-weight: normal;}.o_tour_pointer .o_tour_pointer_content .o_skip_tour{display: inline-block; margin-top: 4px; color: gray; cursor: pointer;}.o_tour_pointer .o_tour_pointer_content .o_skip_tour:hover{color: #4d4d4d;}.o_tour_pointer .o_tour_pointer_content p:last-child{margin-bottom: 0;}.o_tour_pointer.o_left .o_tour_pointer_tip{transform: rotate(90deg) translateY(var(--TourPointer__offset)) scaleY(var(--TourPointer__scale)) rotate(45deg);}.o_tour_pointer.o_right .o_tour_pointer_tip{transform: rotate(270deg) translateY(var(--TourPointer__offset)) scaleY(var(--TourPointer__scale)) rotate(45deg);}.o_tour_pointer.o_top .o_tour_pointer_tip{transform: rotate(180deg) translateY(var(--TourPointer__offset)) scaleY(var(--TourPointer__scale)) rotate(45deg);}.o_tour_pointer.o_bottom .o_tour_pointer_tip{transform: rotate(0deg) translateY(var(--TourPointer__offset)) scaleY(var(--TourPointer__scale)) rotate(45deg);}.o_tour_pointer.o_open{border-color: #ced4da; background-color: #dee2e6; animation: var(--TourPointer__reveal-animation);}.o_tour_pointer.o_open .o_tour_pointer_tip{width: var(--TourPointer__arrow-size); height: var(--TourPointer__arrow-size); border-color: #ced4da; border-radius: 0; background: #eeeeee; box-shadow: none;}.o_tour_pointer.o_open .o_tour_pointer_content{color: #000000;}.o_tour_pointer.o_open.o_left{--TourPointer__translate-x: calc(var(--TourPointer__arrow-size) / -2);}.o_tour_pointer.o_open.o_left .o_tour_pointer_tip{transform: translateX(-50%) rotate(45deg); right: calc(var(--TourPointer__arrow-size) * -1); top: calc(var(--TourPointer__arrow-size) / 2);}.o_tour_pointer.o_open.o_right{--TourPointer__translate-x: calc(var(--TourPointer__arrow-size) / 2);}.o_tour_pointer.o_open.o_right .o_tour_pointer_tip{transform: translateX(50%) rotate(45deg); left: calc(var(--TourPointer__arrow-size) * -1); top: calc(var(--TourPointer__arrow-size) / 2);}.o_tour_pointer.o_open.o_top{--TourPointer__translate-y: calc(var(--TourPointer__arrow-size) / -2);}.o_tour_pointer.o_open.o_top .o_tour_pointer_tip{transform: translateY(-50%) rotate(45deg); bottom: calc(var(--TourPointer__arrow-size) * -1); left: calc(var(--TourPointer__arrow-size) / 2);}.o_tour_pointer.o_open.o_bottom{--TourPointer__translate-y: calc(var(--TourPointer__arrow-size) / 2);}.o_tour_pointer.o_open.o_bottom .o_tour_pointer_tip{transform: translateY(50%) rotate(45deg); top: calc(var(--TourPointer__arrow-size) * -1); left: calc(var(--TourPointer__arrow-size) / 2);}.o_tour_pointer.o_open.o_expand_left.o_top, .o_tour_pointer.o_open.o_expand_left.o_bottom{--TourPointer__translate-x: calc( var(--TourPointer__width) + var(--TourPointer__border-width) - 100% );}.o_tour_pointer.o_open.o_expand_left.o_top .o_tour_pointer_tip, .o_tour_pointer.o_open.o_expand_left.o_bottom .o_tour_pointer_tip{left: initial; right: calc(var(--TourPointer__arrow-size) / 2);}@media print{.o_tour_pointer{display: none !important;}}

/* /web_editor/static/src/components/history_dialog/history_dialog.scss */
 .html-history-dialog .history-container{margin-left: 240px;}.html-history-dialog .history-container > div{padding: 10px 12px; border: 1px solid #ddd; border-top: 0;}.html-history-dialog .history-container .nav{padding-left: 24px;}.html-history-dialog .history-container removed{display: inline; background-color: #f1afaf; text-decoration: line-through; opacity: 0.5;}.html-history-dialog .history-container added{display: inline; background-color: #c8f1af;}.html-history-dialog .history-container p{margin-bottom: 0.6rem;}.html-history-dialog .revision-list{margin: 38px 0 0 8px; overflow: auto; max-height: 100%; width: 220px; float: left;}.html-history-dialog .revision-list .btn{border-radius: 0; display: block; text-align: left; width: 220px; margin-bottom: 8px; position: relative;}.html-history-dialog .revision-list .btn:before{content: '\f105'; font-family: 'FontAwesome'; position: absolute; right: 8px; top: 0; font-size: 34px;}

/* /web_editor/static/src/components/media_dialog/media_dialog.scss */
 .modal:not(.o_legacy_dialog) .o_select_media_dialog .o_we_file_selector_control_panel{top: -1rem; background-color: #FFFFFF;}.modal:not(.o_legacy_dialog) .o_select_media_dialog .o_we_existing_attachments{min-height: 128px;}.modal:not(.o_legacy_dialog) .o_select_media_dialog .o_we_existing_attachments .o_we_attachment_placeholder{flex-grow: 128; flex-basis: 128px;}.modal:not(.o_legacy_dialog) .o_select_media_dialog .o_we_existing_attachments .o_existing_attachment_cell.o_we_image{transition: opacity 0.5s ease 0.5s;}.modal:not(.o_legacy_dialog) .o_select_media_dialog .o_we_existing_attachments .o_existing_attachment_remove{border-radius: 0 0 0 4px;}.modal:not(.o_legacy_dialog) .o_select_media_dialog .o_we_existing_attachments .o_existing_attachment_remove:hover{color: #e6586c;}.modal:not(.o_legacy_dialog) .o_select_media_dialog .o_we_attachment_selected{box-shadow: 0 0 0 3px #71639e;}.modal:not(.o_legacy_dialog) .o_select_media_dialog .o_we_attachment_selected:not(.fa){border: 3px solid #71639e; box-shadow: none;}.modal:not(.o_legacy_dialog) .o_select_media_dialog .o_we_attachment_selected:not(.fa):before{content: ""; position: absolute; top: 5px; left: 5px; bottom: auto; right: auto; width: 19px; height: 19px; background-color: #71639e; font-family: 'FontAwesome'; color: white; border-radius: 50%; text-align: center; z-index: 1; box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);}.modal:not(.o_legacy_dialog) .o_select_media_dialog .o_we_load_more{scroll-margin: 1rem;}.modal:not(.o_legacy_dialog) .o_select_media_dialog .o_we_load_more.o_hide_loading > *{display: none;}.modal:not(.o_legacy_dialog) .o_select_media_dialog .o_scroll_attachments{bottom: 0px; width: 36px; height: 36px; margin-top: -36px; z-index: 2;}.modal:not(.o_legacy_dialog) .o_select_media_dialog .font-icons-icons > span{width: 50px;}.modal:not(.o_legacy_dialog) .o_select_media_dialog .o_video_dialog_form textarea{min-height: 95px;}.modal:not(.o_legacy_dialog) .o_select_media_dialog .o_video_preview{border-top: 1px solid black; border-bottom: 1px solid white; background-image: linear-gradient(-150deg, #2b2b33, #191922); color: white;}.modal:not(.o_legacy_dialog) .o_select_media_dialog .o_video_preview .media_iframe_video{width: 100%;}

/* /web_editor/static/src/components/upload_progress_toast/upload_progress_toast.scss */
 .o_upload_progress_toast{font-size: 16px;}.o_upload_progress_toast .o_we_progressbar:last-child hr{display: none;}

/* /web_editor/static/src/js/editor/odoo-editor/src/base_style.scss */
 li.oe-nested{display: block;}.o_table tr{border-color: #dee2e6;}.o_table tr td{padding: 0.5rem;}.o_text_columns{max-width: 100% !important; padding: 0 !important;}@media screen{.o_text_columns > .row{margin: 0 !important;}.o_text_columns > .row > .col-1:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-1:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-2:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-2:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-3:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-3:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-4:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-4:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-5:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-5:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-6:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-6:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-7:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-7:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-8:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-8:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-9:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-9:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-10:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-10:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-11:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-11:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-12:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-12:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-1:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-1:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-2:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-2:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-3:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-3:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-4:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-4:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-5:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-5:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-6:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-6:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-7:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-7:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-8:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-8:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-9:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-9:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-10:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-10:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-11:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-11:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xs-12:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xs-12:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-1:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-1:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-2:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-2:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-3:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-3:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-4:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-4:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-5:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-5:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-6:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-6:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-7:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-7:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-8:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-8:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-9:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-9:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-10:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-10:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-11:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-11:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-sm-12:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-sm-12:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-1:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-1:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-2:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-2:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-3:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-3:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-4:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-4:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-5:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-5:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-6:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-6:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-7:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-7:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-8:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-8:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-9:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-9:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-10:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-10:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-11:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-11:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-md-12:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-md-12:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-1:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-1:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-2:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-2:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-3:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-3:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-4:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-4:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-5:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-5:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-6:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-6:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-7:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-7:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-8:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-8:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-9:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-9:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-10:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-10:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-11:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-11:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-lg-12:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-lg-12:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-1:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-1:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-2:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-2:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-3:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-3:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-4:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-4:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-5:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-5:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-6:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-6:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-7:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-7:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-8:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-8:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-9:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-9:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-10:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-10:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-11:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-11:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xl-12:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xl-12:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-1:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-1:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-2:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-2:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-3:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-3:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-4:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-4:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-5:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-5:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-6:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-6:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-7:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-7:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-8:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-8:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-9:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-9:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-10:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-10:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-11:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-11:last-of-type{padding-right: 0;}.o_text_columns > .row > .col-xxl-12:first-of-type{padding-left: 0;}.o_text_columns > .row > .col-xxl-12:last-of-type{padding-right: 0;}}.oe-tabs{display: inline-block; white-space: pre-wrap; max-width: 40px; width: 40px;}ol{list-style-type: decimal;}ul{list-style-type: disc;}ol ol{list-style-type: lower-alpha;}ul ul{list-style-type: circle;}ol ol ol{list-style-type: lower-roman;}ul ul ul{list-style-type: square;}ol ol ol ol{list-style-type: decimal;}ul ul ul ul{list-style-type: disc;}ol ol ol ol ol{list-style-type: lower-alpha;}ul ul ul ul ul{list-style-type: circle;}ol ol ol ol ol ol{list-style-type: lower-roman;}ul ul ul ul ul ul{list-style-type: square;}ol ol ol ol ol ol ol{list-style-type: decimal;}ul ul ul ul ul ul ul{list-style-type: disc;}ol ol ol ol ol ol ol ol{list-style-type: lower-alpha;}ul ul ul ul ul ul ul ul{list-style-type: circle;}ol ol ol ol ol ol ol ol ol{list-style-type: lower-roman;}ul ul ul ul ul ul ul ul ul{list-style-type: square;}ol ol ol ol ol ol ol ol ol ol{list-style-type: decimal;}ul ul ul ul ul ul ul ul ul ul{list-style-type: disc;}ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-alpha;}ul ul ul ul ul ul ul ul ul ul ul{list-style-type: circle;}ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-roman;}ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: square;}ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: decimal;}ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: disc;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-alpha;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: circle;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-roman;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: square;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: decimal;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: disc;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-alpha;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: circle;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-roman;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: square;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: decimal;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: disc;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-alpha;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: circle;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-roman;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: square;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: decimal;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: disc;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-alpha;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: circle;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-roman;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: square;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: decimal;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: disc;}ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol ol{list-style-type: lower-alpha;}ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul ul{list-style-type: circle;}

/* /web_editor/static/src/scss/web_editor.common.scss */
 :root{--100: #F8F9FA; --200: #E9ECEF; --300: #DEE2E6; --400: #CED4DA; --500: #ADB5BD; --600: #6C757D; --700: #495057; --800: #343A40; --900: #212529; --white-85: rgba(255, 255, 255, 0.85); --white-75: rgba(255, 255, 255, 0.75); --white-50: rgba(255, 255, 255, 0.5); --white-25: rgba(255, 255, 255, 0.25); --black-75: rgba(0, 0, 0, 0.75); --black-50: rgba(0, 0, 0, 0.5); --black-25: rgba(0, 0, 0, 0.25); --black-15: rgba(0, 0, 0, 0.15); --black: #000000; --white: #FFFFFF; --o-cc1-text: #000000; --o-cc1-headings: #000000; --o-cc1-h2: #000000; --o-cc1-h3: #000000; --o-cc1-h4: #000000; --o-cc1-h5: #000000; --o-cc1-h6: #000000; --o-cc1-link: #65435c; --o-cc1-btn-primary: #714B67; --o-cc1-btn-primary-text: #FFFFFF; --o-cc1-btn-primary-border: #714B67; --o-cc1-btn-secondary: #8595A2; --o-cc1-btn-secondary-text: #FFFFFF; --o-cc1-btn-secondary-border: #8595A2; --o-cc2-text: #000000; --o-cc2-h2: #111827; --o-cc2-h3: #111827; --o-cc2-h4: #111827; --o-cc2-h5: #111827; --o-cc2-h6: #111827; --o-cc2-link: #55394e; --o-cc2-btn-primary: #714B67; --o-cc2-btn-primary-text: #FFFFFF; --o-cc2-btn-primary-border: #714B67; --o-cc2-btn-secondary: #8595A2; --o-cc2-btn-secondary-text: #FFFFFF; --o-cc2-btn-secondary-border: #8595A2; --o-cc3-text: #FFFFFF; --o-cc3-headings: #FFFFFF; --o-cc3-h2: #FFFFFF; --o-cc3-h3: #FFFFFF; --o-cc3-h4: #FFFFFF; --o-cc3-h5: #FFFFFF; --o-cc3-h6: #FFFFFF; --o-cc3-link: #241821; --o-cc3-btn-primary: #714B67; --o-cc3-btn-primary-text: #FFFFFF; --o-cc3-btn-primary-border: #714B67; --o-cc3-btn-secondary-text: #000000; --o-cc3-btn-secondary-border: #F3F2F2; --o-cc4-text: #FFFFFF; --o-cc4-headings: #FFFFFF; --o-cc4-h2: #FFFFFF; --o-cc4-h3: #FFFFFF; --o-cc4-h4: #FFFFFF; --o-cc4-h5: #FFFFFF; --o-cc4-h6: #FFFFFF; --o-cc4-link: black; --o-cc4-btn-primary-text: #FFFFFF; --o-cc4-btn-primary-border: #111827; --o-cc4-btn-secondary-text: #000000; --o-cc4-btn-secondary-border: #F3F2F2; --o-cc5-text: #FFFFFF; --o-cc5-h2: #FFFFFF; --o-cc5-h3: #FFFFFF; --o-cc5-h4: #FFFFFF; --o-cc5-h5: #FFFFFF; --o-cc5-h6: #FFFFFF; --o-cc5-link: #b18aa7; --o-cc5-btn-primary: #714B67; --o-cc5-btn-primary-text: #FFFFFF; --o-cc5-btn-primary-border: #714B67; --o-cc5-btn-secondary-text: #000000; --o-cc5-btn-secondary-border: #F3F2F2; --o-grid-gutter-width: 1.5rem; --o-md-container-max-width: 720px; --o-we-content-to-translate-color: rgba(255, 255, 90, 0.5); --o-we-translated-content-color: rgba(120, 215, 110, 0.5); --o-system-fonts: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Ubuntu, "Noto Sans", Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; --display-1-font-size: 5rem; --display-2-font-size: 4.5rem; --display-3-font-size: 4rem; --display-4-font-size: 3.5rem; --h1-font-size: 2.1875rem; --h2-font-size: 1.75rem; --h3-font-size: 1.53125rem; --h4-font-size: 1.3125rem; --h5-font-size: 1.09375rem; --h6-font-size: 0.875rem; --font-size-base: 0.875rem; --small-font-size: 0.875em; --small-twelve-font-size: 0.75em; --small-ten-font-size: 0.625em; --small-eight-font-size: 0.5em; --lead-font-size: 1.09375rem;}html, body{position: relative; width: 100%; height: 100%;}*[contenteditable=true]{outline: none;}[contenteditable]{overflow-wrap: unset !important;}.css_non_editable_mode_hidden{display: none !important;}.editor_enable .css_editable_mode_hidden{display: none !important;}.note-toolbar{margin-left: 0 !important;}.note-popover .popover > .arrow{display: none;}.note-popover .popover .dropdown-menu .dropdown-item > i, .note-editor .dropdown-menu .dropdown-item > i{visibility: hidden;}.note-popover .popover .dropdown-menu .dropdown-item.checked > i, .note-editor .dropdown-menu .dropdown-item.checked > i{visibility: visible;}#wrapwrap table.table.table-bordered, .o_editable table.table.table-bordered{table-layout: fixed; overflow-wrap: break-word;}#wrapwrap table.table.table-bordered td, .o_editable table.table.table-bordered td{min-width: 20px;}@media (max-width: 767.98px){#wrapwrap .table-responsive > table.table, .o_editable .table-responsive > table.table{table-layout: auto;}}ul.o_checklist{list-style: none;}ul.o_checklist > li{list-style: none; position: relative; margin-left: 20px; margin-right: 20px;}ul.o_checklist > li:not(.oe-nested)::before{content: ''; position: absolute; left: -20px; display: block; height: 13px; width: 13px; top: 4px; border: 1px solid; text-align: center; cursor: pointer;}ul.o_checklist > li.o_checked{text-decoration: line-through;}ul.o_checklist > li.o_checked::before{content: "✓"; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; justify-content: center; padding-left: 1px ; padding-top: 1px;}ul.o_checklist[dir="rtl"] li:not(.oe-nested)::before{left: auto; right: -20px; text-align: right;}ul.o_checklist[dir="ltr"] li:not(.oe-nested)::before{right: auto; left: -20px; text-align: left;}ol > li.o_indent, ul > li.o_indent{margin-left: 0; list-style: none;}ol > li.o_indent::before, ul > li.o_indent::before{content: none;}.o_stars .fa.fa-star{color: gold;}img.o_we_custom_image{display: inline-block;}img.shadow{box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.2);}img.padding-small, .img.padding-small, span.fa.padding-small, iframe.padding-small{padding: 4px;}img.padding-medium, .img.padding-medium, span.fa.padding-medium, iframe.padding-medium{padding: 8px;}img.padding-large, .img.padding-large, span.fa.padding-large, iframe.padding-large{padding: 16px;}img.padding-xl, .img.padding-xl, span.fa.padding-xl, iframe.padding-xl{padding: 32px;}img.ms-auto, img.mx-auto{display: block;}.fa-6x{font-size: 6em;}.fa-7x{font-size: 7em;}.fa-8x{font-size: 8em;}.fa-9x{font-size: 9em;}.fa-10x{font-size: 10em;}.fa.mx-auto{display: block; text-align: center;}.fa.card-img, .fa.card-img-top, .fa.card-img-bottom{width: auto;}.o_small{font-size: 0.875rem;}.display-1-fs{font-size: calc(1.625rem + 4.5vw);}@media (min-width: 1200px){.display-1-fs{font-size: 5rem;}}.display-2-fs{font-size: calc(1.575rem + 3.9vw);}@media (min-width: 1200px){.display-2-fs{font-size: 4.5rem;}}.display-3-fs{font-size: calc(1.525rem + 3.3vw);}@media (min-width: 1200px){.display-3-fs{font-size: 4rem;}}.display-4-fs{font-size: calc(1.475rem + 2.7vw);}@media (min-width: 1200px){.display-4-fs{font-size: 3.5rem;}}.h1-fs{font-size: calc(1.34375rem + 1.125vw);}@media (min-width: 1200px){.h1-fs{font-size: 2.1875rem;}}.h2-fs{font-size: calc(1.3rem + 0.6vw);}@media (min-width: 1200px){.h2-fs{font-size: 1.75rem;}}.h3-fs{font-size: calc(1.278125rem + 0.3375vw);}@media (min-width: 1200px){.h3-fs{font-size: 1.53125rem;}}.h4-fs{font-size: calc(1.25625rem + 0.075vw);}@media (min-width: 1200px){.h4-fs{font-size: 1.3125rem;}}.h5-fs{font-size: 1.09375rem;}.h6-fs{font-size: 0.875rem;}.base-fs{font-size: 0.875rem;}.o_small-fs{font-size: 0.875em;}.o_small_twelve-fs{font-size: 0.75rem;}.o_small_ten-fs{font-size: 0.625rem;}.o_small_eight-fs{font-size: 0.5rem;}div.media_iframe_video{margin: 0 auto; text-align: center; position: relative; overflow: hidden; min-width: 100px;}div.media_iframe_video iframe{width: 100%; height: 100%; position: absolute; top: 0; left: 0; bottom: auto; right: 0; margin: 0 auto;}div.media_iframe_video.padding-small iframe{padding: 4px;}div.media_iframe_video.padding-medium iframe{padding: 8px;}div.media_iframe_video.padding-large iframe{padding: 16px;}div.media_iframe_video.padding-xl iframe{padding: 32px;}div.media_iframe_video .media_iframe_video_size{padding-bottom: 66.5%; position: relative; width: 100%; height: 0;}div.media_iframe_video .css_editable_mode_display{position: absolute; top: 0; left: 0; bottom: 0; right: 0; width: 100%; height: 100%; display: none; z-index: 2;}address .fa.fa-mobile-phone{margin: 0 3px 0 2px;}address .fa.fa-file-text-o{margin-right: 1px;}span[data-oe-type="monetary"]{white-space: nowrap;}ul.oe_menu_editor .oe_menu_placeholder{outline: 1px dashed #4183C4;}ul.oe_menu_editor ul{list-style: none;}ul.oe_menu_editor li div{cursor: url(/web/static/img/openhand.cur), grab;}ul.oe_menu_editor li div :active{cursor: grabbing;}.mt0{margin-top: 0px !important;}.mb0{margin-bottom: 0px !important;}.pt0{padding-top: 0px !important;}.pb0{padding-bottom: 0px !important;}.mt8{margin-top: 8px !important;}.mb8{margin-bottom: 8px !important;}.pt8{padding-top: 8px !important;}.pb8{padding-bottom: 8px !important;}.mt16{margin-top: 16px !important;}.mb16{margin-bottom: 16px !important;}.pt16{padding-top: 16px !important;}.pb16{padding-bottom: 16px !important;}.mt24{margin-top: 24px !important;}.mb24{margin-bottom: 24px !important;}.pt24{padding-top: 24px !important;}.pb24{padding-bottom: 24px !important;}.mt32{margin-top: 32px !important;}.mb32{margin-bottom: 32px !important;}.pt32{padding-top: 32px !important;}.pb32{padding-bottom: 32px !important;}.mt40{margin-top: 40px !important;}.mb40{margin-bottom: 40px !important;}.pt40{padding-top: 40px !important;}.pb40{padding-bottom: 40px !important;}.mt48{margin-top: 48px !important;}.mb48{margin-bottom: 48px !important;}.pt48{padding-top: 48px !important;}.pb48{padding-bottom: 48px !important;}.mt56{margin-top: 56px !important;}.mb56{margin-bottom: 56px !important;}.pt56{padding-top: 56px !important;}.pb56{padding-bottom: 56px !important;}.mt64{margin-top: 64px !important;}.mb64{margin-bottom: 64px !important;}.pt64{padding-top: 64px !important;}.pb64{padding-bottom: 64px !important;}.mt72{margin-top: 72px !important;}.mb72{margin-bottom: 72px !important;}.pt72{padding-top: 72px !important;}.pb72{padding-bottom: 72px !important;}.mt80{margin-top: 80px !important;}.mb80{margin-bottom: 80px !important;}.pt80{padding-top: 80px !important;}.pb80{padding-bottom: 80px !important;}.mt88{margin-top: 88px !important;}.mb88{margin-bottom: 88px !important;}.pt88{padding-top: 88px !important;}.pb88{padding-bottom: 88px !important;}.mt96{margin-top: 96px !important;}.mb96{margin-bottom: 96px !important;}.pt96{padding-top: 96px !important;}.pb96{padding-bottom: 96px !important;}.mt104{margin-top: 104px !important;}.mb104{margin-bottom: 104px !important;}.pt104{padding-top: 104px !important;}.pb104{padding-bottom: 104px !important;}.mt112{margin-top: 112px !important;}.mb112{margin-bottom: 112px !important;}.pt112{padding-top: 112px !important;}.pb112{padding-bottom: 112px !important;}.mt120{margin-top: 120px !important;}.mb120{margin-bottom: 120px !important;}.pt120{padding-top: 120px !important;}.pb120{padding-bottom: 120px !important;}.mt128{margin-top: 128px !important;}.mb128{margin-bottom: 128px !important;}.pt128{padding-top: 128px !important;}.pb128{padding-bottom: 128px !important;}.mt136{margin-top: 136px !important;}.mb136{margin-bottom: 136px !important;}.pt136{padding-top: 136px !important;}.pb136{padding-bottom: 136px !important;}.mt144{margin-top: 144px !important;}.mb144{margin-bottom: 144px !important;}.pt144{padding-top: 144px !important;}.pb144{padding-bottom: 144px !important;}.mt152{margin-top: 152px !important;}.mb152{margin-bottom: 152px !important;}.pt152{padding-top: 152px !important;}.pb152{padding-bottom: 152px !important;}.mt160{margin-top: 160px !important;}.mb160{margin-bottom: 160px !important;}.pt160{padding-top: 160px !important;}.pb160{padding-bottom: 160px !important;}.mt168{margin-top: 168px !important;}.mb168{margin-bottom: 168px !important;}.pt168{padding-top: 168px !important;}.pb168{padding-bottom: 168px !important;}.mt176{margin-top: 176px !important;}.mb176{margin-bottom: 176px !important;}.pt176{padding-top: 176px !important;}.pb176{padding-bottom: 176px !important;}.mt184{margin-top: 184px !important;}.mb184{margin-bottom: 184px !important;}.pt184{padding-top: 184px !important;}.pb184{padding-bottom: 184px !important;}.mt192{margin-top: 192px !important;}.mb192{margin-bottom: 192px !important;}.pt192{padding-top: 192px !important;}.pb192{padding-bottom: 192px !important;}.mt200{margin-top: 200px !important;}.mb200{margin-bottom: 200px !important;}.pt200{padding-top: 200px !important;}.pb200{padding-bottom: 200px !important;}.mt208{margin-top: 208px !important;}.mb208{margin-bottom: 208px !important;}.pt208{padding-top: 208px !important;}.pb208{padding-bottom: 208px !important;}.mt216{margin-top: 216px !important;}.mb216{margin-bottom: 216px !important;}.pt216{padding-top: 216px !important;}.pb216{padding-bottom: 216px !important;}.mt224{margin-top: 224px !important;}.mb224{margin-bottom: 224px !important;}.pt224{padding-top: 224px !important;}.pb224{padding-bottom: 224px !important;}.mt232{margin-top: 232px !important;}.mb232{margin-bottom: 232px !important;}.pt232{padding-top: 232px !important;}.pb232{padding-bottom: 232px !important;}.mt240{margin-top: 240px !important;}.mb240{margin-bottom: 240px !important;}.pt240{padding-top: 240px !important;}.pb240{padding-bottom: 240px !important;}.mt248{margin-top: 248px !important;}.mb248{margin-bottom: 248px !important;}.pt248{padding-top: 248px !important;}.pb248{padding-bottom: 248px !important;}.mt256{margin-top: 256px !important;}.mb256{margin-bottom: 256px !important;}.pt256{padding-top: 256px !important;}.pb256{padding-bottom: 256px !important;}.mt4{margin-top: 4px !important;}.mb4{margin-bottom: 4px !important;}.pt4{padding-top: 4px !important;}.pb4{padding-bottom: 4px !important;}.mt92{margin-top: 92px !important;}.mb92{margin-bottom: 92px !important;}.ml0{margin-left: 0px !important;}.mr0{margin-right: 0px !important;}.ml4{margin-left: 4px !important;}.mr4{margin-right: 4px !important;}.ml8{margin-left: 8px !important;}.mr8{margin-right: 8px !important;}.ml16{margin-left: 16px !important;}.mr16{margin-right: 16px !important;}.ml32{margin-left: 32px !important;}.mr32{margin-right: 32px !important;}.ml64{margin-left: 64px !important;}.mr64{margin-right: 64px !important;}a.o_underline{text-decoration: underline;}a.o_underline:hover{text-decoration: underline;}.o_nocontent_help{pointer-events: auto; max-width: 650px; margin: auto; padding: 15px; z-index: 1000; text-align: center; color: #495057; font-size: 115%;}.o_nocontent_help > p:first-of-type{margin-top: 0; font-weight: bold; font-size: 125%;}.o_nocontent_help a{cursor: pointer;}.o_we_search_prompt{position: relative; min-height: 250px; width: 100%; display: -webkit-box; display: -webkit-flex; display: flex; align-items: center; -webkit-box-pack: start; justify-content: flex-start;}.o_we_search_prompt > h2, .o_we_search_prompt > .h2{max-width: 500px; text-align: center; margin-left: 150px;}.o_we_search_prompt::before{transform: scale(-1, 1); content: ""; position: absolute; top: 0; left: 50px; bottom: auto; right: auto; width: 100px; height: 150px; opacity: .5; filter: var(--WebEditor__SearchPromptArrow-filter, invert(0)); background-image: url("/web_editor/static/src/img/curved_arrow.svg"); background-size: 100%; background-repeat: no-repeat;}@media (max-width: 767.98px){odoo-wysiwyg-container .panel-heading.note-toolbar{overflow-x: auto;}odoo-wysiwyg-container .btn-group{position: static;}.o_technical_modal.o_web_editor_dialog{z-index: 2001;}.o_technical_modal.o_web_editor_dialog > .o_select_media_dialog{max-width: inherit !important; z-index: 2001;}.o_technical_modal.o_web_editor_dialog > .o_select_media_dialog .modal-dialog, .o_technical_modal.o_web_editor_dialog > .o_select_media_dialog .model-content{height: 100%;}.o_technical_modal.o_web_editor_dialog > .o_select_media_dialog .modal-body .nav .nav-item.search{width: 100%;}.o_technical_modal.o_web_editor_dialog > .o_select_media_dialog .modal-body .nav .nav-item.search .btn-group{display: -webkit-box; display: -webkit-flex; display: flex; justify-content: space-around; padding: 5px;}.o_technical_modal.o_web_editor_dialog > .o_select_media_dialog .modal-body .font-icons-icons{text-align: center;}.o_technical_modal.o_web_editor_dialog > .o_select_media_dialog .modal-body .form-control.o_we_search{height: inherit;}.o_technical_modal.o_web_editor_dialog > .o_select_media_dialog .modal-body .o_we_existing_attachments > .row{-webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column;}.o_technical_modal.o_web_editor_dialog > .o_select_media_dialog .modal-body .o_we_existing_attachments > .row > .o_existing_attachment_cell{flex: initial; max-width: 100%;}.o_technical_modal.o_web_editor_dialog > .o_select_media_dialog .modal-body .o_we_existing_attachments > .row > .o_existing_attachment_cell > .o_existing_attachment_remove{opacity: inherit; top: 10px;}}blockquote{padding: 0.5rem 1rem; border-left: 5px solid; border-color: #DEE2E6; font-style: italic;}pre{padding: 0.5rem 1rem; border: 1px solid #dee2e6; border-radius: 0.25rem; background-color: #F8F9FA; color: #212529; white-space: pre-wrap;}pre p{margin-bottom: 0px;}.bg-o-color-1{background-color: #714B67 !important; color: #FFFFFF;}.bg-o-color-1 .text-muted, .o_colored_level .bg-o-color-1 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}a.bg-o-color-1:hover, a.bg-o-color-1:focus, button.bg-o-color-1:hover, button.bg-o-color-1:focus{background-color: #52374b !important; color: #FFFFFF;}.text-o-color-1{color: #714B67 !important;}a.text-o-color-1:hover, a.text-o-color-1:focus{color: #34222f !important;}.bg-o-color-2{background-color: #8595A2 !important; color: #FFFFFF;}.bg-o-color-2 .text-muted, .o_colored_level .bg-o-color-2 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}a.bg-o-color-2:hover, a.bg-o-color-2:focus, button.bg-o-color-2:hover, button.bg-o-color-2:focus{background-color: #6a7c8a !important; color: #FFFFFF;}.text-o-color-2{color: #8595A2 !important;}a.text-o-color-2:hover, a.text-o-color-2:focus{color: #53626e !important;}.bg-o-color-3{background-color: #F3F2F2 !important; color: #000000;}.bg-o-color-3 .text-muted, .o_colored_level .bg-o-color-3 .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-o-color-3:hover, a.bg-o-color-3:focus, button.bg-o-color-3:hover, button.bg-o-color-3:focus{background-color: #dbd7d7 !important; color: #000000;}.text-o-color-3{color: #F3F2F2 !important;}a.text-o-color-3:hover, a.text-o-color-3:focus{color: #c2bdbd !important;}.bg-o-color-4{background-color: #FFFFFF !important; color: #000000;}.bg-o-color-4 .text-muted, .o_colored_level .bg-o-color-4 .text-muted{color: rgba(0, 0, 0, 0.7) !important;}a.bg-o-color-4:hover, a.bg-o-color-4:focus, button.bg-o-color-4:hover, button.bg-o-color-4:focus{background-color: #e6e6e6 !important; color: #000000;}.text-o-color-4{color: #FFFFFF !important;}a.text-o-color-4:hover, a.text-o-color-4:focus{color: #cccccc !important;}.bg-o-color-5{background-color: #111827 !important; color: #FFFFFF;}.bg-o-color-5 .text-muted, .o_colored_level .bg-o-color-5 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}a.bg-o-color-5:hover, a.bg-o-color-5:focus, button.bg-o-color-5:hover, button.bg-o-color-5:focus{background-color: #020203 !important; color: #FFFFFF;}.text-o-color-5{color: #111827 !important;}a.text-o-color-5:hover, a.text-o-color-5:focus{color: black !important;}.o_cc .dropdown-menu .dropdown-item, .o_cc .dropdown-menu .dropdown-item h6, .o_cc .dropdown-menu .dropdown-item .h6, .o_colored_level .o_cc .dropdown-menu .dropdown-item, .o_colored_level .o_cc .dropdown-menu .dropdown-item h6{color: #212529 !important;}.o_cc .dropdown-menu .dropdown-item:hover, .o_cc .dropdown-menu .dropdown-item:focus, .o_cc .dropdown-menu .dropdown-item h6:hover, .o_cc .dropdown-menu .dropdown-item .h6:hover, .o_cc .dropdown-menu .dropdown-item h6:focus, .o_cc .dropdown-menu .dropdown-item .h6:focus, .o_colored_level .o_cc .dropdown-menu .dropdown-item:hover, .o_colored_level .o_cc .dropdown-menu .dropdown-item:focus, .o_colored_level .o_cc .dropdown-menu .dropdown-item h6:hover, .o_colored_level .o_cc .dropdown-menu .dropdown-item h6:focus{color: #1e2125 !important;}.o_cc .dropdown-menu .dropdown-item.disabled, .o_cc .dropdown-menu .dropdown-item.disabled h6, .o_cc .dropdown-menu .dropdown-item.disabled .h6, .o_cc .dropdown-menu .dropdown-item:disabled, .o_cc .dropdown-menu .dropdown-item.o_wysiwyg_loader, .o_cc .dropdown-menu .dropdown-item:disabled h6, .o_cc .dropdown-menu .dropdown-item.o_wysiwyg_loader h6, .o_cc .dropdown-menu .dropdown-item:disabled .h6, .o_cc .dropdown-menu .dropdown-item.o_wysiwyg_loader .h6, .o_colored_level .o_cc .dropdown-menu .dropdown-item.disabled, .o_colored_level .o_cc .dropdown-menu .dropdown-item.disabled h6, .o_colored_level .o_cc .dropdown-menu .dropdown-item:disabled, .o_colored_level .o_cc .dropdown-menu .dropdown-item:disabled h6{color: #ADB5BD !important;}.o_cc .dropdown-menu .dropdown-item .btn-link, .o_colored_level .o_cc .dropdown-menu .dropdown-item .btn-link{color: #714B67;}.o_cc .dropdown-menu .dropdown-item .btn-link:hover, .o_colored_level .o_cc .dropdown-menu .dropdown-item .btn-link:hover{color: #5a3c52;}.o_cc .dropdown-menu .dropdown-item .btn-link:disabled, .o_cc .dropdown-menu .dropdown-item .btn-link.o_wysiwyg_loader, .o_colored_level .o_cc .dropdown-menu .dropdown-item .btn-link:disabled{color: #6C757D;}.o_cc .dropdown-menu .dropdown-item-text .text-muted a, .o_colored_level .o_cc .dropdown-menu .dropdown-item-text .text-muted a{color: #714B67;}.o_cc .dropdown-menu .dropdown-item-text .text-muted a:hover, .o_colored_level .o_cc .dropdown-menu .dropdown-item-text .text-muted a:hover{color: #5a3c52;}.o_cc1{background-color: #FFFFFF; color: #000000; --o-cc-bg: #FFFFFF;}.o_cc1 .text-muted, .o_colored_level .o_cc1 .text-muted{color: rgba(0, 0, 0, 0.7) !important;}.o_cc1 a:not(.btn), .o_cc1 .btn-link, .o_colored_level .o_cc1 a:not(.btn), .o_colored_level .o_cc1 .btn-link{color: #65435c;}.o_cc1 a:not(.btn):hover, .o_cc1 .btn-link:hover, .o_colored_level .o_cc1 a:not(.btn):hover, .o_colored_level .o_cc1 .btn-link:hover{color: #432c3d;}.o_cc1 .btn-fill-primary, .o_cc1 .btn-primary, .o_colored_level .o_cc1 .btn-fill-primary{color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.o_cc1 .btn-fill-primary:hover, .o_cc1 .btn-primary:hover, .o_colored_level .o_cc1 .btn-fill-primary:hover{color: #FFFFFF; background-color: #604058; border-color: #5a3c52;}.btn-check:focus + .o_cc1 .btn-fill-primary, .btn-check:focus + .o_cc1 .btn-primary, .o_cc1 .btn-fill-primary:focus, .o_cc1 .btn-primary:focus, .btn-check:focus + .o_colored_level .o_cc1 .btn-fill-primary, .o_colored_level .o_cc1 .btn-fill-primary:focus{color: #FFFFFF; background-color: #604058; border-color: #5a3c52; box-shadow: 0 0 0 0.25rem rgba(134, 102, 126, 0.5);}.btn-check:checked + .o_cc1 .btn-fill-primary, .btn-check:checked + .o_cc1 .btn-primary, .btn-check:active + .o_cc1 .btn-fill-primary, .btn-check:active + .o_cc1 .btn-primary, .o_cc1 .btn-fill-primary:active, .o_cc1 .btn-primary:active, .o_cc1 .btn-fill-primary.active, .o_cc1 .active.btn-primary, .show > .o_cc1 .btn-fill-primary.dropdown-toggle, .show > .o_cc1 .dropdown-toggle.btn-primary, .btn-check:checked + .o_colored_level .o_cc1 .btn-fill-primary, .btn-check:active + .o_colored_level .o_cc1 .btn-fill-primary, .o_colored_level .o_cc1 .btn-fill-primary:active, .o_colored_level .o_cc1 .btn-fill-primary.active, .show > .o_colored_level .o_cc1 .btn-fill-primary.dropdown-toggle{color: #FFFFFF; background-color: #5a3c52; border-color: #55384d;}.btn-check:checked + .o_cc1 .btn-fill-primary:focus, .btn-check:checked + .o_cc1 .btn-primary:focus, .btn-check:active + .o_cc1 .btn-fill-primary:focus, .btn-check:active + .o_cc1 .btn-primary:focus, .o_cc1 .btn-fill-primary:active:focus, .o_cc1 .btn-primary:active:focus, .o_cc1 .btn-fill-primary.active:focus, .o_cc1 .active.btn-primary:focus, .show > .o_cc1 .btn-fill-primary.dropdown-toggle:focus, .show > .o_cc1 .dropdown-toggle.btn-primary:focus, .btn-check:checked + .o_colored_level .o_cc1 .btn-fill-primary:focus, .btn-check:active + .o_colored_level .o_cc1 .btn-fill-primary:focus, .o_colored_level .o_cc1 .btn-fill-primary:active:focus, .o_colored_level .o_cc1 .btn-fill-primary.active:focus, .show > .o_colored_level .o_cc1 .btn-fill-primary.dropdown-toggle:focus{box-shadow: 0 0 0 0.25rem rgba(134, 102, 126, 0.5);}.o_cc1 .btn-fill-primary:disabled, .o_cc1 .btn-fill-primary.o_wysiwyg_loader, .o_cc1 .btn-primary:disabled, .o_cc1 .btn-primary.o_wysiwyg_loader, .o_cc1 .btn-fill-primary.disabled, .o_cc1 .disabled.btn-primary, .o_colored_level .o_cc1 .btn-fill-primary:disabled, .o_colored_level .o_cc1 .btn-fill-primary.disabled{color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.o_cc1 .btn-outline-primary, .o_colored_level .o_cc1 .btn-outline-primary{color: #714B67; border-color: #714B67;}.o_cc1 .btn-outline-primary:hover, .o_colored_level .o_cc1 .btn-outline-primary:hover{color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.btn-check:focus + .o_cc1 .btn-outline-primary, .o_cc1 .btn-outline-primary:focus, .btn-check:focus + .o_colored_level .o_cc1 .btn-outline-primary, .o_colored_level .o_cc1 .btn-outline-primary:focus{box-shadow: 0 0 0 0.25rem rgba(113, 75, 103, 0.5);}.btn-check:checked + .o_cc1 .btn-outline-primary, .btn-check:active + .o_cc1 .btn-outline-primary, .o_cc1 .btn-outline-primary:active, .o_cc1 .btn-outline-primary.active, .o_cc1 .btn-outline-primary.dropdown-toggle.show, .btn-check:checked + .o_colored_level .o_cc1 .btn-outline-primary, .btn-check:active + .o_colored_level .o_cc1 .btn-outline-primary, .o_colored_level .o_cc1 .btn-outline-primary:active, .o_colored_level .o_cc1 .btn-outline-primary.active, .o_colored_level .o_cc1 .btn-outline-primary.dropdown-toggle.show{color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.btn-check:checked + .o_cc1 .btn-outline-primary:focus, .btn-check:active + .o_cc1 .btn-outline-primary:focus, .o_cc1 .btn-outline-primary:active:focus, .o_cc1 .btn-outline-primary.active:focus, .o_cc1 .btn-outline-primary.dropdown-toggle.show:focus, .btn-check:checked + .o_colored_level .o_cc1 .btn-outline-primary:focus, .btn-check:active + .o_colored_level .o_cc1 .btn-outline-primary:focus, .o_colored_level .o_cc1 .btn-outline-primary:active:focus, .o_colored_level .o_cc1 .btn-outline-primary.active:focus, .o_colored_level .o_cc1 .btn-outline-primary.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(113, 75, 103, 0.5);}.o_cc1 .btn-outline-primary:disabled, .o_cc1 .btn-outline-primary.o_wysiwyg_loader, .o_cc1 .btn-outline-primary.disabled, .o_colored_level .o_cc1 .btn-outline-primary:disabled, .o_colored_level .o_cc1 .btn-outline-primary.disabled{color: #714B67; background-color: transparent;}.o_cc1 .btn-fill-secondary, .o_cc1 .btn-secondary, .o_colored_level .o_cc1 .btn-fill-secondary{color: #FFFFFF; background-color: #8595A2; border-color: #8595A2;}.o_cc1 .btn-fill-secondary:hover, .o_cc1 .btn-secondary:hover, .o_colored_level .o_cc1 .btn-fill-secondary:hover{color: #FFFFFF; background-color: #717f8a; border-color: #6a7782;}.btn-check:focus + .o_cc1 .btn-fill-secondary, .btn-check:focus + .o_cc1 .btn-secondary, .o_cc1 .btn-fill-secondary:focus, .o_cc1 .btn-secondary:focus, .btn-check:focus + .o_colored_level .o_cc1 .btn-fill-secondary, .o_colored_level .o_cc1 .btn-fill-secondary:focus{color: #FFFFFF; background-color: #717f8a; border-color: #6a7782; box-shadow: 0 0 0 0.25rem rgba(151, 165, 176, 0.5);}.btn-check:checked + .o_cc1 .btn-fill-secondary, .btn-check:checked + .o_cc1 .btn-secondary, .btn-check:active + .o_cc1 .btn-fill-secondary, .btn-check:active + .o_cc1 .btn-secondary, .o_cc1 .btn-fill-secondary:active, .o_cc1 .btn-secondary:active, .o_cc1 .btn-fill-secondary.active, .o_cc1 .active.btn-secondary, .show > .o_cc1 .btn-fill-secondary.dropdown-toggle, .show > .o_cc1 .dropdown-toggle.btn-secondary, .btn-check:checked + .o_colored_level .o_cc1 .btn-fill-secondary, .btn-check:active + .o_colored_level .o_cc1 .btn-fill-secondary, .o_colored_level .o_cc1 .btn-fill-secondary:active, .o_colored_level .o_cc1 .btn-fill-secondary.active, .show > .o_colored_level .o_cc1 .btn-fill-secondary.dropdown-toggle{color: #FFFFFF; background-color: #6a7782; border-color: #64707a;}.btn-check:checked + .o_cc1 .btn-fill-secondary:focus, .btn-check:checked + .o_cc1 .btn-secondary:focus, .btn-check:active + .o_cc1 .btn-fill-secondary:focus, .btn-check:active + .o_cc1 .btn-secondary:focus, .o_cc1 .btn-fill-secondary:active:focus, .o_cc1 .btn-secondary:active:focus, .o_cc1 .btn-fill-secondary.active:focus, .o_cc1 .active.btn-secondary:focus, .show > .o_cc1 .btn-fill-secondary.dropdown-toggle:focus, .show > .o_cc1 .dropdown-toggle.btn-secondary:focus, .btn-check:checked + .o_colored_level .o_cc1 .btn-fill-secondary:focus, .btn-check:active + .o_colored_level .o_cc1 .btn-fill-secondary:focus, .o_colored_level .o_cc1 .btn-fill-secondary:active:focus, .o_colored_level .o_cc1 .btn-fill-secondary.active:focus, .show > .o_colored_level .o_cc1 .btn-fill-secondary.dropdown-toggle:focus{box-shadow: 0 0 0 0.25rem rgba(151, 165, 176, 0.5);}.o_cc1 .btn-fill-secondary:disabled, .o_cc1 .btn-fill-secondary.o_wysiwyg_loader, .o_cc1 .btn-secondary:disabled, .o_cc1 .btn-secondary.o_wysiwyg_loader, .o_cc1 .btn-fill-secondary.disabled, .o_cc1 .disabled.btn-secondary, .o_colored_level .o_cc1 .btn-fill-secondary:disabled, .o_colored_level .o_cc1 .btn-fill-secondary.disabled{color: #FFFFFF; background-color: #8595A2; border-color: #8595A2;}.o_cc1 .btn-outline-secondary, .o_colored_level .o_cc1 .btn-outline-secondary{color: #8595A2; border-color: #8595A2;}.o_cc1 .btn-outline-secondary:hover, .o_colored_level .o_cc1 .btn-outline-secondary:hover{color: #FFFFFF; background-color: #8595A2; border-color: #8595A2;}.btn-check:focus + .o_cc1 .btn-outline-secondary, .o_cc1 .btn-outline-secondary:focus, .btn-check:focus + .o_colored_level .o_cc1 .btn-outline-secondary, .o_colored_level .o_cc1 .btn-outline-secondary:focus{box-shadow: 0 0 0 0.25rem rgba(133, 149, 162, 0.5);}.btn-check:checked + .o_cc1 .btn-outline-secondary, .btn-check:active + .o_cc1 .btn-outline-secondary, .o_cc1 .btn-outline-secondary:active, .o_cc1 .btn-outline-secondary.active, .o_cc1 .btn-outline-secondary.dropdown-toggle.show, .btn-check:checked + .o_colored_level .o_cc1 .btn-outline-secondary, .btn-check:active + .o_colored_level .o_cc1 .btn-outline-secondary, .o_colored_level .o_cc1 .btn-outline-secondary:active, .o_colored_level .o_cc1 .btn-outline-secondary.active, .o_colored_level .o_cc1 .btn-outline-secondary.dropdown-toggle.show{color: #FFFFFF; background-color: #8595A2; border-color: #8595A2;}.btn-check:checked + .o_cc1 .btn-outline-secondary:focus, .btn-check:active + .o_cc1 .btn-outline-secondary:focus, .o_cc1 .btn-outline-secondary:active:focus, .o_cc1 .btn-outline-secondary.active:focus, .o_cc1 .btn-outline-secondary.dropdown-toggle.show:focus, .btn-check:checked + .o_colored_level .o_cc1 .btn-outline-secondary:focus, .btn-check:active + .o_colored_level .o_cc1 .btn-outline-secondary:focus, .o_colored_level .o_cc1 .btn-outline-secondary:active:focus, .o_colored_level .o_cc1 .btn-outline-secondary.active:focus, .o_colored_level .o_cc1 .btn-outline-secondary.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(133, 149, 162, 0.5);}.o_cc1 .btn-outline-secondary:disabled, .o_cc1 .btn-outline-secondary.o_wysiwyg_loader, .o_cc1 .btn-outline-secondary.disabled, .o_colored_level .o_cc1 .btn-outline-secondary:disabled, .o_colored_level .o_cc1 .btn-outline-secondary.disabled{color: #8595A2; background-color: transparent;}.o_cc1 .nav-pills .nav-link.active, .o_cc1 .nav-pills .show > .nav-link, .o_colored_level .o_cc1 .nav-pills .nav-link.active, .o_colored_level .o_cc1 .nav-pills .show > .nav-link{background-color: #714B67; color: #FFFFFF;}.o_cc1 .dropdown-menu .dropdown-item.active, .o_cc1 .dropdown-menu .dropdown-item.active h6, .o_cc1 .dropdown-menu .dropdown-item.active .h6, .o_cc1 .dropdown-menu .dropdown-item:active, .o_cc1 .dropdown-menu .dropdown-item:active h6, .o_cc1 .dropdown-menu .dropdown-item:active .h6, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item.active, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item.active h6, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item:active, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item:active h6{background-color: #714B67; color: #FFFFFF !important;}.o_cc1 .dropdown-menu .dropdown-item.active:hover, .o_cc1 .dropdown-menu .dropdown-item.active:focus, .o_cc1 .dropdown-menu .dropdown-item.active h6:hover, .o_cc1 .dropdown-menu .dropdown-item.active .h6:hover, .o_cc1 .dropdown-menu .dropdown-item.active h6:focus, .o_cc1 .dropdown-menu .dropdown-item.active .h6:focus, .o_cc1 .dropdown-menu .dropdown-item:active:hover, .o_cc1 .dropdown-menu .dropdown-item:active:focus, .o_cc1 .dropdown-menu .dropdown-item:active h6:hover, .o_cc1 .dropdown-menu .dropdown-item:active .h6:hover, .o_cc1 .dropdown-menu .dropdown-item:active h6:focus, .o_cc1 .dropdown-menu .dropdown-item:active .h6:focus, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item.active:hover, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item.active:focus, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item.active h6:hover, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item.active h6:focus, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item:active:hover, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item:active:focus, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item:active h6:hover, .o_colored_level .o_cc1 .dropdown-menu .dropdown-item:active h6:focus{color: #FFFFFF !important;}.o_cc1 a.list-group-item, .o_colored_level .o_cc1 a.list-group-item{color: #714B67;}.o_cc1 a.list-group-item.active, .o_colored_level .o_cc1 a.list-group-item.active{background-color: #714B67; color: #FFFFFF; border-color: #714B67;}.o_cc2{background-color: #F3F2F2; color: #000000; --o-cc-bg: #F3F2F2;}.o_cc2 .text-muted, .o_colored_level .o_cc2 .text-muted{color: rgba(0, 0, 0, 0.7) !important;}.o_cc2 h1, .o_cc2 .h1, .o_cc2 h2, .o_cc2 .h2, .o_cc2 h3, .o_cc2 .h3, .o_cc2 h4, .o_cc2 .h4, .o_cc2 h5, .o_cc2 .h5, .o_cc2 h6, .o_cc2 .h6, .o_colored_level .o_cc2 h1, .o_colored_level .o_cc2 h2, .o_colored_level .o_cc2 h3, .o_colored_level .o_cc2 h4, .o_colored_level .o_cc2 h5, .o_colored_level .o_cc2 h6{color: #111827;}.o_cc2 a:not(.btn), .o_cc2 .btn-link, .o_colored_level .o_cc2 a:not(.btn), .o_colored_level .o_cc2 .btn-link{color: #55394e;}.o_cc2 a:not(.btn):hover, .o_cc2 .btn-link:hover, .o_colored_level .o_cc2 a:not(.btn):hover, .o_colored_level .o_cc2 .btn-link:hover{color: #432c3d;}.o_cc2 .btn-fill-primary, .o_cc2 .btn-primary, .o_colored_level .o_cc2 .btn-fill-primary{color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.o_cc2 .btn-fill-primary:hover, .o_cc2 .btn-primary:hover, .o_colored_level .o_cc2 .btn-fill-primary:hover{color: #FFFFFF; background-color: #604058; border-color: #5a3c52;}.btn-check:focus + .o_cc2 .btn-fill-primary, .btn-check:focus + .o_cc2 .btn-primary, .o_cc2 .btn-fill-primary:focus, .o_cc2 .btn-primary:focus, .btn-check:focus + .o_colored_level .o_cc2 .btn-fill-primary, .o_colored_level .o_cc2 .btn-fill-primary:focus{color: #FFFFFF; background-color: #604058; border-color: #5a3c52; box-shadow: 0 0 0 0.25rem rgba(134, 102, 126, 0.5);}.btn-check:checked + .o_cc2 .btn-fill-primary, .btn-check:checked + .o_cc2 .btn-primary, .btn-check:active + .o_cc2 .btn-fill-primary, .btn-check:active + .o_cc2 .btn-primary, .o_cc2 .btn-fill-primary:active, .o_cc2 .btn-primary:active, .o_cc2 .btn-fill-primary.active, .o_cc2 .active.btn-primary, .show > .o_cc2 .btn-fill-primary.dropdown-toggle, .show > .o_cc2 .dropdown-toggle.btn-primary, .btn-check:checked + .o_colored_level .o_cc2 .btn-fill-primary, .btn-check:active + .o_colored_level .o_cc2 .btn-fill-primary, .o_colored_level .o_cc2 .btn-fill-primary:active, .o_colored_level .o_cc2 .btn-fill-primary.active, .show > .o_colored_level .o_cc2 .btn-fill-primary.dropdown-toggle{color: #FFFFFF; background-color: #5a3c52; border-color: #55384d;}.btn-check:checked + .o_cc2 .btn-fill-primary:focus, .btn-check:checked + .o_cc2 .btn-primary:focus, .btn-check:active + .o_cc2 .btn-fill-primary:focus, .btn-check:active + .o_cc2 .btn-primary:focus, .o_cc2 .btn-fill-primary:active:focus, .o_cc2 .btn-primary:active:focus, .o_cc2 .btn-fill-primary.active:focus, .o_cc2 .active.btn-primary:focus, .show > .o_cc2 .btn-fill-primary.dropdown-toggle:focus, .show > .o_cc2 .dropdown-toggle.btn-primary:focus, .btn-check:checked + .o_colored_level .o_cc2 .btn-fill-primary:focus, .btn-check:active + .o_colored_level .o_cc2 .btn-fill-primary:focus, .o_colored_level .o_cc2 .btn-fill-primary:active:focus, .o_colored_level .o_cc2 .btn-fill-primary.active:focus, .show > .o_colored_level .o_cc2 .btn-fill-primary.dropdown-toggle:focus{box-shadow: 0 0 0 0.25rem rgba(134, 102, 126, 0.5);}.o_cc2 .btn-fill-primary:disabled, .o_cc2 .btn-fill-primary.o_wysiwyg_loader, .o_cc2 .btn-primary:disabled, .o_cc2 .btn-primary.o_wysiwyg_loader, .o_cc2 .btn-fill-primary.disabled, .o_cc2 .disabled.btn-primary, .o_colored_level .o_cc2 .btn-fill-primary:disabled, .o_colored_level .o_cc2 .btn-fill-primary.disabled{color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.o_cc2 .btn-outline-primary, .o_colored_level .o_cc2 .btn-outline-primary{color: #714B67; border-color: #714B67;}.o_cc2 .btn-outline-primary:hover, .o_colored_level .o_cc2 .btn-outline-primary:hover{color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.btn-check:focus + .o_cc2 .btn-outline-primary, .o_cc2 .btn-outline-primary:focus, .btn-check:focus + .o_colored_level .o_cc2 .btn-outline-primary, .o_colored_level .o_cc2 .btn-outline-primary:focus{box-shadow: 0 0 0 0.25rem rgba(113, 75, 103, 0.5);}.btn-check:checked + .o_cc2 .btn-outline-primary, .btn-check:active + .o_cc2 .btn-outline-primary, .o_cc2 .btn-outline-primary:active, .o_cc2 .btn-outline-primary.active, .o_cc2 .btn-outline-primary.dropdown-toggle.show, .btn-check:checked + .o_colored_level .o_cc2 .btn-outline-primary, .btn-check:active + .o_colored_level .o_cc2 .btn-outline-primary, .o_colored_level .o_cc2 .btn-outline-primary:active, .o_colored_level .o_cc2 .btn-outline-primary.active, .o_colored_level .o_cc2 .btn-outline-primary.dropdown-toggle.show{color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.btn-check:checked + .o_cc2 .btn-outline-primary:focus, .btn-check:active + .o_cc2 .btn-outline-primary:focus, .o_cc2 .btn-outline-primary:active:focus, .o_cc2 .btn-outline-primary.active:focus, .o_cc2 .btn-outline-primary.dropdown-toggle.show:focus, .btn-check:checked + .o_colored_level .o_cc2 .btn-outline-primary:focus, .btn-check:active + .o_colored_level .o_cc2 .btn-outline-primary:focus, .o_colored_level .o_cc2 .btn-outline-primary:active:focus, .o_colored_level .o_cc2 .btn-outline-primary.active:focus, .o_colored_level .o_cc2 .btn-outline-primary.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(113, 75, 103, 0.5);}.o_cc2 .btn-outline-primary:disabled, .o_cc2 .btn-outline-primary.o_wysiwyg_loader, .o_cc2 .btn-outline-primary.disabled, .o_colored_level .o_cc2 .btn-outline-primary:disabled, .o_colored_level .o_cc2 .btn-outline-primary.disabled{color: #714B67; background-color: transparent;}.o_cc2 .btn-fill-secondary, .o_cc2 .btn-secondary, .o_colored_level .o_cc2 .btn-fill-secondary{color: #FFFFFF; background-color: #8595A2; border-color: #8595A2;}.o_cc2 .btn-fill-secondary:hover, .o_cc2 .btn-secondary:hover, .o_colored_level .o_cc2 .btn-fill-secondary:hover{color: #FFFFFF; background-color: #717f8a; border-color: #6a7782;}.btn-check:focus + .o_cc2 .btn-fill-secondary, .btn-check:focus + .o_cc2 .btn-secondary, .o_cc2 .btn-fill-secondary:focus, .o_cc2 .btn-secondary:focus, .btn-check:focus + .o_colored_level .o_cc2 .btn-fill-secondary, .o_colored_level .o_cc2 .btn-fill-secondary:focus{color: #FFFFFF; background-color: #717f8a; border-color: #6a7782; box-shadow: 0 0 0 0.25rem rgba(151, 165, 176, 0.5);}.btn-check:checked + .o_cc2 .btn-fill-secondary, .btn-check:checked + .o_cc2 .btn-secondary, .btn-check:active + .o_cc2 .btn-fill-secondary, .btn-check:active + .o_cc2 .btn-secondary, .o_cc2 .btn-fill-secondary:active, .o_cc2 .btn-secondary:active, .o_cc2 .btn-fill-secondary.active, .o_cc2 .active.btn-secondary, .show > .o_cc2 .btn-fill-secondary.dropdown-toggle, .show > .o_cc2 .dropdown-toggle.btn-secondary, .btn-check:checked + .o_colored_level .o_cc2 .btn-fill-secondary, .btn-check:active + .o_colored_level .o_cc2 .btn-fill-secondary, .o_colored_level .o_cc2 .btn-fill-secondary:active, .o_colored_level .o_cc2 .btn-fill-secondary.active, .show > .o_colored_level .o_cc2 .btn-fill-secondary.dropdown-toggle{color: #FFFFFF; background-color: #6a7782; border-color: #64707a;}.btn-check:checked + .o_cc2 .btn-fill-secondary:focus, .btn-check:checked + .o_cc2 .btn-secondary:focus, .btn-check:active + .o_cc2 .btn-fill-secondary:focus, .btn-check:active + .o_cc2 .btn-secondary:focus, .o_cc2 .btn-fill-secondary:active:focus, .o_cc2 .btn-secondary:active:focus, .o_cc2 .btn-fill-secondary.active:focus, .o_cc2 .active.btn-secondary:focus, .show > .o_cc2 .btn-fill-secondary.dropdown-toggle:focus, .show > .o_cc2 .dropdown-toggle.btn-secondary:focus, .btn-check:checked + .o_colored_level .o_cc2 .btn-fill-secondary:focus, .btn-check:active + .o_colored_level .o_cc2 .btn-fill-secondary:focus, .o_colored_level .o_cc2 .btn-fill-secondary:active:focus, .o_colored_level .o_cc2 .btn-fill-secondary.active:focus, .show > .o_colored_level .o_cc2 .btn-fill-secondary.dropdown-toggle:focus{box-shadow: 0 0 0 0.25rem rgba(151, 165, 176, 0.5);}.o_cc2 .btn-fill-secondary:disabled, .o_cc2 .btn-fill-secondary.o_wysiwyg_loader, .o_cc2 .btn-secondary:disabled, .o_cc2 .btn-secondary.o_wysiwyg_loader, .o_cc2 .btn-fill-secondary.disabled, .o_cc2 .disabled.btn-secondary, .o_colored_level .o_cc2 .btn-fill-secondary:disabled, .o_colored_level .o_cc2 .btn-fill-secondary.disabled{color: #FFFFFF; background-color: #8595A2; border-color: #8595A2;}.o_cc2 .btn-outline-secondary, .o_colored_level .o_cc2 .btn-outline-secondary{color: #8595A2; border-color: #8595A2;}.o_cc2 .btn-outline-secondary:hover, .o_colored_level .o_cc2 .btn-outline-secondary:hover{color: #FFFFFF; background-color: #8595A2; border-color: #8595A2;}.btn-check:focus + .o_cc2 .btn-outline-secondary, .o_cc2 .btn-outline-secondary:focus, .btn-check:focus + .o_colored_level .o_cc2 .btn-outline-secondary, .o_colored_level .o_cc2 .btn-outline-secondary:focus{box-shadow: 0 0 0 0.25rem rgba(133, 149, 162, 0.5);}.btn-check:checked + .o_cc2 .btn-outline-secondary, .btn-check:active + .o_cc2 .btn-outline-secondary, .o_cc2 .btn-outline-secondary:active, .o_cc2 .btn-outline-secondary.active, .o_cc2 .btn-outline-secondary.dropdown-toggle.show, .btn-check:checked + .o_colored_level .o_cc2 .btn-outline-secondary, .btn-check:active + .o_colored_level .o_cc2 .btn-outline-secondary, .o_colored_level .o_cc2 .btn-outline-secondary:active, .o_colored_level .o_cc2 .btn-outline-secondary.active, .o_colored_level .o_cc2 .btn-outline-secondary.dropdown-toggle.show{color: #FFFFFF; background-color: #8595A2; border-color: #8595A2;}.btn-check:checked + .o_cc2 .btn-outline-secondary:focus, .btn-check:active + .o_cc2 .btn-outline-secondary:focus, .o_cc2 .btn-outline-secondary:active:focus, .o_cc2 .btn-outline-secondary.active:focus, .o_cc2 .btn-outline-secondary.dropdown-toggle.show:focus, .btn-check:checked + .o_colored_level .o_cc2 .btn-outline-secondary:focus, .btn-check:active + .o_colored_level .o_cc2 .btn-outline-secondary:focus, .o_colored_level .o_cc2 .btn-outline-secondary:active:focus, .o_colored_level .o_cc2 .btn-outline-secondary.active:focus, .o_colored_level .o_cc2 .btn-outline-secondary.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(133, 149, 162, 0.5);}.o_cc2 .btn-outline-secondary:disabled, .o_cc2 .btn-outline-secondary.o_wysiwyg_loader, .o_cc2 .btn-outline-secondary.disabled, .o_colored_level .o_cc2 .btn-outline-secondary:disabled, .o_colored_level .o_cc2 .btn-outline-secondary.disabled{color: #8595A2; background-color: transparent;}.o_cc2 .nav-pills .nav-link.active, .o_cc2 .nav-pills .show > .nav-link, .o_colored_level .o_cc2 .nav-pills .nav-link.active, .o_colored_level .o_cc2 .nav-pills .show > .nav-link{background-color: #714B67; color: #FFFFFF;}.o_cc2 .dropdown-menu .dropdown-item.active, .o_cc2 .dropdown-menu .dropdown-item.active h6, .o_cc2 .dropdown-menu .dropdown-item.active .h6, .o_cc2 .dropdown-menu .dropdown-item:active, .o_cc2 .dropdown-menu .dropdown-item:active h6, .o_cc2 .dropdown-menu .dropdown-item:active .h6, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item.active, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item.active h6, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item:active, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item:active h6{background-color: #714B67; color: #FFFFFF !important;}.o_cc2 .dropdown-menu .dropdown-item.active:hover, .o_cc2 .dropdown-menu .dropdown-item.active:focus, .o_cc2 .dropdown-menu .dropdown-item.active h6:hover, .o_cc2 .dropdown-menu .dropdown-item.active .h6:hover, .o_cc2 .dropdown-menu .dropdown-item.active h6:focus, .o_cc2 .dropdown-menu .dropdown-item.active .h6:focus, .o_cc2 .dropdown-menu .dropdown-item:active:hover, .o_cc2 .dropdown-menu .dropdown-item:active:focus, .o_cc2 .dropdown-menu .dropdown-item:active h6:hover, .o_cc2 .dropdown-menu .dropdown-item:active .h6:hover, .o_cc2 .dropdown-menu .dropdown-item:active h6:focus, .o_cc2 .dropdown-menu .dropdown-item:active .h6:focus, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item.active:hover, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item.active:focus, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item.active h6:hover, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item.active h6:focus, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item:active:hover, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item:active:focus, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item:active h6:hover, .o_colored_level .o_cc2 .dropdown-menu .dropdown-item:active h6:focus{color: #FFFFFF !important;}.o_cc2 a.list-group-item, .o_colored_level .o_cc2 a.list-group-item{color: #714B67;}.o_cc2 a.list-group-item.active, .o_colored_level .o_cc2 a.list-group-item.active{background-color: #714B67; color: #FFFFFF; border-color: #714B67;}.o_cc3{background-color: #8595A2; color: #FFFFFF; --o-cc-bg: #8595A2;}.o_cc3 .text-muted, .o_colored_level .o_cc3 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}.o_cc3 a:not(.btn), .o_cc3 .btn-link, .o_colored_level .o_cc3 a:not(.btn), .o_colored_level .o_cc3 .btn-link{color: #241821;}.o_cc3 a:not(.btn):hover, .o_cc3 .btn-link:hover, .o_colored_level .o_cc3 a:not(.btn):hover, .o_colored_level .o_cc3 .btn-link:hover{color: black;}.o_cc3 .btn-fill-primary, .o_cc3 .btn-primary, .o_colored_level .o_cc3 .btn-fill-primary{color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.o_cc3 .btn-fill-primary:hover, .o_cc3 .btn-primary:hover, .o_colored_level .o_cc3 .btn-fill-primary:hover{color: #FFFFFF; background-color: #604058; border-color: #5a3c52;}.btn-check:focus + .o_cc3 .btn-fill-primary, .btn-check:focus + .o_cc3 .btn-primary, .o_cc3 .btn-fill-primary:focus, .o_cc3 .btn-primary:focus, .btn-check:focus + .o_colored_level .o_cc3 .btn-fill-primary, .o_colored_level .o_cc3 .btn-fill-primary:focus{color: #FFFFFF; background-color: #604058; border-color: #5a3c52; box-shadow: 0 0 0 0.25rem rgba(134, 102, 126, 0.5);}.btn-check:checked + .o_cc3 .btn-fill-primary, .btn-check:checked + .o_cc3 .btn-primary, .btn-check:active + .o_cc3 .btn-fill-primary, .btn-check:active + .o_cc3 .btn-primary, .o_cc3 .btn-fill-primary:active, .o_cc3 .btn-primary:active, .o_cc3 .btn-fill-primary.active, .o_cc3 .active.btn-primary, .show > .o_cc3 .btn-fill-primary.dropdown-toggle, .show > .o_cc3 .dropdown-toggle.btn-primary, .btn-check:checked + .o_colored_level .o_cc3 .btn-fill-primary, .btn-check:active + .o_colored_level .o_cc3 .btn-fill-primary, .o_colored_level .o_cc3 .btn-fill-primary:active, .o_colored_level .o_cc3 .btn-fill-primary.active, .show > .o_colored_level .o_cc3 .btn-fill-primary.dropdown-toggle{color: #FFFFFF; background-color: #5a3c52; border-color: #55384d;}.btn-check:checked + .o_cc3 .btn-fill-primary:focus, .btn-check:checked + .o_cc3 .btn-primary:focus, .btn-check:active + .o_cc3 .btn-fill-primary:focus, .btn-check:active + .o_cc3 .btn-primary:focus, .o_cc3 .btn-fill-primary:active:focus, .o_cc3 .btn-primary:active:focus, .o_cc3 .btn-fill-primary.active:focus, .o_cc3 .active.btn-primary:focus, .show > .o_cc3 .btn-fill-primary.dropdown-toggle:focus, .show > .o_cc3 .dropdown-toggle.btn-primary:focus, .btn-check:checked + .o_colored_level .o_cc3 .btn-fill-primary:focus, .btn-check:active + .o_colored_level .o_cc3 .btn-fill-primary:focus, .o_colored_level .o_cc3 .btn-fill-primary:active:focus, .o_colored_level .o_cc3 .btn-fill-primary.active:focus, .show > .o_colored_level .o_cc3 .btn-fill-primary.dropdown-toggle:focus{box-shadow: 0 0 0 0.25rem rgba(134, 102, 126, 0.5);}.o_cc3 .btn-fill-primary:disabled, .o_cc3 .btn-fill-primary.o_wysiwyg_loader, .o_cc3 .btn-primary:disabled, .o_cc3 .btn-primary.o_wysiwyg_loader, .o_cc3 .btn-fill-primary.disabled, .o_cc3 .disabled.btn-primary, .o_colored_level .o_cc3 .btn-fill-primary:disabled, .o_colored_level .o_cc3 .btn-fill-primary.disabled{color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.o_cc3 .btn-outline-primary, .o_colored_level .o_cc3 .btn-outline-primary{color: #714B67; border-color: #714B67;}.o_cc3 .btn-outline-primary:hover, .o_colored_level .o_cc3 .btn-outline-primary:hover{color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.btn-check:focus + .o_cc3 .btn-outline-primary, .o_cc3 .btn-outline-primary:focus, .btn-check:focus + .o_colored_level .o_cc3 .btn-outline-primary, .o_colored_level .o_cc3 .btn-outline-primary:focus{box-shadow: 0 0 0 0.25rem rgba(113, 75, 103, 0.5);}.btn-check:checked + .o_cc3 .btn-outline-primary, .btn-check:active + .o_cc3 .btn-outline-primary, .o_cc3 .btn-outline-primary:active, .o_cc3 .btn-outline-primary.active, .o_cc3 .btn-outline-primary.dropdown-toggle.show, .btn-check:checked + .o_colored_level .o_cc3 .btn-outline-primary, .btn-check:active + .o_colored_level .o_cc3 .btn-outline-primary, .o_colored_level .o_cc3 .btn-outline-primary:active, .o_colored_level .o_cc3 .btn-outline-primary.active, .o_colored_level .o_cc3 .btn-outline-primary.dropdown-toggle.show{color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.btn-check:checked + .o_cc3 .btn-outline-primary:focus, .btn-check:active + .o_cc3 .btn-outline-primary:focus, .o_cc3 .btn-outline-primary:active:focus, .o_cc3 .btn-outline-primary.active:focus, .o_cc3 .btn-outline-primary.dropdown-toggle.show:focus, .btn-check:checked + .o_colored_level .o_cc3 .btn-outline-primary:focus, .btn-check:active + .o_colored_level .o_cc3 .btn-outline-primary:focus, .o_colored_level .o_cc3 .btn-outline-primary:active:focus, .o_colored_level .o_cc3 .btn-outline-primary.active:focus, .o_colored_level .o_cc3 .btn-outline-primary.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(113, 75, 103, 0.5);}.o_cc3 .btn-outline-primary:disabled, .o_cc3 .btn-outline-primary.o_wysiwyg_loader, .o_cc3 .btn-outline-primary.disabled, .o_colored_level .o_cc3 .btn-outline-primary:disabled, .o_colored_level .o_cc3 .btn-outline-primary.disabled{color: #714B67; background-color: transparent;}.o_cc3 .btn-fill-secondary, .o_cc3 .btn-secondary, .o_colored_level .o_cc3 .btn-fill-secondary{color: #000000; background-color: #F3F2F2; border-color: #F3F2F2;}.o_cc3 .btn-fill-secondary:hover, .o_cc3 .btn-secondary:hover, .o_colored_level .o_cc3 .btn-fill-secondary:hover{color: #000000; background-color: #f5f4f4; border-color: #f4f3f3;}.btn-check:focus + .o_cc3 .btn-fill-secondary, .btn-check:focus + .o_cc3 .btn-secondary, .o_cc3 .btn-fill-secondary:focus, .o_cc3 .btn-secondary:focus, .btn-check:focus + .o_colored_level .o_cc3 .btn-fill-secondary, .o_colored_level .o_cc3 .btn-fill-secondary:focus{color: #000000; background-color: #f5f4f4; border-color: #f4f3f3; box-shadow: 0 0 0 0.25rem rgba(207, 206, 206, 0.5);}.btn-check:checked + .o_cc3 .btn-fill-secondary, .btn-check:checked + .o_cc3 .btn-secondary, .btn-check:active + .o_cc3 .btn-fill-secondary, .btn-check:active + .o_cc3 .btn-secondary, .o_cc3 .btn-fill-secondary:active, .o_cc3 .btn-secondary:active, .o_cc3 .btn-fill-secondary.active, .o_cc3 .active.btn-secondary, .show > .o_cc3 .btn-fill-secondary.dropdown-toggle, .show > .o_cc3 .dropdown-toggle.btn-secondary, .btn-check:checked + .o_colored_level .o_cc3 .btn-fill-secondary, .btn-check:active + .o_colored_level .o_cc3 .btn-fill-secondary, .o_colored_level .o_cc3 .btn-fill-secondary:active, .o_colored_level .o_cc3 .btn-fill-secondary.active, .show > .o_colored_level .o_cc3 .btn-fill-secondary.dropdown-toggle{color: #000000; background-color: whitesmoke; border-color: #f4f3f3;}.btn-check:checked + .o_cc3 .btn-fill-secondary:focus, .btn-check:checked + .o_cc3 .btn-secondary:focus, .btn-check:active + .o_cc3 .btn-fill-secondary:focus, .btn-check:active + .o_cc3 .btn-secondary:focus, .o_cc3 .btn-fill-secondary:active:focus, .o_cc3 .btn-secondary:active:focus, .o_cc3 .btn-fill-secondary.active:focus, .o_cc3 .active.btn-secondary:focus, .show > .o_cc3 .btn-fill-secondary.dropdown-toggle:focus, .show > .o_cc3 .dropdown-toggle.btn-secondary:focus, .btn-check:checked + .o_colored_level .o_cc3 .btn-fill-secondary:focus, .btn-check:active + .o_colored_level .o_cc3 .btn-fill-secondary:focus, .o_colored_level .o_cc3 .btn-fill-secondary:active:focus, .o_colored_level .o_cc3 .btn-fill-secondary.active:focus, .show > .o_colored_level .o_cc3 .btn-fill-secondary.dropdown-toggle:focus{box-shadow: 0 0 0 0.25rem rgba(207, 206, 206, 0.5);}.o_cc3 .btn-fill-secondary:disabled, .o_cc3 .btn-fill-secondary.o_wysiwyg_loader, .o_cc3 .btn-secondary:disabled, .o_cc3 .btn-secondary.o_wysiwyg_loader, .o_cc3 .btn-fill-secondary.disabled, .o_cc3 .disabled.btn-secondary, .o_colored_level .o_cc3 .btn-fill-secondary:disabled, .o_colored_level .o_cc3 .btn-fill-secondary.disabled{color: #000000; background-color: #F3F2F2; border-color: #F3F2F2;}.o_cc3 .btn-outline-secondary, .o_colored_level .o_cc3 .btn-outline-secondary{color: #F3F2F2; border-color: #F3F2F2;}.o_cc3 .btn-outline-secondary:hover, .o_colored_level .o_cc3 .btn-outline-secondary:hover{color: #000000; background-color: #F3F2F2; border-color: #F3F2F2;}.btn-check:focus + .o_cc3 .btn-outline-secondary, .o_cc3 .btn-outline-secondary:focus, .btn-check:focus + .o_colored_level .o_cc3 .btn-outline-secondary, .o_colored_level .o_cc3 .btn-outline-secondary:focus{box-shadow: 0 0 0 0.25rem rgba(243, 242, 242, 0.5);}.btn-check:checked + .o_cc3 .btn-outline-secondary, .btn-check:active + .o_cc3 .btn-outline-secondary, .o_cc3 .btn-outline-secondary:active, .o_cc3 .btn-outline-secondary.active, .o_cc3 .btn-outline-secondary.dropdown-toggle.show, .btn-check:checked + .o_colored_level .o_cc3 .btn-outline-secondary, .btn-check:active + .o_colored_level .o_cc3 .btn-outline-secondary, .o_colored_level .o_cc3 .btn-outline-secondary:active, .o_colored_level .o_cc3 .btn-outline-secondary.active, .o_colored_level .o_cc3 .btn-outline-secondary.dropdown-toggle.show{color: #000000; background-color: #F3F2F2; border-color: #F3F2F2;}.btn-check:checked + .o_cc3 .btn-outline-secondary:focus, .btn-check:active + .o_cc3 .btn-outline-secondary:focus, .o_cc3 .btn-outline-secondary:active:focus, .o_cc3 .btn-outline-secondary.active:focus, .o_cc3 .btn-outline-secondary.dropdown-toggle.show:focus, .btn-check:checked + .o_colored_level .o_cc3 .btn-outline-secondary:focus, .btn-check:active + .o_colored_level .o_cc3 .btn-outline-secondary:focus, .o_colored_level .o_cc3 .btn-outline-secondary:active:focus, .o_colored_level .o_cc3 .btn-outline-secondary.active:focus, .o_colored_level .o_cc3 .btn-outline-secondary.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(243, 242, 242, 0.5);}.o_cc3 .btn-outline-secondary:disabled, .o_cc3 .btn-outline-secondary.o_wysiwyg_loader, .o_cc3 .btn-outline-secondary.disabled, .o_colored_level .o_cc3 .btn-outline-secondary:disabled, .o_colored_level .o_cc3 .btn-outline-secondary.disabled{color: #F3F2F2; background-color: transparent;}.o_cc3 .nav-pills .nav-link.active, .o_cc3 .nav-pills .show > .nav-link, .o_colored_level .o_cc3 .nav-pills .nav-link.active, .o_colored_level .o_cc3 .nav-pills .show > .nav-link{background-color: #714B67; color: #FFFFFF;}.o_cc3 .dropdown-menu .dropdown-item.active, .o_cc3 .dropdown-menu .dropdown-item.active h6, .o_cc3 .dropdown-menu .dropdown-item.active .h6, .o_cc3 .dropdown-menu .dropdown-item:active, .o_cc3 .dropdown-menu .dropdown-item:active h6, .o_cc3 .dropdown-menu .dropdown-item:active .h6, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item.active, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item.active h6, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item:active, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item:active h6{background-color: #714B67; color: #FFFFFF !important;}.o_cc3 .dropdown-menu .dropdown-item.active:hover, .o_cc3 .dropdown-menu .dropdown-item.active:focus, .o_cc3 .dropdown-menu .dropdown-item.active h6:hover, .o_cc3 .dropdown-menu .dropdown-item.active .h6:hover, .o_cc3 .dropdown-menu .dropdown-item.active h6:focus, .o_cc3 .dropdown-menu .dropdown-item.active .h6:focus, .o_cc3 .dropdown-menu .dropdown-item:active:hover, .o_cc3 .dropdown-menu .dropdown-item:active:focus, .o_cc3 .dropdown-menu .dropdown-item:active h6:hover, .o_cc3 .dropdown-menu .dropdown-item:active .h6:hover, .o_cc3 .dropdown-menu .dropdown-item:active h6:focus, .o_cc3 .dropdown-menu .dropdown-item:active .h6:focus, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item.active:hover, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item.active:focus, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item.active h6:hover, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item.active h6:focus, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item:active:hover, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item:active:focus, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item:active h6:hover, .o_colored_level .o_cc3 .dropdown-menu .dropdown-item:active h6:focus{color: #FFFFFF !important;}.o_cc3 a.list-group-item, .o_colored_level .o_cc3 a.list-group-item{color: #714B67;}.o_cc3 a.list-group-item.active, .o_colored_level .o_cc3 a.list-group-item.active{background-color: #714B67; color: #FFFFFF; border-color: #714B67;}.o_cc4{background-color: #714B67; color: #FFFFFF; --o-cc-bg: #714B67;}.o_cc4 .text-muted, .o_colored_level .o_cc4 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}.o_cc4 a:not(.btn), .o_cc4 .btn-link, .o_colored_level .o_cc4 a:not(.btn), .o_colored_level .o_cc4 .btn-link{color: black;}.o_cc4 a:not(.btn):hover, .o_cc4 .btn-link:hover, .o_colored_level .o_cc4 a:not(.btn):hover, .o_colored_level .o_cc4 .btn-link:hover{color: black;}.o_cc4 .btn-fill-primary, .o_cc4 .btn-primary, .o_colored_level .o_cc4 .btn-fill-primary{color: #FFFFFF; background-color: #111827; border-color: #111827;}.o_cc4 .btn-fill-primary:hover, .o_cc4 .btn-primary:hover, .o_colored_level .o_cc4 .btn-fill-primary:hover{color: #FFFFFF; background-color: #0e1421; border-color: #0e131f;}.btn-check:focus + .o_cc4 .btn-fill-primary, .btn-check:focus + .o_cc4 .btn-primary, .o_cc4 .btn-fill-primary:focus, .o_cc4 .btn-primary:focus, .btn-check:focus + .o_colored_level .o_cc4 .btn-fill-primary, .o_colored_level .o_cc4 .btn-fill-primary:focus{color: #FFFFFF; background-color: #0e1421; border-color: #0e131f; box-shadow: 0 0 0 0.25rem rgba(53, 59, 71, 0.5);}.btn-check:checked + .o_cc4 .btn-fill-primary, .btn-check:checked + .o_cc4 .btn-primary, .btn-check:active + .o_cc4 .btn-fill-primary, .btn-check:active + .o_cc4 .btn-primary, .o_cc4 .btn-fill-primary:active, .o_cc4 .btn-primary:active, .o_cc4 .btn-fill-primary.active, .o_cc4 .active.btn-primary, .show > .o_cc4 .btn-fill-primary.dropdown-toggle, .show > .o_cc4 .dropdown-toggle.btn-primary, .btn-check:checked + .o_colored_level .o_cc4 .btn-fill-primary, .btn-check:active + .o_colored_level .o_cc4 .btn-fill-primary, .o_colored_level .o_cc4 .btn-fill-primary:active, .o_colored_level .o_cc4 .btn-fill-primary.active, .show > .o_colored_level .o_cc4 .btn-fill-primary.dropdown-toggle{color: #FFFFFF; background-color: #0e131f; border-color: #0d121d;}.btn-check:checked + .o_cc4 .btn-fill-primary:focus, .btn-check:checked + .o_cc4 .btn-primary:focus, .btn-check:active + .o_cc4 .btn-fill-primary:focus, .btn-check:active + .o_cc4 .btn-primary:focus, .o_cc4 .btn-fill-primary:active:focus, .o_cc4 .btn-primary:active:focus, .o_cc4 .btn-fill-primary.active:focus, .o_cc4 .active.btn-primary:focus, .show > .o_cc4 .btn-fill-primary.dropdown-toggle:focus, .show > .o_cc4 .dropdown-toggle.btn-primary:focus, .btn-check:checked + .o_colored_level .o_cc4 .btn-fill-primary:focus, .btn-check:active + .o_colored_level .o_cc4 .btn-fill-primary:focus, .o_colored_level .o_cc4 .btn-fill-primary:active:focus, .o_colored_level .o_cc4 .btn-fill-primary.active:focus, .show > .o_colored_level .o_cc4 .btn-fill-primary.dropdown-toggle:focus{box-shadow: 0 0 0 0.25rem rgba(53, 59, 71, 0.5);}.o_cc4 .btn-fill-primary:disabled, .o_cc4 .btn-fill-primary.o_wysiwyg_loader, .o_cc4 .btn-primary:disabled, .o_cc4 .btn-primary.o_wysiwyg_loader, .o_cc4 .btn-fill-primary.disabled, .o_cc4 .disabled.btn-primary, .o_colored_level .o_cc4 .btn-fill-primary:disabled, .o_colored_level .o_cc4 .btn-fill-primary.disabled{color: #FFFFFF; background-color: #111827; border-color: #111827;}.o_cc4 .btn-outline-primary, .o_colored_level .o_cc4 .btn-outline-primary{color: #111827; border-color: #111827;}.o_cc4 .btn-outline-primary:hover, .o_colored_level .o_cc4 .btn-outline-primary:hover{color: #FFFFFF; background-color: #111827; border-color: #111827;}.btn-check:focus + .o_cc4 .btn-outline-primary, .o_cc4 .btn-outline-primary:focus, .btn-check:focus + .o_colored_level .o_cc4 .btn-outline-primary, .o_colored_level .o_cc4 .btn-outline-primary:focus{box-shadow: 0 0 0 0.25rem rgba(17, 24, 39, 0.5);}.btn-check:checked + .o_cc4 .btn-outline-primary, .btn-check:active + .o_cc4 .btn-outline-primary, .o_cc4 .btn-outline-primary:active, .o_cc4 .btn-outline-primary.active, .o_cc4 .btn-outline-primary.dropdown-toggle.show, .btn-check:checked + .o_colored_level .o_cc4 .btn-outline-primary, .btn-check:active + .o_colored_level .o_cc4 .btn-outline-primary, .o_colored_level .o_cc4 .btn-outline-primary:active, .o_colored_level .o_cc4 .btn-outline-primary.active, .o_colored_level .o_cc4 .btn-outline-primary.dropdown-toggle.show{color: #FFFFFF; background-color: #111827; border-color: #111827;}.btn-check:checked + .o_cc4 .btn-outline-primary:focus, .btn-check:active + .o_cc4 .btn-outline-primary:focus, .o_cc4 .btn-outline-primary:active:focus, .o_cc4 .btn-outline-primary.active:focus, .o_cc4 .btn-outline-primary.dropdown-toggle.show:focus, .btn-check:checked + .o_colored_level .o_cc4 .btn-outline-primary:focus, .btn-check:active + .o_colored_level .o_cc4 .btn-outline-primary:focus, .o_colored_level .o_cc4 .btn-outline-primary:active:focus, .o_colored_level .o_cc4 .btn-outline-primary.active:focus, .o_colored_level .o_cc4 .btn-outline-primary.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(17, 24, 39, 0.5);}.o_cc4 .btn-outline-primary:disabled, .o_cc4 .btn-outline-primary.o_wysiwyg_loader, .o_cc4 .btn-outline-primary.disabled, .o_colored_level .o_cc4 .btn-outline-primary:disabled, .o_colored_level .o_cc4 .btn-outline-primary.disabled{color: #111827; background-color: transparent;}.o_cc4 .btn-fill-secondary, .o_cc4 .btn-secondary, .o_colored_level .o_cc4 .btn-fill-secondary{color: #000000; background-color: #F3F2F2; border-color: #F3F2F2;}.o_cc4 .btn-fill-secondary:hover, .o_cc4 .btn-secondary:hover, .o_colored_level .o_cc4 .btn-fill-secondary:hover{color: #000000; background-color: #f5f4f4; border-color: #f4f3f3;}.btn-check:focus + .o_cc4 .btn-fill-secondary, .btn-check:focus + .o_cc4 .btn-secondary, .o_cc4 .btn-fill-secondary:focus, .o_cc4 .btn-secondary:focus, .btn-check:focus + .o_colored_level .o_cc4 .btn-fill-secondary, .o_colored_level .o_cc4 .btn-fill-secondary:focus{color: #000000; background-color: #f5f4f4; border-color: #f4f3f3; box-shadow: 0 0 0 0.25rem rgba(207, 206, 206, 0.5);}.btn-check:checked + .o_cc4 .btn-fill-secondary, .btn-check:checked + .o_cc4 .btn-secondary, .btn-check:active + .o_cc4 .btn-fill-secondary, .btn-check:active + .o_cc4 .btn-secondary, .o_cc4 .btn-fill-secondary:active, .o_cc4 .btn-secondary:active, .o_cc4 .btn-fill-secondary.active, .o_cc4 .active.btn-secondary, .show > .o_cc4 .btn-fill-secondary.dropdown-toggle, .show > .o_cc4 .dropdown-toggle.btn-secondary, .btn-check:checked + .o_colored_level .o_cc4 .btn-fill-secondary, .btn-check:active + .o_colored_level .o_cc4 .btn-fill-secondary, .o_colored_level .o_cc4 .btn-fill-secondary:active, .o_colored_level .o_cc4 .btn-fill-secondary.active, .show > .o_colored_level .o_cc4 .btn-fill-secondary.dropdown-toggle{color: #000000; background-color: whitesmoke; border-color: #f4f3f3;}.btn-check:checked + .o_cc4 .btn-fill-secondary:focus, .btn-check:checked + .o_cc4 .btn-secondary:focus, .btn-check:active + .o_cc4 .btn-fill-secondary:focus, .btn-check:active + .o_cc4 .btn-secondary:focus, .o_cc4 .btn-fill-secondary:active:focus, .o_cc4 .btn-secondary:active:focus, .o_cc4 .btn-fill-secondary.active:focus, .o_cc4 .active.btn-secondary:focus, .show > .o_cc4 .btn-fill-secondary.dropdown-toggle:focus, .show > .o_cc4 .dropdown-toggle.btn-secondary:focus, .btn-check:checked + .o_colored_level .o_cc4 .btn-fill-secondary:focus, .btn-check:active + .o_colored_level .o_cc4 .btn-fill-secondary:focus, .o_colored_level .o_cc4 .btn-fill-secondary:active:focus, .o_colored_level .o_cc4 .btn-fill-secondary.active:focus, .show > .o_colored_level .o_cc4 .btn-fill-secondary.dropdown-toggle:focus{box-shadow: 0 0 0 0.25rem rgba(207, 206, 206, 0.5);}.o_cc4 .btn-fill-secondary:disabled, .o_cc4 .btn-fill-secondary.o_wysiwyg_loader, .o_cc4 .btn-secondary:disabled, .o_cc4 .btn-secondary.o_wysiwyg_loader, .o_cc4 .btn-fill-secondary.disabled, .o_cc4 .disabled.btn-secondary, .o_colored_level .o_cc4 .btn-fill-secondary:disabled, .o_colored_level .o_cc4 .btn-fill-secondary.disabled{color: #000000; background-color: #F3F2F2; border-color: #F3F2F2;}.o_cc4 .btn-outline-secondary, .o_colored_level .o_cc4 .btn-outline-secondary{color: #F3F2F2; border-color: #F3F2F2;}.o_cc4 .btn-outline-secondary:hover, .o_colored_level .o_cc4 .btn-outline-secondary:hover{color: #000000; background-color: #F3F2F2; border-color: #F3F2F2;}.btn-check:focus + .o_cc4 .btn-outline-secondary, .o_cc4 .btn-outline-secondary:focus, .btn-check:focus + .o_colored_level .o_cc4 .btn-outline-secondary, .o_colored_level .o_cc4 .btn-outline-secondary:focus{box-shadow: 0 0 0 0.25rem rgba(243, 242, 242, 0.5);}.btn-check:checked + .o_cc4 .btn-outline-secondary, .btn-check:active + .o_cc4 .btn-outline-secondary, .o_cc4 .btn-outline-secondary:active, .o_cc4 .btn-outline-secondary.active, .o_cc4 .btn-outline-secondary.dropdown-toggle.show, .btn-check:checked + .o_colored_level .o_cc4 .btn-outline-secondary, .btn-check:active + .o_colored_level .o_cc4 .btn-outline-secondary, .o_colored_level .o_cc4 .btn-outline-secondary:active, .o_colored_level .o_cc4 .btn-outline-secondary.active, .o_colored_level .o_cc4 .btn-outline-secondary.dropdown-toggle.show{color: #000000; background-color: #F3F2F2; border-color: #F3F2F2;}.btn-check:checked + .o_cc4 .btn-outline-secondary:focus, .btn-check:active + .o_cc4 .btn-outline-secondary:focus, .o_cc4 .btn-outline-secondary:active:focus, .o_cc4 .btn-outline-secondary.active:focus, .o_cc4 .btn-outline-secondary.dropdown-toggle.show:focus, .btn-check:checked + .o_colored_level .o_cc4 .btn-outline-secondary:focus, .btn-check:active + .o_colored_level .o_cc4 .btn-outline-secondary:focus, .o_colored_level .o_cc4 .btn-outline-secondary:active:focus, .o_colored_level .o_cc4 .btn-outline-secondary.active:focus, .o_colored_level .o_cc4 .btn-outline-secondary.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(243, 242, 242, 0.5);}.o_cc4 .btn-outline-secondary:disabled, .o_cc4 .btn-outline-secondary.o_wysiwyg_loader, .o_cc4 .btn-outline-secondary.disabled, .o_colored_level .o_cc4 .btn-outline-secondary:disabled, .o_colored_level .o_cc4 .btn-outline-secondary.disabled{color: #F3F2F2; background-color: transparent;}.o_cc4 .nav-pills .nav-link.active, .o_cc4 .nav-pills .show > .nav-link, .o_colored_level .o_cc4 .nav-pills .nav-link.active, .o_colored_level .o_cc4 .nav-pills .show > .nav-link{background-color: #111827; color: #FFFFFF;}.o_cc4 .dropdown-menu .dropdown-item.active, .o_cc4 .dropdown-menu .dropdown-item.active h6, .o_cc4 .dropdown-menu .dropdown-item.active .h6, .o_cc4 .dropdown-menu .dropdown-item:active, .o_cc4 .dropdown-menu .dropdown-item:active h6, .o_cc4 .dropdown-menu .dropdown-item:active .h6, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item.active, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item.active h6, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item:active, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item:active h6{background-color: #111827; color: #FFFFFF !important;}.o_cc4 .dropdown-menu .dropdown-item.active:hover, .o_cc4 .dropdown-menu .dropdown-item.active:focus, .o_cc4 .dropdown-menu .dropdown-item.active h6:hover, .o_cc4 .dropdown-menu .dropdown-item.active .h6:hover, .o_cc4 .dropdown-menu .dropdown-item.active h6:focus, .o_cc4 .dropdown-menu .dropdown-item.active .h6:focus, .o_cc4 .dropdown-menu .dropdown-item:active:hover, .o_cc4 .dropdown-menu .dropdown-item:active:focus, .o_cc4 .dropdown-menu .dropdown-item:active h6:hover, .o_cc4 .dropdown-menu .dropdown-item:active .h6:hover, .o_cc4 .dropdown-menu .dropdown-item:active h6:focus, .o_cc4 .dropdown-menu .dropdown-item:active .h6:focus, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item.active:hover, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item.active:focus, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item.active h6:hover, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item.active h6:focus, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item:active:hover, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item:active:focus, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item:active h6:hover, .o_colored_level .o_cc4 .dropdown-menu .dropdown-item:active h6:focus{color: #FFFFFF !important;}.o_cc4 a.list-group-item, .o_colored_level .o_cc4 a.list-group-item{color: #111827;}.o_cc4 a.list-group-item.active, .o_colored_level .o_cc4 a.list-group-item.active{background-color: #111827; color: #FFFFFF; border-color: #111827;}.o_cc5{background-color: #111827; color: #FFFFFF; --o-cc-bg: #111827;}.o_cc5 .text-muted, .o_colored_level .o_cc5 .text-muted{color: rgba(255, 255, 255, 0.7) !important;}.o_cc5 h1, .o_cc5 .h1, .o_cc5 h2, .o_cc5 .h2, .o_cc5 h3, .o_cc5 .h3, .o_cc5 h4, .o_cc5 .h4, .o_cc5 h5, .o_cc5 .h5, .o_cc5 h6, .o_cc5 .h6, .o_colored_level .o_cc5 h1, .o_colored_level .o_cc5 h2, .o_colored_level .o_cc5 h3, .o_colored_level .o_cc5 h4, .o_colored_level .o_cc5 h5, .o_colored_level .o_cc5 h6{color: #FFFFFF;}.o_cc5 a:not(.btn), .o_cc5 .btn-link, .o_colored_level .o_cc5 a:not(.btn), .o_colored_level .o_cc5 .btn-link{color: #b18aa7;}.o_cc5 a:not(.btn):hover, .o_cc5 .btn-link:hover, .o_colored_level .o_cc5 a:not(.btn):hover, .o_colored_level .o_cc5 .btn-link:hover{color: #905f83;}.o_cc5 .btn-fill-primary, .o_cc5 .btn-primary, .o_colored_level .o_cc5 .btn-fill-primary{color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.o_cc5 .btn-fill-primary:hover, .o_cc5 .btn-primary:hover, .o_colored_level .o_cc5 .btn-fill-primary:hover{color: #FFFFFF; background-color: #604058; border-color: #5a3c52;}.btn-check:focus + .o_cc5 .btn-fill-primary, .btn-check:focus + .o_cc5 .btn-primary, .o_cc5 .btn-fill-primary:focus, .o_cc5 .btn-primary:focus, .btn-check:focus + .o_colored_level .o_cc5 .btn-fill-primary, .o_colored_level .o_cc5 .btn-fill-primary:focus{color: #FFFFFF; background-color: #604058; border-color: #5a3c52; box-shadow: 0 0 0 0.25rem rgba(134, 102, 126, 0.5);}.btn-check:checked + .o_cc5 .btn-fill-primary, .btn-check:checked + .o_cc5 .btn-primary, .btn-check:active + .o_cc5 .btn-fill-primary, .btn-check:active + .o_cc5 .btn-primary, .o_cc5 .btn-fill-primary:active, .o_cc5 .btn-primary:active, .o_cc5 .btn-fill-primary.active, .o_cc5 .active.btn-primary, .show > .o_cc5 .btn-fill-primary.dropdown-toggle, .show > .o_cc5 .dropdown-toggle.btn-primary, .btn-check:checked + .o_colored_level .o_cc5 .btn-fill-primary, .btn-check:active + .o_colored_level .o_cc5 .btn-fill-primary, .o_colored_level .o_cc5 .btn-fill-primary:active, .o_colored_level .o_cc5 .btn-fill-primary.active, .show > .o_colored_level .o_cc5 .btn-fill-primary.dropdown-toggle{color: #FFFFFF; background-color: #5a3c52; border-color: #55384d;}.btn-check:checked + .o_cc5 .btn-fill-primary:focus, .btn-check:checked + .o_cc5 .btn-primary:focus, .btn-check:active + .o_cc5 .btn-fill-primary:focus, .btn-check:active + .o_cc5 .btn-primary:focus, .o_cc5 .btn-fill-primary:active:focus, .o_cc5 .btn-primary:active:focus, .o_cc5 .btn-fill-primary.active:focus, .o_cc5 .active.btn-primary:focus, .show > .o_cc5 .btn-fill-primary.dropdown-toggle:focus, .show > .o_cc5 .dropdown-toggle.btn-primary:focus, .btn-check:checked + .o_colored_level .o_cc5 .btn-fill-primary:focus, .btn-check:active + .o_colored_level .o_cc5 .btn-fill-primary:focus, .o_colored_level .o_cc5 .btn-fill-primary:active:focus, .o_colored_level .o_cc5 .btn-fill-primary.active:focus, .show > .o_colored_level .o_cc5 .btn-fill-primary.dropdown-toggle:focus{box-shadow: 0 0 0 0.25rem rgba(134, 102, 126, 0.5);}.o_cc5 .btn-fill-primary:disabled, .o_cc5 .btn-fill-primary.o_wysiwyg_loader, .o_cc5 .btn-primary:disabled, .o_cc5 .btn-primary.o_wysiwyg_loader, .o_cc5 .btn-fill-primary.disabled, .o_cc5 .disabled.btn-primary, .o_colored_level .o_cc5 .btn-fill-primary:disabled, .o_colored_level .o_cc5 .btn-fill-primary.disabled{color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.o_cc5 .btn-outline-primary, .o_colored_level .o_cc5 .btn-outline-primary{color: #714B67; border-color: #714B67;}.o_cc5 .btn-outline-primary:hover, .o_colored_level .o_cc5 .btn-outline-primary:hover{color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.btn-check:focus + .o_cc5 .btn-outline-primary, .o_cc5 .btn-outline-primary:focus, .btn-check:focus + .o_colored_level .o_cc5 .btn-outline-primary, .o_colored_level .o_cc5 .btn-outline-primary:focus{box-shadow: 0 0 0 0.25rem rgba(113, 75, 103, 0.5);}.btn-check:checked + .o_cc5 .btn-outline-primary, .btn-check:active + .o_cc5 .btn-outline-primary, .o_cc5 .btn-outline-primary:active, .o_cc5 .btn-outline-primary.active, .o_cc5 .btn-outline-primary.dropdown-toggle.show, .btn-check:checked + .o_colored_level .o_cc5 .btn-outline-primary, .btn-check:active + .o_colored_level .o_cc5 .btn-outline-primary, .o_colored_level .o_cc5 .btn-outline-primary:active, .o_colored_level .o_cc5 .btn-outline-primary.active, .o_colored_level .o_cc5 .btn-outline-primary.dropdown-toggle.show{color: #FFFFFF; background-color: #714B67; border-color: #714B67;}.btn-check:checked + .o_cc5 .btn-outline-primary:focus, .btn-check:active + .o_cc5 .btn-outline-primary:focus, .o_cc5 .btn-outline-primary:active:focus, .o_cc5 .btn-outline-primary.active:focus, .o_cc5 .btn-outline-primary.dropdown-toggle.show:focus, .btn-check:checked + .o_colored_level .o_cc5 .btn-outline-primary:focus, .btn-check:active + .o_colored_level .o_cc5 .btn-outline-primary:focus, .o_colored_level .o_cc5 .btn-outline-primary:active:focus, .o_colored_level .o_cc5 .btn-outline-primary.active:focus, .o_colored_level .o_cc5 .btn-outline-primary.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(113, 75, 103, 0.5);}.o_cc5 .btn-outline-primary:disabled, .o_cc5 .btn-outline-primary.o_wysiwyg_loader, .o_cc5 .btn-outline-primary.disabled, .o_colored_level .o_cc5 .btn-outline-primary:disabled, .o_colored_level .o_cc5 .btn-outline-primary.disabled{color: #714B67; background-color: transparent;}.o_cc5 .btn-fill-secondary, .o_cc5 .btn-secondary, .o_colored_level .o_cc5 .btn-fill-secondary{color: #000000; background-color: #F3F2F2; border-color: #F3F2F2;}.o_cc5 .btn-fill-secondary:hover, .o_cc5 .btn-secondary:hover, .o_colored_level .o_cc5 .btn-fill-secondary:hover{color: #000000; background-color: #f5f4f4; border-color: #f4f3f3;}.btn-check:focus + .o_cc5 .btn-fill-secondary, .btn-check:focus + .o_cc5 .btn-secondary, .o_cc5 .btn-fill-secondary:focus, .o_cc5 .btn-secondary:focus, .btn-check:focus + .o_colored_level .o_cc5 .btn-fill-secondary, .o_colored_level .o_cc5 .btn-fill-secondary:focus{color: #000000; background-color: #f5f4f4; border-color: #f4f3f3; box-shadow: 0 0 0 0.25rem rgba(207, 206, 206, 0.5);}.btn-check:checked + .o_cc5 .btn-fill-secondary, .btn-check:checked + .o_cc5 .btn-secondary, .btn-check:active + .o_cc5 .btn-fill-secondary, .btn-check:active + .o_cc5 .btn-secondary, .o_cc5 .btn-fill-secondary:active, .o_cc5 .btn-secondary:active, .o_cc5 .btn-fill-secondary.active, .o_cc5 .active.btn-secondary, .show > .o_cc5 .btn-fill-secondary.dropdown-toggle, .show > .o_cc5 .dropdown-toggle.btn-secondary, .btn-check:checked + .o_colored_level .o_cc5 .btn-fill-secondary, .btn-check:active + .o_colored_level .o_cc5 .btn-fill-secondary, .o_colored_level .o_cc5 .btn-fill-secondary:active, .o_colored_level .o_cc5 .btn-fill-secondary.active, .show > .o_colored_level .o_cc5 .btn-fill-secondary.dropdown-toggle{color: #000000; background-color: whitesmoke; border-color: #f4f3f3;}.btn-check:checked + .o_cc5 .btn-fill-secondary:focus, .btn-check:checked + .o_cc5 .btn-secondary:focus, .btn-check:active + .o_cc5 .btn-fill-secondary:focus, .btn-check:active + .o_cc5 .btn-secondary:focus, .o_cc5 .btn-fill-secondary:active:focus, .o_cc5 .btn-secondary:active:focus, .o_cc5 .btn-fill-secondary.active:focus, .o_cc5 .active.btn-secondary:focus, .show > .o_cc5 .btn-fill-secondary.dropdown-toggle:focus, .show > .o_cc5 .dropdown-toggle.btn-secondary:focus, .btn-check:checked + .o_colored_level .o_cc5 .btn-fill-secondary:focus, .btn-check:active + .o_colored_level .o_cc5 .btn-fill-secondary:focus, .o_colored_level .o_cc5 .btn-fill-secondary:active:focus, .o_colored_level .o_cc5 .btn-fill-secondary.active:focus, .show > .o_colored_level .o_cc5 .btn-fill-secondary.dropdown-toggle:focus{box-shadow: 0 0 0 0.25rem rgba(207, 206, 206, 0.5);}.o_cc5 .btn-fill-secondary:disabled, .o_cc5 .btn-fill-secondary.o_wysiwyg_loader, .o_cc5 .btn-secondary:disabled, .o_cc5 .btn-secondary.o_wysiwyg_loader, .o_cc5 .btn-fill-secondary.disabled, .o_cc5 .disabled.btn-secondary, .o_colored_level .o_cc5 .btn-fill-secondary:disabled, .o_colored_level .o_cc5 .btn-fill-secondary.disabled{color: #000000; background-color: #F3F2F2; border-color: #F3F2F2;}.o_cc5 .btn-outline-secondary, .o_colored_level .o_cc5 .btn-outline-secondary{color: #F3F2F2; border-color: #F3F2F2;}.o_cc5 .btn-outline-secondary:hover, .o_colored_level .o_cc5 .btn-outline-secondary:hover{color: #000000; background-color: #F3F2F2; border-color: #F3F2F2;}.btn-check:focus + .o_cc5 .btn-outline-secondary, .o_cc5 .btn-outline-secondary:focus, .btn-check:focus + .o_colored_level .o_cc5 .btn-outline-secondary, .o_colored_level .o_cc5 .btn-outline-secondary:focus{box-shadow: 0 0 0 0.25rem rgba(243, 242, 242, 0.5);}.btn-check:checked + .o_cc5 .btn-outline-secondary, .btn-check:active + .o_cc5 .btn-outline-secondary, .o_cc5 .btn-outline-secondary:active, .o_cc5 .btn-outline-secondary.active, .o_cc5 .btn-outline-secondary.dropdown-toggle.show, .btn-check:checked + .o_colored_level .o_cc5 .btn-outline-secondary, .btn-check:active + .o_colored_level .o_cc5 .btn-outline-secondary, .o_colored_level .o_cc5 .btn-outline-secondary:active, .o_colored_level .o_cc5 .btn-outline-secondary.active, .o_colored_level .o_cc5 .btn-outline-secondary.dropdown-toggle.show{color: #000000; background-color: #F3F2F2; border-color: #F3F2F2;}.btn-check:checked + .o_cc5 .btn-outline-secondary:focus, .btn-check:active + .o_cc5 .btn-outline-secondary:focus, .o_cc5 .btn-outline-secondary:active:focus, .o_cc5 .btn-outline-secondary.active:focus, .o_cc5 .btn-outline-secondary.dropdown-toggle.show:focus, .btn-check:checked + .o_colored_level .o_cc5 .btn-outline-secondary:focus, .btn-check:active + .o_colored_level .o_cc5 .btn-outline-secondary:focus, .o_colored_level .o_cc5 .btn-outline-secondary:active:focus, .o_colored_level .o_cc5 .btn-outline-secondary.active:focus, .o_colored_level .o_cc5 .btn-outline-secondary.dropdown-toggle.show:focus{box-shadow: 0 0 0 0.25rem rgba(243, 242, 242, 0.5);}.o_cc5 .btn-outline-secondary:disabled, .o_cc5 .btn-outline-secondary.o_wysiwyg_loader, .o_cc5 .btn-outline-secondary.disabled, .o_colored_level .o_cc5 .btn-outline-secondary:disabled, .o_colored_level .o_cc5 .btn-outline-secondary.disabled{color: #F3F2F2; background-color: transparent;}.o_cc5 .nav-pills .nav-link.active, .o_cc5 .nav-pills .show > .nav-link, .o_colored_level .o_cc5 .nav-pills .nav-link.active, .o_colored_level .o_cc5 .nav-pills .show > .nav-link{background-color: #714B67; color: #FFFFFF;}.o_cc5 .dropdown-menu .dropdown-item.active, .o_cc5 .dropdown-menu .dropdown-item.active h6, .o_cc5 .dropdown-menu .dropdown-item.active .h6, .o_cc5 .dropdown-menu .dropdown-item:active, .o_cc5 .dropdown-menu .dropdown-item:active h6, .o_cc5 .dropdown-menu .dropdown-item:active .h6, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item.active, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item.active h6, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item:active, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item:active h6{background-color: #714B67; color: #FFFFFF !important;}.o_cc5 .dropdown-menu .dropdown-item.active:hover, .o_cc5 .dropdown-menu .dropdown-item.active:focus, .o_cc5 .dropdown-menu .dropdown-item.active h6:hover, .o_cc5 .dropdown-menu .dropdown-item.active .h6:hover, .o_cc5 .dropdown-menu .dropdown-item.active h6:focus, .o_cc5 .dropdown-menu .dropdown-item.active .h6:focus, .o_cc5 .dropdown-menu .dropdown-item:active:hover, .o_cc5 .dropdown-menu .dropdown-item:active:focus, .o_cc5 .dropdown-menu .dropdown-item:active h6:hover, .o_cc5 .dropdown-menu .dropdown-item:active .h6:hover, .o_cc5 .dropdown-menu .dropdown-item:active h6:focus, .o_cc5 .dropdown-menu .dropdown-item:active .h6:focus, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item.active:hover, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item.active:focus, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item.active h6:hover, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item.active h6:focus, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item:active:hover, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item:active:focus, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item:active h6:hover, .o_colored_level .o_cc5 .dropdown-menu .dropdown-item:active h6:focus{color: #FFFFFF !important;}.o_cc5 a.list-group-item, .o_colored_level .o_cc5 a.list-group-item{color: #714B67;}.o_cc5 a.list-group-item.active, .o_colored_level .o_cc5 a.list-group-item.active{background-color: #714B67; color: #FFFFFF; border-color: #714B67;}.btn-custom:hover, .btn-fill-custom:hover{filter: invert(0.2);}.btn-outline-custom:not(:hover){background-color: transparent !important; background-image: none !important;}section, .oe_img_bg, [data-oe-shape-data], section > *, .oe_img_bg > *, [data-oe-shape-data] > *{position: relative;}.o_we_shape, .o_we_bg_filter{position: absolute; top: 0; left: 0; bottom: 0; right: 0; position: absolute !important; display: block; overflow: hidden; background-repeat: no-repeat; pointer-events: none;}.o_full_screen_height, .cover_full, .o_half_screen_height, .cover_mid{display: -webkit-box; display: -webkit-flex; display: flex; -webkit-box-orient: vertical; -webkit-box-direction: normal; -webkit-flex-direction: column; flex-direction: column; justify-content: space-around; min-height: 100vh !important;}.o_half_screen_height, .cover_mid{min-height: 55vh !important;}@media (min-width: 992px){.o_container_small{max-width: 720px;}}.oe_img_bg{background-size: cover; background-repeat: no-repeat;}.oe_img_bg.o_bg_img_opt_repeat{background-size: auto; background-repeat: repeat;}.oe_img_bg.o_bg_img_center{background-position: center;}.oe_img_bg.o_bg_img_origin_border_box{background-origin: border-box;}.text-gradient{-webkit-background-clip: text; -webkit-text-fill-color: transparent;}.text-gradient .o_animated_text, .text-gradient .o_animated_text *, .text-gradient.o_animated_text *, .text-gradient .o_text_highlight, .text-gradient .o_text_highlight *, .text-gradient.o_text_highlight *{background-image: inherit; -webkit-background-clip: inherit; -webkit-text-fill-color: inherit;}.text-gradient .fa{display: inherit;}.odoo-editor-editable.odoo-editor-qweb t, .odoo-editor-editable.odoo-editor-qweb [t-if], .odoo-editor-editable.odoo-editor-qweb [t-elif], .odoo-editor-editable.odoo-editor-qweb [t-else], .odoo-editor-editable.odoo-editor-qweb [t-foreach], .o_readonly t, .o_readonly [t-if], .o_readonly [t-elif], .o_readonly [t-else], .o_readonly [t-foreach]{background-color: rgba(0, 0, 102, 0.1) !important;}.odoo-editor-editable.odoo-editor-qweb t, .odoo-editor-editable.odoo-editor-qweb [t-esc], .odoo-editor-editable.odoo-editor-qweb [t-out], .odoo-editor-editable.odoo-editor-qweb [t-raw], .o_readonly t, .o_readonly [t-esc], .o_readonly [t-out], .o_readonly [t-raw]{border-radius: 2px;}.odoo-editor-editable.odoo-editor-qweb [t-esc], .odoo-editor-editable.odoo-editor-qweb [t-out], .odoo-editor-editable.odoo-editor-qweb [t-raw], .o_readonly [t-esc], .o_readonly [t-out], .o_readonly [t-raw]{background-color: rgba(36, 154, 255, 0.16) !important;}.odoo-editor-editable.odoo-editor-qweb [t-esc]:empty::before, .o_readonly [t-esc]:empty::before{content: attr(t-esc);}.odoo-editor-editable.odoo-editor-qweb [t-raw]:empty::before, .o_readonly [t-raw]:empty::before{content: attr(t-raw);}.odoo-editor-editable.odoo-editor-qweb [t-out]:empty::before, .o_readonly [t-out]:empty::before{content: attr(t-out);}.odoo-editor-editable.odoo-editor-qweb t[t-set], .o_readonly t[t-set]{display: none;}.odoo-editor-editable.odoo-editor-qweb t[data-oe-t-inline], .o_readonly t[data-oe-t-inline]{display: inline;}.odoo-editor-editable.odoo-editor-qweb t:not([data-oe-t-inline]), .o_readonly t:not([data-oe-t-inline]){display: block;}.odoo-editor-editable.odoo-editor-qweb t[data-oe-t-inline]:not([data-oe-t-group-active]), .o_readonly t[data-oe-t-inline]:not([data-oe-t-group-active]){display: unset;}.odoo-editor-editable.odoo-editor-qweb [data-oe-t-group]:not([data-oe-t-group-active]), .o_readonly [data-oe-t-group]:not([data-oe-t-group-active]){display: none !important;}.odoo-editor-editable.odoo-editor-qweb [data-oe-t-group][data-oe-t-selectable], .o_readonly [data-oe-t-group][data-oe-t-selectable]{outline: 1px dashed rgba(0, 0, 102, 0.4) !important;}.oe-qweb-select{position: absolute; z-index: 1056; background-color: white;}.o_we_shape.o_we_animated{will-change: transform;}.o_we_shape.o_web_editor_Airy_01{background-image: url("/web_editor/shape/web_editor/Airy/01.svg?c1=%23714B67"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_02{background-image: url("/web_editor/shape/web_editor/Airy/02.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_03{background-image: url("/web_editor/shape/web_editor/Airy/03.svg?c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_03_001{background-image: url("/web_editor/shape/web_editor/Airy/03_001.svg?c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_04{background-image: url("/web_editor/shape/web_editor/Airy/04.svg?c1=%23714B67"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_04_001{background-image: url("/web_editor/shape/web_editor/Airy/04_001.svg?c1=%23714B67"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_05{background-image: url("/web_editor/shape/web_editor/Airy/05.svg?c1=%23714B67"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_05_001{background-image: url("/web_editor/shape/web_editor/Airy/05_001.svg?c1=%23714B67"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_06{background-image: url("/web_editor/shape/web_editor/Airy/06.svg?c2=%238595A2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_07{background-image: url("/web_editor/shape/web_editor/Airy/07.svg?c2=%238595A2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_08{background-image: url("/web_editor/shape/web_editor/Airy/08.svg?c1=%23714B67"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_09{background-image: url("/web_editor/shape/web_editor/Airy/09.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_10{background-image: url("/web_editor/shape/web_editor/Airy/10.svg?c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_11{background-image: url("/web_editor/shape/web_editor/Airy/11.svg?c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_12{background-image: url("/web_editor/shape/web_editor/Airy/12.svg?c1=%23714B67&c3=%23F3F2F2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_12_001{background-image: url("/web_editor/shape/web_editor/Airy/12_001.svg?c1=%23714B67&c3=%23F3F2F2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_13{background-image: url("/web_editor/shape/web_editor/Airy/13.svg?c1=%23714B67&c4=%23FFFFFF"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_13_001{background-image: url("/web_editor/shape/web_editor/Airy/13_001.svg?c1=%23714B67&c4=%23FFFFFF"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Airy_14{background-image: url("/web_editor/shape/web_editor/Airy/14.svg?c1=%23714B67&c4=%23FFFFFF"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_01{background-image: url("/web_editor/shape/web_editor/Blobs/01.svg?c2=%238595A2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_01_001{background-image: url("/web_editor/shape/web_editor/Blobs/01_001.svg?c2=%238595A2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_02{background-image: url("/web_editor/shape/web_editor/Blobs/02.svg?c1=%23714B67&c2=%238595A2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_03{background-image: url("/web_editor/shape/web_editor/Blobs/03.svg?c2=%238595A2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_04{background-image: url("/web_editor/shape/web_editor/Blobs/04.svg?c5=%23111827"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_05{background-image: url("/web_editor/shape/web_editor/Blobs/05.svg?c1=%23714B67"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_06{background-image: url("/web_editor/shape/web_editor/Blobs/06.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_07{background-image: url("/web_editor/shape/web_editor/Blobs/07.svg?c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_08{background-image: url("/web_editor/shape/web_editor/Blobs/08.svg?c1=%23714B67"); background-position: right; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_09{background-image: url("/web_editor/shape/web_editor/Blobs/09.svg?c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_10{background-image: url("/web_editor/shape/web_editor/Blobs/10.svg?c1=%23714B67&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_10_001{background-image: url("/web_editor/shape/web_editor/Blobs/10_001.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_11{background-image: url("/web_editor/shape/web_editor/Blobs/11.svg?c1=%23714B67"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blobs_12{background-image: url("/web_editor/shape/web_editor/Blobs/12.svg?c1=%23714B67"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blocks_01{background-image: url("/web_editor/shape/web_editor/Blocks/01.svg?c1=%23714B67&c3=%23F3F2F2&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blocks_01_001{background-image: url("/web_editor/shape/web_editor/Blocks/01_001.svg?c1=%23714B67&c3=%23F3F2F2&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blocks_02{background-image: url("/web_editor/shape/web_editor/Blocks/02.svg?c1=%23714B67&c3=%23F3F2F2&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blocks_02_001{background-image: url("/web_editor/shape/web_editor/Blocks/02_001.svg?c1=%23714B67&c3=%23F3F2F2&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blocks_03{background-image: url("/web_editor/shape/web_editor/Blocks/03.svg?c1=%23714B67&c4=%23FFFFFF"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Blocks_04{background-image: url("/web_editor/shape/web_editor/Blocks/04.svg?c1=%23714B67&c2=%238595A2&c3=%23F3F2F2&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_01{background-image: url("/web_editor/shape/web_editor/Bold/01.svg?c2=%238595A2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_02{background-image: url("/web_editor/shape/web_editor/Bold/02.svg?c1=%23714B67&c2=%238595A2&c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_03{background-image: url("/web_editor/shape/web_editor/Bold/03.svg?c1=%23714B67&c3=%23F3F2F2&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_04{background-image: url("/web_editor/shape/web_editor/Bold/04.svg?c2=%238595A2&c3=%23F3F2F2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_05{background-image: url("/web_editor/shape/web_editor/Bold/05.svg?c5=%23111827"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_05_001{background-image: url("/web_editor/shape/web_editor/Bold/05_001.svg?c3=%23F3F2F2"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_06{background-image: url("/web_editor/shape/web_editor/Bold/06.svg?c5=%23111827"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_06_001{background-image: url("/web_editor/shape/web_editor/Bold/06_001.svg?c3=%23F3F2F2"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_07{background-image: url("/web_editor/shape/web_editor/Bold/07.svg?c1=%23714B67&c2=%238595A2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_07_001{background-image: url("/web_editor/shape/web_editor/Bold/07_001.svg?c1=%23714B67&c2=%238595A2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_08{background-image: url("/web_editor/shape/web_editor/Bold/08.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_09{background-image: url("/web_editor/shape/web_editor/Bold/09.svg?c2=%238595A2&c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_10{background-image: url("/web_editor/shape/web_editor/Bold/10.svg?c1=%23714B67&c3=%23F3F2F2&c4=%23FFFFFF&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_10_001{background-image: url("/web_editor/shape/web_editor/Bold/10_001.svg?c1=%23714B67&c4=%23FFFFFF&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_11{background-image: url("/web_editor/shape/web_editor/Bold/11.svg?c1=%23714B67&c2=%238595A2&c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_11_001{background-image: url("/web_editor/shape/web_editor/Bold/11_001.svg?c1=%23714B67&c2=%238595A2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_12{background-image: url("/web_editor/shape/web_editor/Bold/12.svg?c1=%23714B67&c2=%238595A2&c5=%23111827"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Bold_12_001{background-image: url("/web_editor/shape/web_editor/Bold/12_001.svg?c1=%23714B67&c2=%238595A2&c5=%23111827"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_01{background-image: url("/web_editor/shape/web_editor/Floats/01.svg?c1=%23714B67&c2=%238595A2&c3=%23F3F2F2&c4=%23FFFFFF&c5=%23111827"); background-position: center right; background-size: auto 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_02{background-image: url("/web_editor/shape/web_editor/Floats/02.svg?c1=%23714B67&c2=%238595A2&c3=%23F3F2F2&c5=%23111827"); background-position: center; background-size: 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_03{background-image: url("/web_editor/shape/web_editor/Floats/03.svg?c1=%23714B67&c2=%238595A2&c3=%23F3F2F2&c5=%23111827"); background-position: center; background-size: 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_04{background-image: url("/web_editor/shape/web_editor/Floats/04.svg?c1=%23714B67&c2=%238595A2&c4=%23FFFFFF&c5=%23111827"); background-position: center; background-size: 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_05{background-image: url("/web_editor/shape/web_editor/Floats/05.svg?c1=%23714B67&c2=%238595A2&c3=%23F3F2F2&c5=%23111827"); background-position: center; background-size: 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_06{background-image: url("/web_editor/shape/web_editor/Floats/06.svg?c1=%23714B67&c2=%238595A2&c3=%23F3F2F2&c5=%23111827"); background-position: center; background-size: auto 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_07{background-image: url("/web_editor/shape/web_editor/Floats/07.svg?c1=%23714B67&c2=%238595A2&c3=%23F3F2F2&c5=%23111827"); background-position: right bottom; background-size: auto 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_08{background-image: url("/web_editor/shape/web_editor/Floats/08.svg?c1=%23714B67&c2=%238595A2&c3=%23F3F2F2&c5=%23111827"); background-position: top left; background-size: auto 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_09{background-image: url("/web_editor/shape/web_editor/Floats/09.svg?c1=%23714B67&c2=%238595A2&c3=%23F3F2F2"); background-position: center right; background-size: auto 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_10{background-image: url("/web_editor/shape/web_editor/Floats/10.svg?c1=%23714B67&c2=%238595A2&c3=%23F3F2F2&c5=%23111827"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_11{background-image: url("/web_editor/shape/web_editor/Floats/11.svg?c1=%23714B67&c3=%23F3F2F2"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_12{background-image: url("/web_editor/shape/web_editor/Floats/12.svg?c1=%23714B67&c2=%238595A2&c3=%23F3F2F2&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat repeat;}.o_we_shape.o_web_editor_Floats_13{background-image: url("/web_editor/shape/web_editor/Floats/13.svg?c1=%23714B67&c2=%238595A2&c5=%23111827"); background-position: center; background-size: auto 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Floats_14{background-image: url("/web_editor/shape/web_editor/Floats/14.svg?c1=%23714B67&c2=%238595A2&c3=%23F3F2F2&c5=%23111827"); background-position: center; background-size: 100%; background-repeat: no-repeat repeat;}.o_we_shape.o_web_editor_Origins_01{background-image: url("/web_editor/shape/web_editor/Origins/01.svg?c2=%238595A2&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_02{background-image: url("/web_editor/shape/web_editor/Origins/02.svg?c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_02_001{background-image: url("/web_editor/shape/web_editor/Origins/02_001.svg?c4=%23FFFFFF&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_03{background-image: url("/web_editor/shape/web_editor/Origins/03.svg?c3=%23F3F2F2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_04{background-image: url("/web_editor/shape/web_editor/Origins/04.svg?c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_04_001{background-image: url("/web_editor/shape/web_editor/Origins/04_001.svg?c3=%23F3F2F2"); background-position: top; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_05{background-image: url("/web_editor/shape/web_editor/Origins/05.svg?c3=%23F3F2F2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_06{background-image: url("/web_editor/shape/web_editor/Origins/06.svg?c3=%23F3F2F2"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_06_001{background-image: url("/web_editor/shape/web_editor/Origins/06_001.svg?c3=%23F3F2F2&c4=%23FFFFFF"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_07{background-image: url("/web_editor/shape/web_editor/Origins/07.svg?c3=%23F3F2F2"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_07_001{background-image: url("/web_editor/shape/web_editor/Origins/07_001.svg?c3=%23F3F2F2&c5=%23111827"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_07_002{background-image: url("/web_editor/shape/web_editor/Origins/07_002.svg?c3=%23F3F2F2&c4=%23FFFFFF&c5=%23111827"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_08{background-image: url("/web_editor/shape/web_editor/Origins/08.svg?c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_09{background-image: url("/web_editor/shape/web_editor/Origins/09.svg?c1=%23714B67&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_09_001{background-image: url("/web_editor/shape/web_editor/Origins/09_001.svg?c3=%23F3F2F2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_10{background-image: url("/web_editor/shape/web_editor/Origins/10.svg?c2=%238595A2&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_11{background-image: url("/web_editor/shape/web_editor/Origins/11.svg?c3=%23F3F2F2&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_11_001{background-image: url("/web_editor/shape/web_editor/Origins/11_001.svg?c3=%23F3F2F2&c4=%23FFFFFF"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_12{background-image: url("/web_editor/shape/web_editor/Origins/12.svg?c3=%23F3F2F2&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_13{background-image: url("/web_editor/shape/web_editor/Origins/13.svg?c3=%23F3F2F2&c5=%23111827"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_14{background-image: url("/web_editor/shape/web_editor/Origins/14.svg?c4=%23FFFFFF"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_14_001{background-image: url("/web_editor/shape/web_editor/Origins/14_001.svg?c3=%23F3F2F2&c4=%23FFFFFF"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_15{background-image: url("/web_editor/shape/web_editor/Origins/15.svg?c4=%23FFFFFF"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_16{background-image: url("/web_editor/shape/web_editor/Origins/16.svg?c3=%23F3F2F2"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_17{background-image: url("/web_editor/shape/web_editor/Origins/17.svg?c3=%23F3F2F2"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Origins_18{background-image: url("/web_editor/shape/web_editor/Origins/18.svg?c1=%23714B67"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_01{background-image: url("/web_editor/shape/web_editor/Rainy/01.svg?c1=%23714B67&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_01_001{background-image: url("/web_editor/shape/web_editor/Rainy/01_001.svg?c1=%23714B67&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_02{background-image: url("/web_editor/shape/web_editor/Rainy/02.svg?c1=%23714B67&c4=%23FFFFFF&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_02_001{background-image: url("/web_editor/shape/web_editor/Rainy/02_001.svg?c1=%23714B67&c4=%23FFFFFF&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_03{background-image: url("/web_editor/shape/web_editor/Rainy/03.svg?c2=%238595A2&c4=%23FFFFFF&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat repeat;}.o_we_shape.o_web_editor_Rainy_03_001{background-image: url("/web_editor/shape/web_editor/Rainy/03_001.svg?c2=%238595A2&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat repeat;}.o_we_shape.o_web_editor_Rainy_04{background-image: url("/web_editor/shape/web_editor/Rainy/04.svg?c1=%23714B67&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_05{background-image: url("/web_editor/shape/web_editor/Rainy/05.svg?c1=%23714B67&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_05_001{background-image: url("/web_editor/shape/web_editor/Rainy/05_001.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_06{background-image: url("/web_editor/shape/web_editor/Rainy/06.svg?c1=%23714B67&c2=%238595A2&c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_07{background-image: url("/web_editor/shape/web_editor/Rainy/07.svg?c1=%23714B67&c2=%238595A2&c3=%23F3F2F2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_08{background-image: url("/web_editor/shape/web_editor/Rainy/08.svg?c1=%23714B67&c4=%23FFFFFF"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_08_001{background-image: url("/web_editor/shape/web_editor/Rainy/08_001.svg?c1=%23714B67&c4=%23FFFFFF"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_09{background-image: url("/web_editor/shape/web_editor/Rainy/09.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_09_001{background-image: url("/web_editor/shape/web_editor/Rainy/09_001.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Rainy_10{background-image: url("/web_editor/shape/web_editor/Rainy/10.svg?c1=%23714B67&c3=%23F3F2F2"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_01{background-image: url("/web_editor/shape/web_editor/Wavy/01.svg?c4=%23FFFFFF"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_01_001{background-image: url("/web_editor/shape/web_editor/Wavy/01_001.svg?c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_02{background-image: url("/web_editor/shape/web_editor/Wavy/02.svg?c4=%23FFFFFF"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_02_001{background-image: url("/web_editor/shape/web_editor/Wavy/02_001.svg?c3=%23F3F2F2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_03{background-image: url("/web_editor/shape/web_editor/Wavy/03.svg?c1=%23714B67&c2=%238595A2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_04{background-image: url("/web_editor/shape/web_editor/Wavy/04.svg?c1=%23714B67&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_05{background-image: url("/web_editor/shape/web_editor/Wavy/05.svg?c1=%23714B67&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_06{background-image: url("/web_editor/shape/web_editor/Wavy/06.svg?c1=%23714B67&c3=%23F3F2F2&c4=%23FFFFFF&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_06_001{background-image: url("/web_editor/shape/web_editor/Wavy/06_001.svg?c1=%23714B67&c3=%23F3F2F2&c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_07{background-image: url("/web_editor/shape/web_editor/Wavy/07.svg?c3=%23F3F2F2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_08{background-image: url("/web_editor/shape/web_editor/Wavy/08.svg?c2=%238595A2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_09{background-image: url("/web_editor/shape/web_editor/Wavy/09.svg?c1=%23714B67&c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_10{background-image: url("/web_editor/shape/web_editor/Wavy/10.svg?c1=%23714B67&c2=%238595A2"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_11{background-image: url("/web_editor/shape/web_editor/Wavy/11.svg?c1=%23714B67&c4=%23FFFFFF"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_12{background-image: url("/web_editor/shape/web_editor/Wavy/12.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_12_001{background-image: url("/web_editor/shape/web_editor/Wavy/12_001.svg?c1=%23714B67"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_13{background-image: url("/web_editor/shape/web_editor/Wavy/13.svg?c4=%23FFFFFF"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_13_001{background-image: url("/web_editor/shape/web_editor/Wavy/13_001.svg?c1=%23714B67"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_14{background-image: url("/web_editor/shape/web_editor/Wavy/14.svg?c1=%23714B67&c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_15{background-image: url("/web_editor/shape/web_editor/Wavy/15.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_16{background-image: url("/web_editor/shape/web_editor/Wavy/16.svg?c1=%23714B67"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_17{background-image: url("/web_editor/shape/web_editor/Wavy/17.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_18{background-image: url("/web_editor/shape/web_editor/Wavy/18.svg?c5=%23111827"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_19{background-image: url("/web_editor/shape/web_editor/Wavy/19.svg?c5=%23111827"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_20{background-image: url("/web_editor/shape/web_editor/Wavy/20.svg?c2=%238595A2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_21{background-image: url("/web_editor/shape/web_editor/Wavy/21.svg?c2=%238595A2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_22{background-image: url("/web_editor/shape/web_editor/Wavy/22.svg?c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_23{background-image: url("/web_editor/shape/web_editor/Wavy/23.svg?c3=%23F3F2F2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_24{background-image: url("/web_editor/shape/web_editor/Wavy/24.svg?c1=%23714B67&c2=%238595A2"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_25{background-image: url("/web_editor/shape/web_editor/Wavy/25.svg?c1=%23714B67&c2=%238595A2"); background-position: top; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_26{background-image: url("/web_editor/shape/web_editor/Wavy/26.svg?c1=%23714B67&c2=%238595A2"); background-position: bottom right; background-size: auto 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_27{background-image: url("/web_editor/shape/web_editor/Wavy/27.svg?c1=%23714B67&c2=%238595A2"); background-position: center; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Wavy_28{background-image: url("/web_editor/shape/web_editor/Wavy/28.svg?c1=%23714B67&c3=%23F3F2F2"); background-position: center; background-size: 100% 100%; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Zigs_01{background-image: url("/web_editor/shape/web_editor/Zigs/01.svg?c2=%238595A2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Zigs_01_001{background-image: url("/web_editor/shape/web_editor/Zigs/01_001.svg?c2=%238595A2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Zigs_02{background-image: url("/web_editor/shape/web_editor/Zigs/02.svg?c2=%238595A2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Zigs_02_001{background-image: url("/web_editor/shape/web_editor/Zigs/02_001.svg?c2=%238595A2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Zigs_03{background-image: url("/web_editor/shape/web_editor/Zigs/03.svg?c1=%23714B67"); background-position: top; background-size: 100% auto; background-repeat: no-repeat repeat;}.o_we_shape.o_web_editor_Zigs_04{background-image: url("/web_editor/shape/web_editor/Zigs/04.svg?c1=%23714B67"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Zigs_05{background-image: url("/web_editor/shape/web_editor/Zigs/05.svg?c3=%23F3F2F2"); background-position: bottom; background-size: 100% auto; background-repeat: no-repeat no-repeat;}.o_we_shape.o_web_editor_Zigs_06{background-image: url("/web_editor/shape/web_editor/Zigs/06.svg?c4=%23FFFFFF&c5=%23111827"); background-position: bottom; background-size: 30px 100%; background-repeat: repeat no-repeat;}.ui-autocomplete{max-height: 45vh; overflow-y: auto; overflow-x: hidden;}.ui-autocomplete .ui-menu-item{padding: 0;}.ui-autocomplete .ui-menu-item > .ui-state-active{border: none; font-weight: normal; margin: 0;}.ui-autocomplete .fw-bold{font-weight: 700 !important;}.o_editor_banner p, .o_editor_banner h1, .o_editor_banner .h1, .o_editor_banner h2, .o_editor_banner .h2, .o_editor_banner h3, .o_editor_banner .h3, .o_editor_banner ul, .o_editor_banner ol{margin-bottom: 1rem;}.o_editor_banner ol ol, .o_editor_banner ul ul, .o_editor_banner ol ul, .o_editor_banner ul ol{margin-bottom: 0;}.o_editor_banner ul.o_checklist > li:not(.oe-nested)::before{top: 0px !important;}

/* /web_editor/static/src/scss/web_editor.frontend.scss */
 @media (max-width: 767.98px){img:not(.o_animate), .media_iframe_video:not(.o_animate){transform: none !important;}.fa:where(:not(.fa-align-right):not(.fa-align-left):not(.fa-chevron-right):not(.fa-chevron-left):not(.fa-arrow-right):not(.fa-arrow-left):not(.fa-hand-o-right):not(.fa-hand-o-left):not(.fa-arrow-circle-right):not(.fa-arrow-circle-left):not(.fa-caret-right):not(.fa-caret-left):not(.fa-rotate-right):not(.fa-rotate-left):not(.fa-angle-double-right):not(.fa-angle-double-left):not(.fa-angle-right):not(.fa-angle-left):not(.fa-quote-right):not(.fa-quote-left):not(.fa-chevron-circle-right):not(.fa-chevron-circle-left):not(.fa-long-arrow-right):not(.fa-long-arrow-left):not(.fa-toggle-right):not(.fa-toggle-left):not(.fa-caret-square-o-right):not(.fa-arrow-circle-o-left):not(.fa-arrow-circle-o-right):not(.fa-caret-square-o-left):not(.o_animate)){transform: none !important;}}.o_wysiwyg_loader{pointer-events: none; min-height: 100px; color: transparent;}.o_wysiwyg_loading{position: absolute; top: 50%; left: 50%; bottom: auto; right: auto; transform: translate(-50%, -50%);}@media (max-width: 767.98px){.o_we_shape:not(.o_shape_show_mobile){display: none;}}.o_we_flip_x{transform: scaleX(-1);}.o_we_flip_y{transform: scaleY(-1);}.o_we_flip_x.o_we_flip_y{transform: scale(-1);}.o_grid_mode{--grid-item-padding-y: 10px; --grid-item-padding-x: 10px;}@media (max-width: 991.98px){.o_grid_mode{row-gap: 0px !important; column-gap: 0px !important;}}.o_grid_mode > *{padding: var(--grid-item-padding-y) var(--grid-item-padding-x) !important;}@media (max-width: 991.98px){.o_grid_mode > *{padding: var(--grid-item-padding-y) calc(0.5 * var(--gutter-x)) !important;}.o_grid_mode > *.o_grid_item_image{--mobile-grid-item-padding-x: clamp(0px, calc(var(--grid-item-padding-x) * 100000), calc(0.5 * var(--gutter-x))); padding: var(--grid-item-padding-y) var(--mobile-grid-item-padding-x) !important;}.o_grid_mode > *.o_grid_item_image > img{max-width: 100%;}}@media (min-width: 992px){.o_grid_mode{display: grid !important; grid-auto-rows: 50px; grid-template-columns: repeat(12, 1fr); row-gap: 0px; column-gap: 0px; --gutter-x: 0px;}.o_grid_mode .o_grid_item > .row, .o_grid_mode .o_grid_item > .o_text_columns > .row{--grid-inner-row-gutter-x: clamp(0px, 2 * var(--grid-item-padding-x), 1.5rem); margin-left: calc(-0.5 * var(--grid-inner-row-gutter-x)); margin-right: calc(-0.5 * var(--grid-inner-row-gutter-x));}.o_grid_mode > *{margin: 0 !important; width: 100%; min-width: 0;}.container-fluid > .o_grid_mode, .container-sm > .o_grid_mode, .container-md > .o_grid_mode, .container-lg > .o_grid_mode, .container-xl > .o_grid_mode, .container-xxl > .o_grid_mode{--gutter-x: 1.5rem;}.o_extra_menu_items .o_grid_mode{display: -webkit-box !important; display: -webkit-flex !important; display: flex !important; row-gap: 0px !important; column-gap: 0px !important;}.o_grid_item_image img, .o_grid_item_image .media_iframe_video{width: 100% !important; height: 100% !important; object-fit: cover !important;}.o_grid_item_image.o_grid_item_image_contain img, .o_grid_item_image img[data-shape]{object-fit: contain !important;}.o_grid_item_image:not(.o_grid_item_image_contain) img[data-shape$="geo_square"]:not( [data-hover-effect="dolly_zoom"], [data-hover-effect="outline"], [data-hover-effect="image_mirror_blur"]){object-fit: cover !important;}.o_grid_item_image > a{width: 100%; height: 100%;}}body.editor_enable:not(.o_basic_theme) .odoo-editor-editable img::selection{background-color: transparent !important;}

/* /portal/static/src/scss/portal.scss */
 body{direction: ltr;}header .navbar-brand{-webkit-box-flex: 0; -webkit-flex: 0 0 auto; flex: 0 0 auto; max-width: 75%;}header .navbar-brand.logo{padding-top: 0; padding-bottom: 0;}header .navbar-brand.logo img{object-fit: contain; display: block; width: auto; height: 2.3125rem;}@media (max-width: 767.98px){header .navbar-brand.logo img{max-height: 2.3125rem;}}header .nav-link{white-space: nowrap;}.navbar{margin-bottom: 0;}@media (max-width: 767.98px){.navbar .nav.navbar-nav.float-end{float: none !important;}}@media (min-width: 768px){.navbar-expand-md ul.nav > li.divider{display: list-item;}}ul.flex-column > li > a{padding: 2px 15px;}a.fa:hover, .btn-link.fa:hover{text-decoration: none;}.jumbotron{margin-bottom: 0;}li > p{margin: 0;}.container-fluid .container-fluid, .container-sm .container-fluid, .container-md .container-fluid, .container-lg .container-fluid, .container-xl .container-fluid, .container-xxl .container-fluid, .container-fluid .container-sm, .container-sm .container-sm, .container-md .container-sm, .container-lg .container-sm, .container-xl .container-sm, .container-xxl .container-sm, .container-fluid .container-md, .container-sm .container-md, .container-md .container-md, .container-lg .container-md, .container-xl .container-md, .container-xxl .container-md, .container-fluid .container-lg, .container-sm .container-lg, .container-md .container-lg, .container-lg .container-lg, .container-xl .container-lg, .container-xxl .container-lg, .container-fluid .container-xl, .container-sm .container-xl, .container-md .container-xl, .container-lg .container-xl, .container-xl .container-xl, .container-xxl .container-xl, .container-fluid .container-xxl, .container-sm .container-xxl, .container-md .container-xxl, .container-lg .container-xxl, .container-xl .container-xxl, .container-xxl .container-xxl, .container .container, .o_container_small .container, .container .o_container_small, .o_container_small .o_container_small, .container .container-fluid, .o_container_small .container-fluid, .container .container-sm, .o_container_small .container-sm, .container .container-md, .o_container_small .container-md, .container .container-lg, .o_container_small .container-lg, .container .container-xl, .o_container_small .container-xl, .container .container-xxl, .o_container_small .container-xxl{padding-right: 0; padding-left: 0;}#wrap .container::before, #wrap .o_container_small::before, #wrap .container::after, #wrap .o_container_small::after, #wrap .container-fluid::before, #wrap .container-sm::before, #wrap .container-md::before, #wrap .container-lg::before, #wrap .container-xl::before, #wrap .container-xxl::before, #wrap .container-fluid::after, #wrap .container-sm::after, #wrap .container-md::after, #wrap .container-lg::after, #wrap .container-xl::after, #wrap .container-xxl::after{content: ""; display: table; clear: both;}#wrap .navbar > .container::before, #wrap .navbar > .container-fluid::before, #wrap .navbar > .container-sm::before, #wrap .navbar > .container-md::before, #wrap .navbar > .container-lg::before, #wrap .navbar > .container-xl::before, #wrap .navbar > .container-xxl::before, #wrap .navbar > .o_container_small::before, #wrap .navbar > .container::after, #wrap .navbar > .container-fluid::after, #wrap .navbar > .container-sm::after, #wrap .navbar > .container-md::after, #wrap .navbar > .container-lg::after, #wrap .navbar > .container-xl::after, #wrap .navbar > .container-xxl::after, #wrap .navbar > .o_container_small::after{display: none;}[class^="col-lg-"]{min-height: 24px;}.input-group:where(:not(:has(.was-validated :invalid ~ .invalid-feedback, .is-invalid ~ .invalid-feedback))){-webkit-flex-flow: row nowrap; flex-flow: row nowrap;}.list-group-item:not([class*="list-group-item-"]):not(.active){color: #000000;}.o_portal_wrap .o_portal_navbar .breadcrumb, .o_portal .breadcrumb{background-color: inherit;}.o_page_header{margin-bottom: 1rem; padding-bottom: 0.5rem; border-bottom-width: 1px; border-bottom-style: solid; border-bottom-color: #dee2e6;}img.float-end, .media_iframe_video.float-end, .o_image.float-end{margin-left: 0.75rem;}img.float-start, .media_iframe_video.float-start, .o_image.float-start{margin-right: 0.75rem;}::-moz-selection{background: rgba(150, 150, 220, 0.3);}::selection{background: rgba(150, 150, 220, 0.3);}.oe_search_box{padding-right: 23px; text-overflow: ellipsis;}.para_large{font-size: 120%;}.jumbotron .para_large p{font-size: 150%;}.readable{font-size: 120%; max-width: 700px; margin-left: auto; margin-right: auto;}.readable .container, .readable .o_container_small{padding-left: 0; padding-right: 0; width: auto;}.oe_dark{background-color: rgba(200, 200, 200, 0.14);}.oe_black{background-color: rgba(0, 0, 0, 0.9); color: white;}.oe_green{background-color: #169C78; color: white;}.oe_green .text-muted{color: #ddd !important;}.oe_blue_light{background-color: #41b6ab; color: white;}.oe_blue_light .text-muted{color: #ddd !important;}.oe_blue{background-color: #34495e; color: white;}.oe_orange{background-color: #f05442; color: white;}.oe_orange .text-muted{color: #ddd !important;}.oe_purple{background-color: #b163a3; color: white;}.oe_purple .text-muted{color: #ddd !important;}.oe_red{background-color: #9C1b31; color: white;}.oe_red .text-muted{color: #ddd !important;}.oe_none{background-color: #FFFFFF;}.oe_yellow{background-color: #A2A51B;}.oe_green{background-color: #149F2C;}.o_portal > tbody.o_portal_report_tbody{vertical-align: middle;}.o_portal_wrap .o_portal_my_home > .o_page_header > a:hover{text-decoration: none;}.o_portal_wrap .o_portal_navbar .breadcrumb{padding-left: 0; padding-right: 0;}.o_portal_wrap .o_portal_my_doc_table th{padding-top: 0.25rem; padding-bottom: 0.25rem; max-width: 500px;}.o_portal_wrap .o_portal_my_doc_table td{padding-top: 0.5rem; padding-bottom: 0.5rem; max-width: 10rem;}.o_portal_wrap .o_portal_my_doc_table td, .o_portal_wrap .o_portal_my_doc_table th{vertical-align: middle; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;}.o_portal_wrap .o_portal_my_doc_table td:has(a), .o_portal_wrap .o_portal_my_doc_table th:has(a){color: #714B67;}.o_portal_wrap .o_my_sidebar div[itemprop="address"] > div{margin-top: 0.5em;}@media (max-width: 991.98px){.o_portal_wrap #o_portal_navbar_content{margin: 0.5rem 0 0; padding: 0.5rem; border-top: 1px solid #dee2e6; background-color: #E9ECEF;}}.o_portal_wrap table.table tr{word-wrap: break-word;}.o_portal_address span[itemprop="name"]{margin-bottom: 0.3em;}.o_portal_address div[itemprop="address"] > div{position: relative;}.o_portal_address div[itemprop="address"] > div span[itemprop="streetAddress"]{line-height: 1.2; margin-bottom: 0.3em;}.o_portal_address div[itemprop="address"] > div .fa{line-height: 1.5; color: #adb5bd;}.o_portal_address div[itemprop="address"] > div .fa + span, .o_portal_address div[itemprop="address"] > div .fa + div{display: block;}.oe_attachments .o_image_small{height: 40px; width: 50px; background-repeat: no-repeat;}.o_portal_sidebar .o_portal_html_view{overflow: hidden; background: white; position: relative;}.o_portal_sidebar .o_portal_html_view .o_portal_html_loader{position: absolute; top: 45%; left: 0; bottom: auto; right: 0;}.o_portal_sidebar .o_portal_html_view iframe{position: relative;}@media (min-width: 992px){.o_portal_sidebar .o_portal_sidebar_content{position: -webkit-sticky; position: sticky; top: 5rem; left: auto; bottom: auto; right: auto; z-index: 1;}}.o_portal_sidebar .o_portal_sidebar_content span.oe_currency_value{word-break: break-word !important; white-space: normal !important;}.o_portal_chatter{padding: 10px;}.o_portal_chatter .o_portal_chatter_avatar{--Avatar-size: 45px;}.o_portal_chatter .o_portal_chatter_header{margin-bottom: 15px;}.o_portal_chatter .o_portal_chatter_composer{margin-bottom: 15px;}.o_portal_chatter .o_portal_chatter_composer_body textarea{border: 0;}.o_portal_chatter .o_portal_chatter_composer_body > div{border: 1px solid var(--o-border-color);}.o_portal_chatter .o_portal_chatter_messages{margin-bottom: 15px; overflow-wrap: break-word; word-break: break-word;}.o_portal_chatter .o_portal_chatter_messages .o_portal_chatter_message div.flex-grow-1 > p:not(.o_portal_chatter_puslished_date):last-of-type{margin-bottom: 5px;}.o_portal_chatter .o_portal_chatter_messages .o_portal_chatter_message_title p{font-size: 85%; color: #6C757D; margin: 0px;}.o_portal_chatter .o_portal_chatter_pager{text-align: center;}.o_portal_chatter .o_portal_chatter_attachment .o_portal_chatter_attachment_name, .o_portal_chatter_composer .o_portal_chatter_attachment .o_portal_chatter_attachment_name{max-width: 200px;}.o_portal_chatter .o_portal_chatter_attachment .o_portal_chatter_attachment_delete, .o_portal_chatter_composer .o_portal_chatter_attachment .o_portal_chatter_attachment_delete{position: absolute; top: 0; left: auto; bottom: auto; right: 0; opacity: 0;}.o_portal_chatter .o_portal_chatter_attachment:hover .o_portal_chatter_attachment_delete, .o_portal_chatter_composer .o_portal_chatter_attachment:hover .o_portal_chatter_attachment_delete{opacity: 1;}.o_portal_chatter .o_portal_message_internal_off .o_portal_chatter_visibility_on, .o_portal_chatter_composer .o_portal_message_internal_off .o_portal_chatter_visibility_on{display: none;}.o_portal_chatter .o_portal_message_internal_on .o_portal_chatter_visibility_off, .o_portal_chatter_composer .o_portal_message_internal_on .o_portal_chatter_visibility_off{display: none;}.o_portal_security_body section{margin-top: 3rem; border-top: 1px solid #dee2e6; padding-top: 1.5rem;}.o_portal_security_body section form.oe_reset_password_form{max-width: initial; margin: initial;}.o_portal_security_body section label, .o_portal_security_body section button{white-space: nowrap;}.o_portal_security_body section[name="portal_deactivate_account"] label{white-space: normal !important;}.o_footer_copyright .o_footer_copyright_name{vertical-align: middle;}.o_footer_copyright .js_language_selector{display: inline-block;}@media (min-width: 768px){.o_footer_copyright .row{display: -webkit-box; display: -webkit-flex; display: flex;}.o_footer_copyright .row > div{margin: auto 0;}}

/* /payment/static/src/scss/payment_form.scss */
 .o_payment_form .o_outline:hover{border-color: #714B67;}.o_payment_form .o_outline:not(:first-child):hover{box-shadow: 0 -1px 0 #714B67;}.o_payment_form .o_outline .o_payment_option_label:before{position: absolute; inset: 0; content: ''; cursor: pointer;}

/* /payment/static/src/scss/payment_provider.scss */
 .o_form_view .o_payment_provider_desc{margin-top: 10px;}.o_form_view .o_payment_provider_desc ul{list-style-type: none; padding: 0;}.o_form_view .o_payment_provider_desc ul i.fa{margin-right: 5px;}.o_form_view .o_payment_provider_desc ul i.fa.fa-check{color: green;}.o_form_view .o_warning_text{color: #f0ad4e;}

/* /payment/static/src/scss/portal_templates.scss */
 div[name="o_payment_status_alert"] div > p{margin-bottom: 0;}@media (min-width: 768px){.o_payment_summary_separator{border-left: 1px solid #dee2e6;}}

/* /auth_totp_portal/static/src/scss/auth_totp_portal.scss */
 .o_auth_totp_enable_2FA .o_field_copy{height: 24px; position: relative; width: 100% !important; border-radius: 5px; border: 1px solid #714B67; font-size: 0.75rem; text-transform: uppercase; color: #71639e; font-weight: 700; text-align: center; padding-right: 6rem; word-break: break-word;}.o_auth_totp_enable_2FA .o_field_copy .o_clipboard_button{position: absolute; top: 0; left: auto; bottom: auto; right: 0;}.o_auth_totp_enable_2FA .o_field_copy .o_clipboard_button.o_btn_text_copy{position: absolute; top: 0; right: 0;}.o_auth_totp_enable_2FA .o_field_copy .o_clipboard_button.o_btn_char_copy{height: 100%;}.o_auth_totp_enable_2FA .o_field_copy.o_field_copy_url{cursor: pointer; padding-left: 3px;}