<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    <t t-name="mail.MessageInReply">
        <t t-set="message" t-value="props.message"/>
        <div class="o-mail-MessageInReply" t-att-class="{
                'me-5': store.discuss.isActive or (env.inChatWindow and !props.alignedRight),
                'd-flex justify-content-end ms-5': env.inChatWindow and props.alignedRight,
            }">
            <small class="position-relative d-block text-small mb-1" t-attf-class="{{ env.inChatWindow and props.alignedRight ? 'justify-content-end pe-5': 'ps-5' }}">
                <span class="o-mail-MessageInReply-corner position-absolute bottom-0 top-50 pe-4 border-top text-300" t-attf-class="{{ env.inChatWindow and props.alignedRight ? 'o-isRightAlign border-end' : 'o-isLeftAlign border-start' }}" t-att-class="{ 'ms-n2': store.discuss.isActive }"/>
                <span t-if="!message.parentMessage.isEmpty" class="d-inline-flex align-items-center text-muted opacity-75" t-att-class="{ 'cursor-pointer opacity-100-hover': props.onClick }" t-on-click="() => this.props.onClick?.()">
                    <img class="o-mail-MessageInReply-avatar me-2 rounded" t-att-src="authorAvatarUrl" t-att-title="message.parentMessage.author?.name ?? message.parentMessage.email_from" alt="Avatar"/>
                    <span class="o-mail-MessageInReply-content overflow-hidden">
                        <b>@<t t-out="message.parentMessage.author?.name ?? message.parentMessage.email_from"/></b>:
                        <br t-if="env.inChatWindow and !props.alignedRight"/>
                        <span class="o-mail-MessageInReply-message ms-1 text-break">
                            <t t-if="!message.parentMessage.isBodyEmpty">
                                <t t-out="message.parentMessage.body"/>
                            </t>
                            <t t-elif="message.parentMessage.attachments.length > 0">
                                <span class="me-2 fst-italic">Click to see the attachments</span>
                                <i class="fa fa-image"/>
                            </t>
                        </span>
                    </span>
                </span>
                <i t-else="" class="text-muted ms-2">Original message was deleted</i>
            </small>
        </div>
    </t>
</templates>
