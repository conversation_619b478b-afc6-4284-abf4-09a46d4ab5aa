# Generated by Django 4.2.21 on 2025-07-15 03:13

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='ResCompany',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('legal_name', models.CharField(blank=True, max_length=200)),
                ('street', models.CharField(blank=True, max_length=128)),
                ('street2', models.CharField(blank=True, max_length=128)),
                ('city', models.Char<PERSON>ield(blank=True, max_length=128)),
                ('state_id', models.CharField(blank=True, max_length=128)),
                ('zip', models.CharField(blank=True, max_length=24)),
                ('country_id', models.<PERSON>r<PERSON><PERSON>(default='PK', max_length=2)),
                ('phone', models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=32)),
                ('email', models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=240)),
                ('website', models.CharField(blank=True, max_length=64)),
                ('vat', models.CharField(blank=True, help_text='Tax ID', max_length=32)),
                ('company_registry', models.CharField(blank=True, max_length=64)),
                ('currency_id', models.CharField(default='PKR', max_length=3)),
                ('fiscalyear_last_day', models.IntegerField(default=31)),
                ('fiscalyear_last_month', models.IntegerField(default=12)),
                ('period_lock_date', models.DateField(blank=True, null=True)),
                ('fiscalyear_lock_date', models.DateField(blank=True, null=True)),
                ('chart_template_id', models.CharField(blank=True, max_length=64)),
                ('bank_account_code_prefix', models.CharField(default='1014', max_length=32)),
                ('cash_account_code_prefix', models.CharField(default='1011', max_length=32)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Companies',
                'db_table': 'res_company',
            },
        ),
        migrations.CreateModel(
            name='AccountAccount',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, help_text='Account Name', max_length=128)),
                ('code', models.CharField(db_index=True, help_text='Account Code', max_length=64)),
                ('account_type', models.CharField(choices=[('asset_receivable', 'Receivable'), ('asset_cash', 'Bank and Cash'), ('asset_current', 'Current Assets'), ('asset_non_current', 'Non-current Assets'), ('asset_prepayments', 'Prepayments'), ('asset_fixed', 'Fixed Assets'), ('liability_payable', 'Payable'), ('liability_credit_card', 'Credit Card'), ('liability_current', 'Current Liabilities'), ('liability_non_current', 'Non-current Liabilities'), ('equity', 'Equity'), ('equity_unaffected', 'Current Year Earnings'), ('income', 'Income'), ('income_other', 'Other Income'), ('expense', 'Expenses'), ('expense_depreciation', 'Depreciation'), ('expense_direct_cost', 'Cost of Revenue'), ('off_balance', 'Off-Balance Sheet')], db_index=True, max_length=64)),
                ('reconcile', models.BooleanField(default=False, help_text='Allow reconciliation of journal items')),
                ('deprecated', models.BooleanField(default=False, help_text='Deprecated accounts are hidden by default')),
                ('currency_id', models.CharField(blank=True, help_text='Forces all moves for this account to have this currency', max_length=3)),
                ('note', models.TextField(blank=True, help_text='Internal notes')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
            ],
            options={
                'db_table': 'account_account',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='ResPartner',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(db_index=True, max_length=128)),
                ('display_name', models.CharField(blank=True, max_length=128)),
                ('ref', models.CharField(blank=True, help_text='Internal Reference', max_length=64)),
                ('is_company', models.BooleanField(default=False)),
                ('street', models.CharField(blank=True, max_length=128)),
                ('street2', models.CharField(blank=True, max_length=128)),
                ('city', models.CharField(blank=True, max_length=128)),
                ('state_id', models.CharField(blank=True, max_length=128)),
                ('zip', models.CharField(blank=True, max_length=24)),
                ('country_id', models.CharField(default='PK', max_length=2)),
                ('phone', models.CharField(blank=True, max_length=32)),
                ('mobile', models.CharField(blank=True, max_length=32)),
                ('email', models.CharField(blank=True, max_length=240)),
                ('website', models.CharField(blank=True, max_length=64)),
                ('function', models.CharField(blank=True, help_text='Job Position', max_length=128)),
                ('title', models.CharField(blank=True, max_length=16)),
                ('customer_rank', models.IntegerField(default=0, help_text='Customer ranking for prioritization')),
                ('supplier_rank', models.IntegerField(default=0, help_text='Vendor ranking for prioritization')),
                ('credit_limit', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('vat', models.CharField(blank=True, help_text='Tax ID', max_length=32)),
                ('active', models.BooleanField(default=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('parent_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.respartner')),
            ],
            options={
                'db_table': 'res_partner',
                'indexes': [models.Index(fields=['name'], name='res_partner_name_0c4920_idx'), models.Index(fields=['customer_rank'], name='res_partner_custome_9a0c0e_idx'), models.Index(fields=['supplier_rank'], name='res_partner_supplie_c6dab8_idx')],
            },
        ),
        migrations.CreateModel(
            name='AccountJournal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('code', models.CharField(max_length=5)),
                ('type', models.CharField(choices=[('sale', 'Sales'), ('purchase', 'Purchase'), ('cash', 'Cash'), ('bank', 'Bank'), ('general', 'Miscellaneous')], max_length=8)),
                ('active', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=10, help_text='Used to order journals in the dashboard')),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('default_account_id', models.ForeignKey(help_text='Default account for journal entries', on_delete=django.db.models.deletion.RESTRICT, to='accounting.accountaccount')),
            ],
            options={
                'db_table': 'account_journal',
                'ordering': ['sequence', 'type', 'code'],
                'unique_together': {('code', 'company_id')},
            },
        ),
        migrations.AddIndex(
            model_name='accountaccount',
            index=models.Index(fields=['code', 'company_id'], name='account_acc_code_5f9be8_idx'),
        ),
        migrations.AddIndex(
            model_name='accountaccount',
            index=models.Index(fields=['account_type'], name='account_acc_account_9c1489_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='accountaccount',
            unique_together={('code', 'company_id')},
        ),
    ]
