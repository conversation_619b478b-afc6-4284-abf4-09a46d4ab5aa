#!/usr/bin/env python3
import xmlrpc.client

def fix_uom_categories():
    try:
        # Connection details
        url = 'http://localhost:8069'
        db = 'dataicraft'
        username = '<EMAIL>'
        password = 'admin123'
        
        # Connect to Odoo
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            print("❌ Authentication failed!")
            return
        
        print(f"✅ Connected to Odoo as user ID: {uid}")
        
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        print("\n🔧 === Fixing Unit of Measure Categories === 🔧")
        
        # Step 1: Check existing UoM categories
        print("\n📊 Checking existing UoM categories...")
        
        uom_categories = models.execute_kw(
            db, uid, password,
            'uom.category', 'search_read',
            [[]],
            {'fields': ['name', 'id']}
        )
        
        print("Existing UoM categories:")
        category_map = {}
        for cat in uom_categories:
            print(f"  ID: {cat['id']} - Name: {cat['name']}")
            category_map[cat['name']] = cat['id']
        
        # Step 2: Check existing UoMs
        print("\n📏 Checking existing Units of Measure...")
        
        uoms = models.execute_kw(
            db, uid, password,
            'uom.uom', 'search_read',
            [[]],
            {'fields': ['name', 'category_id', 'uom_type', 'id']}
        )
        
        print("Existing UoMs:")
        uom_map = {}
        for uom in uoms:
            cat_name = uom['category_id'][1] if uom['category_id'] else 'No Category'
            print(f"  ID: {uom['id']} - Name: {uom['name']} - Category: {cat_name} - Type: {uom['uom_type']}")
            uom_map[uom['name']] = uom['id']
        
        # Step 3: Ensure standard UoM categories exist
        print("\n🔧 Ensuring standard UoM categories exist...")
        
        standard_categories = [
            'Unit',
            'Weight', 
            'Working Time',
            'Length / Distance',
            'Surface',
            'Volume'
        ]
        
        for cat_name in standard_categories:
            if cat_name not in category_map:
                try:
                    cat_id = models.execute_kw(
                        db, uid, password,
                        'uom.category', 'create',
                        [{'name': cat_name}]
                    )
                    category_map[cat_name] = cat_id
                    print(f"  ✅ Created category: {cat_name} (ID: {cat_id})")
                except Exception as e:
                    print(f"  ⚠️ Category {cat_name}: {e}")
            else:
                print(f"  ✅ Category exists: {cat_name}")
        
        # Step 4: Ensure standard UoMs exist
        print("\n📏 Ensuring standard UoMs exist...")
        
        # Get category IDs
        unit_cat_id = category_map.get('Unit')
        time_cat_id = category_map.get('Working Time')
        
        standard_uoms = [
            {
                'name': 'Unit(s)',
                'category_id': unit_cat_id,
                'uom_type': 'reference',
                'factor': 1.0,
                'rounding': 0.01
            },
            {
                'name': 'Hour(s)',
                'category_id': time_cat_id,
                'uom_type': 'reference', 
                'factor': 1.0,
                'rounding': 0.01
            }
        ]
        
        for uom_data in standard_uoms:
            if uom_data['name'] not in uom_map:
                try:
                    uom_id = models.execute_kw(
                        db, uid, password,
                        'uom.uom', 'create',
                        [uom_data]
                    )
                    uom_map[uom_data['name']] = uom_id
                    print(f"  ✅ Created UoM: {uom_data['name']} (ID: {uom_id})")
                except Exception as e:
                    print(f"  ⚠️ UoM {uom_data['name']}: {e}")
            else:
                print(f"  ✅ UoM exists: {uom_data['name']}")
        
        # Step 5: Fix products with incorrect UoMs
        print("\n🛠️ Fixing products with UoM issues...")
        
        # Find products with potential UoM issues
        products = models.execute_kw(
            db, uid, password,
            'product.template', 'search_read',
            [[]],
            {'fields': ['name', 'uom_id', 'uom_po_id', 'type', 'service_type']}
        )
        
        fixed_products = 0
        
        for product in products:
            try:
                product_name = product['name']
                product_type = product['type']
                service_type = product.get('service_type', False)
                
                # Determine correct UoM based on product type
                if product_type == 'service' and service_type == 'timesheet':
                    # Timesheet services should use Hours
                    correct_uom_id = uom_map.get('Hour(s)')
                    if correct_uom_id and product['uom_id'][0] != correct_uom_id:
                        models.execute_kw(
                            db, uid, password,
                            'product.template', 'write',
                            [[product['id']], {
                                'uom_id': correct_uom_id,
                                'uom_po_id': correct_uom_id
                            }]
                        )
                        print(f"  ✅ Fixed {product_name}: Set to Hours")
                        fixed_products += 1
                else:
                    # Other products should use Units
                    correct_uom_id = uom_map.get('Unit(s)')
                    if correct_uom_id and product['uom_id'][0] != correct_uom_id:
                        models.execute_kw(
                            db, uid, password,
                            'product.template', 'write',
                            [[product['id']], {
                                'uom_id': correct_uom_id,
                                'uom_po_id': correct_uom_id
                            }]
                        )
                        print(f"  ✅ Fixed {product_name}: Set to Units")
                        fixed_products += 1
                        
            except Exception as e:
                print(f"  ⚠️ Error fixing product {product.get('name', 'Unknown')}: {e}")
        
        # Step 6: Set company default UoMs
        print("\n🏢 Setting company default UoMs...")
        
        try:
            company_ids = models.execute_kw(
                db, uid, password,
                'res.company', 'search', [[]]
            )
            
            if company_ids:
                models.execute_kw(
                    db, uid, password,
                    'res.company', 'write',
                    [company_ids, {
                        'timesheet_encode_uom_id': uom_map.get('Hour(s)'),
                    }]
                )
                print("  ✅ Set company timesheet UoM to Hours")
        
        except Exception as e:
            print(f"  ⚠️ Company settings: {e}")
        
        print(f"\n🎉 UoM Fix Complete!")
        print(f"✅ Fixed {fixed_products} products")
        print(f"✅ Ensured all standard UoM categories exist")
        print(f"✅ Ensured all standard UoMs exist")
        
        print(f"\n📋 Summary:")
        print(f"  📊 UoM Categories: {len(category_map)}")
        print(f"  📏 Units of Measure: {len(uom_map)}")
        print(f"  🛠️ Products Fixed: {fixed_products}")
        
        print(f"\n🔄 Next Steps:")
        print("1. Refresh your browser")
        print("2. Try creating the order again")
        print("3. Ensure products have correct UoM categories")
        print("4. Check that timesheet products use 'Hours'")
        print("5. Check that physical products use 'Units'")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    fix_uom_categories()
