# Generated by Django 4.2.21 on 2025-07-15 16:48

import datetime
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('accounting', '0002_accountanalyticaccount_accountfiscalposition_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='AccountAgedTrialBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('report_date', models.DateField(db_index=True)),
                ('not_due', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('period_1', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('period_2', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('period_3', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('period_4', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('period_5', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('total', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
            ],
            options={
                'db_table': 'account_aged_trial_balance',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='AccountInvoiceReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(db_index=True)),
                ('invoice_date', models.DateField(blank=True, db_index=True, null=True)),
                ('invoice_date_due', models.DateField(blank=True, db_index=True, null=True)),
                ('country_id', models.CharField(blank=True, db_index=True, max_length=2)),
                ('move_type', models.CharField(db_index=True, max_length=16)),
                ('state', models.CharField(db_index=True, max_length=8)),
                ('currency_id', models.CharField(db_index=True, max_length=3)),
                ('price_subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('price_average', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('residual', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('quantity', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
            ],
            options={
                'db_table': 'account_invoice_report',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='AccountMoveLineReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField(db_index=True)),
                ('account_type', models.CharField(db_index=True, max_length=64)),
                ('account_code', models.CharField(db_index=True, max_length=64)),
                ('account_name', models.CharField(max_length=128)),
                ('debit', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('credit', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('balance', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('reconciled', models.BooleanField(db_index=True, default=False)),
            ],
            options={
                'db_table': 'account_move_line_report',
                'managed': False,
            },
        ),
        migrations.CreateModel(
            name='AccountAsset',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('code', models.CharField(blank=True, max_length=32)),
                ('value', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('value_residual', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('method_number', models.IntegerField(default=5)),
                ('method_period', models.IntegerField(default=1)),
                ('method_end', models.DateField(blank=True, null=True)),
                ('purchase_date', models.DateField(blank=True, null=True)),
                ('purchase_value', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('open', 'Running'), ('close', 'Close')], default='draft', max_length=8)),
                ('active', models.BooleanField(default=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'account_asset_asset',
            },
        ),
        migrations.CreateModel(
            name='AccountBankStatement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('reference', models.CharField(blank=True, max_length=32)),
                ('date', models.DateField(db_index=True)),
                ('date_done', models.DateTimeField(blank=True, null=True)),
                ('balance_start', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('balance_end_real', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('balance_end', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('state', models.CharField(choices=[('open', 'New'), ('posted', 'Validated')], default='open', max_length=8)),
                ('currency_id', models.CharField(default='PKR', max_length=3)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('journal_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.accountjournal')),
            ],
            options={
                'db_table': 'account_bank_statement',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='AccountBudgetPost',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('code', models.CharField(blank=True, max_length=64)),
                ('account_ids', models.ManyToManyField(related_name='budget_posts', to='accounting.accountaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
            ],
            options={
                'db_table': 'account_budget_post',
            },
        ),
        migrations.CreateModel(
            name='CrossoveredBudget',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('code', models.CharField(blank=True, max_length=16)),
                ('date_from', models.DateField()),
                ('date_to', models.DateField()),
                ('state', models.CharField(choices=[('draft', 'Draft'), ('cancel', 'Cancelled'), ('confirm', 'Confirmed'), ('validate', 'Validated'), ('done', 'Done')], default='draft', max_length=8)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('user_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='budgets', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'crossovered_budget',
            },
        ),
        migrations.CreateModel(
            name='MailMessage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('subject', models.CharField(blank=True, max_length=256)),
                ('date', models.DateTimeField(db_index=True, default=datetime.datetime.now)),
                ('body', models.TextField(blank=True)),
                ('attachment_ids', models.CharField(blank=True, max_length=256)),
                ('message_type', models.CharField(choices=[('email', 'Email'), ('comment', 'Comment'), ('notification', 'System notification'), ('user_notification', 'User notification')], default='email', max_length=32)),
                ('email_from', models.CharField(blank=True, max_length=128)),
                ('reply_to', models.CharField(blank=True, max_length=128)),
                ('model', models.CharField(blank=True, db_index=True, max_length=128)),
                ('res_id', models.IntegerField(blank=True, db_index=True, null=True)),
                ('record_name', models.CharField(blank=True, max_length=128)),
                ('author_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
            ],
            options={
                'db_table': 'mail_message',
            },
        ),
        migrations.CreateModel(
            name='ProductCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('complete_name', models.CharField(blank=True, max_length=256)),
                ('sequence', models.IntegerField(default=10)),
                ('child_id', models.ManyToManyField(blank=True, related_name='parent_categories', to='accounting.productcategory')),
                ('parent_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_categories', to='accounting.productcategory')),
                ('property_account_expense_categ_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='expense_categories', to='accounting.accountaccount')),
                ('property_account_income_categ_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='income_categories', to='accounting.accountaccount')),
                ('property_stock_account_input_categ_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_input_categories', to='accounting.accountaccount')),
                ('property_stock_account_output_categ_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_output_categories', to='accounting.accountaccount')),
                ('property_stock_valuation_account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_valuation_categories', to='accounting.accountaccount')),
            ],
            options={
                'verbose_name_plural': 'Product Categories',
                'db_table': 'product_category',
            },
        ),
        migrations.CreateModel(
            name='ProductProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('default_code', models.CharField(blank=True, help_text='Internal Reference', max_length=64)),
                ('barcode', models.CharField(blank=True, max_length=64, unique=True)),
                ('active', models.BooleanField(default=True)),
            ],
            options={
                'db_table': 'product_product',
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='New', max_length=64)),
                ('origin', models.CharField(blank=True, max_length=64)),
                ('partner_ref', models.CharField(blank=True, max_length=64)),
                ('state', models.CharField(choices=[('draft', 'RFQ'), ('sent', 'RFQ Sent'), ('to approve', 'To Approve'), ('purchase', 'Purchase Order'), ('done', 'Locked'), ('cancel', 'Cancelled')], default='draft', max_length=12)),
                ('date_order', models.DateTimeField(default=datetime.datetime.now)),
                ('date_approve', models.DateTimeField(blank=True, null=True)),
                ('date_planned', models.DateTimeField(blank=True, null=True)),
                ('amount_untaxed', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('amount_tax', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('amount_total', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('notes', models.TextField(blank=True)),
                ('currency_id', models.CharField(default='PKR', max_length=3)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('fiscal_position_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountfiscalposition')),
                ('partner_id', models.ForeignKey(help_text='Vendor', on_delete=django.db.models.deletion.RESTRICT, to='accounting.respartner')),
                ('payment_term_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountpaymentterm')),
                ('user_id', models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'purchase_order',
            },
        ),
        migrations.CreateModel(
            name='PurchaseOrderLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.TextField(help_text='Description')),
                ('sequence', models.IntegerField(default=10)),
                ('product_qty', models.DecimalField(decimal_places=4, default=1, max_digits=16)),
                ('product_uom', models.IntegerField(blank=True, null=True)),
                ('qty_received', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('qty_invoiced', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('price_unit', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('price_subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('price_total', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('price_tax', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('date_planned', models.DateTimeField()),
                ('account_analytic_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountanalyticaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('invoice_lines', models.ManyToManyField(blank=True, to='accounting.accountmoveline')),
                ('order_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_line', to='accounting.purchaseorder')),
                ('product_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, to='accounting.productproduct')),
                ('taxes_id', models.ManyToManyField(blank=True, to='accounting.accounttax')),
            ],
            options={
                'db_table': 'purchase_order_line',
            },
        ),
        migrations.CreateModel(
            name='ResCountry',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('code', models.CharField(max_length=2, unique=True)),
                ('address_format', models.TextField(blank=True)),
                ('currency_id', models.CharField(blank=True, max_length=3)),
                ('phone_code', models.IntegerField(blank=True, null=True)),
            ],
            options={
                'verbose_name_plural': 'Countries',
                'db_table': 'res_country',
            },
        ),
        migrations.CreateModel(
            name='ResCurrency',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=3, unique=True)),
                ('symbol', models.CharField(max_length=4)),
                ('rate', models.DecimalField(decimal_places=6, default=1.0, max_digits=12)),
                ('rounding', models.DecimalField(decimal_places=6, default=0.01, max_digits=12)),
                ('decimal_places', models.IntegerField(default=2)),
                ('active', models.BooleanField(default=True)),
                ('position', models.CharField(choices=[('after', 'After Amount'), ('before', 'Before Amount')], default='after', max_length=8)),
            ],
            options={
                'verbose_name_plural': 'Currencies',
                'db_table': 'res_currency',
            },
        ),
        migrations.CreateModel(
            name='SaleOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(default='New', max_length=64)),
                ('origin', models.CharField(blank=True, max_length=64)),
                ('client_order_ref', models.CharField(blank=True, max_length=64)),
                ('reference', models.CharField(blank=True, max_length=64)),
                ('state', models.CharField(choices=[('draft', 'Quotation'), ('sent', 'Quotation Sent'), ('sale', 'Sales Order'), ('done', 'Locked'), ('cancel', 'Cancelled')], default='draft', max_length=8)),
                ('date_order', models.DateTimeField(default=datetime.datetime.now)),
                ('validity_date', models.DateField(blank=True, null=True)),
                ('confirmation_date', models.DateTimeField(blank=True, null=True)),
                ('amount_untaxed', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('amount_tax', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('amount_total', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('note', models.TextField(blank=True)),
                ('team_id', models.IntegerField(blank=True, null=True)),
                ('currency_id', models.CharField(default='PKR', max_length=3)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('fiscal_position_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountfiscalposition')),
                ('partner_id', models.ForeignKey(help_text='Customer', on_delete=django.db.models.deletion.RESTRICT, to='accounting.respartner')),
                ('partner_invoice_id', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='invoice_orders', to='accounting.respartner')),
                ('partner_shipping_id', models.ForeignKey(on_delete=django.db.models.deletion.RESTRICT, related_name='shipping_orders', to='accounting.respartner')),
                ('payment_term_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountpaymentterm')),
                ('user_id', models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'sale_order',
            },
        ),
        migrations.CreateModel(
            name='SaleOrderLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.TextField(help_text='Description')),
                ('sequence', models.IntegerField(default=10)),
                ('product_uom_qty', models.DecimalField(decimal_places=4, default=1, max_digits=16)),
                ('product_uom', models.IntegerField(blank=True, null=True)),
                ('qty_delivered', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('qty_invoiced', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('qty_to_invoice', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('price_unit', models.DecimalField(decimal_places=4, default=0, max_digits=16)),
                ('price_subtotal', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('price_total', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('price_tax', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('discount', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('invoice_status', models.CharField(choices=[('upselling', 'Upselling Opportunity'), ('invoiced', 'Fully Invoiced'), ('to invoice', 'To Invoice'), ('no', 'Nothing to Invoice')], default='no', max_length=16)),
                ('analytic_account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountanalyticaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('invoice_lines', models.ManyToManyField(blank=True, to='accounting.accountmoveline')),
                ('order_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='order_line', to='accounting.saleorder')),
                ('product_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.RESTRICT, to='accounting.productproduct')),
                ('tax_id', models.ManyToManyField(blank=True, to='accounting.accounttax')),
            ],
            options={
                'db_table': 'sale_order_line',
            },
        ),
        migrations.CreateModel(
            name='StockLocation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('complete_name', models.CharField(blank=True, max_length=256)),
                ('active', models.BooleanField(default=True)),
                ('usage', models.CharField(choices=[('supplier', 'Vendor Location'), ('view', 'View'), ('internal', 'Internal Location'), ('customer', 'Customer Location'), ('inventory', 'Inventory Loss'), ('procurement', 'Procurement'), ('production', 'Production'), ('transit', 'Transit Location')], default='internal', max_length=12)),
                ('child_ids', models.ManyToManyField(blank=True, related_name='parent_locations', to='accounting.stocklocation')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('location_id', models.ForeignKey(blank=True, help_text='Parent Location', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='child_locations', to='accounting.stocklocation')),
                ('valuation_in_account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_valuation_in_locations', to='accounting.accountaccount')),
                ('valuation_out_account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='stock_valuation_out_locations', to='accounting.accountaccount')),
            ],
            options={
                'db_table': 'stock_location',
            },
        ),
        migrations.CreateModel(
            name='StockWarehouse',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('code', models.CharField(max_length=5)),
                ('sequence', models.IntegerField(default=10)),
                ('active', models.BooleanField(default=True)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('lot_stock_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_lot_stock', to='accounting.stocklocation')),
                ('partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
                ('view_location_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_view', to='accounting.stocklocation')),
                ('wh_input_stock_loc_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_input', to='accounting.stocklocation')),
                ('wh_output_stock_loc_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_output', to='accounting.stocklocation')),
                ('wh_pack_stock_loc_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_pack', to='accounting.stocklocation')),
                ('wh_qc_stock_loc_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='warehouse_qc', to='accounting.stocklocation')),
            ],
            options={
                'db_table': 'stock_warehouse',
            },
        ),
        migrations.CreateModel(
            name='StockMove',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=256)),
                ('sequence', models.IntegerField(default=10)),
                ('priority', models.CharField(default='1', max_length=1)),
                ('date', models.DateTimeField(default=datetime.datetime.now)),
                ('date_expected', models.DateTimeField(default=datetime.datetime.now)),
                ('date_deadline', models.DateTimeField(blank=True, null=True)),
                ('product_qty', models.DecimalField(decimal_places=4, default=1, max_digits=16)),
                ('product_uom_qty', models.DecimalField(decimal_places=4, default=1, max_digits=16)),
                ('product_uom', models.IntegerField(default=1)),
                ('state', models.CharField(choices=[('draft', 'New'), ('cancel', 'Cancelled'), ('waiting', 'Waiting Another Move'), ('confirmed', 'Waiting Availability'), ('partially_available', 'Partially Available'), ('assigned', 'Available'), ('done', 'Done')], default='draft', max_length=20)),
                ('origin', models.CharField(blank=True, max_length=64)),
                ('procure_method', models.CharField(choices=[('make_to_stock', 'Take From Stock'), ('make_to_order', 'Create Procurement')], default='make_to_stock', max_length=16)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('account_move_ids', models.ManyToManyField(blank=True, help_text='Generated accounting entries', to='accounting.accountmove')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('location_dest_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_move_location_dest_id', to='accounting.stocklocation')),
                ('location_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='stock_move_location_id', to='accounting.stocklocation')),
                ('partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
                ('product_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.productproduct')),
                ('purchase_line_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.purchaseorderline')),
                ('sale_line_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.saleorderline')),
            ],
            options={
                'db_table': 'stock_move',
            },
        ),
        migrations.CreateModel(
            name='ResCountryState',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('code', models.CharField(max_length=3)),
                ('country_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescountry')),
            ],
            options={
                'db_table': 'res_country_state',
            },
        ),
        migrations.CreateModel(
            name='ProductTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('sequence', models.IntegerField(default=1)),
                ('description', models.TextField(blank=True)),
                ('description_purchase', models.TextField(blank=True)),
                ('description_sale', models.TextField(blank=True)),
                ('type', models.CharField(choices=[('consu', 'Consumable'), ('service', 'Service'), ('product', 'Storable Product')], default='consu', max_length=8)),
                ('list_price', models.DecimalField(decimal_places=2, default=0, help_text='Sale Price', max_digits=16)),
                ('standard_price', models.DecimalField(decimal_places=2, default=0, help_text='Cost Price', max_digits=16)),
                ('sale_ok', models.BooleanField(default=True)),
                ('purchase_ok', models.BooleanField(default=True)),
                ('tracking', models.CharField(choices=[('none', 'No Tracking'), ('lot', 'By Lots'), ('serial', 'By Unique Serial Number')], default='none', max_length=8)),
                ('active', models.BooleanField(default=True)),
                ('create_date', models.DateTimeField(auto_now_add=True)),
                ('write_date', models.DateTimeField(auto_now=True)),
                ('categ_id', models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to='accounting.productcategory')),
                ('company_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('property_account_expense_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='expense_products', to='accounting.accountaccount')),
                ('property_account_income_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='income_products', to='accounting.accountaccount')),
                ('supplier_taxes_id', models.ManyToManyField(blank=True, help_text='Vendor Taxes', related_name='supplier_products', to='accounting.accounttax')),
                ('taxes_id', models.ManyToManyField(blank=True, help_text='Customer Taxes', to='accounting.accounttax')),
            ],
            options={
                'db_table': 'product_template',
            },
        ),
        migrations.AddField(
            model_name='productproduct',
            name='product_tmpl_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='product_variant_ids', to='accounting.producttemplate'),
        ),
        migrations.CreateModel(
            name='MailTrackingValue',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('field', models.CharField(max_length=64)),
                ('field_desc', models.CharField(max_length=128)),
                ('field_type', models.CharField(max_length=16)),
                ('old_value_integer', models.IntegerField(blank=True, null=True)),
                ('old_value_float', models.DecimalField(blank=True, decimal_places=2, max_digits=16, null=True)),
                ('old_value_monetary', models.DecimalField(blank=True, decimal_places=2, max_digits=16, null=True)),
                ('old_value_char', models.CharField(blank=True, max_length=256)),
                ('old_value_text', models.TextField(blank=True)),
                ('old_value_datetime', models.DateTimeField(blank=True, null=True)),
                ('new_value_integer', models.IntegerField(blank=True, null=True)),
                ('new_value_float', models.DecimalField(blank=True, decimal_places=2, max_digits=16, null=True)),
                ('new_value_monetary', models.DecimalField(blank=True, decimal_places=2, max_digits=16, null=True)),
                ('new_value_char', models.CharField(blank=True, max_length=256)),
                ('new_value_text', models.TextField(blank=True)),
                ('new_value_datetime', models.DateTimeField(blank=True, null=True)),
                ('mail_message_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.mailmessage')),
            ],
            options={
                'db_table': 'mail_tracking_value',
            },
        ),
        migrations.CreateModel(
            name='MailNotification',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('notification_type', models.CharField(choices=[('inbox', 'Inbox'), ('email', 'Email')], default='inbox', max_length=8)),
                ('notification_status', models.CharField(choices=[('ready', 'Ready to Send'), ('sent', 'Sent'), ('bounce', 'Bounced'), ('exception', 'Exception'), ('canceled', 'Canceled')], default='ready', max_length=16)),
                ('is_read', models.BooleanField(default=False)),
                ('read_date', models.DateTimeField(blank=True, null=True)),
                ('failure_type', models.CharField(blank=True, max_length=64)),
                ('failure_reason', models.TextField(blank=True)),
                ('mail_message_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.mailmessage')),
                ('res_partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.respartner')),
            ],
            options={
                'db_table': 'mail_notification',
            },
        ),
        migrations.CreateModel(
            name='MailMessageSubtype',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('description', models.TextField(blank=True)),
                ('internal', models.BooleanField(default=True)),
                ('relation_field', models.CharField(blank=True, max_length=64)),
                ('res_model', models.CharField(blank=True, max_length=64)),
                ('default', models.BooleanField(default=True)),
                ('sequence', models.IntegerField(default=1)),
                ('hidden', models.BooleanField(default=False)),
                ('parent_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.mailmessagesubtype')),
            ],
            options={
                'db_table': 'mail_message_subtype',
            },
        ),
        migrations.AddField(
            model_name='mailmessage',
            name='notification_ids',
            field=models.ManyToManyField(blank=True, to='accounting.mailnotification'),
        ),
        migrations.AddField(
            model_name='mailmessage',
            name='parent_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.mailmessage'),
        ),
        migrations.AddField(
            model_name='mailmessage',
            name='subtype_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.mailmessagesubtype'),
        ),
        migrations.AddField(
            model_name='mailmessage',
            name='tracking_value_ids',
            field=models.ManyToManyField(blank=True, to='accounting.mailtrackingvalue'),
        ),
        migrations.CreateModel(
            name='IrSequence',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('code', models.CharField(db_index=True, max_length=64)),
                ('implementation', models.CharField(choices=[('standard', 'Standard'), ('no_gap', 'No Gap')], default='standard', max_length=8)),
                ('active', models.BooleanField(default=True)),
                ('prefix', models.CharField(blank=True, max_length=64)),
                ('suffix', models.CharField(blank=True, max_length=64)),
                ('number_next', models.IntegerField(default=1)),
                ('number_increment', models.IntegerField(default=1)),
                ('padding', models.IntegerField(default=0)),
                ('use_date_range', models.BooleanField(default=False)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
            ],
            options={
                'db_table': 'ir_sequence',
            },
        ),
        migrations.CreateModel(
            name='IrCron',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('active', models.BooleanField(default=True)),
                ('interval_number', models.IntegerField(default=1)),
                ('interval_type', models.CharField(choices=[('minutes', 'Minutes'), ('hours', 'Hours'), ('days', 'Days'), ('weeks', 'Weeks'), ('months', 'Months')], default='months', max_length=8)),
                ('numbercall', models.IntegerField(default=-1, help_text='Number of calls, -1 for unlimited')),
                ('doall', models.BooleanField(default=True)),
                ('nextcall', models.DateTimeField()),
                ('model_id', models.CharField(max_length=64)),
                ('function', models.CharField(max_length=64)),
                ('args', models.TextField(blank=True)),
                ('priority', models.IntegerField(default=5)),
                ('user_id', models.ForeignKey(default=1, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'ir_cron',
            },
        ),
        migrations.CreateModel(
            name='CrossoveredBudgetLines',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date_from', models.DateField()),
                ('date_to', models.DateField()),
                ('planned_amount', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('practical_amount', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('theoretical_amount', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('percentage', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('analytic_account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.accountanalyticaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('crossovered_budget_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='crossovered_budget_line', to='accounting.crossoveredbudget')),
                ('general_budget_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.accountbudgetpost')),
            ],
            options={
                'db_table': 'crossovered_budget_lines',
            },
        ),
        migrations.CreateModel(
            name='AccountReconcileModel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('sequence', models.IntegerField(default=10)),
                ('rule_type', models.CharField(choices=[('writeoff_button', 'Button to generate counterpart entry'), ('writeoff_suggestion', 'Rule to suggest counterpart entry'), ('invoice_matching', 'Rule to match invoices/bills')], default='writeoff_button', max_length=25)),
                ('auto_reconcile', models.BooleanField(default=False)),
                ('to_check', models.BooleanField(default=False)),
                ('match_nature', models.CharField(blank=True, choices=[('amount_received', 'Amount Received'), ('amount_paid', 'Amount Paid')], max_length=16)),
                ('match_amount', models.CharField(blank=True, choices=[('lower', 'Is Lower Than'), ('greater', 'Is Greater Than'), ('between', 'Is Between')], max_length=8)),
                ('match_amount_min', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('match_amount_max', models.DecimalField(decimal_places=2, default=0, max_digits=16)),
                ('match_label', models.CharField(blank=True, choices=[('contains', 'Contains'), ('not_contains', 'Not Contains'), ('match_regex', 'Match Regex')], max_length=16)),
                ('match_label_param', models.CharField(blank=True, max_length=100)),
                ('match_note', models.CharField(blank=True, choices=[('contains', 'Contains'), ('not_contains', 'Not Contains'), ('match_regex', 'Match Regex')], max_length=16)),
                ('match_note_param', models.CharField(blank=True, max_length=100)),
                ('match_transaction_type', models.CharField(blank=True, max_length=100)),
                ('match_same_currency', models.BooleanField(default=True)),
                ('label', models.CharField(blank=True, max_length=64)),
                ('amount_type', models.CharField(choices=[('fixed', 'Fixed'), ('percentage', 'Percentage of balance')], default='percentage', max_length=16)),
                ('amount', models.DecimalField(decimal_places=2, default=100, max_digits=16)),
                ('account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.accountaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('journal_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='reconcile_models', to='accounting.accountjournal')),
                ('match_journal_ids', models.ManyToManyField(blank=True, related_name='reconcile_models_match', to='accounting.accountjournal')),
                ('tax_ids', models.ManyToManyField(blank=True, related_name='reconcile_models', to='accounting.accounttax')),
            ],
            options={
                'db_table': 'account_reconcile_model',
            },
        ),
        migrations.CreateModel(
            name='AccountFinancialReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=128)),
                ('sequence', models.IntegerField(default=10)),
                ('level', models.IntegerField(default=0)),
                ('type', models.CharField(choices=[('sum', 'View'), ('accounts', 'Accounts'), ('account_type', 'Account Type'), ('account_report', 'Report Value')], default='sum', max_length=16)),
                ('account_type_ids', models.CharField(blank=True, max_length=256)),
                ('sign', models.IntegerField(choices=[(1, 'Positive'), (-1, 'Negative')], default=1)),
                ('display_detail', models.CharField(choices=[('no_detail', 'No detail'), ('detail_flat', 'Display children flat'), ('detail_with_hierarchy', 'Display children with hierarchy')], default='detail_flat', max_length=32)),
                ('style_overwrite', models.BooleanField(default=False)),
                ('account_ids', models.ManyToManyField(blank=True, to='accounting.accountaccount')),
                ('account_report_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='report_references', to='accounting.accountfinancialreport')),
                ('children_ids', models.ManyToManyField(blank=True, related_name='parent_reports', to='accounting.accountfinancialreport')),
                ('parent_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='children_reports', to='accounting.accountfinancialreport')),
            ],
            options={
                'db_table': 'account_financial_report',
            },
        ),
        migrations.CreateModel(
            name='AccountBankStatementLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sequence', models.IntegerField(default=1)),
                ('date', models.DateField(db_index=True)),
                ('name', models.CharField(max_length=64)),
                ('ref', models.CharField(blank=True, max_length=32)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=16)),
                ('currency_id', models.CharField(default='PKR', max_length=3)),
                ('account_number', models.CharField(blank=True, max_length=32)),
                ('partner_name', models.CharField(blank=True, max_length=128)),
                ('transaction_type', models.CharField(blank=True, max_length=32)),
                ('is_reconciled', models.BooleanField(default=False)),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('move_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountmove')),
                ('partner_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner')),
                ('statement_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='line_ids', to='accounting.accountbankstatement')),
            ],
            options={
                'db_table': 'account_bank_statement_line',
            },
        ),
        migrations.CreateModel(
            name='AccountAutomaticEntryWizard',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(choices=[('change_period', 'Change Period'), ('change_account', 'Change Account'), ('reverse', 'Reverse')], max_length=16)),
                ('date', models.DateField()),
                ('account_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='accounting.accountaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('journal_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.accountjournal')),
                ('move_ids', models.ManyToManyField(help_text='Moves to process', to='accounting.accountmove')),
            ],
            options={
                'db_table': 'account_automatic_entry_wizard',
            },
        ),
        migrations.CreateModel(
            name='AccountAssetDepreciationLine',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('sequence', models.IntegerField()),
                ('amount', models.DecimalField(decimal_places=2, max_digits=16)),
                ('remaining_value', models.DecimalField(decimal_places=2, max_digits=16)),
                ('depreciated_value', models.DecimalField(decimal_places=2, max_digits=16)),
                ('depreciation_date', models.DateField(db_index=True)),
                ('move_posted', models.BooleanField(default=False)),
                ('asset_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='depreciation_line_ids', to='accounting.accountasset')),
                ('move_id', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountmove')),
            ],
            options={
                'db_table': 'account_asset_depreciation_line',
            },
        ),
        migrations.CreateModel(
            name='AccountAssetCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=64)),
                ('type', models.CharField(choices=[('sale', 'Deferred Revenue'), ('purchase', 'Deferred Expense'), ('expense', 'Expense')], default='purchase', max_length=8)),
                ('method', models.CharField(choices=[('linear', 'Linear'), ('degressive', 'Degressive'), ('degressive_then_linear', 'Degressive then Linear')], default='linear', max_length=25)),
                ('method_number', models.IntegerField(default=5, help_text='Number of depreciations')),
                ('method_period', models.IntegerField(default=1, help_text='Period length')),
                ('method_progress_factor', models.DecimalField(decimal_places=2, default=0.3, max_digits=4)),
                ('prorata', models.BooleanField(default=True)),
                ('active', models.BooleanField(default=True)),
                ('account_asset_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='asset_categories', to='accounting.accountaccount')),
                ('account_depreciation_expense_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='asset_expense_categories', to='accounting.accountaccount')),
                ('account_depreciation_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='depreciation_categories', to='accounting.accountaccount')),
                ('company_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany')),
                ('journal_id', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.accountjournal')),
            ],
            options={
                'db_table': 'account_asset_category',
            },
        ),
        migrations.AddField(
            model_name='accountasset',
            name='category_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.accountassetcategory'),
        ),
        migrations.AddField(
            model_name='accountasset',
            name='company_id',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='accounting.rescompany'),
        ),
        migrations.AddField(
            model_name='accountasset',
            name='invoice_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.accountmove'),
        ),
        migrations.AddField(
            model_name='accountasset',
            name='partner_id',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='accounting.respartner'),
        ),
        migrations.AddIndex(
            model_name='mailmessage',
            index=models.Index(fields=['model', 'res_id'], name='mail_messag_model_4f1aa2_idx'),
        ),
        migrations.AddIndex(
            model_name='mailmessage',
            index=models.Index(fields=['date'], name='mail_messag_date_4319bc_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='irsequence',
            unique_together={('code', 'company_id')},
        ),
    ]
