#!/usr/bin/env python3
import xmlrpc.client

def enable_accounting_features():
    try:
        # Connection details
        url = 'http://localhost:8069'
        db = 'dataicraft'
        username = '<EMAIL>'
        password = 'admin123'
        
        # Connect to Odoo
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            print("❌ Authentication failed!")
            return
        
        print(f"✅ Connected to Odoo as user ID: {uid}")
        
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        print("\n=== Enabling Accounting Features ===")
        
        # Get company settings
        company_ids = models.execute_kw(
            db, uid, password,
            'res.company', 'search', [[]]
        )
        
        if company_ids:
            company_id = company_ids[0]
            print(f"📊 Configuring company ID: {company_id}")
            
            # Enable accounting features
            company_settings = {
                'anglo_saxon_accounting': True,
                'tax_exigibility': True,
                'account_use_credit_limit': True,
                'expects_chart_of_accounts': True,
            }
            
            try:
                models.execute_kw(
                    db, uid, password,
                    'res.company', 'write',
                    [company_ids, company_settings]
                )
                print("✅ Company accounting settings updated")
            except Exception as e:
                print(f"⚠️ Company settings: {e}")
        
        # Enable system-wide accounting features
        print("\n📋 Enabling system features...")
        
        # Try to enable various accounting features through ir.config_parameter
        config_params = [
            ('account.use_invoice_terms', 'True'),
            ('account.display_invoice_amount_total_words', 'True'),
            ('account.show_line_subtotals_tax_selection', 'True'),
        ]
        
        for param, value in config_params:
            try:
                # Check if parameter exists
                param_ids = models.execute_kw(
                    db, uid, password,
                    'ir.config_parameter', 'search',
                    [[['key', '=', param]]]
                )
                
                if param_ids:
                    # Update existing parameter
                    models.execute_kw(
                        db, uid, password,
                        'ir.config_parameter', 'write',
                        [param_ids, {'value': value}]
                    )
                else:
                    # Create new parameter
                    models.execute_kw(
                        db, uid, password,
                        'ir.config_parameter', 'create',
                        [{'key': param, 'value': value}]
                    )
                
                print(f"✅ {param}: {value}")
                
            except Exception as e:
                print(f"⚠️ {param}: {e}")
        
        print(f"\n=== Creating Additional Menu Access ===")
        
        # Grant admin user access to all accounting groups
        try:
            # Find accounting-related groups
            group_ids = models.execute_kw(
                db, uid, password,
                'res.groups', 'search',
                [[['name', 'ilike', 'account']]]
            )
            
            if group_ids:
                # Get admin user
                admin_user_ids = models.execute_kw(
                    db, uid, password,
                    'res.users', 'search',
                    [[['id', '=', uid]]]
                )
                
                if admin_user_ids:
                    # Add user to all accounting groups
                    models.execute_kw(
                        db, uid, password,
                        'res.users', 'write',
                        [admin_user_ids, {'groups_id': [(4, gid) for gid in group_ids]}]
                    )
                    print(f"✅ Added admin to {len(group_ids)} accounting groups")
            
        except Exception as e:
            print(f"⚠️ Group assignment: {e}")
        
        print(f"\n=== Summary ===")
        print("✅ Accounting features configuration completed")
        print("🔄 Please refresh your browser and check:")
        print("   1. Go to Invoicing app")
        print("   2. Look for expanded menu options")
        print("   3. Check Configuration → Chart of Accounts")
        print("   4. Check Accounting → Journal Entries")
        print("   5. Check Reporting → Financial Reports")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    enable_accounting_features()
