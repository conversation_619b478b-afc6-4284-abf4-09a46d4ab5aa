#!/usr/bin/env python3
import psycopg2
import json

def comprehensive_uom_investigation():
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='dataicraft',
            user='odoo',
            password='odoo123'
        )
        cur = conn.cursor()
        
        print("🔍 === COMPREHENSIVE UoM INVESTIGATION === 🔍")
        
        # Step 1: Check ALL UoMs in system
        print("\n📊 ALL UoMs in System:")
        cur.execute("""
            SELECT u.id, u.name, c.name as category, u.uom_type, u.factor, u.active
            FROM uom_uom u
            JOIN uom_category c ON u.category_id = c.id
            ORDER BY c.name, u.uom_type, u.name;
        """)
        
        all_uoms = cur.fetchall()
        hours_uoms = []
        units_uoms = []
        
        for uom in all_uoms:
            uom_id, name, category, uom_type, factor, active = uom
            print(f"  📏 ID:{uom_id} | {name} | {category} | {uom_type} | Factor:{factor} | Active:{active}")
            
            if 'hour' in str(name).lower():
                hours_uoms.append(uom)
            if 'unit' in str(name).lower():
                units_uoms.append(uom)
        
        print(f"\n⏰ Hours-related UoMs found: {len(hours_uoms)}")
        for h in hours_uoms:
            print(f"  🕐 ID:{h[0]} | {h[1]} | {h[2]} | Active:{h[5]}")
        
        print(f"\n📦 Units-related UoMs found: {len(units_uoms)}")
        for u in units_uoms:
            print(f"  📦 ID:{u[0]} | {u[1]} | {u[2]} | Active:{u[5]}")
        
        # Step 2: Check ALL products and their UoMs (including variants)
        print(f"\n📦 ALL Products and Their UoMs:")
        
        cur.execute("""
            SELECT 
                pt.id as template_id,
                pt.name as template_name,
                pt.uom_id as template_uom_id,
                u1.name as template_uom_name,
                c1.name as template_uom_category,
                pp.id as variant_id,
                pp.default_code as variant_code,
                COUNT(pp.id) as variant_count
            FROM product_template pt
            LEFT JOIN uom_uom u1 ON pt.uom_id = u1.id
            LEFT JOIN uom_category c1 ON u1.category_id = c1.id
            LEFT JOIN product_product pp ON pt.id = pp.product_tmpl_id
            GROUP BY pt.id, pt.name, pt.uom_id, u1.name, c1.name, pp.id, pp.default_code
            ORDER BY pt.id;
        """)
        
        products = cur.fetchall()
        non_units_products = []
        
        for product in products:
            template_id, template_name, template_uom_id, template_uom_name, template_uom_category, variant_id, variant_code, variant_count = product
            
            # Parse JSON name if needed
            if isinstance(template_name, str) and template_name.startswith('{'):
                try:
                    name_dict = json.loads(template_name)
                    display_name = name_dict.get('en_US', template_name)
                except:
                    display_name = template_name
            else:
                display_name = template_name
            
            print(f"  📦 Template ID:{template_id} | {display_name}")
            print(f"     UoM: {template_uom_name} (ID:{template_uom_id}) | Category: {template_uom_category}")
            print(f"     Variants: {variant_count}")
            
            if template_uom_id != 1:  # Not using Units UoM
                non_units_products.append({
                    'id': template_id,
                    'name': display_name,
                    'uom_id': template_uom_id,
                    'uom_name': template_uom_name,
                    'category': template_uom_category
                })
            print()
        
        print(f"\n🔴 Products NOT using Units UoM: {len(non_units_products)}")
        for product in non_units_products:
            print(f"  ❌ ID:{product['id']} | {product['name']} | UoM:{product['uom_name']} ({product['category']})")
        
        # Step 3: Check for any existing order lines
        print(f"\n📋 Checking Existing Order Lines:")
        
        cur.execute("""
            SELECT 
                sol.id,
                so.name as order_name,
                so.state,
                pt.name as product_name,
                sol.product_uom as line_uom_id,
                u1.name as line_uom_name,
                c1.name as line_uom_category,
                pt.uom_id as product_uom_id,
                u2.name as product_uom_name,
                c2.name as product_uom_category
            FROM sale_order_line sol
            JOIN sale_order so ON sol.order_id = so.id
            LEFT JOIN product_product pp ON sol.product_id = pp.id
            LEFT JOIN product_template pt ON pp.product_tmpl_id = pt.id
            LEFT JOIN uom_uom u1 ON sol.product_uom = u1.id
            LEFT JOIN uom_category c1 ON u1.category_id = c1.id
            LEFT JOIN uom_uom u2 ON pt.uom_id = u2.id
            LEFT JOIN uom_category c2 ON u2.category_id = c2.id
            ORDER BY sol.id DESC
            LIMIT 10;
        """)
        
        order_lines = cur.fetchall()
        
        if order_lines:
            print(f"Found {len(order_lines)} order lines:")
            for line in order_lines:
                line_id, order_name, state, product_name, line_uom_id, line_uom_name, line_uom_category, product_uom_id, product_uom_name, product_uom_category = line
                
                # Parse JSON name
                if isinstance(product_name, str) and product_name.startswith('{'):
                    try:
                        name_dict = json.loads(product_name)
                        display_product_name = name_dict.get('en_US', product_name)
                    except:
                        display_product_name = product_name
                else:
                    display_product_name = product_name
                
                print(f"  📋 Line {line_id} | Order: {order_name} ({state})")
                print(f"     Product: {display_product_name}")
                print(f"     Line UoM: {line_uom_name} (ID:{line_uom_id}) | {line_uom_category}")
                print(f"     Product UoM: {product_uom_name} (ID:{product_uom_id}) | {product_uom_category}")
                
                if line_uom_category != product_uom_category:
                    print(f"     🔴 CONFLICT: {line_uom_category} != {product_uom_category}")
                print()
        else:
            print("✅ No order lines found")
        
        # Step 4: Check purchase order lines too
        print(f"\n🛒 Checking Purchase Order Lines:")
        
        cur.execute("""
            SELECT 
                pol.id,
                po.name as order_name,
                po.state,
                pt.name as product_name,
                pol.product_uom as line_uom_id,
                u1.name as line_uom_name,
                c1.name as line_uom_category,
                pt.uom_po_id as product_uom_id,
                u2.name as product_uom_name,
                c2.name as product_uom_category
            FROM purchase_order_line pol
            JOIN purchase_order po ON pol.order_id = po.id
            LEFT JOIN product_product pp ON pol.product_id = pp.id
            LEFT JOIN product_template pt ON pp.product_tmpl_id = pt.id
            LEFT JOIN uom_uom u1 ON pol.product_uom = u1.id
            LEFT JOIN uom_category c1 ON u1.category_id = c1.id
            LEFT JOIN uom_uom u2 ON pt.uom_po_id = u2.id
            LEFT JOIN uom_category c2 ON u2.category_id = c2.id
            ORDER BY pol.id DESC
            LIMIT 5;
        """)
        
        po_lines = cur.fetchall()
        
        if po_lines:
            print(f"Found {len(po_lines)} purchase order lines:")
            for line in po_lines:
                line_id, order_name, state, product_name, line_uom_id, line_uom_name, line_uom_category, product_uom_id, product_uom_name, product_uom_category = line
                
                # Parse JSON name
                if isinstance(product_name, str) and product_name.startswith('{'):
                    try:
                        name_dict = json.loads(product_name)
                        display_product_name = name_dict.get('en_US', product_name)
                    except:
                        display_product_name = product_name
                else:
                    display_product_name = product_name
                
                print(f"  🛒 Line {line_id} | Order: {order_name} ({state})")
                print(f"     Product: {display_product_name}")
                print(f"     Line UoM: {line_uom_name} (ID:{line_uom_id}) | {line_uom_category}")
                print(f"     Product UoM: {product_uom_name} (ID:{product_uom_id}) | {product_uom_category}")
                
                if line_uom_category != product_uom_category:
                    print(f"     🔴 CONFLICT: {line_uom_category} != {product_uom_category}")
                print()
        else:
            print("✅ No purchase order lines found")
        
        # Step 5: Provide exact fix
        print(f"\n🔧 EXACT FIXES NEEDED:")
        
        if non_units_products:
            print("1. Fix these products to use Units UoM:")
            for product in non_units_products:
                print(f"   UPDATE product_template SET uom_id = 1, uom_po_id = 1 WHERE id = {product['id']};")
        
        if order_lines or po_lines:
            print("2. Clear all existing order lines:")
            print("   DELETE FROM sale_order_line;")
            print("   DELETE FROM purchase_order_line;")
            print("   DELETE FROM sale_order;")
            print("   DELETE FROM purchase_order;")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    comprehensive_uom_investigation()
