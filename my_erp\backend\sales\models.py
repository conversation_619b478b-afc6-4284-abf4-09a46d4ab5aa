"""
Sales Module Models - Integrated with Accounting
Based on Odoo's Sales Module Structure
"""
from django.db import models
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from decimal import Decimal
from datetime import date, datetime

# Import accounting models for integration
from accounting.models import (
    ResCompany, ResPartner, AccountAccount, AccountJournal, AccountMove,
    AccountMoveLine, AccountTax, AccountAnalyticAccount, ProductTemplate,
    ProductProduct, AccountPaymentTerm, AccountFiscalPosition
)


class SalesTeam(models.Model):
    """Sales Team - Based on Odoo crm.team"""

    name = models.CharField(max_length=64)
    active = models.BooleanField(default=True)
    sequence = models.IntegerField(default=10)
    user_id = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, help_text="Team Leader")
    member_ids = models.ManyToManyField(User, blank=True, related_name='sales_teams', help_text="Team Members")

    # Targets and Goals
    invoiced_target = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Monthly Target")
    quotation_count = models.IntegerField(default=0)
    sale_order_count = models.IntegerField(default=0)

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'crm_team'

    def __str__(self):
        return self.name


class SaleOrder(models.Model):
    """Sales Order - Enhanced version integrated with accounting"""

    STATES = [
        ('draft', 'Quotation'),
        ('sent', 'Quotation Sent'),
        ('sale', 'Sales Order'),
        ('done', 'Locked'),
        ('cancel', 'Cancelled'),
    ]

    INVOICE_STATUS = [
        ('upselling', 'Upselling Opportunity'),
        ('invoiced', 'Fully Invoiced'),
        ('to invoice', 'To Invoice'),
        ('no', 'Nothing to Invoice'),
    ]

    # Basic Information
    name = models.CharField(max_length=64, default='New')
    origin = models.CharField(max_length=64, blank=True, help_text="Source Document")
    client_order_ref = models.CharField(max_length=64, blank=True, help_text="Customer Reference")
    reference = models.CharField(max_length=64, blank=True)
    state = models.CharField(max_length=8, choices=STATES, default='draft')

    # Dates
    date_order = models.DateTimeField(default=datetime.now)
    validity_date = models.DateField(null=True, blank=True)
    confirmation_date = models.DateTimeField(null=True, blank=True)
    expected_date = models.DateTimeField(null=True, blank=True)

    # Partner Information
    partner_id = models.ForeignKey(ResPartner, on_delete=models.RESTRICT, help_text="Customer")
    partner_invoice_id = models.ForeignKey(ResPartner, on_delete=models.RESTRICT, related_name='sales_invoice_orders', help_text="Invoice Address")
    partner_shipping_id = models.ForeignKey(ResPartner, on_delete=models.RESTRICT, related_name='sales_shipping_orders', help_text="Delivery Address")

    # Sales Information
    user_id = models.ForeignKey(User, on_delete=models.CASCADE, default=1, help_text="Salesperson")
    team_id = models.ForeignKey(SalesTeam, null=True, blank=True, on_delete=models.SET_NULL)

    # Pricing
    pricelist_id = models.IntegerField(null=True, blank=True, help_text="Pricelist reference")
    currency_id = models.CharField(max_length=3, default='PKR')

    # Amounts
    amount_untaxed = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_tax = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_total = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_undiscounted = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Accounting Integration
    payment_term_id = models.ForeignKey(AccountPaymentTerm, null=True, blank=True, on_delete=models.SET_NULL)
    fiscal_position_id = models.ForeignKey(AccountFiscalPosition, null=True, blank=True, on_delete=models.SET_NULL)
    analytic_account_id = models.ForeignKey(AccountAnalyticAccount, null=True, blank=True, on_delete=models.SET_NULL)

    # Invoice Status
    invoice_status = models.CharField(max_length=16, choices=INVOICE_STATUS, default='no')
    invoice_ids = models.ManyToManyField(AccountMove, blank=True, help_text="Generated Invoices", related_name='sale_orders')
    invoice_count = models.IntegerField(default=0)

    # Delivery
    picking_policy = models.CharField(max_length=16, choices=[('direct', 'As soon as possible'), ('one', 'When all products are ready')], default='direct')
    warehouse_id = models.IntegerField(null=True, blank=True, help_text="Warehouse reference")

    # Other
    note = models.TextField(blank=True, help_text="Terms and Conditions")
    signature = models.TextField(blank=True)
    signed_by = models.CharField(max_length=64, blank=True)
    signed_on = models.DateTimeField(null=True, blank=True)

    # Opportunity
    opportunity_id = models.IntegerField(null=True, blank=True, help_text="CRM Opportunity reference")

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'sale_order'
        ordering = ['-date_order', '-name']

    def __str__(self):
        return self.name

    def action_confirm(self):
        """Confirm the sales order"""
        if self.state == 'draft':
            self.state = 'sale'
            self.confirmation_date = datetime.now()
            self.name = self._get_sequence()
            self.save()

            # Update invoice status
            self._compute_invoice_status()
            return True
        return False

    def _get_sequence(self):
        """Generate sequence number"""
        return f"SO{datetime.now().year}{self.id:05d}"

    def _compute_invoice_status(self):
        """Compute invoice status"""
        if not self.order_line.exists():
            self.invoice_status = 'no'
        elif all(line.qty_invoiced >= line.product_uom_qty for line in self.order_line.all()):
            self.invoice_status = 'invoiced'
        elif any(line.qty_invoiced > 0 for line in self.order_line.all()):
            self.invoice_status = 'to invoice'
        else:
            self.invoice_status = 'to invoice'
        self.save()


class SaleOrderLine(models.Model):
    """Sales Order Line - Enhanced with accounting integration"""

    INVOICE_STATUS = [
        ('upselling', 'Upselling Opportunity'),
        ('invoiced', 'Fully Invoiced'),
        ('to invoice', 'To Invoice'),
        ('no', 'Nothing to Invoice'),
    ]

    order_id = models.ForeignKey(SaleOrder, on_delete=models.CASCADE, related_name='order_line')
    name = models.TextField(help_text="Description")
    sequence = models.IntegerField(default=10)

    # Product Information
    product_id = models.ForeignKey(ProductProduct, null=True, blank=True, on_delete=models.RESTRICT)
    product_template_id = models.ForeignKey(ProductTemplate, null=True, blank=True, on_delete=models.RESTRICT)
    product_uom_qty = models.DecimalField(max_digits=16, decimal_places=4, default=1, help_text="Ordered Quantity")
    product_uom = models.IntegerField(null=True, blank=True, help_text="Unit of Measure")

    # Quantities
    qty_delivered = models.DecimalField(max_digits=16, decimal_places=4, default=0)
    qty_delivered_method = models.CharField(max_length=16, choices=[('manual', 'Manual'), ('stock_move', 'Stock Moves')], default='manual')
    qty_invoiced = models.DecimalField(max_digits=16, decimal_places=4, default=0)
    qty_to_invoice = models.DecimalField(max_digits=16, decimal_places=4, default=0)

    # Pricing
    price_unit = models.DecimalField(max_digits=16, decimal_places=4, default=0)
    price_subtotal = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    price_total = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    price_tax = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    discount = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    # Taxes
    tax_id = models.ManyToManyField(AccountTax, blank=True, help_text="Taxes", related_name='sale_order_lines')

    # Invoice Integration
    invoice_lines = models.ManyToManyField(AccountMoveLine, blank=True, help_text="Invoice Lines", related_name='sale_order_lines')
    invoice_status = models.CharField(max_length=16, choices=INVOICE_STATUS, default='no')

    # Analytics
    analytic_account_id = models.ForeignKey(AccountAnalyticAccount, null=True, blank=True, on_delete=models.SET_NULL)

    # Other
    customer_lead = models.DecimalField(max_digits=16, decimal_places=2, default=0, help_text="Delivery Lead Time")
    display_type = models.CharField(max_length=16, choices=[('line_section', 'Section'), ('line_note', 'Note')], blank=True)

    company_id = models.ForeignKey(ResCompany, on_delete=models.CASCADE)

    # Timestamps
    create_date = models.DateTimeField(auto_now_add=True)
    write_date = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'sale_order_line'

    def __str__(self):
        return f"{self.order_id.name} - {self.name[:50]}"

    def create_invoice_line(self, invoice):
        """Create invoice line from sales order line"""
        if self.display_type:
            return None

        # Get income account from product or category
        account = self._get_income_account()

        # Create invoice line (AccountMoveLine)
        invoice_line = AccountMoveLine.objects.create(
            move_id=invoice,
            name=self.name,
            account_id=account,
            quantity=self.product_uom_qty,
            price_unit=self.price_unit,
            discount=self.discount,
            analytic_account_id=self.analytic_account_id,
            company_id=self.company_id,
        )

        # Add taxes
        invoice_line.tax_ids.set(self.tax_id.all())

        # Link to sales order line
        self.invoice_lines.add(invoice_line)

        # Update quantities
        self.qty_invoiced += self.product_uom_qty
        self.qty_to_invoice = self.product_uom_qty - self.qty_invoiced
        self._compute_invoice_status()

        return invoice_line

    def _get_income_account(self):
        """Get income account for this product"""
        if self.product_id and self.product_id.product_tmpl_id.property_account_income_id:
            return self.product_id.product_tmpl_id.property_account_income_id
        elif self.product_id and self.product_id.product_tmpl_id.categ_id.property_account_income_categ_id:
            return self.product_id.product_tmpl_id.categ_id.property_account_income_categ_id
        else:
            # Default income account
            return AccountAccount.objects.filter(
                account_type='income',
                company_id=self.company_id
            ).first()

    def _compute_invoice_status(self):
        """Compute invoice status"""
        if self.qty_invoiced >= self.product_uom_qty:
            self.invoice_status = 'invoiced'
        elif self.qty_invoiced > 0:
            self.invoice_status = 'to invoice'
        else:
            self.invoice_status = 'to invoice'
        self.save()

    def _compute_amounts(self):
        """Compute line amounts"""
        price = self.price_unit * (1 - (self.discount / 100))
        self.price_subtotal = price * self.product_uom_qty

        # Calculate taxes
        taxes = self.tax_id.all()
        tax_amount = 0
        for tax in taxes:
            tax_amount += (self.price_subtotal * tax.amount / 100)

        self.price_tax = tax_amount
        self.price_total = self.price_subtotal + self.price_tax
        self.save()
