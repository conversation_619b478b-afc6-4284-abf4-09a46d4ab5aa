#!/usr/bin/env python3
import psycopg2

def realtime_uom_diagnosis():
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='dataicraft',
            user='odoo',
            password='odoo123'
        )
        cur = conn.cursor()
        
        print("🔍 === Real-Time UoM Diagnosis === 🔍")
        
        # Step 1: Show EXACT current UoM structure
        print("\n📊 EXACT UoM Structure:")
        
        cur.execute("""
            SELECT 
                c.id as cat_id,
                c.name as category,
                u.id as uom_id,
                u.name as uom_name,
                u.uom_type,
                u.factor
            FROM uom_category c
            JOIN uom_uom u ON c.id = u.category_id
            ORDER BY c.name, u.uom_type, u.name;
        """)
        
        uoms = cur.fetchall()
        current_cat = None
        
        for uom in uoms:
            cat_id, category, uom_id, uom_name, uom_type, factor = uom
            
            if category != current_cat:
                current_cat = category
                print(f"\n📂 {category} (ID: {cat_id})")
            
            print(f"  📏 {uom_name} (ID: {uom_id}) - {uom_type} - Factor: {factor}")
        
        # Step 2: Check ALL products and their UoMs
        print(f"\n📦 Product UoM Analysis:")
        
        cur.execute("""
            SELECT 
                pt.id,
                pt.name,
                pt.type,
                pt.service_type,
                u1.name as uom_name,
                u1.id as uom_id,
                c1.name as uom_category,
                u2.name as uom_po_name,
                u2.id as uom_po_id,
                c2.name as uom_po_category
            FROM product_template pt
            LEFT JOIN uom_uom u1 ON pt.uom_id = u1.id
            LEFT JOIN uom_category c1 ON u1.category_id = c1.id
            LEFT JOIN uom_uom u2 ON pt.uom_po_id = u2.id
            LEFT JOIN uom_category c2 ON u2.category_id = c2.id
            ORDER BY pt.name
            LIMIT 10;
        """)
        
        products = cur.fetchall()
        
        print("First 10 products:")
        for product in products:
            pid, name, ptype, service_type, uom_name, uom_id, uom_cat, uom_po_name, uom_po_id, uom_po_cat = product
            print(f"  📦 {name[:30]}")
            print(f"     Type: {ptype} | Service: {service_type}")
            print(f"     UoM: {uom_name} (ID: {uom_id}) - {uom_cat}")
            print(f"     PO UoM: {uom_po_name} (ID: {uom_po_id}) - {uom_po_cat}")
            print()
        
        # Step 3: Check for ANY existing order lines
        print(f"\n📋 Existing Order Lines:")
        
        cur.execute("""
            SELECT 
                sol.id,
                so.name as order_name,
                so.state,
                pp.name as product_name,
                u1.name as line_uom,
                u1.id as line_uom_id,
                c1.name as line_uom_category,
                u2.name as product_uom,
                u2.id as product_uom_id,
                c2.name as product_uom_category
            FROM sale_order_line sol
            JOIN sale_order so ON sol.order_id = so.id
            LEFT JOIN product_product pp ON sol.product_id = pp.id
            LEFT JOIN product_template pt ON pp.product_tmpl_id = pt.id
            LEFT JOIN uom_uom u1 ON sol.product_uom = u1.id
            LEFT JOIN uom_category c1 ON u1.category_id = c1.id
            LEFT JOIN uom_uom u2 ON pt.uom_id = u2.id
            LEFT JOIN uom_category c2 ON u2.category_id = c2.id
            WHERE so.state IN ('draft', 'sent')
            ORDER BY sol.id DESC
            LIMIT 5;
        """)
        
        so_lines = cur.fetchall()
        
        if so_lines:
            print("Recent Sales Order Lines:")
            for line in so_lines:
                line_id, order_name, state, product_name, line_uom, line_uom_id, line_uom_cat, product_uom, product_uom_id, product_uom_cat = line
                
                print(f"  📋 Line {line_id} - Order: {order_name} ({state})")
                print(f"     Product: {product_name}")
                print(f"     Line UoM: {line_uom} (ID: {line_uom_id}) - {line_uom_cat}")
                print(f"     Product UoM: {product_uom} (ID: {product_uom_id}) - {product_uom_cat}")
                
                if line_uom_cat != product_uom_cat:
                    print(f"     🔴 CONFLICT: {line_uom_cat} != {product_uom_cat}")
                else:
                    print(f"     ✅ OK: Categories match")
                print()
        else:
            print("No draft sales order lines found")
        
        # Step 4: Nuclear option - show EVERYTHING that could cause conflicts
        print(f"\n🔍 Potential Conflict Sources:")
        
        # Check for products with different UoM categories
        cur.execute("""
            SELECT 
                pt.name,
                u1.name as uom_name,
                c1.name as uom_category,
                u2.name as po_uom_name,
                c2.name as po_uom_category
            FROM product_template pt
            JOIN uom_uom u1 ON pt.uom_id = u1.id
            JOIN uom_category c1 ON u1.category_id = c1.id
            JOIN uom_uom u2 ON pt.uom_po_id = u2.id
            JOIN uom_category c2 ON u2.category_id = c2.id
            WHERE c1.id != c2.id;
        """)
        
        conflicts = cur.fetchall()
        
        if conflicts:
            print("Products with UoM/PO UoM category conflicts:")
            for conflict in conflicts:
                name, uom_name, uom_cat, po_uom_name, po_uom_cat = conflict
                print(f"  🔴 {name}")
                print(f"     UoM: {uom_name} ({uom_cat})")
                print(f"     PO UoM: {po_uom_name} ({po_uom_cat})")
        else:
            print("✅ No products with UoM category conflicts")
        
        # Step 5: Provide EXACT fix commands
        print(f"\n🔧 EXACT Fix Commands:")
        
        print("1. Clear ALL order lines:")
        print("   DELETE FROM sale_order_line;")
        print("   DELETE FROM purchase_order_line;")
        
        print("\n2. Clear ALL orders:")
        print("   DELETE FROM sale_order;")
        print("   DELETE FROM purchase_order;")
        
        print("\n3. Set ALL products to Units:")
        print("   UPDATE product_template SET uom_id = 1, uom_po_id = 1;")
        
        print("\n4. Remove problematic UoMs:")
        print("   DELETE FROM uom_uom WHERE name LIKE '%Hour%' AND category_id = 1;")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    realtime_uom_diagnosis()
