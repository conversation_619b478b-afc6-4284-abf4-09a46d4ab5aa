.o-mail-Thread:focus-visible {
    outline: 0;
}
.o-mail-Thread-newMessage {
    transition: opacity 0.5s;

    span {
        font-size: 0.8rem;
    }
}

.o_mail_notification {
    & a:hover {
        text-decoration: underline;
    }
    display: inline;
}

.o-mail-Thread-jumpPresent {
    background-color: mix(map-get($theme-colors, 'info'), $o-webclient-background-color, 5%);
    z-index: $o-mail-NavigableList-zIndex - 1;
}

.o-mail-NotificationMessage p {
    margin-bottom: 0;
}

.o-mail-NotificationMessage:has(.o_hide_author) {
    & .o-mail-NotificationMessage-author {
        display: none!important;
    }
}
