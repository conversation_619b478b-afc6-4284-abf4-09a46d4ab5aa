#!/usr/bin/env python3
import psycopg2

def database_uom_fix():
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='dataicraft',
            user='odoo',
            password='odoo123'
        )
        cur = conn.cursor()
        
        print("🔧 === Database-Level UoM Fix === 🔧")
        
        # Step 1: Delete all draft order lines that might have UoM conflicts
        print("\n🗑️ Clearing all problematic order lines...")
        
        cur.execute("DELETE FROM sale_order_line WHERE order_id IN (SELECT id FROM sale_order WHERE state IN ('draft', 'sent'));")
        deleted_so_lines = cur.rowcount
        print(f"✅ Deleted {deleted_so_lines} sales order lines")
        
        cur.execute("DELETE FROM purchase_order_line WHERE order_id IN (SELECT id FROM purchase_order WHERE state IN ('draft', 'sent'));")
        deleted_po_lines = cur.rowcount
        print(f"✅ Deleted {deleted_po_lines} purchase order lines")
        
        # Step 2: Delete draft orders
        cur.execute("DELETE FROM sale_order WHERE state IN ('draft', 'sent');")
        deleted_so = cur.rowcount
        print(f"✅ Deleted {deleted_so} draft sales orders")
        
        cur.execute("DELETE FROM purchase_order WHERE state IN ('draft', 'sent');")
        deleted_po = cur.rowcount
        print(f"✅ Deleted {deleted_po} draft purchase orders")
        
        # Step 3: Set ALL products to use Units UoM (ID: 1)
        print("\n🔧 Setting all products to use Units UoM...")
        
        cur.execute("UPDATE product_template SET uom_id = 1, uom_po_id = 1;")
        updated_products = cur.rowcount
        print(f"✅ Updated {updated_products} products to use Units UoM")
        
        # Step 4: Remove the conflicting Hour UoM from Unit category
        print("\n🗑️ Removing conflicting Hour UoM...")
        
        cur.execute("DELETE FROM uom_uom WHERE name = 'Hour' AND category_id = 1;")
        deleted_hour = cur.rowcount
        print(f"✅ Deleted {deleted_hour} conflicting Hour UoM")
        
        # Step 5: Update company timesheet settings
        print("\n🔧 Updating company settings...")
        
        # Find Hours UoM in Working Time category (ID: 4)
        cur.execute("UPDATE res_company SET timesheet_encode_uom_id = 4;")
        updated_company = cur.rowcount
        print(f"✅ Updated {updated_company} company timesheet settings")
        
        # Step 6: Verify the fix
        print("\n✅ Verifying the fix...")
        
        cur.execute("SELECT COUNT(*) FROM product_template WHERE uom_id != 1;")
        non_units_products = cur.fetchone()[0]
        print(f"Products not using Units UoM: {non_units_products}")
        
        cur.execute("SELECT COUNT(*) FROM sale_order WHERE state IN ('draft', 'sent');")
        remaining_so = cur.fetchone()[0]
        print(f"Remaining draft sales orders: {remaining_so}")
        
        cur.execute("SELECT COUNT(*) FROM purchase_order WHERE state IN ('draft', 'sent');")
        remaining_po = cur.fetchone()[0]
        print(f"Remaining draft purchase orders: {remaining_po}")
        
        # Commit all changes
        conn.commit()
        
        print(f"\n🎉 Database UoM Fix Complete!")
        print(f"✅ All products now use Units UoM")
        print(f"✅ All conflicting orders removed")
        print(f"✅ Conflicting Hour UoM removed")
        print(f"✅ Company settings updated")
        
        print(f"\n📋 Current State:")
        print(f"  📦 ALL products → Units UoM (Unit category)")
        print(f"  ⏰ Timesheet encoding → Hours UoM (Working Time category)")
        print(f"  🚫 No UoM category conflicts")
        
        print(f"\n🔄 Next Steps:")
        print("1. Refresh your browser (F5)")
        print("2. Try creating a new order")
        print("3. All products will use 'Units' UoM")
        print("4. The UoM error should be completely resolved")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    database_uom_fix()
