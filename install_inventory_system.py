#!/usr/bin/env python3
import xmlrpc.client
import time

def install_inventory_system():
    try:
        # Connection details
        url = 'http://localhost:8069'
        db = 'dataicraft'
        username = '<EMAIL>'
        password = 'admin123'
        
        # Connect to Odoo
        common = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/common')
        uid = common.authenticate(db, username, password, {})
        
        if not uid:
            print("❌ Authentication failed!")
            return
        
        print(f"✅ Connected to Odoo as user ID: {uid}")
        
        models = xmlrpc.client.ServerProxy(f'{url}/xmlrpc/2/object')
        
        print("\n=== Installing Complete Inventory Management System ===")
        
        # Core inventory modules in installation order
        inventory_modules = [
            'stock',                    # Core Inventory/Warehouse Management
            'stock_account',           # Inventory Accounting Integration
            'sale_stock',              # Sales & Inventory Integration
            'purchase_stock',          # Purchase & Inventory Integration
            'delivery',                # Delivery Methods
            'stock_picking_batch',     # Batch Picking
            'stock_landed_costs',      # Landed Costs
            'stock_dropshipping',      # Drop Shipping
            'mrp',                     # Manufacturing (if available)
        ]
        
        installed_modules = []
        
        for module_name in inventory_modules:
            try:
                print(f"\n📦 Processing {module_name}...")
                
                # Search for the module
                module_ids = models.execute_kw(
                    db, uid, password,
                    'ir.module.module', 'search',
                    [[['name', '=', module_name]]]
                )
                
                if not module_ids:
                    print(f"   ❌ Module not found: {module_name}")
                    continue
                
                # Get module info
                module_info = models.execute_kw(
                    db, uid, password,
                    'ir.module.module', 'read',
                    [module_ids], {'fields': ['name', 'state', 'shortdesc']}
                )
                
                module = module_info[0]
                state = module['state']
                desc = module['shortdesc']
                
                print(f"   📋 {desc}")
                print(f"   📊 Current state: {state}")
                
                if state == 'installed':
                    print(f"   ✅ Already installed")
                    installed_modules.append(module_name)
                elif state == 'uninstalled':
                    print(f"   🔄 Installing...")
                    
                    # Install the module
                    models.execute_kw(
                        db, uid, password,
                        'ir.module.module', 'button_immediate_install',
                        [module_ids]
                    )
                    
                    print(f"   ✅ Installation triggered")
                    installed_modules.append(module_name)
                    
                    # Wait a bit for installation to process
                    time.sleep(2)
                    
                elif state == 'uninstallable':
                    print(f"   ⚠️ Not available in Community Edition")
                else:
                    print(f"   ⚠️ State: {state}")
                    
            except Exception as e:
                print(f"   ❌ Error installing {module_name}: {e}")
        
        print(f"\n=== Installation Summary ===")
        print(f"✅ Successfully processed {len(installed_modules)} modules:")
        for module in installed_modules:
            print(f"   ✅ {module}")
        
        print(f"\n=== Configuring Inventory Settings ===")
        
        # Enable inventory features in company settings
        try:
            company_ids = models.execute_kw(
                db, uid, password,
                'res.company', 'search', [[]]
            )
            
            if company_ids:
                company_settings = {
                    'anglo_saxon_accounting': True,  # Important for inventory accounting
                }
                
                models.execute_kw(
                    db, uid, password,
                    'res.company', 'write',
                    [company_ids, company_settings]
                )
                print("✅ Company inventory settings updated")
        
        except Exception as e:
            print(f"⚠️ Company settings error: {e}")
        
        print(f"\n=== Creating Inventory Menu Structure ===")
        
        # The inventory module should create its own menus, but let's verify
        try:
            # Check if Inventory app menu exists
            inventory_menu_ids = models.execute_kw(
                db, uid, password,
                'ir.ui.menu', 'search',
                [[['name', 'ilike', 'Inventory'], ['parent_id', '=', False]]]
            )
            
            if inventory_menu_ids:
                print(f"✅ Inventory app menu found (ID: {inventory_menu_ids[0]})")
            else:
                print("⚠️ Inventory app menu not found - may need server restart")
        
        except Exception as e:
            print(f"⚠️ Menu check error: {e}")
        
        print(f"\n🎉 INVENTORY SYSTEM INSTALLATION COMPLETE! 🎉")
        print(f"\n📦 What You Now Have:")
        print("   ✅ Complete Warehouse Management")
        print("   ✅ Stock Movements & Transfers")
        print("   ✅ Inventory Valuation & Accounting")
        print("   ✅ Sales & Purchase Integration")
        print("   ✅ Multi-Location Management")
        print("   ✅ Barcode Scanning Support")
        print("   ✅ Inventory Reports & Analytics")
        print("   ✅ Automated Reordering Rules")
        print("   ✅ Lot & Serial Number Tracking")
        print("   ✅ Delivery Methods & Shipping")
        
        print(f"\n🔄 Next Steps:")
        print("1. 🔄 Restart Odoo server (recommended)")
        print("2. 🌐 Refresh your browser (F5)")
        print("3. 🔍 Look for 'Inventory' app on dashboard")
        print("4. 📊 Check Invoicing → Configuration for inventory settings")
        print("5. 🏭 Start creating products and managing stock!")
        
        print(f"\n📋 Inventory App Menu Structure:")
        print("   📦 INVENTORY APP")
        print("   ├── 📊 Dashboard (Stock overview)")
        print("   ├── 📦 Products")
        print("   │   ├── Products")
        print("   │   ├── Product Categories")
        print("   │   └── Product Variants")
        print("   ├── 🏭 Operations")
        print("   │   ├── Transfers")
        print("   │   ├── Receipts")
        print("   │   ├── Delivery Orders")
        print("   │   └── Internal Transfers")
        print("   ├── 📊 Reporting")
        print("   │   ├── Inventory Valuation")
        print("   │   ├── Stock Moves")
        print("   │   └── Inventory Analysis")
        print("   └── ⚙️ Configuration")
        print("       ├── Warehouses")
        print("       ├── Locations")
        print("       ├── Routes")
        print("       └── Reordering Rules")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    install_inventory_system()
