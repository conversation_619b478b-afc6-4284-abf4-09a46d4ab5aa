#!/usr/bin/env python3
import psycopg2

def fix_user_authentication():
    try:
        conn = psycopg2.connect(
            host='localhost',
            database='dataicraft',
            user='odoo',
            password='odoo123'
        )
        cur = conn.cursor()
        
        print("🔧 === FIXING USER AUTHENTICATION === 🔧")
        
        # Step 1: Check current users
        print("\n👤 Step 1: Checking current users...")
        
        cur.execute("SELECT id, login, active FROM res_users ORDER BY id;")
        users = cur.fetchall()
        
        print(f"Current users:")
        for user in users:
            user_id, login, active = user
            print(f"  👤 ID: {user_id}, Login: {login}, Active: {active}")
        
        # Step 2: Check if admin user exists
        cur.execute("SELECT id FROM res_users WHERE login = '<EMAIL>';")
        admin_user = cur.fetchone()
        
        if admin_user:
            admin_id = admin_user[0]
            print(f"✅ Admin user exists with ID: {admin_id}")
            
            # Ensure admin user is active
            cur.execute("UPDATE res_users SET active = true WHERE id = %s;", (admin_id,))
            print(f"✅ Admin user activated")
            
        else:
            print("❌ Admin user not found, creating new one...")
            
            # Create admin user
            cur.execute("""
                INSERT INTO res_users (login, password, active, company_id, partner_id)
                VALUES ('<EMAIL>', 'admin123', true, 1, 1)
                RETURNING id;
            """)
            admin_id = cur.fetchone()[0]
            print(f"✅ Created admin user with ID: {admin_id}")
        
        # Step 3: Ensure admin has proper groups
        print("\n🔐 Step 2: Setting admin permissions...")
        
        # Get admin group IDs
        cur.execute("""
            SELECT id FROM res_groups 
            WHERE name LIKE '%Admin%' OR name LIKE '%Settings%' 
            OR category_id IN (SELECT id FROM ir_module_category WHERE name = 'Administration');
        """)
        admin_groups = cur.fetchall()
        
        # Clear existing group memberships for admin
        cur.execute("DELETE FROM res_groups_users_rel WHERE uid = %s;", (admin_id,))
        
        # Add admin to all admin groups
        for group in admin_groups:
            group_id = group[0]
            cur.execute("""
                INSERT INTO res_groups_users_rel (gid, uid) 
                VALUES (%s, %s) 
                ON CONFLICT DO NOTHING;
            """, (group_id, admin_id))
        
        print(f"✅ Added admin to {len(admin_groups)} admin groups")
        
        # Step 4: Clear user sessions
        print("\n🧹 Step 3: Clearing user sessions...")
        
        cur.execute("DELETE FROM ir_sessions;")
        deleted_sessions = cur.rowcount
        print(f"✅ Cleared {deleted_sessions} user sessions")
        
        # Step 5: Reset admin password (ensure it's properly hashed)
        print("\n🔑 Step 4: Resetting admin password...")
        
        # Use Odoo's password hashing
        import hashlib
        password = 'admin123'
        password_hash = hashlib.sha1(password.encode()).hexdigest()
        
        cur.execute("UPDATE res_users SET password = %s WHERE id = %s;", (password_hash, admin_id))
        print(f"✅ Reset admin password")
        
        # Step 6: Ensure company and partner exist
        print("\n🏢 Step 5: Checking company and partner...")
        
        cur.execute("SELECT COUNT(*) FROM res_company;")
        company_count = cur.fetchone()[0]
        print(f"Companies: {company_count}")
        
        cur.execute("SELECT COUNT(*) FROM res_partner WHERE id = 1;")
        main_partner = cur.fetchone()[0]
        print(f"Main partner exists: {main_partner}")
        
        if main_partner == 0:
            # Create main partner
            cur.execute("""
                INSERT INTO res_partner (id, name, is_company, supplier_rank, customer_rank)
                VALUES (1, 'Dataicraft (Pvt.) Ltd', true, 0, 0);
            """)
            print(f"✅ Created main partner")
        
        # Commit changes
        conn.commit()
        
        print(f"\n🎉 USER AUTHENTICATION FIXED!")
        
        # Final verification
        print(f"\n✅ Final Status:")
        cur.execute("SELECT id, login, active FROM res_users WHERE login = '<EMAIL>';")
        admin_check = cur.fetchone()
        
        if admin_check:
            print(f"✅ Admin user: ID {admin_check[0]}, Login: {admin_check[1]}, Active: {admin_check[2]}")
        else:
            print(f"❌ Admin user still not found")
        
        cur.execute("SELECT COUNT(*) FROM res_groups_users_rel WHERE uid = %s;", (admin_id,))
        group_count = cur.fetchone()[0]
        print(f"✅ Admin has {group_count} group memberships")
        
        print(f"\n🔄 Next Steps:")
        print("1. Restart Odoo server")
        print("2. Clear browser cache completely")
        print("3. Go to http://localhost:8069")
        print("4. Login with: <EMAIL> / admin123")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ Error: {e}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()

if __name__ == "__main__":
    fix_user_authentication()
