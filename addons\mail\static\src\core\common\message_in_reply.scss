.o-mail-MessageInReply-corner {
    &.o-isLeftAlign {
        left: $o-mail-Message-sidebarWidth / 2;
        border-radius: $o-RoundedRectangle-small 0 0 0;
    }

    &.o-isRightAlign {
        right: $o-mail-Message-sidebarWidth / 2;
        border-radius: 0 $o-RoundedRectangle-small 0 0;
    }
}

.o-mail-MessageInReply-avatar {
    width: $o-mail-Avatar-size / 2;
    height: $o-mail-Avatar-size / 2;
}

.o-mail-MessageInReply-content {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
}

.o-mail-MessageInReply-message {
    // Make the body single line when possible
    p, div {
        display: inline;
        margin: 0;
    }

    br {
        display: none;
    }
}

