"""
Django Admin Configuration for Accounting Module
"""
from django.contrib import admin
from django.utils.html import format_html
from .models import (
    ResCompany, ResPartner, AccountAccount, AccountJournal
)


@admin.register(ResCompany)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ['name', 'vat', 'currency_id', 'create_date']
    search_fields = ['name', 'vat']
    list_filter = ['currency_id', 'create_date']


@admin.register(ResPartner)
class PartnerAdmin(admin.ModelAdmin):
    list_display = ['name', 'email', 'phone', 'is_customer', 'is_vendor', 'active']
    list_filter = ['active', 'country_id', 'company_id', 'customer_rank', 'supplier_rank']
    search_fields = ['name', 'email', 'vat']
    ordering = ['name']


@admin.register(AccountAccount)
class AccountAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'account_type', 'balance_display', 'reconcile', 'deprecated']
    list_filter = ['account_type', 'reconcile', 'deprecated', 'company_id']
    search_fields = ['code', 'name']
    ordering = ['code']

    def balance_display(self, obj):
        balance = obj.balance
        color = 'green' if balance >= 0 else 'red'
        return format_html(
            '<span style="color: {};">{:,.2f}</span>',
            color, balance
        )
    balance_display.short_description = 'Balance'


@admin.register(AccountJournal)
class JournalAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'type', 'active', 'sequence']
    list_filter = ['type', 'active', 'company_id']
    search_fields = ['code', 'name']
    ordering = ['sequence', 'code']


# Customize admin site
admin.site.site_header = "ERP Accounting System"
admin.site.site_title = "ERP Admin"
admin.site.index_title = "Accounting & Finance Management"
