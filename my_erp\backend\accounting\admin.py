"""
Django Admin Configuration for Accounting Module
"""
from django.contrib import admin
from django.utils.html import format_html
from .models import (
    Company, AccountType, Account, Customer, Vendor, TaxRate,
    Journal, JournalEntry, JournalEntryLine, Invoice, InvoiceLine,
    Bill, Payment
)


@admin.register(Company)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ['name', 'tax_id', 'currency', 'created_at']
    search_fields = ['name', 'tax_id']
    list_filter = ['currency', 'created_at']


@admin.register(AccountType)
class AccountTypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'type', 'is_active']
    list_filter = ['type', 'is_active']
    search_fields = ['name']


@admin.register(Account)
class AccountAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'account_type', 'balance_display', 'is_active']
    list_filter = ['account_type', 'is_active', 'company']
    search_fields = ['code', 'name']
    ordering = ['code']
    
    def balance_display(self, obj):
        balance = obj.balance
        color = 'green' if balance >= 0 else 'red'
        return format_html(
            '<span style="color: {};">{:,.2f}</span>',
            color, balance
        )
    balance_display.short_description = 'Balance'


@admin.register(Customer)
class CustomerAdmin(admin.ModelAdmin):
    list_display = ['name', 'email', 'phone', 'outstanding_balance_display', 'is_active']
    list_filter = ['is_active', 'country', 'company']
    search_fields = ['name', 'email', 'company_name']
    
    def outstanding_balance_display(self, obj):
        balance = obj.outstanding_balance
        color = 'red' if balance > 0 else 'green'
        return format_html(
            '<span style="color: {};">{:,.2f}</span>',
            color, balance
        )
    outstanding_balance_display.short_description = 'Outstanding'


@admin.register(Vendor)
class VendorAdmin(admin.ModelAdmin):
    list_display = ['name', 'email', 'phone', 'outstanding_balance_display', 'is_active']
    list_filter = ['is_active', 'country', 'company']
    search_fields = ['name', 'email', 'company_name']
    
    def outstanding_balance_display(self, obj):
        balance = obj.outstanding_balance
        color = 'red' if balance > 0 else 'green'
        return format_html(
            '<span style="color: {};">{:,.2f}</span>',
            color, balance
        )
    outstanding_balance_display.short_description = 'Outstanding'


@admin.register(TaxRate)
class TaxRateAdmin(admin.ModelAdmin):
    list_display = ['name', 'rate', 'is_active']
    list_filter = ['is_active', 'company']
    search_fields = ['name']


@admin.register(Journal)
class JournalAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'type', 'is_active']
    list_filter = ['type', 'is_active', 'company']
    search_fields = ['code', 'name']


class JournalEntryLineInline(admin.TabularInline):
    model = JournalEntryLine
    extra = 2
    fields = ['account', 'description', 'debit_amount', 'credit_amount', 'partner']


@admin.register(JournalEntry)
class JournalEntryAdmin(admin.ModelAdmin):
    list_display = ['reference', 'date', 'journal', 'total_debit', 'total_credit', 'state', 'is_balanced']
    list_filter = ['state', 'journal', 'date', 'company']
    search_fields = ['reference', 'description']
    inlines = [JournalEntryLineInline]
    readonly_fields = ['total_debit', 'total_credit', 'is_balanced']
    
    def get_readonly_fields(self, request, obj=None):
        if obj and obj.state == 'POSTED':
            return self.readonly_fields + ['reference', 'journal', 'date', 'description']
        return self.readonly_fields


class InvoiceLineInline(admin.TabularInline):
    model = InvoiceLine
    extra = 1
    fields = ['description', 'quantity', 'unit_price', 'tax_rate', 'line_total']
    readonly_fields = ['line_total']


@admin.register(Invoice)
class InvoiceAdmin(admin.ModelAdmin):
    list_display = ['number', 'customer', 'date', 'due_date', 'total_amount', 'outstanding_amount', 'status', 'is_overdue']
    list_filter = ['status', 'date', 'due_date', 'company']
    search_fields = ['number', 'customer__name']
    inlines = [InvoiceLineInline]
    readonly_fields = ['outstanding_amount', 'is_overdue']
    
    def is_overdue(self, obj):
        if obj.is_overdue:
            return format_html('<span style="color: red;">Yes</span>')
        return format_html('<span style="color: green;">No</span>')
    is_overdue.short_description = 'Overdue'


@admin.register(Bill)
class BillAdmin(admin.ModelAdmin):
    list_display = ['number', 'vendor', 'date', 'due_date', 'total_amount', 'outstanding_amount', 'status']
    list_filter = ['status', 'date', 'due_date', 'company']
    search_fields = ['number', 'vendor__name', 'vendor_reference']
    readonly_fields = ['outstanding_amount']


@admin.register(Payment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ['reference', 'type', 'get_partner', 'amount', 'date', 'method']
    list_filter = ['type', 'method', 'date', 'company']
    search_fields = ['reference', 'customer__name', 'vendor__name']
    
    def get_partner(self, obj):
        if obj.customer:
            return obj.customer.name
        elif obj.vendor:
            return obj.vendor.name
        return '-'
    get_partner.short_description = 'Partner'


# Customize admin site
admin.site.site_header = "ERP Accounting System"
admin.site.site_title = "ERP Admin"
admin.site.index_title = "Accounting & Finance Management"
