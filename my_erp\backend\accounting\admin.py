"""
Django Admin Configuration for Accounting Module
"""
from django.contrib import admin
from django.utils.html import format_html
from .models import (
    ResCompany, ResPartner, AccountAccount, AccountJournal,
    AccountTax, AccountMove, AccountMoveLine, AccountPayment,
    AccountPaymentTerm, AccountFiscalPosition, AccountAnalyticAccount,
    AccountBankStatement, AccountBankStatementLine, AccountAsset, AccountAssetCategory,
    AccountReconcileModel, CrossoveredBudget, CrossoveredBudgetLines,
    ProductTemplate, ProductProduct, ProductCategory, SaleOrder, SaleOrderLine,
    PurchaseOrder, PurchaseOrderLine, StockLocation, StockWarehouse, StockMove,
    IrSequence, MailMessage, ResCountry, ResCurrency
)


@admin.register(ResCompany)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ['name', 'vat', 'currency_id', 'create_date']
    search_fields = ['name', 'vat']
    list_filter = ['currency_id', 'create_date']


@admin.register(ResPartner)
class PartnerAdmin(admin.ModelAdmin):
    list_display = ['name', 'email', 'phone', 'is_customer', 'is_vendor', 'active']
    list_filter = ['active', 'country_id', 'company_id', 'customer_rank', 'supplier_rank']
    search_fields = ['name', 'email', 'vat']
    ordering = ['name']


@admin.register(AccountAccount)
class AccountAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'account_type', 'balance_display', 'reconcile', 'deprecated']
    list_filter = ['account_type', 'reconcile', 'deprecated', 'company_id']
    search_fields = ['code', 'name']
    ordering = ['code']

    def balance_display(self, obj):
        balance = obj.balance
        color = 'green' if balance >= 0 else 'red'
        return format_html(
            '<span style="color: {};">{}</span>',
            color, f"{balance:,.2f}"
        )
    balance_display.short_description = 'Balance'


@admin.register(AccountJournal)
class JournalAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'type', 'active', 'sequence']
    list_filter = ['type', 'active', 'company_id']
    search_fields = ['code', 'name']
    ordering = ['sequence', 'code']


@admin.register(AccountTax)
class TaxAdmin(admin.ModelAdmin):
    list_display = ['name', 'type_tax_use', 'amount_type', 'amount', 'active']
    list_filter = ['type_tax_use', 'amount_type', 'active', 'company_id']
    search_fields = ['name', 'description']


class AccountMoveLineInline(admin.TabularInline):
    model = AccountMoveLine
    extra = 2
    fields = ['account_id', 'name', 'debit', 'credit', 'partner_id']
    readonly_fields = ['balance']


@admin.register(AccountMove)
class MoveAdmin(admin.ModelAdmin):
    list_display = ['name', 'date', 'journal_id', 'partner_id', 'move_type', 'state', 'amount_total']
    list_filter = ['move_type', 'state', 'journal_id', 'date', 'company_id']
    search_fields = ['name', 'ref', 'partner_id__name']
    inlines = [AccountMoveLineInline]
    readonly_fields = ['amount_untaxed', 'amount_tax', 'amount_total']
    ordering = ['-date', '-name']


@admin.register(AccountMoveLine)
class MoveLineAdmin(admin.ModelAdmin):
    list_display = ['move_id', 'account_id', 'name', 'debit', 'credit', 'balance', 'partner_id', 'reconciled']
    list_filter = ['reconciled', 'account_id__account_type', 'date', 'company_id']
    search_fields = ['name', 'ref', 'move_id__name', 'partner_id__name']
    ordering = ['-date', '-move_id']


@admin.register(AccountPayment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ['name', 'date', 'partner_id', 'payment_type', 'amount', 'state']
    list_filter = ['payment_type', 'partner_type', 'state', 'date', 'company_id']
    search_fields = ['name', 'ref', 'partner_id__name']
    ordering = ['-date', '-name']


@admin.register(AccountPaymentTerm)
class PaymentTermAdmin(admin.ModelAdmin):
    list_display = ['name', 'active', 'sequence']
    list_filter = ['active', 'company_id']
    search_fields = ['name']
    ordering = ['sequence', 'name']


@admin.register(AccountFiscalPosition)
class FiscalPositionAdmin(admin.ModelAdmin):
    list_display = ['name', 'active', 'auto_apply', 'vat_required', 'sequence']
    list_filter = ['active', 'auto_apply', 'vat_required', 'company_id']
    search_fields = ['name']
    ordering = ['sequence', 'name']


@admin.register(AccountAnalyticAccount)
class AnalyticAccountAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'partner_id', 'active']
    list_filter = ['active', 'company_id']
    search_fields = ['name', 'code', 'partner_id__name']
    ordering = ['name']


# ============================================================================
# BUSINESS PROCESSES & WORKFLOWS
# ============================================================================

@admin.register(AccountBankStatement)
class BankStatementAdmin(admin.ModelAdmin):
    list_display = ['name', 'date', 'journal_id', 'balance_start', 'balance_end_real', 'state']
    list_filter = ['state', 'journal_id', 'date', 'company_id']
    search_fields = ['name', 'reference']
    ordering = ['-date']


@admin.register(AccountBankStatementLine)
class BankStatementLineAdmin(admin.ModelAdmin):
    list_display = ['statement_id', 'date', 'name', 'amount', 'partner_id', 'is_reconciled']
    list_filter = ['is_reconciled', 'date', 'statement_id__journal_id']
    search_fields = ['name', 'ref', 'partner_name']
    ordering = ['-date']


@admin.register(AccountAsset)
class AssetAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'category_id', 'value', 'value_residual', 'state']
    list_filter = ['state', 'category_id', 'active', 'company_id']
    search_fields = ['name', 'code']
    ordering = ['name']


@admin.register(AccountAssetCategory)
class AssetCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'type', 'method', 'method_number', 'active']
    list_filter = ['type', 'method', 'active', 'company_id']
    search_fields = ['name']
    ordering = ['name']


@admin.register(AccountReconcileModel)
class ReconcileModelAdmin(admin.ModelAdmin):
    list_display = ['name', 'rule_type', 'sequence', 'auto_reconcile']
    list_filter = ['rule_type', 'auto_reconcile', 'company_id']
    search_fields = ['name']
    ordering = ['sequence', 'name']


@admin.register(IrSequence)
class SequenceAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'prefix', 'number_next', 'active']
    list_filter = ['active', 'implementation', 'company_id']
    search_fields = ['name', 'code']
    ordering = ['name']


# ============================================================================
# BUDGET MANAGEMENT
# ============================================================================

@admin.register(CrossoveredBudget)
class BudgetAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'date_from', 'date_to', 'state', 'user_id']
    list_filter = ['state', 'date_from', 'date_to', 'company_id']
    search_fields = ['name', 'code']
    ordering = ['-date_from']


@admin.register(CrossoveredBudgetLines)
class BudgetLineAdmin(admin.ModelAdmin):
    list_display = ['crossovered_budget_id', 'general_budget_id', 'planned_amount', 'practical_amount', 'percentage']
    list_filter = ['crossovered_budget_id', 'general_budget_id', 'company_id']
    search_fields = ['crossovered_budget_id__name', 'general_budget_id__name']


# ============================================================================
# PRODUCT & SALES
# ============================================================================

@admin.register(ProductCategory)
class ProductCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'complete_name', 'parent_id', 'sequence']
    list_filter = ['parent_id']
    search_fields = ['name', 'complete_name']
    ordering = ['sequence', 'name']


@admin.register(ProductTemplate)
class ProductTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'type', 'categ_id', 'list_price', 'standard_price', 'sale_ok', 'purchase_ok']
    list_filter = ['type', 'categ_id', 'sale_ok', 'purchase_ok', 'active']
    search_fields = ['name', 'description']
    ordering = ['name']


@admin.register(ProductProduct)
class ProductProductAdmin(admin.ModelAdmin):
    list_display = ['name', 'default_code', 'barcode', 'active']
    list_filter = ['active', 'product_tmpl_id__categ_id']
    search_fields = ['product_tmpl_id__name', 'default_code', 'barcode']
    ordering = ['product_tmpl_id__name']


class SaleOrderLineInline(admin.TabularInline):
    model = SaleOrderLine
    extra = 1
    fields = ['product_id', 'name', 'product_uom_qty', 'price_unit', 'price_subtotal']
    readonly_fields = ['price_subtotal']


@admin.register(SaleOrder)
class SaleOrderAdmin(admin.ModelAdmin):
    list_display = ['name', 'date_order', 'partner_id', 'state', 'amount_total']
    list_filter = ['state', 'date_order', 'company_id']
    search_fields = ['name', 'partner_id__name', 'client_order_ref']
    inlines = [SaleOrderLineInline]
    ordering = ['-date_order']


class PurchaseOrderLineInline(admin.TabularInline):
    model = PurchaseOrderLine
    extra = 1
    fields = ['product_id', 'name', 'product_qty', 'price_unit', 'price_subtotal']
    readonly_fields = ['price_subtotal']


@admin.register(PurchaseOrder)
class PurchaseOrderAdmin(admin.ModelAdmin):
    list_display = ['name', 'date_order', 'partner_id', 'state', 'amount_total']
    list_filter = ['state', 'date_order', 'company_id']
    search_fields = ['name', 'partner_id__name', 'partner_ref']
    inlines = [PurchaseOrderLineInline]
    ordering = ['-date_order']


# ============================================================================
# INVENTORY
# ============================================================================

@admin.register(StockLocation)
class StockLocationAdmin(admin.ModelAdmin):
    list_display = ['name', 'complete_name', 'usage', 'active']
    list_filter = ['usage', 'active', 'company_id']
    search_fields = ['name', 'complete_name']
    ordering = ['complete_name']


@admin.register(StockWarehouse)
class StockWarehouseAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'partner_id', 'active']
    list_filter = ['active', 'company_id']
    search_fields = ['name', 'code']
    ordering = ['sequence', 'name']


@admin.register(StockMove)
class StockMoveAdmin(admin.ModelAdmin):
    list_display = ['name', 'product_id', 'product_qty', 'location_id', 'location_dest_id', 'state', 'date']
    list_filter = ['state', 'date', 'product_id', 'company_id']
    search_fields = ['name', 'product_id__product_tmpl_id__name', 'origin']
    ordering = ['-date']


# ============================================================================
# SYSTEM & LOCALIZATION
# ============================================================================

@admin.register(ResCountry)
class CountryAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'phone_code', 'currency_id']
    search_fields = ['name', 'code']
    ordering = ['name']


@admin.register(ResCurrency)
class CurrencyAdmin(admin.ModelAdmin):
    list_display = ['name', 'symbol', 'rate', 'decimal_places', 'active']
    list_filter = ['active']
    search_fields = ['name', 'symbol']
    ordering = ['name']


@admin.register(MailMessage)
class MailMessageAdmin(admin.ModelAdmin):
    list_display = ['subject', 'date', 'message_type', 'author_id', 'model', 'res_id']
    list_filter = ['message_type', 'date', 'model']
    search_fields = ['subject', 'body', 'author_id__name']
    ordering = ['-date']


# Customize admin site
admin.site.site_header = "🚀 Complete ERP System - 50+ Odoo Models"
admin.site.site_title = "ERP Admin"
admin.site.index_title = "Complete Accounting & Business Management"
