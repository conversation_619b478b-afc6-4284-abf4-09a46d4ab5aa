"""
Django Admin Configuration for Accounting Module
"""
from django.contrib import admin
from django.utils.html import format_html
from .models import (
    # Core Accounting Models
    ResCompany, ResPartner, AccountAccount, AccountJournal,
    AccountTax, AccountTaxRepartitionLine, AccountMove, AccountMoveLine,
    AccountFullReconcile, AccountPartialReconcile, AccountPayment,
    AccountPaymentTerm, AccountPaymentTermLine, AccountFiscalPosition,
    AccountFiscalPositionTax, AccountFiscalPositionAccount,
    AccountAnalyticAccount, AccountAnalyticLine,

    # Business Process Models
    IrSequence, AccountReconcileModel, AccountBankStatement, AccountBankStatementLine,
    AccountAsset, AccountAssetCategory, AccountAssetDepreciationLine,
    AccountBudgetPost, CrossoveredBudget, CrossoveredBudgetLines,
    IrCron, AccountAutomaticEntryWizard,

    # Reporting Models
    AccountInvoiceReport, AccountMoveLineReport, AccountAgedTrialBalance,
    AccountFinancialReport,

    # Integration Models
    ProductTemplate, ProductProduct, ProductCategory,
    StockLocation, StockWarehouse, StockMove,

    # System Models
    MailMessage, MailMessageSubtype, MailNotification, MailTrackingValue,
    ResCountry, ResCountryState, ResCurrency
)


@admin.register(ResCompany)
class CompanyAdmin(admin.ModelAdmin):
    list_display = ['name', 'vat', 'currency_id', 'create_date']
    search_fields = ['name', 'vat']
    list_filter = ['currency_id', 'create_date']


@admin.register(ResPartner)
class PartnerAdmin(admin.ModelAdmin):
    list_display = ['name', 'email', 'phone', 'is_customer', 'is_vendor', 'active']
    list_filter = ['active', 'country_id', 'company_id', 'customer_rank', 'supplier_rank']
    search_fields = ['name', 'email', 'vat']
    ordering = ['name']


@admin.register(AccountAccount)
class AccountAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'account_type', 'balance_display', 'reconcile', 'deprecated']
    list_filter = ['account_type', 'reconcile', 'deprecated', 'company_id']
    search_fields = ['code', 'name']
    ordering = ['code']

    def balance_display(self, obj):
        balance = obj.balance
        color = 'green' if balance >= 0 else 'red'
        return format_html(
            '<span style="color: {};">{}</span>',
            color, f"{balance:,.2f}"
        )
    balance_display.short_description = 'Balance'


@admin.register(AccountJournal)
class JournalAdmin(admin.ModelAdmin):
    list_display = ['code', 'name', 'type', 'active', 'sequence']
    list_filter = ['type', 'active', 'company_id']
    search_fields = ['code', 'name']
    ordering = ['sequence', 'code']


@admin.register(AccountTax)
class TaxAdmin(admin.ModelAdmin):
    list_display = ['name', 'type_tax_use', 'amount_type', 'amount', 'active']
    list_filter = ['type_tax_use', 'amount_type', 'active', 'company_id']
    search_fields = ['name', 'description']


class AccountMoveLineInline(admin.TabularInline):
    model = AccountMoveLine
    extra = 2
    fields = ['account_id', 'name', 'debit', 'credit', 'partner_id']
    readonly_fields = ['balance']


@admin.register(AccountMove)
class MoveAdmin(admin.ModelAdmin):
    list_display = ['name', 'date', 'journal_id', 'partner_id', 'move_type', 'state', 'amount_total']
    list_filter = ['move_type', 'state', 'journal_id', 'date', 'company_id']
    search_fields = ['name', 'ref', 'partner_id__name']
    inlines = [AccountMoveLineInline]
    readonly_fields = ['amount_untaxed', 'amount_tax', 'amount_total']
    ordering = ['-date', '-name']


@admin.register(AccountMoveLine)
class MoveLineAdmin(admin.ModelAdmin):
    list_display = ['move_id', 'account_id', 'name', 'debit', 'credit', 'balance', 'partner_id', 'reconciled']
    list_filter = ['reconciled', 'account_id__account_type', 'date', 'company_id']
    search_fields = ['name', 'ref', 'move_id__name', 'partner_id__name']
    ordering = ['-date', '-move_id']


@admin.register(AccountPayment)
class PaymentAdmin(admin.ModelAdmin):
    list_display = ['name', 'date', 'partner_id', 'payment_type', 'amount', 'state']
    list_filter = ['payment_type', 'partner_type', 'state', 'date', 'company_id']
    search_fields = ['name', 'ref', 'partner_id__name']
    ordering = ['-date', '-name']


@admin.register(AccountPaymentTerm)
class PaymentTermAdmin(admin.ModelAdmin):
    list_display = ['name', 'active', 'sequence']
    list_filter = ['active', 'company_id']
    search_fields = ['name']
    ordering = ['sequence', 'name']


@admin.register(AccountFiscalPosition)
class FiscalPositionAdmin(admin.ModelAdmin):
    list_display = ['name', 'active', 'auto_apply', 'vat_required', 'sequence']
    list_filter = ['active', 'auto_apply', 'vat_required', 'company_id']
    search_fields = ['name']
    ordering = ['sequence', 'name']


@admin.register(AccountAnalyticAccount)
class AnalyticAccountAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'partner_id', 'active']
    list_filter = ['active', 'company_id']
    search_fields = ['name', 'code', 'partner_id__name']
    ordering = ['name']


# ============================================================================
# BUSINESS PROCESSES & WORKFLOWS
# ============================================================================

@admin.register(AccountBankStatement)
class BankStatementAdmin(admin.ModelAdmin):
    list_display = ['name', 'date', 'journal_id', 'balance_start', 'balance_end_real', 'state']
    list_filter = ['state', 'journal_id', 'date', 'company_id']
    search_fields = ['name', 'reference']
    ordering = ['-date']


@admin.register(AccountBankStatementLine)
class BankStatementLineAdmin(admin.ModelAdmin):
    list_display = ['statement_id', 'date', 'name', 'amount', 'partner_id', 'is_reconciled']
    list_filter = ['is_reconciled', 'date', 'statement_id__journal_id']
    search_fields = ['name', 'ref', 'partner_name']
    ordering = ['-date']


@admin.register(AccountAsset)
class AssetAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'category_id', 'value', 'value_residual', 'state']
    list_filter = ['state', 'category_id', 'active', 'company_id']
    search_fields = ['name', 'code']
    ordering = ['name']


@admin.register(AccountAssetCategory)
class AssetCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'type', 'method', 'method_number', 'active']
    list_filter = ['type', 'method', 'active', 'company_id']
    search_fields = ['name']
    ordering = ['name']


@admin.register(AccountReconcileModel)
class ReconcileModelAdmin(admin.ModelAdmin):
    list_display = ['name', 'rule_type', 'sequence', 'auto_reconcile']
    list_filter = ['rule_type', 'auto_reconcile', 'company_id']
    search_fields = ['name']
    ordering = ['sequence', 'name']


@admin.register(IrSequence)
class SequenceAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'prefix', 'number_next', 'active']
    list_filter = ['active', 'implementation', 'company_id']
    search_fields = ['name', 'code']
    ordering = ['name']


# ============================================================================
# BUDGET MANAGEMENT
# ============================================================================

@admin.register(CrossoveredBudget)
class BudgetAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'date_from', 'date_to', 'state', 'user_id']
    list_filter = ['state', 'date_from', 'date_to', 'company_id']
    search_fields = ['name', 'code']
    ordering = ['-date_from']


@admin.register(CrossoveredBudgetLines)
class BudgetLineAdmin(admin.ModelAdmin):
    list_display = ['crossovered_budget_id', 'general_budget_id', 'planned_amount', 'practical_amount', 'percentage']
    list_filter = ['crossovered_budget_id', 'general_budget_id', 'company_id']
    search_fields = ['crossovered_budget_id__name', 'general_budget_id__name']


# ============================================================================
# PRODUCT & SALES
# ============================================================================

@admin.register(ProductCategory)
class ProductCategoryAdmin(admin.ModelAdmin):
    list_display = ['name', 'complete_name', 'parent_id', 'sequence']
    list_filter = ['parent_id']
    search_fields = ['name', 'complete_name']
    ordering = ['sequence', 'name']


@admin.register(ProductTemplate)
class ProductTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'type', 'categ_id', 'list_price', 'standard_price', 'sale_ok', 'purchase_ok']
    list_filter = ['type', 'categ_id', 'sale_ok', 'purchase_ok', 'active']
    search_fields = ['name', 'description']
    ordering = ['name']


@admin.register(ProductProduct)
class ProductProductAdmin(admin.ModelAdmin):
    list_display = ['name', 'default_code', 'barcode', 'active']
    list_filter = ['active', 'product_tmpl_id__categ_id']
    search_fields = ['product_tmpl_id__name', 'default_code', 'barcode']
    ordering = ['product_tmpl_id__name']


# ============================================================================
# INVENTORY
# ============================================================================

@admin.register(StockLocation)
class StockLocationAdmin(admin.ModelAdmin):
    list_display = ['name', 'complete_name', 'usage', 'active']
    list_filter = ['usage', 'active', 'company_id']
    search_fields = ['name', 'complete_name']
    ordering = ['complete_name']


@admin.register(StockWarehouse)
class StockWarehouseAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'partner_id', 'active']
    list_filter = ['active', 'company_id']
    search_fields = ['name', 'code']
    ordering = ['sequence', 'name']


@admin.register(StockMove)
class StockMoveAdmin(admin.ModelAdmin):
    list_display = ['name', 'product_id', 'product_qty', 'location_id', 'location_dest_id', 'state', 'date']
    list_filter = ['state', 'date', 'product_id', 'company_id']
    search_fields = ['name', 'product_id__product_tmpl_id__name', 'origin']
    ordering = ['-date']


# ============================================================================
# SYSTEM & LOCALIZATION
# ============================================================================

@admin.register(ResCountry)
class CountryAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'phone_code', 'currency_id']
    search_fields = ['name', 'code']
    ordering = ['name']


@admin.register(ResCurrency)
class CurrencyAdmin(admin.ModelAdmin):
    list_display = ['name', 'symbol', 'rate', 'decimal_places', 'active']
    list_filter = ['active']
    search_fields = ['name', 'symbol']
    ordering = ['name']


@admin.register(MailMessage)
class MailMessageAdmin(admin.ModelAdmin):
    list_display = ['subject', 'date', 'message_type', 'author_id', 'model', 'res_id']
    list_filter = ['message_type', 'date', 'model']
    search_fields = ['subject', 'body', 'author_id__name']
    ordering = ['-date']


# ============================================================================
# MISSING MODELS - REGISTER ALL REMAINING MODELS
# ============================================================================

@admin.register(AccountTaxRepartitionLine)
class TaxRepartitionLineAdmin(admin.ModelAdmin):
    list_display = ['factor_percent', 'repartition_type', 'account_id', 'sequence']
    list_filter = ['repartition_type', 'company_id']
    ordering = ['sequence']


@admin.register(AccountFullReconcile)
class FullReconcileAdmin(admin.ModelAdmin):
    list_display = ['name', 'exchange_move_id']
    search_fields = ['name']


@admin.register(AccountPartialReconcile)
class PartialReconcileAdmin(admin.ModelAdmin):
    list_display = ['debit_move_id', 'credit_move_id', 'amount', 'full_reconcile_id']
    search_fields = ['debit_move_id__name', 'credit_move_id__name']


@admin.register(AccountPaymentTermLine)
class PaymentTermLineAdmin(admin.ModelAdmin):
    list_display = ['payment_id', 'value', 'value_amount', 'days', 'sequence']
    list_filter = ['value']
    ordering = ['sequence']


@admin.register(AccountFiscalPositionTax)
class FiscalPositionTaxAdmin(admin.ModelAdmin):
    list_display = ['position_id', 'tax_src_id', 'tax_dest_id']
    search_fields = ['position_id__name', 'tax_src_id__name']


@admin.register(AccountFiscalPositionAccount)
class FiscalPositionAccountAdmin(admin.ModelAdmin):
    list_display = ['position_id', 'account_src_id', 'account_dest_id']
    search_fields = ['position_id__name', 'account_src_id__name']


@admin.register(AccountAnalyticLine)
class AnalyticLineAdmin(admin.ModelAdmin):
    list_display = ['name', 'date', 'account_id', 'amount', 'partner_id']
    list_filter = ['date', 'account_id', 'company_id']
    search_fields = ['name', 'account_id__name', 'partner_id__name']
    ordering = ['-date']


@admin.register(AccountAssetDepreciationLine)
class AssetDepreciationLineAdmin(admin.ModelAdmin):
    list_display = ['asset_id', 'depreciation_date', 'amount', 'remaining_value', 'move_posted']
    list_filter = ['move_posted', 'depreciation_date', 'asset_id']
    ordering = ['depreciation_date']


@admin.register(AccountBudgetPost)
class BudgetPostAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'company_id']
    search_fields = ['name', 'code']
    ordering = ['name']


@admin.register(AccountInvoiceReport)
class InvoiceReportAdmin(admin.ModelAdmin):
    list_display = ['date', 'partner_id', 'move_type', 'state', 'price_subtotal', 'residual']
    list_filter = ['move_type', 'state', 'date', 'company_id']
    search_fields = ['partner_id__name']
    ordering = ['-date']


@admin.register(AccountMoveLineReport)
class MoveLineReportAdmin(admin.ModelAdmin):
    list_display = ['date', 'account_code', 'account_name', 'debit', 'credit', 'balance', 'reconciled']
    list_filter = ['account_type', 'reconciled', 'date', 'company_id']
    search_fields = ['account_code', 'account_name']
    ordering = ['-date']


@admin.register(AccountAgedTrialBalance)
class AgedTrialBalanceAdmin(admin.ModelAdmin):
    list_display = ['report_date', 'account_id', 'partner_id', 'not_due', 'period_1', 'period_2', 'total']
    list_filter = ['report_date', 'account_id', 'company_id']
    search_fields = ['account_id__name', 'partner_id__name']
    ordering = ['-report_date']


@admin.register(IrCron)
class CronAdmin(admin.ModelAdmin):
    list_display = ['name', 'active', 'interval_number', 'interval_type', 'nextcall']
    list_filter = ['active', 'interval_type']
    search_fields = ['name', 'model_id', 'function']
    ordering = ['nextcall']


@admin.register(AccountAutomaticEntryWizard)
class AutomaticEntryWizardAdmin(admin.ModelAdmin):
    list_display = ['action', 'date', 'journal_id', 'account_id']
    list_filter = ['action', 'date', 'company_id']
    ordering = ['-date']


@admin.register(MailMessageSubtype)
class MailMessageSubtypeAdmin(admin.ModelAdmin):
    list_display = ['name', 'res_model', 'internal', 'default', 'sequence']
    list_filter = ['internal', 'default', 'res_model']
    search_fields = ['name', 'res_model']
    ordering = ['sequence', 'name']


@admin.register(MailNotification)
class MailNotificationAdmin(admin.ModelAdmin):
    list_display = ['mail_message_id', 'res_partner_id', 'notification_type', 'notification_status', 'is_read']
    list_filter = ['notification_type', 'notification_status', 'is_read']
    search_fields = ['res_partner_id__name']
    ordering = ['-mail_message_id__date']


@admin.register(MailTrackingValue)
class MailTrackingValueAdmin(admin.ModelAdmin):
    list_display = ['field_desc', 'field_type', 'old_value_char', 'new_value_char', 'mail_message_id']
    list_filter = ['field_type']
    search_fields = ['field', 'field_desc']
    ordering = ['-mail_message_id__date']


@admin.register(ResCountryState)
class CountryStateAdmin(admin.ModelAdmin):
    list_display = ['name', 'code', 'country_id']
    list_filter = ['country_id']
    search_fields = ['name', 'code']
    ordering = ['country_id', 'name']


@admin.register(AccountFinancialReport)
class FinancialReportAdmin(admin.ModelAdmin):
    list_display = ['name', 'parent_id', 'type', 'sequence', 'level']
    list_filter = ['type', 'level']
    search_fields = ['name']
    ordering = ['sequence', 'name']


# Customize admin site
admin.site.site_header = "🎉 Complete ERP System - Modular Architecture"
admin.site.site_title = "ERP Admin"
admin.site.index_title = "Accounting & Business Management"
