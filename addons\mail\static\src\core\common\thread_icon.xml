<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <t t-name="mail.ThreadIcon">
        <div class="o-mail-ThreadIcon d-flex justify-content-center flex-shrink-0" t-att-class="props.className">
            <t t-if="props.thread.type === 'channel'">
                <div t-if="props.thread.authorizedGroupFullName" class="fa fa-fw fa-hashtag" t-att-title="props.thread.accessRestrictedToGroupText"/>
                <div t-if="!props.thread.authorizedGroupFullName" class="fa fa-fw fa-globe" title="Public Channel"/>
            </t>
            <t t-elif="props.thread.type === 'chat' and chatPartner">
                <t name="chat">
                    <t name="chat_static">
                        <div t-if="chatPartner.im_status === 'online'" class="fa fa-fw fa-circle text-success" title="Online"/>
                        <div t-elif="chatPartner.im_status === 'offline'" class="fa fa-fw fa-circle-o" title="Offline"/>
                        <div t-elif="chatPartner.im_status === 'away'" class="fa fa-fw fa-circle o-yellow" title="Away"/>
                        <div t-elif="chatPartner.im_status === 'bot'" class="fa fa-fw fa-heart text-success" title="Bot"/>
                        <div t-else="" class="fa fa-fw fa-question-circle" title="No IM status available"/>
                    </t>
                </t>
            </t>
            <div t-elif="props.thread.type === 'group'" class="o-mail-ThreadIcon fa fa-fw fa-users" title="Grouped Chat"/>
            <t t-elif="props.thread.type === 'mailbox'">
                <div t-if="props.thread.id === 'inbox'" class="fa fa-fw fa-inbox" title="Inbox"/>
                <div t-elif="props.thread.id === 'starred'" class="fa fa-fw fa-star-o" title="Favorites"/>
                <div t-elif="props.thread.id === 'history'" class="fa fa-fw fa-history" title="History"/>
            </t>
        </div>
    </t>

</templates>
