
/**************************************************
*  Filepath: /web/static/src/polyfills/object.js  *
*  Lines: 3                                       *
**************************************************/
if (!Object.hasOwn) {
    Object.hasOwn = (obj, key) => Object.prototype.hasOwnProperty.call(obj, key);
}
;

/*************************************************
*  Filepath: /web/static/src/polyfills/array.js  *
*  Lines: 11                                     *
*************************************************/
if (!Array.prototype.at) {
    Object.defineProperty(Array.prototype, "at", {
        enumerable: false,
        value: function (index) {
            if (index >= 0) {
                return this[index];
            }
            return this[this.length + index];
        }
    });
}
;

/***********************************************
*  Filepath: /web/static/src/module_loader.js  *
*  Lines: 202                                  *
***********************************************/
/**
 *------------------------------------------------------------------------------
 * Odoo Web Boostrap Code
 *------------------------------------------------------------------------------
 */
(function () {
    "use strict";

    class ModuleLoader {
        /** @type {Map<string,{fn: Function, deps: string[]}>} mapping name => deps/fn */
        factories = new Map();
        /** @type {Set<string>} names of modules waiting to be started */
        jobs = new Set();
        /** @type {Set<string>} names of failed modules */
        failed = new Set();

        /** @type {Map<string,any>} mapping name => value */
        modules = new Map();

        bus = new EventTarget();

        checkErrorProm = null;

        /**
         * @param {string} name
         * @param {string[]} deps
         * @param {Function} factory
         */
        define(name, deps, factory) {
            if (typeof name !== "string") {
                throw new Error(`Invalid name definition: ${name} (should be a string)"`);
            }
            if (!(deps instanceof Array)) {
                throw new Error(`Dependencies should be defined by an array: ${deps}`);
            }
            if (typeof factory !== "function") {
                throw new Error(`Factory should be defined by a function ${factory}`);
            }
            if (!this.factories.has(name)) {
                this.factories.set(name, {
                    deps,
                    fn: factory,
                    ignoreMissingDeps: globalThis.__odooIgnoreMissingDependencies,
                });
                this.addJob(name);
                this.checkErrorProm ||= Promise.resolve().then(() => {
                    this.checkAndReportErrors();
                    this.checkErrorProm = null;
                });
            }
        }

        addJob(name) {
            this.jobs.add(name);
            this.startModules();
        }

        findJob() {
            for (const job of this.jobs) {
                if (this.factories.get(job).deps.every((dep) => this.modules.has(dep))) {
                    return job;
                }
            }
            return null;
        }

        startModules() {
            let job;
            while ((job = this.findJob())) {
                this.startModule(job);
            }
        }

        startModule(name) {
            const require = (name) => this.modules.get(name);
            this.jobs.delete(name);
            const factory = this.factories.get(name);
            let value = null;
            try {
                value = factory.fn(require);
            } catch (error) {
                this.failed.add(name);
                throw new Error(`Error while loading "${name}":\n${error}`);
            }
            this.modules.set(name, value);
            this.bus.dispatchEvent(
                new CustomEvent("module-started", { detail: { moduleName: name, module: value } })
            );
        }

        findErrors() {
            // cycle detection
            const dependencyGraph = new Map();
            for (const job of this.jobs) {
                dependencyGraph.set(job, this.factories.get(job).deps);
            }
            function visitJobs(jobs, visited = new Set()) {
                for (const job of jobs) {
                    const result = visitJob(job, visited);
                    if (result) {
                        return result;
                    }
                }
                return null;
            }

            function visitJob(job, visited) {
                if (visited.has(job)) {
                    const jobs = Array.from(visited).concat([job]);
                    const index = jobs.indexOf(job);
                    return jobs
                        .slice(index)
                        .map((j) => `"${j}"`)
                        .join(" => ");
                }
                const deps = dependencyGraph.get(job);
                return deps ? visitJobs(deps, new Set(visited).add(job)) : null;
            }

            // missing dependencies
            const missing = new Set();
            for (const job of this.jobs) {
                const factory = this.factories.get(job);
                if (factory.ignoreMissingDeps) {
                    continue;
                }
                for (const dep of factory.deps) {
                    if (!this.factories.has(dep)) {
                        missing.add(dep);
                    }
                }
            }

            return {
                failed: [...this.failed],
                cycle: visitJobs(this.jobs),
                missing: [...missing],
                unloaded: [...this.jobs].filter((j) => !this.factories.get(j).ignoreMissingDeps),
            };
        }

        async checkAndReportErrors() {
            const { failed, cycle, missing, unloaded } = this.findErrors();
            if (!failed.length && !unloaded.length) {
                return;
            }
            const debug = new URLSearchParams(location.search).get("debug");
            if (debug && debug !== "0") {
                const style = document.createElement("style");
                style.textContent = `
                    body::before {
                        font-weight: bold;
                        content: "An error occurred while loading javascript modules, you may find more information in the devtools console";
                        position: fixed;
                        left: 0;
                        bottom: 0;
                        z-index: 100000000000;
                        background-color: #C00;
                        color: #DDD;
                    }
                `;
                document.head.appendChild(style);
            }

            if (failed.length) {
                console.error("The following modules failed to load because of an error:", failed);
            }
            if (missing) {
                console.error(
                    "The following modules are needed by other modules but have not been defined, they may not be present in the correct asset bundle:",
                    missing
                );
            }
            if (cycle) {
                console.error(
                    "The following modules could not be loaded because they form a dependency cycle:",
                    cycle
                );
            }
            if (unloaded) {
                console.error(
                    "The following modules could not be loaded because they have unmet dependencies, this is a secondary error which is likely caused by one of the above problems:",
                    unloaded
                );
            }
        }
    }

    if (!globalThis.odoo) {
        globalThis.odoo = {};
    }
    const odoo = globalThis.odoo;
    if (odoo.debug && !new URLSearchParams(location.search).has("debug")) {
        // remove debug mode if not explicitely set in url
        odoo.debug = "";
    }

    const loader = new ModuleLoader();
    odoo.define = loader.define.bind(loader);

    odoo.loader = loader;
})();
;

/*****************************************
*  Filepath: /web/static/src/session.js  *
*  Lines: 10                             *
*****************************************/
odoo.define('@web/session', [], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const session = __exports.session = odoo.__session_info__ || {};
delete odoo.__session_info__;

return __exports;
});
;

/*****************************************************
*  Filepath: /web/static/src/core/browser/cookie.js  *
*  Lines: 45                                         *
*****************************************************/
odoo.define('@web/core/browser/cookie', [], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

/**
 * Utils to make use of document.cookie
 * https://developer.mozilla.org/en-US/docs/Web/HTTP/Cookies
 * As recommended, storage should not be done by the cookie
 * but with localStorage/sessionStorage
 */

const COOKIE_TTL = 24 * 60 * 60 * 365;

const cookie = __exports.cookie = {
    get _cookieMonster() {
        return document.cookie;
    },
    set _cookieMonster(value) {
        document.cookie = value;
    },
    get(str) {
        const parts = this._cookieMonster.split("; ");
        for (const part of parts) {
            const [key, value] = part.split(/=(.*)/);
            if (key === str) {
                return value || "";
            }
        }
    },
    set(key, value, ttl = COOKIE_TTL) {
        let fullCookie = [];
        if (value !== undefined) {
            fullCookie.push(`${key}=${value}`);
        }
        fullCookie = fullCookie.concat(["path=/", `max-age=${Math.floor(ttl)}`]);
        this._cookieMonster = fullCookie.join("; ");
    },
    delete(key) {
        this.set(key, "kill", 0);
    },
};

return __exports;
});
;

/************************************************************
*  Filepath: /web/static/src/legacy/js/core/minimal_dom.js  *
*  Lines: 145                                               *
************************************************************/
odoo.define('@web/legacy/js/core/minimal_dom', [], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const DEBOUNCE = __exports.DEBOUNCE = 400;
const BUTTON_HANDLER_SELECTOR = __exports.BUTTON_HANDLER_SELECTOR = 'a, button, input[type="submit"], input[type="button"], .btn';

/**
 * Protects a function which is to be used as a handler by preventing its
 * execution for the duration of a previous call to it (including async
 * parts of that call).
 *
 * Limitation: as the handler is ignored during async actions,
 * the 'preventDefault' or 'stopPropagation' calls it may want to do
 * will be ignored too. Using the 'preventDefault' and 'stopPropagation'
 * arguments solves that problem.
 *
 * @param {function} fct
 *      The function which is to be used as a handler. If a promise
 *      is returned, it is used to determine when the handler's action is
 *      finished. Otherwise, the return is used as jQuery uses it.
 * @param {function|boolean} preventDefault
 * @param {function|boolean} stopPropagation
 */
__exports.makeAsyncHandler = makeAsyncHandler; function makeAsyncHandler(fct, preventDefault, stopPropagation) {
    // TODO in master, add those as arguments.
    const stopImmediatePropagation = this && this.__makeAsyncHandler_stopImmediatePropagation;

    let pending = false;
    function _isLocked() {
        return pending;
    }
    function _lock() {
        pending = true;
    }
    function _unlock() {
        pending = false;
    }
    return function (ev) {
        if (preventDefault === true || preventDefault && preventDefault()) {
            ev.preventDefault();
        }
        if (stopPropagation === true || stopPropagation && stopPropagation()) {
            ev.stopPropagation();
        }
        if (stopImmediatePropagation === true || stopImmediatePropagation && stopImmediatePropagation()) {
            ev.stopImmediatePropagation();
        }

        if (_isLocked()) {
            // If a previous call to this handler is still pending, ignore
            // the new call.
            return;
        }

        _lock();
        const result = fct.apply(this, arguments);
        Promise.resolve(result).finally(_unlock);
        return result;
    };
}

/**
 * Creates a debounced version of a function to be used as a button click
 * handler. Also improves the handler to disable the button for the time of
 * the debounce and/or the time of the async actions it performs.
 *
 * Limitation: if two handlers are put on the same button, the button will
 * become enabled again once any handler's action finishes (multiple click
 * handlers should however not be bound to the same button).
 *
 * @param {function} fct
 *      The function which is to be used as a button click handler. If a
 *      promise is returned, it is used to determine when the button can be
 *      re-enabled. Otherwise, the return is used as jQuery uses it.
 */
__exports.makeButtonHandler = makeButtonHandler; function makeButtonHandler(fct) {
    // TODO in master, add those as arguments. Even though buttons are probably
    // blocked by the o_website_btn_loading and related classes, it is not
    // necessarily true for all event types.
    const preventDefault = this && this.__makeButtonHandler_preventDefault;
    const stopPropagation = this && this.__makeButtonHandler_stopPropagation;
    const stopImmediatePropagation = this && this.__makeButtonHandler_stopImmediatePropagation;

    // Fallback: if the final handler is not bound to a button, at least
    // make it an async handler (also handles the case where some events
    // might ignore the disabled state of the button).
    fct = makeAsyncHandler.call({
        '__makeAsyncHandler_stopImmediatePropagation': stopImmediatePropagation,
    }, fct, preventDefault, stopPropagation);

    return function (ev) {
        const result = fct.apply(this, arguments);

        const buttonEl = ev.target && ev.target.closest && ev.target.closest(BUTTON_HANDLER_SELECTOR);
        if (!(buttonEl instanceof HTMLElement)) {
            return result;
        }

        // Disable the button for the duration of the handler's action
        // or at least for the duration of the click debounce. This makes
        // a 'real' debounce creation useless. Also, during the debouncing
        // part, the button is disabled without any visual effect.
        buttonEl.classList.add('pe-none');
        Promise.resolve(DEBOUNCE && new Promise(r => setTimeout(r, DEBOUNCE)))
            .then(function () {
                buttonEl.classList.remove('pe-none');
                const restore = addButtonLoadingEffect(buttonEl);
                return Promise.resolve(result).then(restore, restore);
            });

        return result;
    };
}

/**
 * Gives the button a loading effect by disabling it and adding a `fa`
 * spinner icon.
 * The existing button `fa` icons will be hidden through css.
 *
 * @param {HTMLElement} btnEl - the button to disable/load
 * @return {function} a callback function that will restore the button
 *         initial state
 */
__exports.addButtonLoadingEffect = addButtonLoadingEffect; function addButtonLoadingEffect(btnEl) {
    if (!(btnEl instanceof HTMLElement)) {
        return () => {};
    }
    // Note that pe-none is used alongside "disabled" so that the behavior is
    // the same on links not using the "btn" class -> pointer-events disabled.
    btnEl.classList.add('o_website_btn_loading', 'disabled', 'pe-none');
    btnEl.disabled = true;
    const loaderEl = document.createElement('span');
    loaderEl.classList.add('fa', 'fa-refresh', 'fa-spin', 'me-2');
    btnEl.prepend(loaderEl);
    return () => {
        btnEl.classList.remove('o_website_btn_loading', 'disabled', 'pe-none');
        btnEl.disabled = false;
        loaderEl.remove();
    };
}

return __exports;
});
;

/*************************************************************
*  Filepath: /web/static/src/legacy/js/public/lazyloader.js  *
*  Lines: 193                                                *
*************************************************************/
odoo.define('@web/legacy/js/public/lazyloader', ['@web/legacy/js/core/minimal_dom'], function (require) {
'use strict';
let __exports = {};
/** @odoo-module **/

const {
    BUTTON_HANDLER_SELECTOR,
    makeAsyncHandler,
    makeButtonHandler,
} = require('@web/legacy/js/core/minimal_dom');

// Track when all JS files have been lazy loaded. Will allow to unblock the
// related DOM sections when the whole JS have been loaded and executed.
let allScriptsLoadedResolve = null;
const _allScriptsLoaded = new Promise(resolve => {
    allScriptsLoadedResolve = resolve;
}).then(stopWaitingLazy);

const retriggeringWaitingProms = [];
/**
 * Function to use as an event handler to replay the incoming event after the
 * whole lazy JS has been loaded. Note that blocking the incoming event is left
 * up to the caller (i.e. a potential wrapper, @see waitLazy).
 *
 * @param {Event} ev
 * @returns {Promise}
 */
async function waitForLazyAndRetrigger(ev) {
    // Wait for the lazy JS to be loaded before re-triggering the event.
    const targetEl = ev.target;
    await _allScriptsLoaded;
    // Loaded scripts were able to add a delay to wait for before re-triggering
    // events: we wait for it here.
    await Promise.all(retriggeringWaitingProms);

    // At the end of the current execution queue, retrigger the event. Note that
    // the event is reconstructed: this is necessary in some cases, e.g. submit
    // buttons. Probably because the event was originally defaultPrevented.
    setTimeout(() => {
        // Extra safety check: the element might have been removed from the DOM
        if (targetEl.isConnected) {
            targetEl.dispatchEvent(new ev.constructor(ev.type, ev));
        }
    }, 0);
}

const loadingEffectHandlers = [];
/**
 * Adds the given event listener and saves it for later removal.
 *
 * @param {HTMLElement} el
 * @param {string} type
 * @param {Function} handler
 */
function registerLoadingEffectHandler(el, type, handler) {
    el.addEventListener(type, handler, {capture: true});
    loadingEffectHandlers.push({el, type, handler});
}

let waitingLazy = false;

/**
 * Automatically adds a loading effect on clicked buttons (that were not marked
 * with a specific class). Once the whole JS has been loaded, the events will be
 * triggered again.
 *
 * For forms, we automatically prevent submit events (since can be triggered
 * without click on a button) but we do not retrigger them (could be duplicate
 * with re-trigger of a click on a submit button otherwise). However, submitting
 * a form in any way should most of the time simulate a click on the submit
 * button if any anyway.
 *
 * @todo This function used to consider the o_wait_lazy_js class. In master, the
 * uses of this classes should be removed in XML templates.
 * @see stopWaitingLazy
 */
function waitLazy() {
    if (waitingLazy) {
        return;
    }
    waitingLazy = true;

    document.body.classList.add('o_lazy_js_waiting');

    // TODO should probably find the wrapwrap another way but in future versions
    // the element will be gone anyway.
    const mainEl = document.getElementById('wrapwrap') || document.body;
    const loadingEffectButtonEls = [...mainEl.querySelectorAll(BUTTON_HANDLER_SELECTOR)]
        // We target all buttons but...
        .filter(el => {
            // ... we allow to disable the effect by adding a specific class if
            // needed. Note that if some non-lazy loaded code is adding an event
            // handler on some buttons, it means that if they do not have that
            // class, they will show a loading effect and not do anything until
            // lazy JS is loaded anyway. This is not ideal, especially since
            // this was added as a stable fix/imp, but this is a compromise: on
            // next page visits, the cache should limit to effect of the lazy
            // loading anyway.
            return !el.classList.contains('o_no_wait_lazy_js')
                // ... we also allow do not consider links with a href which is
                // not "#". They could be linked to handlers that prevent their
                // default behavior but we consider that following the link
                // should still be relevant in that case.
                && !(el.nodeName === 'A' && el.href && el.getAttribute('href') !== '#');
        });
    // Note: this is a limitation/a "risk" to only block and retrigger those
    // specific event types.
    const loadingEffectEventTypes = ['mouseover', 'mouseenter', 'mousedown', 'mouseup', 'click', 'mouseout', 'mouseleave'];
    for (const buttonEl of loadingEffectButtonEls) {
        for (const eventType of loadingEffectEventTypes) {
            const loadingEffectHandler = eventType === 'click'
                ? makeButtonHandler.call({
                    '__makeButtonHandler_preventDefault': true,
                    '__makeButtonHandler_stopImmediatePropagation': true,
                }, waitForLazyAndRetrigger)
                : makeAsyncHandler.call({
                    '__makeAsyncHandler_stopImmediatePropagation': true,
                }, waitForLazyAndRetrigger, true);
            registerLoadingEffectHandler(buttonEl, eventType, loadingEffectHandler);
        }
    }

    for (const formEl of document.querySelectorAll('form:not(.o_no_wait_lazy_js)')) {
        registerLoadingEffectHandler(formEl, 'submit', ev => {
            ev.preventDefault();
            ev.stopImmediatePropagation();
        });
    }
}
/**
 * Undo what @see waitLazy did.
 */
function stopWaitingLazy() {
    if (!waitingLazy) {
        return;
    }
    waitingLazy = false;

    document.body.classList.remove('o_lazy_js_waiting');

    for (const { el, type, handler } of loadingEffectHandlers) {
        el.removeEventListener(type, handler, {capture: true});
    }
}

// Start waiting for lazy loading as soon as the DOM is available
if (document.readyState !== 'loading') {
    waitLazy();
} else {
    document.addEventListener('DOMContentLoaded', function () {
        waitLazy();
    });
}

// As soon as the document is fully loaded, start loading the whole remaining JS
if (document.readyState === 'complete') {
    setTimeout(_loadScripts, 0);
} else {
    window.addEventListener('load', function () {
        setTimeout(_loadScripts, 0);
    });
}

/**
 * @param {DOMElement[]} scripts
 * @param {integer} index
 */
function _loadScripts(scripts, index) {
    if (scripts === undefined) {
        scripts = document.querySelectorAll('script[data-src]');
    }
    if (index === undefined) {
        index = 0;
    }
    if (index >= scripts.length) {
        allScriptsLoadedResolve();
        return;
    }
    const script = scripts[index];
    script.addEventListener('load', _loadScripts.bind(this, scripts, index + 1));
    script.setAttribute('defer', 'defer');
    script.src = script.dataset.src;
    script.removeAttribute('data-src');
}

__exports[Symbol.for("default")] = {
    loadScripts: _loadScripts,
    allScriptsLoaded: _allScriptsLoaded,
    registerPageReadinessDelay: retriggeringWaitingProms.push.bind(retriggeringWaitingProms),
};

return __exports;
});
;

/*******************************************************************
*  Filepath: /web_editor/static/src/js/frontend/loader_loading.js  *
*  Lines: 33                                                       *
*******************************************************************/
(function () {
'use strict';

/**
 * This file makes sure textarea elements with a specific editor class are
 * tweaked as soon as the DOM is ready so that they appear to be loading.
 *
 * They must then be loaded using standard Odoo modules system. In particular,
 * @see @web_editor/js/frontend/loadWysiwygFromTextarea
 */

document.addEventListener('DOMContentLoaded', () => {
    // Standard loop for better browser support
    var textareaEls = document.querySelectorAll('textarea.o_wysiwyg_loader');
    for (var i = 0; i < textareaEls.length; i++) {
        var textarea = textareaEls[i];
        var wrapper = document.createElement('div');
        wrapper.classList.add('position-relative', 'o_wysiwyg_textarea_wrapper');

        var loadingElement = document.createElement('div');
        loadingElement.classList.add('o_wysiwyg_loading');
        var loadingIcon = document.createElement('i');
        loadingIcon.classList.add('text-600', 'text-center',
            'fa', 'fa-circle-o-notch', 'fa-spin', 'fa-2x');
        loadingElement.appendChild(loadingIcon);
        wrapper.appendChild(loadingElement);

        textarea.parentNode.insertBefore(wrapper, textarea);
        wrapper.insertBefore(textarea, loadingElement);
    }
});

})();


//# sourceMappingURL=/web/assets/a5bde4f/web.assets_frontend_minimal.js.map